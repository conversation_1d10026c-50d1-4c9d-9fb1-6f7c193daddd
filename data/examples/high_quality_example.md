# 车载空调系统设计文档

## 1. 功能/行为描述 (Functional/Behavioral Description)

### 1.1 主要功能
- **温度控制功能**：
  - 输入：目标温度设定值(16°C-30°C)、当前车内温度传感器读数、外部环境温度
  - 输出：制冷/制热模式激活信号、风机速度控制信号
  - 处理逻辑：PID控制算法，根据温差动态调整制冷/制热强度和风速
  - 前置条件：系统电源开启，温度传感器工作正常
  - 后置条件：车内温度逐渐接近设定值(±0.5°C)

- **空气质量控制功能**：
  - 输入：PM2.5传感器数据、VOC传感器数据、CO2浓度传感器数据
  - 输出：内外循环模式切换信号、离子发生器控制信号、风量调节信号
  - 处理逻辑：基于空气质量指数(AQI)阈值判断，当AQI>150时自动切换为内循环
  - 异常处理：传感器故障时默认切换到定时内外循环交替模式(5分钟周期)

- **除霜/除雾功能**：
  - 输入：前挡风玻璃温度传感器数据、车内湿度传感器数据
  - 输出：除霜模式激活信号、风向控制信号、压缩机高功率运行信号
  - 处理逻辑：当检测到玻璃温度<5°C且湿度>85%时自动激活
  - 边界条件：电池电量<20%时限制最大功率至70%

### 1.2 功能依赖关系
- 温度控制功能优先级最高，可覆盖其他功能的部分设置
- 除霜/除雾功能在安全考虑下可临时覆盖用户设置的风向
- 空气质量控制在极端污染条件下(AQI>300)可临时降低制冷/制热效率以确保过滤效果

### 1.3 自适应控制功能
- 基于机器学习算法的乘员习惯识别系统
- 根据历史设置模式、外部温度和使用时间预测用户偏好
- 自动调整默认温度、风速和风向，提升用户体验
- 适应精度：连续使用2周后达到80%以上的设置预测准确率

## 2. 状态机行为 (State Machine Behavior)

### 2.1 系统状态定义
1. **关闭状态(OFF)**：
   - 描述：系统完全断电，所有组件停止工作
   - 进入动作：保存当前设置到非易失性存储器
   - 退出动作：加载上次使用的设置参数

2. **待机状态(STANDBY)**：
   - 描述：系统上电但未激活制冷/制热功能，仅监控传感器数据
   - 进入动作：初始化传感器系统，执行自检程序(时长<2秒)
   - 退出动作：根据环境条件预设初始参数

3. **制冷状态(COOLING)**：
   - 描述：压缩机工作在制冷模式，蒸发器吸收车内热量
   - 进入动作：启动压缩机，设置膨胀阀开度，激活冷媒循环
   - 持续活动：动态调整压缩机转速(800-6500RPM)以匹配负载需求

4. **制热状态(HEATING)**：
   - 描述：热泵模式或PTC加热器工作，向车内输送热量
   - 进入动作：反向激活四通阀或启动PTC加热元件
   - 退出动作：冷却PTC元件(持续30秒)以防止热损伤

5. **除霜状态(DEFROST)**：
   - 描述：高功率热风定向吹向前挡风玻璃，快速除霜/除雾
   - 进入动作：将风向调至前挡，风速设为最高，温度设为最高
   - 持续活动：监测玻璃温度变化率，调整输出功率

6. **自诊断状态(DIAGNOSTIC)**：
   - 描述：系统执行全面自检，检测各组件工作状态
   - 进入动作：依次测试各执行器和传感器响应
   - 退出动作：生成诊断报告，必要时存储故障代码

7. **节能状态(ECO)**：
   - 描述：系统以最小能耗运行，牺牲部分响应速度
   - 持续活动：限制压缩机最高转速，延长温度调节周期
   - 退出动作：恢复标准控制参数

8. **紧急状态(EMERGENCY)**：
   - 描述：检测到严重故障时的安全运行模式
   - 进入动作：关闭受影响组件，激活备用系统
   - 持续活动：限制功能，定期尝试恢复正常运行

### 2.2 状态转换
- **OFF → STANDBY**：触发条件：点火开关ON或远程启动信号
- **STANDBY → COOLING**：触发条件：设定温度<当前温度-1°C且用户激活系统
- **STANDBY → HEATING**：触发条件：设定温度>当前温度+1.5°C且用户激活系统
- **COOLING/HEATING → DEFROST**：触发条件：检测到前挡风玻璃温度<5°C且湿度>85%
- **任意状态 → DIAGNOSTIC**：触发条件：用户激活诊断模式或检测到性能异常
- **COOLING/HEATING → ECO**：触发条件：用户选择ECO模式或车辆电量<30%
- **任意状态 → EMERGENCY**：触发条件：检测到传感器严重故障或制冷剂压力异常
- **任意状态 → OFF**：触发条件：点火开关OFF或用户关闭系统

## 3. 活动流程 (Activity)

### 3.1 系统启动流程
1. 接收电源ON信号
2. 执行系统自检(传感器、执行器、通信总线)
3. 读取环境参数(车内温度、湿度、外部温度)
4. 读取用户配置或加载默认设置
5. 决策点：上次使用模式是否适用于当前环境？
   - 是：恢复上次使用的模式和设置
   - 否：计算并应用最适合当前环境的默认设置
6. 激活用户界面，显示当前状态和设置
7. 进入待机状态，等待用户输入或自动控制触发

### 3.2 温度调节流程
1. 接收目标温度设定
2. 计算当前温度与目标温度的差值(ΔT)
3. 决策点：ΔT > 0？
   - 是：激活制冷模式
   - 否：激活制热模式
4. 根据|ΔT|大小计算初始功率需求
5. 并行活动：
   - 调整压缩机转速/PTC功率
   - 设置适当风速
   - 调整风向分配
6. 监测温度变化率
7. 决策点：接近目标温度？(|ΔT| < 1°C)
   - 是：进入精细调节模式
   - 否：继续当前运行模式
8. 循环：每30秒重新评估一次，直到达到目标温度或接收新设定

### 3.3 空气质量控制流程
1. 持续监测空气质量传感器数据
2. 计算综合空气质量指数
3. 决策点：空气质量是否低于阈值？
   - 是：激活空气净化流程
   - 否：维持正常通风模式
4. 空气净化流程：
   - 切换至内循环模式
   - 激活离子发生器
   - 提高风量通过HEPA过滤器
5. 监测空气质量改善情况
6. 决策点：空气质量是否恢复正常？
   - 是：恢复正常通风模式
   - 否：维持净化模式并发出提示
7. 定期(每15分钟)短时切换至外循环以防止CO2积累

## 4. 用例场景 (Use Case)

### 4.1 基本温度控制用例
- **参与者**：车辆驾驶员/乘客
- **目标**：调节车内温度至舒适水平
- **基本流程**：
  1. 用户按下空调开关激活系统
  2. 系统显示当前温度和默认设置
  3. 用户通过旋钮/触摸屏调整目标温度
  4. 系统确认设置并显示目标温度
  5. 系统评估所需模式(制热/制冷)
  6. 系统激活相应组件(压缩机/加热器)
  7. 系统调整风机速度以优化舒适度和能效
  8. 系统持续监测车内温度变化
  9. 当接近目标温度时，系统调整为维持模式
  10. 用户可随时调整设置或关闭系统
- **替代流程A**：自动模式
  1. 用户按下"AUTO"按钮
  2. 系统基于预设舒适温度(默认22°C)自动控制所有参数
  3. 系统根据温差动态调整风速和模式
- **替代流程B**：快速制热/制冷
  1. 用户按下"MAX"按钮
  2. 系统临时忽略能效考虑，以最大功率运行
  3. 达到接近目标温度后自动切换回正常模式

### 4.2 除雾/除霜用例
- **参与者**：车辆驾驶员
- **目标**：快速清除挡风玻璃上的雾气/霜层以确保行车安全
- **基本流程**：
  1. 用户按下除霜按钮
  2. 系统将风向自动调整至前挡风玻璃
  3. 系统激活高温输出模式
  4. 系统提高风机速度至高速
  5. 系统自动切换至外循环模式以减少湿度
  6. 系统持续监测玻璃温度和湿度
  7. 当检测到玻璃清晰(温度>10°C且湿度<70%)时提示用户
  8. 用户确认视野良好后可退出除霜模式
  9. 系统恢复至之前的空调设置
- **替代流程**：自动检测除霜需求
  1. 系统传感器检测到玻璃有结霜/起雾风险
  2. 系统自动提示用户是否需要激活除霜模式
  3. 用户确认后系统执行除霜程序

### 4.3 远程预热/预冷用例
- **参与者**：车辆所有者，手机应用
- **目标**：在进入车辆前预先调节车内温度
- **基本流程**：
  1. 用户通过手机应用登录车辆账户
  2. 应用显示当前车内温度和状态
  3. 用户设置预期上车时间和目标温度
  4. 应用发送指令至车辆控制系统
  5. 系统在计算的最佳时间点激活空调
  6. 系统根据外部温度和目标值选择工作模式
  7. 应用实时显示车内温度变化
  8. 达到目标温度后系统切换至维持模式
  9. 用户上车后系统自动切换至正常操作模式
- **替代流程**：能源优化模式
  1. 系统检测到车辆电量低于预设阈值
  2. 系统计算预热/预冷所需能耗
  3. 系统建议调整目标温度或缩短预热时间
  4. 用户确认调整后系统以优化模式运行

## 5. 系统结构 (System Structure)

### 5.1 主要组件
1. **控制单元(ECU)**：
   - 属性：32位微控制器，256KB RAM，2MB闪存，CAN总线接口
   - 操作：执行控制算法，处理传感器数据，发送执行器命令
   - 接口：与车辆中央控制器、显示单元和传感器网络通信

2. **压缩机组件**：
   - 属性：变频电动压缩机，800-6500RPM，最大功率3.2kW
   - 操作：压缩制冷剂，创建高低压区域
   - 接口：接收ECU控制信号，发送工作状态数据

3. **热交换系统**：
   - 属性：铝制冷凝器和蒸发器，总换热面积>0.8m²
   - 操作：实现车内外热量交换
   - 子组件：冷凝器、蒸发器、膨胀阀、四通阀(热泵模式)

4. **风道系统**：
   - 属性：多区域独立控制，8个可调风向叶片
   - 操作：分配和引导气流至不同区域
   - 子组件：风道通道、风向调节电机、混合风门

5. **风机组件**：
   - 属性：无刷直流电机，10级速度调节，最大风量800m³/h
   - 操作：产生气流，控制风速
   - 接口：接收PWM控制信号，发送转速反馈

6. **传感器网络**：
   - 属性：温度精度±0.3°C，湿度精度±3%RH
   - 操作：采集环境数据，监测系统状态
   - 子组件：温度传感器(8个)、湿度传感器(2个)、压力传感器(2个)、空气质量传感器

7. **用户界面**：
   - 属性：7英寸触控显示屏，物理旋钮控制
   - 操作：接收用户输入，显示系统状态
   - 接口：通过CAN总线与ECU通信

8. **空气净化系统**：
   - 属性：HEPA过滤器(PM2.5过滤效率>99%)，负离子发生器
   - 操作：过滤空气中的颗粒物和有害气体
   - 子组件：过滤器组件、离子发生器、活性炭层

### 5.2 组件关系
- **组合关系**：
  - 控制单元包含主处理器和多个I/O模块
  - 风道系统包含多个独立控制的风向调节装置

- **聚合关系**：
  - 传感器网络聚合了分布在车内不同位置的传感器
  - 热交换系统聚合了制冷剂循环的各个部件

- **关联关系**：
  - 控制单元与用户界面双向关联(命令和反馈)
  - 传感器网络与控制单元单向关联(数据流)

- **依赖关系**：
  - 风机组件依赖于控制单元的PWM信号
  - 空气净化系统依赖于风道系统的气流分配

## 6. 参数关系 (Parametric)

### 6.1 热力学参数
- **制冷能力(Q_cooling)**：
  - 单位：kW
  - 范围：1.5-5.8kW
  - 公式：Q_cooling = m_ref × (h_evap_out - h_evap_in)
  - 其中：m_ref为制冷剂质量流量(kg/s)，h为比焓(kJ/kg)

- **制热能力(Q_heating)**：
  - 单位：kW
  - 范围：1.8-6.2kW
  - 公式：Q_heating = m_ref × (h_cond_in - h_cond_out) + P_PTC
  - 其中：P_PTC为PTC加热器功率(kW)

- **能效比(COP)**：
  - 单位：无量纲
  - 范围：2.0-4.5(制冷)，1.8-3.8(制热)
  - 公式：COP_cooling = Q_cooling / P_input
  - 公式：COP_heating = Q_heating / P_input
  - 其中：P_input为系统总输入功率(kW)

### 6.2 控制参数
- **PID温度控制**：
  - 温度误差：e(t) = T_target - T_actual
  - 控制输出：u(t) = Kp×e(t) + Ki×∫e(t)dt + Kd×de(t)/dt
  - 参数范围：Kp=2.5-4.0, Ki=0.08-0.15, Kd=0.5-1.2

- **风速控制函数**：
  - 风速级别 = f(|ΔT|, user_preference)
  - 当|ΔT| > 5°C时，风速 = max(user_setting, auto_level)
  - 当|ΔT| < 1°C时，风速递减至低速以减少噪音

- **压缩机容量调节**：
  - 容量百分比 = 30% + 70% × (|ΔT|/ΔT_max)^0.8
  - 限制条件：变化率 < 10%/秒(防止突变)

### 6.3 舒适度参数
- **预测平均投票数(PMV)**：
  - 范围：-3(冷)至+3(热)，0为最佳
  - 目标：-0.5 < PMV < +0.5
  - 影响因素：温度、湿度、气流速度、平均辐射温度

- **温度均匀性指数(TUI)**：
  - 公式：TUI = 1 - σT/ΔT_ref
  - 其中：σT为车内测点温度标准差，ΔT_ref为参考温差(3°C)
  - 目标：TUI > 0.85

## 7. 交互序列 (Sequence)

### 7.1 系统启动序列
1. **用户→控制单元**：发送电源ON命令
2. **控制单元→传感器网络**：发送初始化命令
3. **传感器网络→控制单元**：返回初始环境数据(温度、湿度)
4. **控制单元→用户界面**：发送状态更新，显示当前条件
5. **控制单元→控制单元**：执行内部诊断(200ms)
6. **控制单元→风机组件**：发送低速运行命令
7. **控制单元→压缩机组件**：根据需求发送启动命令
8. **压缩机组件→控制单元**：确认启动状态
9. **控制单元→风道系统**：发送初始风向设置
10. **用户界面→用户**：显示系统已激活和当前设置

### 7.2 温度调节交互序列
1. **用户→用户界面**：设置目标温度(例如22°C)
2. **用户界面→控制单元**：转发温度设置请求
3. **控制单元→传感器网络**：请求当前温度数据
4. **传感器网络→控制单元**：返回当前温度(例如27°C)
5. **控制单元→控制单元**：计算所需模式和功率(ΔT=5°C，需制冷)
6. **控制单元→压缩机组件**：发送制冷模式命令，初始容量70%
7. **控制单元→风机组件**：设置风速级别4(中高)
8. **控制单元→风道系统**：优化风向分布命令
9. **控制单元→用户界面**：发送操作模式确认
10. **循环交互(每30秒)**：
    - **传感器网络→控制单元**：更新温度数据
    - **控制单元→执行器组件**：调整控制参数

### 7.3 故障处理交互序列
1. **传感器网络→控制单元**：报告异常压力读数
2. **控制单元→控制单元**：执行故障诊断算法
3. **控制单元→压缩机组件**：发送降低容量命令
4. **控制单元→用户界面**：发送警告消息
5. **用户界面→用户**：显示故障代码和建议操作
6. **用户→用户界面**：确认消息
7. **控制单元→控制单元**：记录故障代码到非易失性存储器
8. **控制单元→所有组件**：如果严重，发送安全模式命令
9. **各组件→控制单元**：确认进入安全模式
10. **控制单元→远程服务器**：发送诊断数据(如果联网)

## 8. 需求规格 (Requirements)

### 8.1 功能性需求
- **FR-01**：系统必须能够将车内温度控制在16°C至30°C范围内，精度±0.5°C
- **FR-02**：系统必须支持至少4个独立温度区域的控制(前排左右、后排左右)
- **FR-03**：系统必须能够在外部温度-20°C至+45°C的条件下正常工作
- **FR-04**：系统必须能够在5分钟内将车内温度从45°C降至25°C(夏季条件下)
- **FR-05**：系统必须能够在8分钟内将车内温度从-10°C升至+20°C(冬季条件下)
- **FR-06**：系统必须支持自动、手动、ECO和MAX四种基本运行模式
- **FR-07**：系统必须能够检测并过滤PM2.5颗粒物，过滤效率>95%
- **FR-08**：系统必须支持通过手机应用进行远程控制和预设

### 8.2 非功能性需求
- **NFR-01**：系统在正常运行条件下的能耗不得超过车辆总电量的15%
- **NFR-02**：系统在最大风速下的噪声级别不得超过55dB(A)
- **NFR-03**：系统MTBF(平均故障间隔时间)必须大于8,000小时
- **NFR-04**：系统必须在点火后2秒内完成初始化并可以接收用户输入
- **NFR-05**：用户界面的响应时间必须小于100ms
- **NFR-06**：系统必须符合ISO 13043:2011道路车辆空调系统标准
- **NFR-07**：系统必须能够存储至少100条故障记录，包含时间戳和详细参数
- **NFR-08**：系统软件必须支持OTA(空中下载)更新功能

### 8.3 设计约束
- **CON-01**：系统总重量不得超过12kg
- **CON-02**：系统必须适配现有车辆电源架构(12V/48V)
- **CON-03**：系统必须使用环保制冷剂R-1234yf
- **CON-04**：系统必须能够在车辆CAN总线上通信，支持UDS诊断协议
- **CON-05**：系统设计必须考虑-40°C至+85°C的存储温度条件

### 8.4 验证标准
- **VER-01**：温度控制精度测试必须在风洞环境中进行，测量至少9个车内点位
- **VER-02**：系统能效必须通过标准驾驶循环测试验证
- **VER-03**：除霜性能必须在-10°C环境温度下验证，前挡风玻璃80%区域必须在5分钟内除霜
- **VER-04**：系统必须通过EMC(电磁兼容性)测试，符合ISO 11452标准 