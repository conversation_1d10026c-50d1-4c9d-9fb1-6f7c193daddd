# config/settings.py
"""
配置设置
使用pydantic_settings管理配置项
"""
import os
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional

class Settings(BaseSettings):
    """配置设置类"""
    # 使用 .env 文件来加载环境变量
    model_config = SettingsConfigDict(
        env_file=os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'),
        extra='ignore'
    )
    
    # OpenAI API 配置
    base_url: str = "https://openrouter.ai/api/v1"
    # base_url: str = "https://api.deepseek.com"
    llm_model: str = "deepseek/deepseek-chat-v3-0324"
    # llm_model: str = "deepseek-reasoner"
    # openai_api_key: str ="***********************************"
    openai_api_key: str  # 从环境变量或.env文件自动加载

    # 文本分块参数
    chunk_size: int = 4000
    chunk_overlap: int = 600  # 增加重叠区域大小，减少信息丢失
    
    # 工作流配置
    max_retries: int = 3
    temperature: float = 0.1
    
    # 输出目录
    output_dir: str = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "output")

    # 需求扩展配置
    requirement_expansion_examples_path: Optional[str] = None  # 可选的示例文件路径
    
    # 任务提取配置
    task_extraction_enhanced: bool = False  # 启用增强的任务提取
    task_extraction_min_content_length: int = 10  # 最小内容长度，用于过滤无效任务
    task_extraction_similarity_threshold: float = 0.9  # 相似度阈值，用于合并相似任务

    # Neo4j数据库配置
    neo4j_uri: str = "bolt://localhost:7687"
    neo4j_user: str = "neo4j"
    neo4j_password: str = "123456789"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)


# 创建全局设置对象
settings = Settings()