# CLAUDE.md - ygagentlanggraph AI Team Configuration

This file provides guidance to <PERSON> when working with the ygagentlanggraph project - a reusable LangGraph framework being transformed from MBSE to requirement management AI microservice.

## Development Commands

### Python Core Package
```bash
# Install dependencies
pip install -r requirements.txt

# Development mode
pip install -e .

# Run tests
python -m pytest tests/

# Run specific test
python -m pytest tests/test_mbse_workflow.py -v

# Lint code
ruff check src/
ruff format src/

# Type check
mypy src/

# Run main workflow
python src/main.py --short_req "实现一个简单的自行车系统"
```

### LangGraph Development
```bash
# Visualize workflow
python src/main.py --short_req "test" --log_level DEBUG
# This will generate mermaid.png showing the workflow

# Debug specific agent
python -c "from src.agents.req_agent import RequirementAgent; import asyncio; asyncio.run(test_agent())"
```

## Architecture Overview

### 📁 AI-RME 文档体系
所有AI-RME相关文档位于 `AI-RME/` 目录：
- **AI-RME融合架构设计.md**: 需求管理AI微服务架构设计
- **AI-RME集成LightRAG_Augment.md**: LightRAG集成方案
- **ArchitectureYG.md**: 系统架构设计文档
- **摇光需求管理系统-Dify工作流设计方案.md**: 需求管理工作流设计
- **项目上下文获取策略说明.md**: 上下文获取策略

### 需求管理AI微服务架构

**改造目标**：从MBSE智能体 → 需求管理工程AI微服务
**主系统**：yg4AI (Java Spring Boot + MCP服务端，端口8890)

**AI微服务架构** (`src/ai_service/`)
- 轻量级LightRAG core集成 (无Docker依赖)
- LangGraph驱动的需求分析工作流
- MCP客户端连接yg4AI需求管理系统
- 需求知识图谱本地构建与管理

**LightRAG Core集成** (`src/embedded_lightrag/`)
- **EmbeddedLightRAG**: 轻量级RAG引擎封装
- **KnowledgeGraphBuilder**: 需求数据到知识图谱转换
- **VectorStore**: 本地向量存储 (SQLite/内存)
- **MCPDataSource**: 通过MCP协议获取需求数据

**AI工作流引擎** (`src/workflows/`)
- **BusinessRequirementAnalysisWorkflow**: 业务需求智能分析
- **RequirementChangeImpactWorkflow**: 需求变更影响分析  
- **RequirementComplianceWorkflow**: 需求合规性检查
- **RequirementConflictDetectionWorkflow**: 需求冲突检测

**MCP客户端** (`src/mcp_client/`)
- **YGMCPClient**: yg4AI系统MCP接口客户端
- **RequirementFetcher**: 从主系统获取需求数据
- **ResultPublisher**: 将分析结果回传主系统

**存储架构**
- **本地缓存**: 知识图谱和向量数据
- **临时存储**: 处理过程中的中间数据
- **主数据**: 通过MCP从yg4AI获取

### Key Concepts

**嵌入式LightRAG集成模式**
```python
from src.embedded_lightrag import EmbeddedLightRAG
from src.mcp_client import YGMCPClient

# 轻量级RAG集成
rag = EmbeddedLightRAG(
    vector_store="local",  # 本地向量存储
    graph_store="memory",  # 内存图存储
    llm_provider="openai"
)

# MCP客户端配置
mcp_client = YGMCPClient(
    base_url="http://localhost:8890",
    tools=["get_requirements", "update_analysis"]
)
```

**AI工作流数据流**
1. **数据获取**: 通过MCP从yg4AI获取需求
2. **知识构建**: LightRAG构建知识图谱
3. **AI分析**: LangGraph工作流执行
4. **结果回传**: 通过MCP返回分析结果

**嵌入式架构优势**
- **轻量级**: 无Docker依赖，本地运行
- **高性能**: 内存存储，快速响应
- **零侵入**: 不改变主系统架构
- **可扩展**: 支持多种存储后端

### Configuration

**Environment Variables**
```bash
# LLM Configuration
OPENAI_API_KEY=your-openai-key
OPENAI_BASE_URL=https://api.openai.com/v1
LLM_MODEL=gpt-4o-mini

# MCP Configuration
YG_SERVER_URL=http://localhost:8890
MCP_TIMEOUT=30

# LightRAG Configuration
LIGHTRAG_STORAGE_DIR=./rag_storage
VECTOR_STORE_TYPE=local
GRAPH_STORE_TYPE=memory

# Processing
MAX_TOKENS=4000
TEMPERATURE=0.3
BATCH_SIZE=100
```

**File Structure**
```
ygagentlanggraph/
├── src/
│   ├── embedded_lightrag/    # 嵌入式LightRAG封装
│   ├── mcp_client/          # MCP客户端实现
│   ├── workflows/           # AI工作流引擎
│   ├── ai_service/          # AI服务封装
│   ├── utils/               # 工具函数
│   └── config/              # 配置管理
├── tests/
│   ├── integration/         # 集成测试
│   └── unit/               # 单元测试
├── rag_storage/            # LightRAG存储
├── logs/                   # 应用日志
└── scripts/                # 启动脚本
```

## AI Team Task Assignments

| Task Category | Recommended Agent | Notes & Best Practices |
|---------------|-------------------|------------------------|
| **嵌入式LightRAG集成** | `backend-developer` | 轻量级RAG核心封装 |
| **MCP客户端开发** | `backend-developer` | yg4AI系统MCP接口调用 |
| **AI工作流引擎** | `backend-developer` | LangGraph驱动的需求分析 |
| **性能优化** | `performance-optimizer` | 内存存储和查询优化 |
| **API设计** | `api-architect` | MCP接口规范设计 |
| **代码审查** | `code-reviewer` | AI微服务安全性检查 |

### 嵌入式LightRAG开发重点

#### 1. **LightRAG包引入方式**
```python
# ✅ 正确方式：直接包引入，不提取源码
from lightrag import LightRAG
from lightrag.llm.openai import gpt_4o_mini_complete, openai_embed

# ❌ 错误方式：提取源码、复制粘贴、修改源码
# 不修改LightRAG源码，直接使用pip安装的包

# ⚠️ 开发规则：
# 1. 先查看LightRAG源码理解用法
# 2. 基于pip包开发，不复制源码
# 3. 遇到问题先搜索lightrag官方文档
# 4. 运行验证前先看LIGHT_RAG_INTEGRATION_SUMMARY.md

Example: @backend-developer "implement LightRAG integration using pip package lightrag-hku"

#### 2. **MCP客户端集成**
```python
# Use backend-developer for:
# - yg4AI的MCP接口客户端实现
# - 需求数据拉取和结果上传
# - 错误处理和重试机制
# - 连接池和缓存优化

Example: @backend-developer "implement MCP client for yg4AI requirement fetching"
```

#### 3. **AI工作流实现**
```python
# Use backend-developer for:
# - AI-RME文档中的四个核心工作流
# - 需求分析、变更影响、合规检查、冲突检测
# - 与LightRAG的知识图谱集成
# - 结果格式化和MCP回传

Example: @backend-developer "implement requirement compliance check workflow with LightRAG"
```

#### 4. **AI-RME文档写作规范**
所有AI-RME相关文档写入 `AI-RME/` 目录：
```bash
# 新增文档示例
AI-RME/需求管理微服务改造计划.md
AI-RME/MCP协议集成方案.md
AI-RME/知识图谱构建策略.md
```

#### 5. **文档查阅策略**
- **架构设计** → 查阅 `AI-RME融合架构设计.md`
- **LightRAG集成** → 查阅 `AI-RME集成LightRAG_Augment.md`
- **工作流设计** → 查阅 `摇光需求管理系统-Dify工作流设计方案.md`
- **上下文获取** → 查阅 `项目上下文获取策略说明.md`

### 嵌入式架构开发模式

#### 核心集成路径
1. **LightRAG包安装**: `pip install lightrag-hku` 直接使用
2. **封装层设计**: 基于LightRAG包创建适配层，不修改源码
3. **MCP集成**: 通过MCP协议与yg4AI主系统交互
4. **工作流实现**: 基于LangGraph的AI需求分析

#### 开发优先级
```
阶段1: LightRAG Core嵌入式封装
阶段2: MCP客户端实现
阶段3: AI工作流开发
阶段4: 集成测试和优化
```

### Testing Strategy

#### Unit Tests
```bash
# Run all tests
pytest tests/

# Run specific agent tests
pytest tests/agents/test_requirement_agent.py -v

# Test with coverage
pytest tests/ --cov=src --cov-report=html
```

#### Integration Tests
```bash
# Test full workflow
python tests/integration/test_full_workflow.py

# Test with sample documents
python tests/integration/test_word_processing.py
```

### Deployment Options

#### Local Development
```bash
# Start MongoDB
docker run -d -p 27017:27017 --name mongodb mongo:latest

# Start Neo4j
docker run -d -p 7474:7474 -p 7687:7687 --name neo4j neo4j:latest

# Run application
python src/main.py --doc_path data/input/sample.docx
```

#### Production Deployment
```bash
# Docker Compose
docker-compose up -d

# Kubernetes
kubectl apply -f k8s/
```

### Common Patterns

#### Error Handling
```python
from src.core.exceptions import MBSEGeneratorError

class RequirementAgent:
    def process(self, requirement):
        try:
            # Processing logic
            pass
        except Exception as e:
            raise MBSEGeneratorError(f"Requirement processing failed: {e}")
```

#### State Management
```python
from langgraph.graph import StateGraph
from src.graph.workflow_state import MBSEState

workflow = StateGraph(MBSEState)
workflow.add_node("requirement_analysis", requirement_agent)
workflow.add_edge("requirement_analysis", "diagram_generation")
```

#### Configuration Management
```python
from src.config.settings import settings

class DocumentProcessor:
    def __init__(self):
        self.chunk_size = settings.chunk_size
        self.chunk_overlap = settings.chunk_overlap
```

## Emergency Protocols

For critical issues spanning multiple components:
1. **Start with** `tech-lead-orchestrator` to analyze scope
2. **Delegate to specialists** based on component boundaries
3. **Use** `code-reviewer` **before any production deployment**

### Performance Monitoring

```bash
# Profile workflow execution
python -m cProfile -o profile.stats src/main.py --short_req "test"

# Memory usage analysis
python -m memory_profiler src/main.py --short_req "test"
```

### 项目一体化启动记忆系统

#### 🎯 开发环境说明
**重要**：本项目运行在**远程服务器Python环境**，本地WSL环境仅用于代码编辑
- **代码运行**：远程服务器执行（我本地跑不起来）
- **测试流程**：我提出测试方案 → 你执行测试 → 告诉我结果 → 我继续开发
- **开发模式**：迭代式开发 + 远程验证

#### 🎯 自动识别模式
- **当前目录启动** → 立即进入 ygagentlanggraph 改造模式
- **改造目标** → 从MBSE智能体 → 需求管理AI微服务
- **三层架构** → ygagentlanggraph(改造战场) → yg4AI(Java需求管理主系统) → LightRAG(学习参考)

#### 🚀 开发测试流程
```bash
# 本地仅用于代码编辑，不执行
# 测试时我会告诉你：
# 1. 测试哪个模块
# 2. 具体测试命令
# 3. 预期验证点
# 你来执行并反馈结果

# ⚠️ 重要规则：
# 1. 绝不盲猜，先查看LightRAG源码理解正确用法
# 2. 基于pip包lightrag-hku开发，不提取源码
# 3. 遇到问题先搜索官方文档和examples
# 4. 每次修改后必须测试验证
# 5. 测试时叫我执行，不可自行运行

# 示例测试流程：
# 我："测试需求分析工作流，运行：python src/main.py --short_req '用户登录需求'"
# 你：[执行后告诉我输出结果]
# 我：[根据结果继续下一步开发]
```

#### 🔗 项目关系固化记忆
| 项目 | 角色 | 开发状态 | 路径 | 记忆触发 |
|------|------|----------|------|----------|
| **ygagentlanggraph** | 改造战场 | ✅ 从MBSE → 需求管理AI微服务 | 当前目录 | 立即进入改造模式 |
| **yg4AI** | Java需求管理主系统 | 🔗 MCP服务端 | `/mnt/c/Users/<USER>/IdeaProjects/yg4AI` | 端口8890 |
| **LightRAG** | 学习参考 | 📚 只读学习 | `/mnt/c/Users/<USER>/PycharmProjects/LightRAG` | 不开发，仅学习 |

#### ⚡ 记忆触发器
- 看到 `ygagentlanggraph` 目录 → 立即进入**改造模式**（MBSE→需求管理AI微服务）
- 看到 `yg4AI` → Java需求管理主系统，MCP服务端端口8890
- 看到 `import lightrag` → 正确，嵌入式使用
- 看到 `lightrag-server` → 错误，我们不这样用
- 看到 `docker compose` → 错误，我们走嵌入式路线

### Debugging Tips

1. **Enable debug logging**: `--log_level DEBUG`
2. **Visualize workflows**: Check `mermaid.png` after each run
3. **Check MongoDB**: Use MongoDB Compass for data inspection
4. **Neo4j Browser**: Access at http://localhost:7474 for graph queries