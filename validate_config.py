#!/usr/bin/env python3
"""
配置验证脚本
检查本地模型路径和API配置
"""

import os
from pathlib import Path
from src.embedded_lightrag import LightRAGConfig

def check_model_paths():
    """检查本地模型路径"""
    print("🔍 检查本地模型路径...")
    
    config = LightRAGConfig()
    
    # 检查embedding模型
    embedding_path = config.embedding_model_path
    print(f"📁 Embedding模型路径: {embedding_path}")
    
    if os.path.exists(embedding_path):
        print("✅ Embedding模型路径存在")
        # 检查必要的文件
        required_files = ["config.json", "pytorch_model.bin"]
        for file in required_files:
            file_path = os.path.join(embedding_path, file)
            if os.path.exists(file_path):
                print(f"✅ {file} 存在")
            else:
                print(f"⚠️  {file} 不存在")
    else:
        print(f"❌ Embedding模型路径不存在: {embedding_path}")
    
    # 检查reranking模型
    reranking_path = config.reranking_model_path
    print(f"\n📁 Reranking模型路径: {reranking_path}")
    
    if os.path.exists(reranking_path):
        print("✅ Reranking模型路径存在")
        # 检查必要的文件
        required_files = ["config.json", "pytorch_model.bin"]
        for file in required_files:
            file_path = os.path.join(reranking_path, file)
            if os.path.exists(file_path):
                print(f"✅ {file} 存在")
            else:
                print(f"⚠️  {file} 不存在")
    else:
        print(f"❌ Reranking模型路径不存在: {reranking_path}")

def check_api_config():
    """检查API配置"""
    print("\n🔍 检查API配置...")
    
    config = LightRAGConfig()
    
    print(f"🔗 API Base URL: {config.api_base}")
    print(f"🤖 LLM Model: {config.llm_model_name}")
    
    # 检查API密钥
    api_key = config.api_key
    if api_key:
        print("✅ API密钥已设置")
        print(f"🔑 密钥前缀: {api_key[:10]}...")
    else:
        print("❌ API密钥未设置")
        print("请设置: export OPENAI_API_KEY=your-openrouter-key")

def print_environment_setup():
    """打印环境设置指导"""
    print("\n📋 环境变量设置指导:")
    print("export OPENAI_API_KEY=your-openrouter-key")
    print("export OPENAI_BASE_URL=https://openrouter.ai/api/v1")
    print("export LLM_MODEL=deepseek/deepseek-chat-v3-0324")
    print("export EMBEDDING_MODEL_PATH=/home/<USER>/localai/Qwen3-embedding-8B")
    print("export RERANKING_MODEL_PATH=/home/<USER>/localai/Qwen3-Reranker-4B")
    print("export USE_LOCAL_EMBEDDING=true")
    print("export USE_LOCAL_RERANKING=true")
    print("export USE_API_LLM=true")

def main():
    """主函数"""
    print("🚀 LightRAG配置验证\n")
    
    check_model_paths()
    check_api_config()
    print_environment_setup()
    
    print("\n✅ 配置验证完成")

if __name__ == "__main__":
    main()