<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmlns:xmi="http://www.omg.org/spec/XMI/20131001" xmlns:uml="http://www.omg.org/spec/UML/20131001" xmlns:sysml="http://www.omg.org/spec/SysML/20181001/SysML" xmlns:DSL_Customization="http://www.magicdraw.com/schemas/DSL_Customization.xmi" xmlns:MD_Customization_for_SysML__additional_stereotypes="http://www.magicdraw.com/spec/Customization/180/SysML" xmlns:StandardProfile="http://www.omg.org/spec/UML/20131001/StandardProfile" xmlns:MagicDraw_Profile="http://www.omg.org/spec/UML/20131001/MagicDrawProfile">
    <uml:Model xmi:type="uml:Model" xmi:id="model-bicycle-req-uuid" name="自行车系统需求模型">
        <packagedElement xmi:id="pkg-systemfunc-uuid" name="系统功能" xmi:type="uml:Package">
            <packagedElement xmi:id="blk-brake-uuid" name="刹车系统" xmi:type="uml:Class"/>
            <packagedElement xmi:id="blk-elec-uuid" name="电气系统" xmi:type="uml:Class"/>
            <packagedElement xmi:id="blk-frame-uuid" name="车架" xmi:type="uml:Class"/>
            <packagedElement xmi:id="blk-mech-uuid" name="机械结构" xmi:type="uml:Class"/>
            <packagedElement xmi:id="blk-shift-uuid" name="变速机构" xmi:type="uml:Class"/>
            <packagedElement xmi:id="blk-steering-uuid" name="转向控制" xmi:type="uml:Class"/>
            <packagedElement xmi:id="blk-wheel-uuid" name="轮子" xmi:type="uml:Class"/>
            <packagedElement xmi:id="rel-derive-weight-frame-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="req-frame-uuid"/>
                <supplier xmi:idref="req-weight-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-brake-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-brake-uuid"/>
                <supplier xmi:idref="req-brake-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-elec-safety-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-elec-uuid"/>
                <supplier xmi:idref="req-electrical-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-frame-material-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-frame-uuid"/>
                <supplier xmi:idref="req-frame-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-mech-frontrear-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-mech-uuid"/>
                <supplier xmi:idref="req-frontrear-drive-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-shift-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-shift-uuid"/>
                <supplier xmi:idref="req-shift-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-steering-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-steering-uuid"/>
                <supplier xmi:idref="req-steering-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-weight-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-frame-uuid"/>
                <supplier xmi:idref="req-weight-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-satisfy-wheel-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="blk-wheel-uuid"/>
                <supplier xmi:idref="req-wheel-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-verify-brake-resp-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="tc-brake-resp-uuid"/>
                <supplier xmi:idref="req-brake-response-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-verify-electrical-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="tc-light-safety-uuid"/>
                <supplier xmi:idref="req-electrical-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-verify-load-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="tc-loadtest-uuid"/>
                <supplier xmi:idref="req-weight-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-verify-roadtest-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="tc-roadtest-uuid"/>
                <supplier xmi:idref="req-roadtest-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="rel-verify-shift-uuid" xmi:type="uml:Abstraction">
                <client xmi:idref="tc-shift-test-uuid"/>
                <supplier xmi:idref="req-shift-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="req-brake-response-uuid" name="制动响应时间" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-brake-uuid" name="刹车系统" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-electrical-uuid" name="电气安全" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-frame-uuid" name="车架材料" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-frontrear-drive-uuid" name="前后轮驱动" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-load-test-uuid" name="载重测试" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-mech-conn-uuid" name="机械连接标准" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-priority-uuid" name="优先级" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-roadtest-uuid" name="行驶测试" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-shift-uuid" name="变速功能" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-steering-uuid" name="转向控制" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-weight-uuid" name="结构重量" xmi:type="uml:Class"/>
            <packagedElement xmi:id="req-wheel-uuid" name="轮径和轮胎宽度" xmi:type="uml:Class"/>
            <packagedElement xmi:id="tc-brake-resp-uuid" name="刹车响应测试" xmi:type="uml:Activity">
                <ownedParameter xmi:type="uml:Parameter" xmi:id="_tc_brake_resp_uuid_internal_verdict_param" name="verdict" visibility="public" direction="return">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </ownedParameter>
                <node xmi:type="uml:ActivityParameterNode" xmi:id="_tc_brake_resp_uuid_internal_verdict_node" name="verdict" visibility="public" parameter="_tc_brake_resp_uuid_internal_verdict_param">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </node>
            </packagedElement>
            <packagedElement xmi:id="tc-light-safety-uuid" name="灯光安全测试" xmi:type="uml:Activity">
                <ownedParameter xmi:type="uml:Parameter" xmi:id="_tc_light_safety_uuid_internal_verdict_param" name="verdict" visibility="public" direction="return">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </ownedParameter>
                <node xmi:type="uml:ActivityParameterNode" xmi:id="_tc_light_safety_uuid_internal_verdict_node" name="verdict" visibility="public" parameter="_tc_light_safety_uuid_internal_verdict_param">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </node>
            </packagedElement>
            <packagedElement xmi:id="tc-loadtest-uuid" name="载重测试" xmi:type="uml:Activity">
                <ownedParameter xmi:type="uml:Parameter" xmi:id="_tc_loadtest_uuid_internal_verdict_param" name="verdict" visibility="public" direction="return">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </ownedParameter>
                <node xmi:type="uml:ActivityParameterNode" xmi:id="_tc_loadtest_uuid_internal_verdict_node" name="verdict" visibility="public" parameter="_tc_loadtest_uuid_internal_verdict_param">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </node>
            </packagedElement>
            <packagedElement xmi:id="tc-roadtest-uuid" name="行驶性能测试" xmi:type="uml:Activity">
                <ownedParameter xmi:type="uml:Parameter" xmi:id="_tc_roadtest_uuid_internal_verdict_param" name="verdict" visibility="public" direction="return">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </ownedParameter>
                <node xmi:type="uml:ActivityParameterNode" xmi:id="_tc_roadtest_uuid_internal_verdict_node" name="verdict" visibility="public" parameter="_tc_roadtest_uuid_internal_verdict_param">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </node>
            </packagedElement>
            <packagedElement xmi:id="tc-shift-test-uuid" name="变速测试" xmi:type="uml:Activity">
                <ownedParameter xmi:type="uml:Parameter" xmi:id="_tc_shift_test_uuid_internal_verdict_param" name="verdict" visibility="public" direction="return">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </ownedParameter>
                <node xmi:type="uml:ActivityParameterNode" xmi:id="_tc_shift_test_uuid_internal_verdict_node" name="verdict" visibility="public" parameter="_tc_shift_test_uuid_internal_verdict_param">
                    <type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"/>
                </node>
            </packagedElement>
        </packagedElement>
    </uml:Model>
    <sysml:Requirement xmi:id="_req_frontrear_drive_uuid__" base_Class="req-frontrear-drive-uuid" Id="REQ-001" Text="系统应能实现前后轮驱动，支持人力踩踏驱动自行车前行"/>
    <sysml:Requirement xmi:id="_req_shift_uuid__" base_Class="req-shift-uuid" Id="REQ-002" Text="具备变速功能，支持至少3个档位（低速、中速、高速）"/>
    <sysml:Requirement xmi:id="_req_brake_uuid__" base_Class="req-brake-uuid" Id="REQ-003" Text="具有刹车系统，支持前后轮制动"/>
    <sysml:Requirement xmi:id="_req_steering_uuid__" base_Class="req-steering-uuid" Id="REQ-004" Text="提供车把转向控制，支持左右转向"/>
    <sysml:Requirement xmi:id="_req_weight_uuid__" base_Class="req-weight-uuid" Id="REQ-005" Text="结构重量不超过15公斤，确保便携性"/>
    <sysml:Requirement xmi:id="_req_wheel_uuid__" base_Class="req-wheel-uuid" Id="REQ-006" Text="轮径为700mm，轮胎宽度为25mm，适应城市道路"/>
    <sysml:Requirement xmi:id="_req_frame_uuid__" base_Class="req-frame-uuid" Id="REQ-007" Text="车架材料为铝合金，强度满足承载200kg的要求"/>
    <sysml:Requirement xmi:id="_req_brake_response_uuid__" base_Class="req-brake-response-uuid" Id="REQ-008" Text="制动响应时间不超过0.2秒"/>
    <sysml:Requirement xmi:id="_req_mech_conn_uuid__" base_Class="req-mech-conn-uuid" Id="REQ-009" Text="所有机械连接必须符合标准尺寸（如螺栓孔径为10mm）"/>
    <sysml:Requirement xmi:id="_req_electrical_uuid__" base_Class="req-electrical-uuid" Id="REQ-010" Text="电气部分（如灯光）必须符合安全规范"/>
    <sysml:Requirement xmi:id="_req_load_test_uuid__" base_Class="req-load-test-uuid" Id="REQ-011" Text="通过载重测试验证承载能力"/>
    <sysml:Requirement xmi:id="_req_roadtest_uuid__" base_Class="req-roadtest-uuid" Id="REQ-012" Text="通过行驶测试验证变速和刹车性能"/>
    <sysml:Requirement xmi:id="_req_priority_uuid__" base_Class="req-priority-uuid" Id="REQ-013" Text="基础行驶功能优先，次要为变速和灯光"/>
    <sysml:Block xmi:id="_blk_mech_uuid_application" base_Class="blk-mech-uuid"/>
    <sysml:Block xmi:id="_blk_elec_uuid_application" base_Class="blk-elec-uuid"/>
    <sysml:Block xmi:id="_blk_shift_uuid_application" base_Class="blk-shift-uuid"/>
    <sysml:Block xmi:id="_blk_brake_uuid_application" base_Class="blk-brake-uuid"/>
    <sysml:Block xmi:id="_blk_steering_uuid_application" base_Class="blk-steering-uuid"/>
    <sysml:Block xmi:id="_blk_wheel_uuid_application" base_Class="blk-wheel-uuid"/>
    <sysml:Block xmi:id="_blk_frame_uuid_application" base_Class="blk-frame-uuid"/>
    <sysml:TestCase xmi:id="_tc_loadtest_uuid_application" base_Behavior="tc-loadtest-uuid"/>
    <sysml:TestCase xmi:id="_tc_roadtest_uuid_application" base_Behavior="tc-roadtest-uuid"/>
    <sysml:TestCase xmi:id="_tc_shift_test_uuid_application" base_Behavior="tc-shift-test-uuid"/>
    <sysml:TestCase xmi:id="_tc_brake_resp_uuid_application" base_Behavior="tc-brake-resp-uuid"/>
    <sysml:TestCase xmi:id="_tc_light_safety_uuid_application" base_Behavior="tc-light-safety-uuid"/>
    <sysml:DeriveReqt xmi:id="_rel_derive_weight_frame_uuid_application" base_Abstraction="rel-derive-weight-frame-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_mech_frontrear_uuid_application" base_Abstraction="rel-satisfy-mech-frontrear-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_shift_uuid_application" base_Abstraction="rel-satisfy-shift-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_brake_uuid_application" base_Abstraction="rel-satisfy-brake-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_steering_uuid_application" base_Abstraction="rel-satisfy-steering-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_weight_uuid_application" base_Abstraction="rel-satisfy-weight-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_wheel_uuid_application" base_Abstraction="rel-satisfy-wheel-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_frame_material_uuid_application" base_Abstraction="rel-satisfy-frame-material-uuid"/>
    <sysml:Satisfy xmi:id="_rel_satisfy_elec_safety_uuid_application" base_Abstraction="rel-satisfy-elec-safety-uuid"/>
    <sysml:Verify xmi:id="_rel_verify_load_uuid_application" base_Abstraction="rel-verify-load-uuid"/>
    <sysml:Verify xmi:id="_rel_verify_roadtest_uuid_application" base_Abstraction="rel-verify-roadtest-uuid"/>
    <sysml:Verify xmi:id="_rel_verify_shift_uuid_application" base_Abstraction="rel-verify-shift-uuid"/>
    <sysml:Verify xmi:id="_rel_verify_brake_resp_uuid_application" base_Abstraction="rel-verify-brake-resp-uuid"/>
    <sysml:Verify xmi:id="_rel_verify_electrical_uuid_application" base_Abstraction="rel-verify-electrical-uuid"/>
</xmi:XMI>