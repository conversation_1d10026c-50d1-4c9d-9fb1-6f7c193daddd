from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-a4bed4a0b42f4f198e1ecb2604d0fa6f",
#     base_url='https://api.deepseek.com'
# )
chat = ChatOpenAI(
    model="gpt-4.1-mini",
    #model="gpt-4.1-nano-2025-04-14",
    temperature=0.0,
    #max_tokens=8192,
    timeout=None,
    api_key="sk-TP4VOmsDu2OdsE8H88BaBd09D1B74bD983C7Ca67E6E9AeBf",
    base_url='https://happyapi.org/v1'
)
# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-pdRe1ohnoKmJSCGWBrCBnPH9226q7hVEgC6ijVfhZPREGUz0",
#     base_url='https://api.chatfire.cn/v1'
# )
# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-1XvJYiJNpvUQBFpUwdUorXQrcWOJOMYLWwKLIRVvW9ygsQN1",
#     base_url='https://opus.gptuu.com/v1'
# )
question1 = """"我们的目标是构建一个名为“智慧办公套件”的系统模型，旨在全面提升办公效率和协作体验。系统规划中，一个核心部分是“文档协同模块”。对于这个模块，我们首先确立了“保障文档安全与版本控制”的总体需求。这一需求进一步细化为几个具体的要求，比如系统必须支持“文档多用户实时编辑”，并且需要实现“细粒度的操作权限管理”。为了支持实时编辑功能，将开发一个“协同编辑引擎”作为关键的技术组件。同时，我们会设计一套“并发编辑压力测试”用例来验证协同编辑引擎在高并发场景下的稳定性。对于权限管理要求，则由“用户身份认证与授权服务”这一核心区块来满足，并通过“多角色权限组合验证”测试用例确保其符合设计。
此外，系统中还包含一个“任务与日程管理”组件包。该组件包的基础需求是“高效的任务分配与追踪”。在此基础上，派生出“支持创建周期性重复任务”的具体功能点。这一功能点的实现将依赖于“任务调度与提醒模块”。我们将通过“重复任务生成准确性测试”来检验该模块的可靠性。整个“任务与日程管理”的可用性，特别是“高效的任务分配与追踪”这一核心需求，会由“任务分配与状态流转测试”用例进行验证。"""
question2 = """"我们的目标是构建一个名为“智慧办公套件”的系统模型，旨在全面提升办公效率和协作体验。系统规划中，一个核心部分是“文档协同模块”。对于这个模块，我们首先确立了“保障文档安全与版本控制”的总体需求。这一需求进一步细化为几个具体的要求，比如系统必须支持“文档多用户实时编辑”，并且需要实现“细粒度的操作权限管理”。为了支持实时编辑功能，将开发一个“协同编辑引擎”作为关键的技术组件。同时，我们会设计一套“并发编辑压力测试”用例来验证协同编辑引擎在高并发场景下的稳定性。对于权限管理要求，则由“用户身份认证与授权服务”这一核心区块来满足，并通过“多角色权限组合验证”测试用例确保其符合设计。
此外，系统中还包含一个“任务与日程管理”组件包。该组件包的基础需求是“高效的任务分配与追踪”。在此基础上，派生出“支持创建周期性重复任务”的具体功能点。这一功能点的实现将依赖于“任务调度与提醒模块”。我们将通过“重复任务生成准确性测试”来检验该模块的可靠性。整个“任务与日程管理”的可用性，特别是“高效的任务分配与追踪”这一核心需求，会由“任务分配与状态流转测试”用例进行验证。"""
question3 = """
我们的目标是构建一个名为“智慧办公套件”的系统模型，旨在全面提升办公效率和协作体验。系统规划中，一个核心部分是“文档协同模块”。对于这个模块，我们首先确立了“保障文档安全与版本控制”的总体需求。这一需求进一步细化为几个具体的要求，比如系统必须支持“文档多用户实时编辑”，并且需要实现“细粒度的操作权限管理”。为了支持实时编辑功能，将开发一个“协同编辑引擎”作为关键的技术组件。同时，我们会设计一套“并发编辑压力测试”用例来验证协同编辑引擎在高并发场景下的稳定性。对于权限管理要求，则由“用户身份认证与授权服务”这一核心区块来满足，并通过“多角色权限组合验证”测试用例确保其符合设计。
此外，系统中还包含一个“任务与日程管理”组件包。该组件包的基础需求是“高效的任务分配与追踪”。在此基础上，派生出“支持创建周期性重复任务”的具体功能点。这一功能点的实现将依赖于“任务调度与提醒模块”。我们将通过“重复任务生成准确性测试”来检验该模块的可靠性。整个“任务与日程管理”的可用性，特别是“高效的任务分配与追踪”这一核心需求，会由“任务分配与状态流转测试”用例进行验证。
"""

prompt1 = """
## 角色
你是一位专业的 SysML 需求图建模专家。你精通 SysML 需求图的规范，能够准确地从自然语言描述中提取出包、需求（及其ID和文本）、系统模块（Block）、测试用例（TestCase）以及它们之间的关系（如 DeriveReqt, Satisfy, Verify）。

## 规则
你的目标是根据输入的文本描述，分析并生成构建 SysML 需求图所需的元素信息。请遵循以下步骤进行思考和分析，并生成中间的思考过程：

1.  **识别模型和包 (Model & Package)**:
    *   确定文本描述的顶层模型名称。
    *   识别主要的包 (Package) 及其名称，所有其他元素通常属于某个包。
    *   为每个识别的元素分配合理的名称和临时ID（最终JSON中ID需全局唯一，可使用描述性名称加后缀，如 `-uuid`）。

2.  **识别需求 (Requirements)**:
    *   找出文本中明确定义的需求。
    *   为每个需求提取其用户指定的 `ID` (如 "REQ-001", "1")，`名称` (name)，和 `文本描述` (text)。
    *   分配一个临时的唯一系统 ID (e.g., `req-capacity-spec-uuid`).

3.  **识别系统模块/区块 (Blocks)**:
    *   找出文本中描述的用于满足需求的系统组成部分、模块或区块。这些是 `Block` 元素。
    *   为每个 Block 提取其 `名称`。
    *   分配一个临时的唯一系统 ID (e.g., `blk-car-system-uuid`).

4.  **识别测试用例 (TestCases)**:
    *   找出文本中描述的用于验证需求的测试活动或测试用例。这些是 `TestCase` 元素。
    *   为每个 TestCase 提取其 `名称`。
    *   分配一个临时的唯一系统 ID (e.g., `tc-capacity-test-uuid`).

5.  **识别派生关系 (DeriveReqt Relationships)**:
    *   注意描述需求之间层级或细化关系的词语，如“派生自”、“分解为”、“细化自”。
    *   对于每个派生关系，明确哪个是更通用的“源需求”（Supplier in SysML Abstraction context）和哪个是更具体的“派生需求”（Client in SysML Abstraction context）。根据用户定义：“总需求下的更为详细的需求”，源需求是总需求，派生需求是详细需求。
    *   记录源需求和派生需求的临时ID。
    *   分配一个临时的唯一系统 ID 给这个关系 (e.g., `rel-derive-1-uuid`).

6.  **识别满足关系 (Satisfy Relationships)**:
    *   注意描述模块如何满足需求的词语，如“满足”、“实现”、“负责”。
    *   对于每个满足关系，明确哪个“系统模块 (Block)”（Client）满足了哪个“需求”（Supplier）。
    *   记录相关的 Block 和 Requirement 的临时ID。
    *   分配一个临时的唯一系统 ID 给这个关系 (e.g., `rel-satisfy-1-uuid`).

7.  **识别验证关系 (Verify Relationships)**:
    *   注意描述测试用例如何验证需求的词语，如“验证”、“测试”、“确保”。
    *   对于每个验证关系，明确哪个“测试用例 (TestCase)”（Client）验证了哪个“需求”（Supplier）。
    *   记录相关的 TestCase 和 Requirement 的临时ID。
    *   分配一个临时的唯一系统 ID 给这个关系 (e.g., `rel-verify-1-uuid`).

8.  **编译和整理输出**:
    *   汇总所有识别出的元素（模型、包、需求、模块、测试用例）及其属性。
    *   汇总所有识别出的关系及其源和目标。
    *   准备一个清晰的、结构化的中间表示（“整理优化输出”），概述提取到的所有信息，为最终生成JSON做准备。确保所有临时ID都是唯一的。


## 样例

### 输入样例：
"请描述“项目Alpha”的需求模型。
该模型包含一个名为“核心功能”的包。
在“核心功能”包中，定义了以下需求：
1.  一个顶层需求，ID为“R1”，名为“用户认证”，其内容为“系统必须提供用户注册和登录功能”。
2.  一个细化需求，ID为“R1.1”，名为“密码安全”，其内容为“用户密码必须经过加密存储，并符合复杂性要求”。此需求是从“用户认证”派生出来的。
一个名为“认证服务”的模块（Block），用于满足“用户认证”需求。
一个名为“登录功能测试”的测试用例（TestCase），用于验证“用户认证”需求。"

### 输出文本 (CoT):
请你按照如下的8步进行思考推理并输出：

#### 第一步：识别模型和包
- 模型名称: "项目Alpha需求模型" (model-alpha-req-uuid)
- 主要包: "核心功能" (pkg-corefunc-uuid)

#### 第二步：识别需求 (Requirements)
- 需求1:
    - 用户指定 ID(文本需求对应ID): "R1"
    - 名称: "用户认证"
    - 文本描述: "系统必须提供用户注册和登录功能"
    - 临时系统 ID: req-userauth-uuid
- 需求2:
    - 用户指定 ID(文本需求对应ID): "R1.1"
    - 名称: "密码安全"
    - 文本描述: "用户密码必须经过加密存储，并符合复杂性要求"
    - 临时系统 ID: req-passsec-uuid

#### 第三步：识别系统模块/区块 (Blocks)
- 模块1:
    - 名称: "认证服务"
    - 临时系统 ID: blk-authsvc-uuid

#### 第四步：识别测试用例 (TestCases)
- 测试用例1:
    - 名称: "登录功能测试"
    - 临时系统 ID: tc-logintest-uuid

#### 第五步：识别派生关系 (DeriveReqt Relationships)
- 派生关系1:
    - 描述: "密码安全" (req-passsec-uuid) 是从 "用户认证" (req-userauth-uuid) 派生出来的。
    - 源需求 (General/Supplier): "用户认证" (req-userauth-uuid)
    - 派生需求 (Specific/Client): "密码安全" (req-passsec-uuid)
    - 临时系统 ID: rel-derive-auth-passsec-uuid

#### 第六步：识别满足关系 (Satisfy Relationships)
- 满足关系1:
    - 描述: "认证服务" (blk-authsvc-uuid) 满足 "用户认证" (req-userauth-uuid)。
    - 系统模块 (Client): "认证服务" (blk-authsvc-uuid)
    - 需求 (Supplier): "用户认证" (req-userauth-uuid)
    - 临时系统 ID: rel-satisfy-authsvc-userauth-uuid

#### 第七步：识别验证关系 (Verify Relationships)
- 验证关系1:
    - 描述: "登录功能测试" (tc-logintest-uuid) 验证 "用户认证" (req-userauth-uuid)。
    - 测试用例 (Client): "登录功能测试" (tc-logintest-uuid)
    - 需求 (Supplier): "用户认证" (req-userauth-uuid)
    - 临时系统 ID: rel-verify-logintest-userauth-uuid

#### 第八步：整理优化输出
---
模型: 项目Alpha需求模型 (model-alpha-req-uuid)
  包: 核心功能 (pkg-corefunc-uuid)
    需求:
      - ID: R1, 名称: 用户认证, 文本: 系统必须提供用户注册和登录功能 (sysId: req-userauth-uuid)
      - ID: R1.1, 名称: 密码安全, 文本: 用户密码必须经过加密存储，并符合复杂性要求 (sysId: req-passsec-uuid)
    系统模块 (Blocks):
      - 名称: 认证服务 (sysId: blk-authsvc-uuid)
    测试用例 (TestCases):
      - 名称: 登录功能测试 (sysId: tc-logintest-uuid)
    关系:
      - DeriveReqt (sysId: rel-derive-auth-passsec-uuid):
        - 源需求: req-userauth-uuid (用户认证)
        - 派生需求: req-passsec-uuid (密码安全)
      - Satisfy (sysId: rel-satisfy-authsvc-userauth-uuid):
        - 系统模块: blk-authsvc-uuid (认证服务)
        - 需求: req-userauth-uuid (用户认证)
      - Verify (sysId: rel-verify-logintest-userauth-uuid):
        - 测试用例: tc-logintest-uuid (登录功能测试)
        - 需求: req-userauth-uuid (用户认证)
---


```

## 具体任务
输入：          
""" + question2  + """输出：请你一步一步进行推理思考。"""

messages = [
    HumanMessage(content=prompt1),
]

print("⭐⭐⭐正在执行任务：", question2)
response = chat.invoke(messages)

print("😊😊😊推理结果：", response.content)
print("😊😊😊使用消耗", response.usage_metadata)

prompt2 = prompt1 + response.content + """
根据以上详细的推理和“整理优化输出”，请严格按照以下 JSON 格式生成 SysML 需求图的完整描述。请确保：
1.  所有 `id` 字段都是全局唯一的（可以使用推理中建议的临时系统 ID）。
2.  `parentId` 正确反映了元素的包含关系（例如，需求、模块、测试用例、关系通常属于某个包）。
3.  对于 `DeriveReqt` 关系, `sourceRequirementId` 指向通用需求的ID，`derivedRequirementId` 指向具体派生需求的ID。
4.  对于 `Satisfy` 关系, `blockId` 指向执行满足的模块的ID，`requirementId` 指向被满足的需求的ID。
5.  对于 `Verify` 关系, `testCaseId` 指向执行验证的测试用例的ID，`requirementId` 指向被验证的需求的ID。

```json
{
  "model": [
    {
      "id": "model-req-unique-id",
      "name": "RequirementsModelName"
    }
  ],
  "elements": [
    // Packages
    {
      "id": "pkg-req-unique-id",
      "type": "Package",
      "name": "PackageName"
    },
    // Requirements
    {
      "id": "xml-req-unique-id-1",
      "type": "Requirement",
      "name": "RequirementName",
      "reqId": "text-req-unique-id-1",
      "text": "Requirement description text.",
      "parentId": "pkg-req-unique-id"
    },
    // Blocks (System Elements that satisfy requirements)
    {
      "id": "blk-req-unique-id-1",
      "type": "Block",
      "name": "BlockName",
      "parentId": "pkg-req-unique-id"
    },
    // TestCases
    {
      "id": "tc-unique-id-1",
      "type": "TestCase",
      "name": "TestCaseName",
      "parentId": "pkg-req-unique-id"
    },
    // Relationships
    {
      "id": "rel-derive-unique-id-1",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-general-id",
      "derivedRequirementId": "req-specific-id",
      "parentId": "pkg-req-unique-id"
    },
    {
      "id": "rel-satisfy-unique-id-1",
      "type": "Satisfy",
      "blockId": "blk-req-unique-id-1",
      "requirementId": "req-unique-id-1",
      "parentId": "pkg-req-unique-id"
    },
    {
      "id": "rel-verify-unique-id-1",
      "type": "Verify",
      "testCaseId": "tc-unique-id-1",
      "requirementId": "req-unique-id-1",
      "parentId": "pkg-req-unique-id"
    }
  ]
}
```

请严格按照上面的JSON结构输出结果。"""

print("⭐⭐⭐处理任务：", "复合COT")

messages = [
    HumanMessage(content=prompt2),
]

response = chat.invoke(messages)

print("😊😊😊处理结果", response.content)
print("😊😊😊使用消耗", response.usage_metadata)