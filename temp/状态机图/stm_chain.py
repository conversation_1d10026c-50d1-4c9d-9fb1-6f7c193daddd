from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-a4bed4a0b42f4f198e1ecb2604d0fa6f",
#     base_url='https://api.deepseek.com'
# )
chat = ChatOpenAI(
    model="gpt-4.1-mini",
    #model="gpt-4.1-nano-2025-04-14",
    temperature=0.0,
    #max_tokens=8192,
    timeout=None,
    api_key="sk-TP4VOmsDu2OdsE8H88BaBd09D1B74bD983C7Ca67E6E9AeBf",
    base_url='https://happyapi.org/v1'
)
# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-pdRe1ohnoKmJSCGWBrCBnPH9226q7hVEgC6ijVfhZPREGUz0",
#     base_url='https://api.chatfire.cn/v1'
# )
# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-1XvJYiJNpvUQBFpUwdUorXQrcWOJOMYLWwKLIRVvW9ygsQN1",
#     base_url='https://opus.gptuu.com/v1'
# )
question1 = """"请描述一个简单的“门禁系统”的状态机。该状态机属于“门控制器”模块，所有具体的行为都定义在“门禁行为库”包中。
系统启动后，首先进入“锁定”状态。这是初始状态。
当接收到“有效开锁信号”时，如果“安全系统已解除”，门禁从“锁定”状态转换到“开锁中”状态，并在转换时执行“记录开锁尝试”这个已定义的行为。
进入“开锁中”状态时，会调用“执行解锁门闩”行为。在“开锁中”状态，系统会持续调用“保持门锁打开”行为。离开“开锁中”状态时，会调用“执行检查门是否已关闭”行为。
一段时间后（触发“超时事件”），系统从“开锁中”状态自动转换回“锁定”状态，并执行“执行自动上锁”这个已定义的行为。
还有一个“报警”状态。如果从“锁定”状态检测到“强制开门事件”，系统会转换到“报警”状态，并调用“执行鸣响警报”行为作为效果。
“锁定”状态是一个复合状态，它有一个名为“内部安全检查”的子区域。此子区域包含一个初始伪状态，转换到一个“自检”状态，然后转换到一个最终伪状态。“锁定”状态还有一个名为“ep_lock”的进入点。
“有效开锁信号”是一个信号。“超时事件”和“强制开门事件”也是事件。"""
question2 = """"请描述一个简单的“门禁系统”的状态机。该状态机属于“门控制器”模块，所有具体的行为都定义在“门禁行为库”包中。
系统启动后，首先进入“锁定”状态。这是初始状态。
当接收到“有效开锁信号”时，如果“安全系统已解除”，门禁从“锁定”状态转换到“开锁中”状态，并在转换时执行“记录开锁尝试”这个已定义的行为。
进入“开锁中”状态时，会调用“执行解锁门闩”行为。在“开锁中”状态，系统会持续调用“保持门锁打开”行为。离开“开锁中”状态时，会调用“执行检查门是否已关闭”行为。
一段时间后（触发“超时事件”），系统从“开锁中”状态自动转换回“锁定”状态，并执行“执行自动上锁”这个已定义的行为。
还有一个“报警”状态。如果从“锁定”状态检测到“强制开门事件”，系统会转换到“报警”状态，并调用“执行鸣响警报”行为作为效果。
“锁定”状态是一个复合状态，它有一个名为“内部安全检查”的子区域。此子区域包含一个初始伪状态，转换到一个“自检”状态，然后转换到一个最终伪状态。“锁定”状态还有一个名为“ep_lock”的进入点。
“有效开锁信号”是一个信号。“超时事件”和“强制开门事件”也是事件。"""
question3 = """
请描述一个简单的“门禁系统”的状态机。该状态机属于“门控制器”模块，所有具体的行为都定义在“门禁行为库”包中。
系统启动后，首先进入“锁定”状态。这是初始状态。
当接收到“有效开锁信号”时，如果“安全系统已解除”，门禁从“锁定”状态转换到“开锁中”状态，并在转换时执行“记录开锁尝试”这个已定义的行为。
进入“开锁中”状态时，会调用“执行解锁门闩”行为。在“开锁中”状态，系统会持续调用“保持门锁打开”行为。离开“开锁中”状态时，会调用“执行检查门是否已关闭”行为。
一段时间后（触发“超时事件”），系统从“开锁中”状态自动转换回“锁定”状态，并执行“执行自动上锁”这个已定义的行为。
还有一个“报警”状态。如果从“锁定”状态检测到“强制开门事件”，系统会转换到“报警”状态，并调用“执行鸣响警报”行为作为效果。
“锁定”状态是一个复合状态，它有一个名为“内部安全检查”的子区域。此子区域包含一个初始伪状态，转换到一个“自检”状态，然后转换到一个最终伪状态。“锁定”状态还有一个名为“ep_lock”的进入点。
“有效开锁信号”是一个信号。“超时事件”和“强制开门事件”也是事件。"
"""

prompt1 = """

## 角色
你是一位专业的 SysML 状态机图建模专家。你精通 SysML 状态机图的规范，能够准确地从流程、行为或系统生命周期的自然语言描述中提取出状态机、区域、状态（简单、复合、最终）、伪状态（初始、最终、进入/退出点、选择、连接、派生/汇合点）、转换、触发器（事件）、守卫条件和效果/状态行为（进入、执行、退出）等元素，并理解它们之间的关系。特别地，你理解状态的entry/do/exit行为以及转换的effect行为通常是通过一个内嵌的包装活动（包含InitialNode->CallBehaviorAction->FinalNode的结构）来调用一个在别处定义的具体行为。

## 规则
你的目标是根据输入的文本描述，分析并生成构建 SysML 状态机图所需的元素信息。请遵循以下步骤进行思考和分析，并生成中间的思考过程：

1.  **识别顶层容器 (Model, Package, Block)**:
    *   确定文本描述的顶层模型名称。
    *   识别主要的包 (Package) 及其名称。
    *   识别状态机所描述其行为的块 (Block/Class)，或状态机直接所属的包。
    *   为每个识别的元素分配合理的名称和临时ID。

2.  **识别状态机 (StateMachine) 和区域 (Region)**:
    *   找出核心的状态机定义，并为其命名。确定其是块的 `ownedBehavior` 还是包的 `packagedElement`。
    *   状态机总是至少包含一个主区域 (Region)。识别此区域。复合状态也包含区域。
    *   分配临时ID。

3.  **识别状态 (States - Simple, Composite, Final)**:
    *   从描述中找出系统可能处于的各种稳定情况，这些是状态 (State)。
    *   区分简单状态和复合状态（复合状态通常描述为包含子状态或有明确的进入/退出点）。
    *   识别是否有明确的最终状态 (FinalState)。
    *   为每个状态命名并分配临时ID。记录其所属的区域。

4.  **识别伪状态 (Pseudostates)**:
    *   `InitialNode`: 识别状态机或区域的起点 (通常称为初始状态)。
    *   `FinalState`: (作为一种特殊State处理，见上一步) 识别状态机或区域的终点。
    *   `EntryPoint`/`ExitPoint`: 如果描述了复合状态的特定入口和出口，识别它们。
    *   `Choice`: 识别基于守卫条件选择不同路径的决策点。
    *   `Junction`: 识别多个转换路径汇合或分支出多个路径的点。
    *   `Fork`/`Join`: 识别并发区域的开始和结束。
    *   为每个伪状态分配合理的名称（可选）和临时ID，记录其类型 (`kind`) 和所属区域或复合状态。

5.  **识别状态行为 (Entry, Do, Exit Activities)**:
    *   对于每个状态，识别是否有描述进入时 (`entry`)，持续执行时 (`doActivity`)，或退出时 (`exit`) 执行的动作。
    *   这些动作的核心是**调用一个在别处（通常在专门的“行为库”包中）定义的具体行为 (Activity)**。
    *   为这个**被调用的具体行为**命名并分配临时ID（例如，`act-actual-perform-task-uuid`）。将其记录为一个独立的 `Activity` 类型的元素，并指明其父包（如“行为库”）。
    *   在状态的JSON表示中，记录这个被调用的具体行为的ID (e.g., `calledBehaviorId`)。同时，可以为包装这个调用的内嵌活动隐式分配一个ID（例如 `wrapper-entry-for-stateX-uuid`），这个包装活动ID主要用于XML生成时的内部结构。

6.  **识别转换 (Transitions)**:
    *   找出从一个状态（或伪状态）到另一个状态（或伪状态）的路径或变化，这些是转换 (Transition)。
    *   明确每个转换的源 (source) 和目标 (target)。
    *   记录转换所属的区域。分配临时ID。

7.  **识别转换的组成部分 (Triggers, Guards, Effects)**:
    *   **触发器 (Triggers)**: 对于每个转换，确定是什么事件或信号触发了它。
        *   识别相关的事件 (Event)，如信号事件 (SignalEvent)。
        *   识别这些事件关联的信号 (Signal)（如果适用）。
        *   为事件和信号命名并分配临时ID。在转换上记录触发器引用的事件ID。
    *   **守卫条件 (Guards)**: 确定转换发生前必须满足的条件。记录守卫表达式和语言 (如 "English", "OCL")。
    *   **效果行为 (Effects)**: 确定转换发生时执行的动作。
        *   这个动作的核心是**调用一个在别处（通常在专门的“行为库”包中）定义的具体行为 (Activity)**。
        *   为这个**被调用的具体行为**命名并分配临时ID（例如，`act-actual-log-event-uuid`）。将其记录为一个独立的 `Activity` 类型的元素，并指明其父包。
        *   在转换的JSON表示中，记录这个被调用的具体行为的ID (e.g., `calledBehaviorId`)。同样，可以为包装这个调用的内嵌活动隐式分配一个ID。

8.  **识别其他辅助元素**:
    *   如在触发器中用到的信号 (Signal)，或在守卫条件中可能用到的属性 (Property on a Block)。
    *   所有被状态行为或转换效果所调用的具体行为 (Activity)，都应作为独立的元素被识别，并通常放置在一个共享的“行为库”包中。

9.  **编译和整理输出**:
    *   汇总所有识别出的元素（模型、包、块、状态机、区域、状态、伪状态、转换、**被调用的具体活动**、事件、信号等）及其属性。
    *   明确元素间的关系（例如，状态属于区域，转换属于区域，状态机属于块或包，状态/转换的`entry/do/exit/effect`通过`calledBehaviorId`引用一个“行为库”中的活动等）。
    *   准备一个清晰的、结构化的中间表示（“整理优化输出”），概述提取到的所有信息。

## 样例

### 输入样例：
"请描述一个简单的“门禁系统”的状态机。该状态机属于“门控制器”模块，所有具体的行为都定义在“门禁行为库”包中。
系统启动后，首先进入“锁定”状态。这是初始状态。
当接收到“有效开锁信号”时，如果“安全系统已解除”，门禁从“锁定”状态转换到“开锁中”状态，并在转换时执行“记录开锁尝试”这个已定义的行为。
进入“开锁中”状态时，会调用“执行解锁门闩”行为。在“开锁中”状态，系统会持续调用“保持门锁打开”行为。离开“开锁中”状态时，会调用“执行检查门是否已关闭”行为。
一段时间后（触发“超时事件”），系统从“开锁中”状态自动转换回“锁定”状态，并执行“执行自动上锁”这个已定义的行为。
还有一个“报警”状态。如果从“锁定”状态检测到“强制开门事件”，系统会转换到“报警”状态，并调用“执行鸣响警报”行为作为效果。
“锁定”状态是一个复合状态，它有一个名为“内部安全检查”的子区域。此子区域包含一个初始伪状态，转换到一个“自检”状态，然后转换到一个最终伪状态。“锁定”状态还有一个名为“ep_lock”的进入点。
“有效开锁信号”是一个信号。“超时事件”和“强制开门事件”也是事件。"

### 输出文本 (CoT):
请你按照如下的9步进行思考推理并输出：

#### 第一步：识别顶层容器
- 模型名称: "门禁系统模型" (model-door-access-sm-uuid)
- 包: "门禁控制包" (pkg-door-control-uuid)
- 包: "门禁行为库" (pkg-door-behaviors-uuid)
- 状态机所属块: "门控制器" (blk-door-controller-uuid, parentId: pkg-door-control-uuid)

#### 第二步：识别状态机 (StateMachine) 和区域 (Region)
- 状态机: "门禁状态机" (sm-door-access-uuid)，属于 blk-door-controller-uuid。
- 主区域: "主区域" (region-door-main-uuid)，属于 sm-door-access-uuid。

#### 第三步：识别状态 (States - Simple, Composite, Final)
- **锁定状态 (State, isComposite: true)**: "锁定" (state-locked-uuid)，在 region-door-main-uuid。
- **开锁中状态 (State)**: "开锁中" (state-unlocking-uuid)，在 region-door-main-uuid。
- **报警状态 (State)**: "报警" (state-alarm-uuid)，在 region-door-main-uuid。
- **自检状态 (State)**: "自检" (state-selfcheck-uuid)，将属于 "锁定" 状态的子区域。

#### 第四步：识别伪状态 (Pseudostates)
- **主区域初始伪状态**: (ps-main-initial-uuid)，kind: initial, 在 region-door-main-uuid。
- **"锁定"状态的进入点**: "ep_lock" (ps-locked-entry1-uuid)，kind: entryPoint, 连接点属于 state-locked-uuid。
- **"锁定"状态内部子区域的初始伪状态**: (ps-locked-sub-initial-uuid)，kind: initial, 在 "锁定" 的子区域 "内部安全检查" (region-locked-sub-uuid)。
- **"锁定"状态内部子区域的最终伪状态**: (ps-locked-sub-final-uuid)，kind: final, 在 "内部安全检查" (region-locked-sub-uuid)。

#### 第五步：识别状态行为 (Entry, Do, Exit Activities)
- **"开锁中"状态 (state-unlocking-uuid)**:
    - Entry: 调用 "执行解锁门闩" (act-execute-unlock-bolt-uuid, parentId: pkg-door-behaviors-uuid)。
        - JSON中表示为: `entry: { wrapperActivityId: "wrapper-entry-unlocking-uuid", calledBehaviorId: "act-execute-unlock-bolt-uuid" }`
    - DoActivity: 调用 "保持门锁打开" (act-keep-door-open-uuid, parentId: pkg-door-behaviors-uuid)。
        - JSON中表示为: `doActivity: { wrapperActivityId: "wrapper-do-unlocking-uuid", calledBehaviorId: "act-keep-door-open-uuid" }`
    - Exit: 调用 "执行检查门是否已关闭" (act-execute-check-closed-uuid, parentId: pkg-door-behaviors-uuid)。
        - JSON中表示为: `exit: { wrapperActivityId: "wrapper-exit-unlocking-uuid", calledBehaviorId: "act-execute-check-closed-uuid" }`

#### 第六步：识别转换 (Transitions)
- **T1**: 从 ps-main-initial-uuid 到 state-locked-uuid。(trans-initial-to-locked-uuid)，在 region-door-main-uuid。
- **T2**: 从 state-locked-uuid 到 state-unlocking-uuid。(trans-locked-to-unlocking-uuid)，在 region-door-main-uuid。
- **T3**: 从 state-unlocking-uuid 到 state-locked-uuid。(trans-unlocking-to-locked-uuid)，在 region-door-main-uuid。
- **T4**: 从 state-locked-uuid 到 state-alarm-uuid。(trans-locked-to-alarm-uuid)，在 region-door-main-uuid。
- **T5 (锁定状态内部)**: 从 ps-locked-sub-initial-uuid 到 state-selfcheck-uuid。(trans-subinitial-to-selfcheck-uuid)，在 region-locked-sub-uuid。
- **T6 (锁定状态内部)**: 从 state-selfcheck-uuid 到 ps-locked-sub-final-uuid。(trans-selfcheck-to-subfinal-uuid)，在 region-locked-sub-uuid。

#### 第七步：识别转换的组成部分 (Triggers, Guards, Effects)
- **对于 T2 (locked -> unlocking)**:
    - Trigger: 引用 "有效开锁信号事件" (event-valid-unlock-sig-event-uuid)
    - Guard: "安全系统已解除 == true" (language: "English")
    - Effect: 调用 "记录开锁尝试" (act-log-unlock-attempt-uuid, parentId: pkg-door-behaviors-uuid)。
        - JSON中表示为: `effect: { wrapperActivityId: "wrapper-effect-t2-uuid", calledBehaviorId: "act-log-unlock-attempt-uuid" }`
- **对于 T3 (unlocking -> locked)**:
    - Trigger: 引用 "超时事件" (event-timeout-uuid)
    - Effect: 调用 "执行自动上锁" (act-execute-auto-lock-uuid, parentId: pkg-door-behaviors-uuid)。
        - JSON中表示为: `effect: { wrapperActivityId: "wrapper-effect-t3-uuid", calledBehaviorId: "act-execute-auto-lock-uuid" }`
- **对于 T4 (locked -> alarm)**:
    - Trigger: 引用 "强制开门事件" (event-forced-open-event-uuid)
    - Effect: 调用 "执行鸣响警报" (act-execute-sound-alarm-uuid, parentId: pkg-door-behaviors-uuid)。
        - JSON中表示为: `effect: { wrapperActivityId: "wrapper-effect-t4-uuid", calledBehaviorId: "act-execute-sound-alarm-uuid" }`

#### 第八步：识别其他辅助元素
- **信号 (Signal)**:
    - "有效开锁信号" (sig-valid-unlock-uuid, parentId: pkg-door-control-uuid)
- **事件 (Event)**:
    - "有效开锁信号事件" (event-valid-unlock-sig-event-uuid, type: SignalEvent, signalId: sig-valid-unlock-uuid, parentId: pkg-door-control-uuid)
    - "超时事件" (event-timeout-uuid, type: Event, parentId: pkg-door-control-uuid) (假设为通用事件，或TimeEvent)
    - "强制开门事件" (event-forced-open-event-uuid, type: Event, parentId: pkg-door-control-uuid) (假设为通用事件)
- **被调用的具体活动 (Activities in pkg-door-behaviors-uuid)**:
    - "记录开锁尝试" (act-log-unlock-attempt-uuid)
    - "执行解锁门闩" (act-execute-unlock-bolt-uuid)
    - "保持门锁打开" (act-keep-door-open-uuid)
    - "执行检查门是否已关闭" (act-execute-check-closed-uuid)
    - "执行自动上锁" (act-execute-auto-lock-uuid)
    - "执行鸣响警报" (act-execute-sound-alarm-uuid)
    (所有这些活动的 parentId 都是 pkg-door-behaviors-uuid)

#### 第九步：整理优化输出
---
模型: 门禁系统模型 (model-door-access-sm-uuid)
  包: 门禁控制包 (pkg-door-control-uuid)
    块: 门控制器 (blk-door-controller-uuid, classifierBehaviorId: sm-door-access-uuid)
      状态机: 门禁状态机 (sm-door-access-uuid)
        主区域: 主区域 (region-door-main-uuid)
          初始伪状态: (ps-main-initial-uuid, kind: initial)
          状态: 锁定 (state-locked-uuid, type: State, isComposite: true)
            进入点: ep_lock (ps-locked-entry1-uuid, kind: entryPoint, parentId: state-locked-uuid)
            子区域: 内部安全检查 (region-locked-sub-uuid, parentId: state-locked-uuid)
              初始伪状态: (ps-locked-sub-initial-uuid, kind: initial, parentId: region-locked-sub-uuid)
              状态: 自检 (state-selfcheck-uuid, parentId: region-locked-sub-uuid)
              最终伪状态: (ps-locked-sub-final-uuid, kind: final, parentId: region-locked-sub-uuid)
              转换 (parentId: region-locked-sub-uuid):
                - T5: (ps-locked-sub-initial-uuid) -> (state-selfcheck-uuid) (trans-subinitial-to-selfcheck-uuid)
                - T6: (state-selfcheck-uuid) -> (ps-locked-sub-final-uuid) (trans-selfcheck-to-subfinal-uuid)
          状态: 开锁中 (state-unlocking-uuid, type: State, parentId: region-door-main-uuid)
            Entry: { wrapperActivityId: "wrapper-entry-unlocking-uuid", calledBehaviorId: "act-execute-unlock-bolt-uuid" }
            DoActivity: { wrapperActivityId: "wrapper-do-unlocking-uuid", calledBehaviorId: "act-keep-door-open-uuid" }
            Exit: { wrapperActivityId: "wrapper-exit-unlocking-uuid", calledBehaviorId: "act-execute-check-closed-uuid" }
          状态: 报警 (state-alarm-uuid, type: State, parentId: region-door-main-uuid)
          转换 (parentId: region-door-main-uuid):
            - T1: (ps-main-initial-uuid) -> (state-locked-uuid) (trans-initial-to-locked-uuid)
            - T2: (state-locked-uuid) -> (state-unlocking-uuid) (trans-locked-to-unlocking-uuid)
                TriggerIds: ["event-valid-unlock-sig-event-uuid"]
                Guard: { expression: "安全系统已解除 == true", language: "English" }
                Effect: { wrapperActivityId: "wrapper-effect-t2-uuid", calledBehaviorId: "act-log-unlock-attempt-uuid" }
            - T3: (state-unlocking-uuid) -> (state-locked-uuid) (trans-unlocking-to-locked-uuid)
                TriggerIds: ["event-timeout-uuid"]
                Effect: { wrapperActivityId: "wrapper-effect-t3-uuid", calledBehaviorId: "act-execute-auto-lock-uuid" }
            - T4: (state-locked-uuid) -> (state-alarm-uuid) (trans-locked-to-alarm-uuid)
                TriggerIds: ["event-forced-open-event-uuid"]
                Effect: { wrapperActivityId: "wrapper-effect-t4-uuid", calledBehaviorId: "act-execute-sound-alarm-uuid" }
    信号 (parentId: pkg-door-control-uuid):
      - 有效开锁信号 (sig-valid-unlock-uuid)
    事件 (parentId: pkg-door-control-uuid):
      - 有效开锁信号事件 (event-valid-unlock-sig-event-uuid, type: SignalEvent, signalId: sig-valid-unlock-uuid)
      - 超时事件 (event-timeout-uuid, type: Event)
      - 强制开门事件 (event-forced-open-event-uuid, type: Event)
  包: 门禁行为库 (pkg-door-behaviors-uuid)
    活动:
      - 记录开锁尝试 (act-log-unlock-attempt-uuid)
      - 执行解锁门闩 (act-execute-unlock-bolt-uuid)
      - 保持门锁打开 (act-keep-door-open-uuid)
      - 执行检查门是否已关闭 (act-execute-check-closed-uuid)
      - 执行自动上锁 (act-execute-auto-lock-uuid)
      - 执行鸣响警报 (act-execute-sound-alarm-uuid)
---
## 具体任务
输入：          
""" + question2  + """输出：请你一步一步进行推理思考。"""

messages = [
    HumanMessage(content=prompt1),
]

print("⭐⭐⭐正在执行任务：", question2)
response = chat.invoke(messages)

print("😊😊😊推理结果：", response.content)
print("😊😊😊使用消耗", response.usage_metadata)

prompt2 = prompt1 + response.content + """
根据以上详细的推理和“整理优化输出”，请严格按照以下 JSON 格式生成 SysML 状态机图的完整描述。请确保：
1.  所有 `id` 字段都是全局唯一的。
2.  `parentId` 正确反映了元素的包含关系。
3.  对于 `State` 元素：
    *   如果它是复合状态，应包含 `regions` (Region ID列表) 和/或 `connectionPoints` (Pseudostate ID列表)。`isComposite: true` 也可以作为一个可选的显式标记。
    *   如果它是最终状态，其 `type` 应为 `FinalState`。简单状态则为 `State`。
    *   `entry`, `doActivity`, `exit` 行为应表示为一个对象，包含 `wrapperActivityId` (内嵌包装活动的唯一ID) 和 `calledBehaviorId` (被调用的、在行为库中定义的具体活动的ID)。如果状态没有某个行为，则对应的键（如`entry`）不存在。
4.  对于 `Pseudostate` 元素，`kind` 字段必须准确表示其类型。其 `parentId` 可以是Region或作为连接点的State。
5.  对于 `Transition` 元素：
    *   `sourceId` 和 `targetId` 正确引用了源和目标状态/伪状态的ID。
    *   `triggerIds` 是一个列表，包含触发此转换的事件ID。
    *   `guard` 是一个对象，包含 `expression` 和 `language`。
    *   `effect` 行为应表示为一个对象，包含 `wrapperActivityId` 和 `calledBehaviorId`。如果转换没有效果行为，则 `effect` 键不存在。
6.  所有被 `calledBehaviorId` 引用的活动 (Activity)，都应作为独立的元素定义在 `elements` 列表中，并且通常其 `parentId` 指向一个行为库包。
7.  `SignalEvent` 元素应有 `signalId` 引用其关联的 `Signal`。
8.  `Block` 元素可以通过 `classifierBehaviorId` 引用其主要的 `StateMachine`。

```json
{
  "model": [
    {
      "id": "model-unique-id",
      "name": "StateMachineModelName",
      "type": "Model"
    }
  ],
  "elements": [
    // Packages
    {
      "id": "pkg-main-app-uuid",
      "type": "Package",
      "name": "MainApplicationPackage",
      "parentId": "model-unique-id"
    },
    {
      "id": "pkg-behaviors-library-uuid",
      "type": "Package",
      "name": "BehaviorsLibrary",
      "parentId": "model-unique-id"
    },
    // Blocks (Classes that own StateMachines)
    {
      "id": "blk-device-controller-uuid",
      "type": "Block",
      "name": "DeviceController",
      "parentId": "pkg-main-app-uuid",
      "classifierBehaviorId": "sm-device-lifecycle-uuid"
    },
    // StateMachines
    {
      "id": "sm-device-lifecycle-uuid",
      "type": "StateMachine",
      "name": "DeviceLifecycleSM",
      "parentId": "blk-device-controller-uuid"
    },
    // Regions
    {
      "id": "region-main-sm-uuid",
      "type": "Region",
      "parentId": "sm-device-lifecycle-uuid"
    },
    // States
    {
      "id": "state-idle-uuid",
      "type": "State",
      "name": "Idle",
      "parentId": "region-main-sm-uuid",
      "entry": {
        "wrapperActivityId": "wrapper-entry-idle-uuid", // Unique ID for the implicit wrapper activity
        "calledBehaviorId": "act-perform-idle-setup-uuid" // ID of Activity in behaviors library
      }
    },
    {
      "id": "state-active-uuid",
      "type": "State",
      "name": "Active",
      "parentId": "region-main-sm-uuid",
      "doActivity": {
        "wrapperActivityId": "wrapper-do-active-uuid",
        "calledBehaviorId": "act-process-data-uuid"
      },
      "exit": {
        "wrapperActivityId": "wrapper-exit-active-uuid",
        "calledBehaviorId": "act-perform-active-cleanup-uuid"
      },
      "isComposite": true, // Optional explicit marker for composite state
      "connectionPoints": ["ps-entry-active-uuid"],
      "regions": ["region-sub-active-uuid"]
    },
    {
        "id": "region-sub-active-uuid",
        "type": "Region",
        "name": "SubRegionForActive",
        "parentId": "state-active-uuid"
    },
    {
      "id": "state-final-device-uuid",
      "type": "FinalState",
      "parentId": "region-main-sm-uuid"
    },
    // Pseudostates
    {
      "id": "ps-initial-sm-uuid",
      "type": "Pseudostate",
      "kind": "initial",
      "parentId": "region-main-sm-uuid"
    },
    {
      "id": "ps-entry-active-uuid",
      "type": "Pseudostate",
      "kind": "entryPoint",
      "name": "enterActive",
      "parentId": "state-active-uuid" // Belongs to the composite state
    },
    // Transitions
    {
      "id": "trans-idle-to-active-uuid",
      "type": "Transition",
      "sourceId": "state-idle-uuid",
      "targetId": "state-active-uuid",
      "parentId": "region-main-sm-uuid",
      "triggerIds": ["event-start-command-uuid"],
      "guard": {
        "expression": "isReadyToActivate == true",
        "language": "English"
      },
      "effect": {
        "wrapperActivityId": "wrapper-effect-idle-active-uuid",
        "calledBehaviorId": "act-log-activation-uuid"
      }
    },
    // Activities (defined in a behavior library)
    {
      "id": "act-perform-idle-setup-uuid",
      "type": "Activity",
      "name": "PerformIdleSetup",
      "parentId": "pkg-behaviors-library-uuid"
    },
    {
      "id": "act-process-data-uuid",
      "type": "Activity",
      "name": "ProcessDataContinuously",
      "parentId": "pkg-behaviors-library-uuid"
    },
    {
      "id": "act-perform-active-cleanup-uuid",
      "type": "Activity",
      "name": "PerformActiveCleanup",
      "parentId": "pkg-behaviors-library-uuid"
    },
    {
      "id": "act-log-activation-uuid",
      "type": "Activity",
      "name": "LogActivationAttempt",
      "parentId": "pkg-behaviors-library-uuid"
    },
    // Signals and Events
    {
      "id": "sig-start-cmd-uuid",
      "type": "Signal",
      "name": "StartCommandSignal",
      "parentId": "pkg-main-app-uuid"
    },
    {
      "id": "event-start-command-uuid",
      "type": "SignalEvent",
      "name": "ReceiveStartCommand",
      "signalId": "sig-start-cmd-uuid",
      "parentId": "pkg-main-app-uuid"
    }
  ]
}
```

请严格按照上面的JSON结构输出结果。"""

print("⭐⭐⭐处理任务：", "复合COT")

messages = [
    HumanMessage(content=prompt2),
]

response = chat.invoke(messages)

print("😊😊😊处理结果", response.content)
print("😊😊😊使用消耗", response.usage_metadata)