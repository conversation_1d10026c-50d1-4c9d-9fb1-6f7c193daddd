import json
from neo4j import GraphDatabase
import traceback
from collections import defaultdict

# ==== Neo4j 连接配置 ====
URI = "bolt://localhost:7687"
USER = "neo4j"
PASSWORD = "123456789" # 请替换为您的密码

class Neo4jStateMachineToJsonConverter:
    def __init__(self, uri, user, password):
        self._driver = None
        self.elements_map = {} # 存储所有重建的JSON元素对象，以ID为键
        self.model_info = {}     # 存储模型信息

        try:
            self._driver = GraphDatabase.driver(uri, auth=(user, password),
                                               connection_timeout=30, max_connection_lifetime=3600)
            self._driver.verify_connectivity()
            print("成功连接到 Neo4j 并验证连接。")
        except Exception as e:
            print(f"错误：无法连接到 Neo4j 或验证失败 - {e}")
            self._driver = None
            raise

    def close(self):
        if self._driver:
            self._driver.close()
            print("Neo4j 连接已关闭。")

    def _execute_read_tx(self, tx, query, parameters=None):
        try:
            result = tx.run(query, parameters if parameters else {})
            return [record.data() for record in result]
        except Exception as e:
            print(f"错误：执行 Cypher 读取查询失败\n查询: {query}\n参数: {parameters}\n错误: {e}")
            traceback.print_exc()
            raise

    def _fetch_model_and_base_nodes(self, session):
        """获取模型节点和所有其他元素节点及其基本属性。"""
        print("步骤 1：获取模型和元素节点...")
        self.elements_map.clear()
        self.model_info.clear()

        # 1a. 获取模型信息
        model_query = "MATCH (m:Model) WHERE m.elementId IS NOT NULL RETURN m.elementId AS id, m.name AS name, m.type AS type LIMIT 1"
        model_records = session.execute_read(self._execute_read_tx, model_query)
        if model_records:
            model_data = model_records[0]
            self.model_info = {"id": model_data["id"], "name": model_data.get("name"), "type": model_data.get("type", "Model")}
            print(f"  - 成功获取模型信息: ID={self.model_info.get('id')}")
        else:
            print("警告：未能在 Neo4j 中找到 :Model 节点。")
            # 如果没有模型节点，可能无法继续，或者需要一个默认模型ID进行后续查询
            return False # 指示失败

        # 1b. 获取所有其他元素节点
        node_query = """
        MATCH (n)
        WHERE n.elementId IS NOT NULL AND NOT n:Model 
        RETURN n.elementId AS id, labels(n) AS labels, properties(n) AS props
        """
        node_records = session.execute_read(self._execute_read_tx, node_query)
        print(f"  - 成功获取 {len(node_records)} 个元素节点记录 (非模型)。")

        for record in node_records:
            props = record['props']
            element_id = record['id']
            
            # 从属性中获取原始JSON的'type'
            element_type_json = props.get('type')
            if not element_type_json:
                # 如果属性中没有，尝试从标签推断（这不太可靠，因为标签可能不直接映射到JSON的type）
                # 假设上传时Node的label和JSON的type一致
                primary_label = None
                for lbl in record['labels']:
                    if lbl != '_Neo4jDesktop_VisualNode' and lbl != 'Database': # 排除Neo4j内部标签
                        primary_label = lbl
                        break
                element_type_json = primary_label if primary_label else "Unknown"
                if primary_label != props.get('type') and props.get('type'): # 补充日志
                    print(f"警告: 节点 {element_id} 的属性 'type' ({props.get('type')}) 与推断标签 ({primary_label}) 不一致。使用属性 'type'。")
                    element_type_json = props.get('type')

            if element_type_json == "Unknown":
                print(f"警告：无法确定节点 {element_id} 的JSON类型，已跳过。标签: {record['labels']}")
                continue

            element_json = {"id": element_id, "type": element_type_json}
            if "name" in props: element_json["name"] = props["name"]

            # 添加特定类型的属性
            if element_type_json == "Block" and "classifierBehaviorId" in props:
                element_json["classifierBehaviorId"] = props["classifierBehaviorId"]
            elif element_type_json == "Pseudostate" and "kind" in props:
                element_json["kind"] = props["kind"]
            elif element_type_json == "State" and "isComposite" in props:
                element_json["isComposite"] = props["isComposite"]
            elif element_type_json == "Transition":
                if "guard_expression" in props: # 从上传时的属性名恢复
                    element_json["guard"] = {
                        "expression": props["guard_expression"],
                        "language": props.get("guard_language")
                    }
            elif element_type_json == "SignalEvent" and "referencesSignalId" in props:
                element_json["signalId"] = props["referencesSignalId"]
            # 注意: entry/exit/doActivity/effect/triggers/connectionPoints/regions 将通过关系查询来填充

            self.elements_map[element_id] = element_json
        
        print(f"  - 解析了 {len(self.elements_map)} 个有效元素节点到 elements_map。")
        return True

    def _reconstruct_structure_and_references(self, session):
        """获取关系，重建元素的 parentId, sourceId, targetId, behaviors, etc."""
        print("步骤 2：重建结构和引用...")

        model_id = self.model_info.get("id")
        if not model_id:
            print("错误: 模型ID未设置，无法重建父子关系。")
            return

        # 2a. 处理父子关系 (CONTAINS, HAS_REGION, HAS_SUBVERTEX, HAS_CONNECTION_POINT, HAS_TRANSITION)
        # 并设置 parentId
        # HAS_CLASSIFIER_BEHAVIOR 也设置 parentId (Block -> StateMachine)
        parent_child_queries = [
            "MATCH (p)-[:CONTAINS]->(c) RETURN p.elementId AS pId, c.elementId AS cId",
            "MATCH (p)-[:HAS_CLASSIFIER_BEHAVIOR]->(c) RETURN p.elementId AS pId, c.elementId AS cId", # SM的父是Block
            "MATCH (p)-[:HAS_REGION]->(c) RETURN p.elementId AS pId, c.elementId AS cId",
            "MATCH (p)-[:HAS_SUBVERTEX]->(c) RETURN p.elementId AS pId, c.elementId AS cId",
            "MATCH (p)-[:HAS_CONNECTION_POINT]->(c) RETURN p.elementId AS pId, c.elementId AS cId",
            "MATCH (p)-[:HAS_TRANSITION]->(c) RETURN p.elementId AS pId, c.elementId AS cId",
        ]
        for query in parent_child_queries:
            records = session.execute_read(self._execute_read_tx, query)
            for record in records:
                parent_id = record['pId']
                child_id = record['cId']
                if child_id in self.elements_map:
                    # 只有当父ID不是顶层模型ID时，才设置parentId，因为顶层元素的parentId是隐式的
                    if parent_id != model_id:
                        self.elements_map[child_id]["parentId"] = parent_id
        
        # 2b. 填充State的 connectionPoints 和 regions 数组
        state_cp_query = """
        MATCH (s:State)-[:HAS_CONNECTION_POINT]->(cp:Pseudostate)
        RETURN s.elementId AS stateId, cp.elementId AS cpId
        """
        state_cp_records = session.execute_read(self._execute_read_tx, state_cp_query)
        for record in state_cp_records:
            state = self.elements_map.get(record['stateId'])
            if state:
                if "connectionPoints" not in state: state["connectionPoints"] = []
                state["connectionPoints"].append(record['cpId'])

        state_region_query = """
        MATCH (s:State)-[:HAS_REGION]->(r:Region)
        RETURN s.elementId AS stateId, r.elementId AS regionId
        """
        state_region_records = session.execute_read(self._execute_read_tx, state_region_query)
        for record in state_region_records:
            state = self.elements_map.get(record['stateId'])
            if state:
                if "regions" not in state: state["regions"] = []
                state["regions"].append(record['regionId'])

        # 2c. 填充Transition的 sourceId, targetId, triggerIds
        transition_details_query = """
        MATCH (t:Transition)
        OPTIONAL MATCH (t)-[:HAS_SOURCE]->(src)
        OPTIONAL MATCH (t)-[:HAS_TARGET]->(tgt)
        OPTIONAL MATCH (t)-[:HAS_TRIGGER]->(triggerEvent)
        RETURN t.elementId AS transId, src.elementId AS sourceId, tgt.elementId AS targetId, collect(triggerEvent.elementId) AS triggerEventIds
        """
        # collect() 会将所有匹配的triggerEventId聚合成一个列表
        trans_records = session.execute_read(self._execute_read_tx, transition_details_query)
        for record in trans_records:
            trans = self.elements_map.get(record['transId'])
            if trans:
                if record['sourceId']: trans["sourceId"] = record['sourceId']
                if record['targetId']: trans["targetId"] = record['targetId']
                # 过滤掉 triggerEventIds 中的 null (如果没有触发器)
                trigger_ids = [tid for tid in record.get('triggerEventIds', []) if tid is not None]
                if trigger_ids: trans["triggerIds"] = trigger_ids
        
        # 2d. 填充State和Transition的Behavior (entry, exit, doActivity, effect)
        behavior_rels_map = {
            "State": [
                ("HAS_ENTRY_BEHAVIOR", "entry"), 
                ("HAS_EXIT_BEHAVIOR", "exit"),
                ("HAS_DO_BEHAVIOR", "doActivity")
            ],
            "Transition": [("HAS_EFFECT", "effect")]
        }

        for owner_type, behaviors in behavior_rels_map.items():
            for rel_type, json_key in behaviors:
                query = f"""
                MATCH (owner:{owner_type})-[r:{rel_type}]->(wrapper:Activity)
                OPTIONAL MATCH (wrapper)-[:CALLS_BEHAVIOR]->(called:Activity)
                RETURN owner.elementId AS ownerId, wrapper.elementId AS wrapperId, called.elementId AS calledId
                """
                behavior_records = session.execute_read(self._execute_read_tx, query)
                for record in behavior_records:
                    owner_elem = self.elements_map.get(record['ownerId'])
                    if owner_elem:
                        behavior_obj = {"wrapperActivityId": record['wrapperId']}
                        if record['calledId']:
                            behavior_obj["calledBehaviorId"] = record['calledId']
                        owner_elem[json_key] = behavior_obj
        
        # 2e. 填充 SignalEvent 的 signalId (已经在 _fetch_model_and_base_nodes 中通过属性处理了)
        # 如果是通过关系建模的：
        # signal_event_query = "MATCH (se:SignalEvent)-[:REFERENCES_SIGNAL]->(s:Signal) RETURN se.elementId AS seId, s.elementId AS sigId"
        # ...

        print("  - 结构和引用重建完成。")


    def fetch_and_reconstruct_json(self):
        if not self._driver:
            print("错误：Neo4j 驱动未初始化。")
            return None

        try:
            with self._driver.session(database="neo4j") as session:
                if not self._fetch_model_and_base_nodes(session):
                    print("错误: 获取基础节点失败，无法继续。")
                    return None
                self._reconstruct_structure_and_references(session)

            print("步骤 3：构建最终 JSON 对象...")
            final_elements_list = list(self.elements_map.values())
            
            # 确保parentId的正确性：如果一个元素的parentId是模型ID，则不应在JSON中出现parentId字段
            model_id = self.model_info.get("id")
            for elem in final_elements_list:
                if "parentId" in elem and elem["parentId"] == model_id:
                    del elem["parentId"]


            final_json_output = {
                "model": [self.model_info] if self.model_info and "id" in self.model_info else [],
                "elements": final_elements_list
            }
            print("JSON 重建完成。")
            return final_json_output

        except Exception as e:
            print(f"错误：从 Neo4j 获取或重建数据时发生错误：{e}")
            traceback.print_exc()
            return None

# --- 主执行块 ---
if __name__ == "__main__":
    converter = None
    try:
        converter = Neo4jStateMachineToJsonConverter(URI, USER, PASSWORD) # 请修改为你的密码
        if converter._driver:
            reconstructed_json_data = converter.fetch_and_reconstruct_json()
            if reconstructed_json_data:
                print("\n--- 重建的状态机 JSON 数据 ---")
                # 为了更好的可读性，可以先对elements列表排序，例如按type再按id
                # reconstructed_json_data["elements"].sort(key=lambda x: (x.get("type", ""), x.get("id", "")))
                print(json.dumps(reconstructed_json_data, indent=2, ensure_ascii=False))
                
                try:
                    with open("reconstructed_statemachine_data.json", "w", encoding="utf-8") as f:
                        json.dump(reconstructed_json_data, f, indent=2, ensure_ascii=False)
                    print("\n已将重建的 JSON 保存到 reconstructed_statemachine_data.json")
                except Exception as e:
                    print(f"\n错误：保存重建的 JSON 到文件时出错：{e}")
            else:
                print("未能成功重建 JSON 数据。")
    except Exception as main_e:
        print(f"主程序发生错误: {main_e}")
        traceback.print_exc()
    finally:
        if converter and converter._driver:
            converter.close()