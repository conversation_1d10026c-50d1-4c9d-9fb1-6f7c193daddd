import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
from collections import defaultdict

class ParametricDiagramConverter:
    """
    专门针对参数图的JSON到XML转换器
    基于 /temp/参数图/par_func.py 的实现模式进行增强
    """
    
    # 使用与示例文件相同的命名空间
    NAMESPACES = {
        'xmi': 'http://www.omg.org/XMI',
        'uml': 'http://www.omg.org/spec/UML/20090901',
        'sysml': 'http://www.omg.org/spec/SysML/20131001',
        'MD_Customization_for_SysML__additional_stereotypes': 'http://www.magicdraw.com/spec/Customization/190/SysML'
    }
    
    def __init__(self, json_data):
        if isinstance(json_data, str):
            self.json_data = json.loads(json_data)
        else:
            self.json_data = json_data
            
        self.xml_elements = {}
        self.nested_connector_ends = []
        
        # 注册命名空间
        for prefix, uri in self.NAMESPACES.items():
            ET.register_namespace(prefix, uri)
    
    def create_element(self, tag, attrs=None, parent=None, text=None):
        """辅助函数：创建XML元素"""
        if attrs is None:
            attrs = {}
        elem = ET.Element(tag, attrs)
        if parent is not None:
            parent.append(elem)
        if text is not None:
            elem.text = text
        return elem
    
    def add_diagram_extension(self, block_id, diagram_name="参数图"):
        """为Block添加参数图扩展"""
        diagram_id = f"_{block_id}_diagram"
        rep_id = f"_{block_id}_rep"
        contents_id = f"_{block_id}_contents"
        
        extension = ET.Element('xmi:Extension', {'extender': 'MagicDraw UML 2021x'})
        model_extension = ET.SubElement(extension, 'modelExtension')
        owned_diagram = ET.SubElement(model_extension, 'ownedDiagram', {
            'xmi:type': 'uml:Diagram', 'xmi:id': diagram_id, 'name': diagram_name,
            'visibility': 'public', 'context': block_id, 'ownerOfDiagram': block_id
        })
        
        inner_extension = ET.SubElement(owned_diagram, 'xmi:Extension', {'extender': 'MagicDraw UML 2021x'})
        diagram_rep = ET.SubElement(inner_extension, 'diagramRepresentation')
        diagram_obj = ET.SubElement(diagram_rep, 'diagram:DiagramRepresentationObject', {
            'ID': rep_id, 'initialFrameSizeSet': 'true',
            'requiredFeature': 'com.nomagic.magicdraw.plugins.impl.sysml#SysML;MD_customization_for_SysML.mdzip;UML_Standard_Profile.mdzip',
            'type': 'SysML Parametric Diagram', 'umlType': 'Composite Structure Diagram',
            'xmi:id': f"_{block_id}_xmi", 'xmi:version': '2.0',
            'xmlns:binary': 'http://www.nomagic.com/ns/cameo/client/binary/1.0',
            'xmlns:diagram': 'http://www.nomagic.com/ns/magicdraw/core/diagram/1.0',
            'xmlns:xmi': 'http://www.omg.org/XMI',
            'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance'
        })
        
        ET.SubElement(diagram_obj, 'diagramContents', {
            'contentHash': 'bfe6e2a79c6e668f74eded668a40a36b357487c5',
            'exporterName': 'MagicDraw UML', 'exporterVersion': '2021x', 'xmi:id': contents_id
        })
        
        return extension
    
    def generate_xml(self):
        """主要的XML生成函数"""
        # 1. 初始化XML根和基本结构
        root_attrs = {
            'xmi:version': '2.5',
            'xmlns:xmi': self.NAMESPACES['xmi'],
            'xmlns:uml': self.NAMESPACES['uml'],
            'xmlns:sysml': self.NAMESPACES['sysml'],
            'xmlns:MD_Customization_for_SysML__additional_stereotypes': self.NAMESPACES['MD_Customization_for_SysML__additional_stereotypes']
        }
        root = ET.Element('xmi:XMI', root_attrs)
        
        # 2. 预处理JSON数据
        elements_by_id = {elem['id']: elem for elem in self.json_data['elements']}
        children_by_parent_id = defaultdict(list)
        for elem in self.json_data['elements']:
            parent_id = elem.get('parentId')
            if parent_id:
                children_by_parent_id[parent_id].append(elem)
        
        # 3. 创建顶层Model元素
        model_data = self.json_data['model']
        if isinstance(model_data, list):
            model_data = model_data[0]
        
        model = self.create_element('uml:Model', {
            'xmi:type': 'uml:Model',
            'xmi:id': model_data['id'],
            'name': model_data['name']
        }, root)
        self.xml_elements[model_data['id']] = model
        
        # 4. 递归构建UML元素树
        def build_tree(parent_id, parent_xml_element):
            if parent_id not in children_by_parent_id:
                return
            
            for elem_data in children_by_parent_id[parent_id]:
                elem_id = elem_data['id']
                elem_type = elem_data['type']
                elem_name = elem_data.get('name', '')
                
                # 处理不同类型的元素
                if elem_type in ["Package", "Block", "ConstraintBlock", "ValueType"]:
                    tag = 'packagedElement'
                    if elem_type == "Package": 
                        uml_type = "uml:Package"
                    elif elem_type == "ValueType": 
                        uml_type = "uml:DataType"
                    else: 
                        uml_type = "uml:Class"
                    
                    elem = self.create_element(tag, {
                        'xmi:type': uml_type, 
                        'xmi:id': elem_id, 
                        'name': elem_name
                    }, parent_xml_element)
                    self.xml_elements[elem_id] = elem
                    
                    # 为Block添加图表扩展
                    if elem_type == "Block":
                        elem.append(self.add_diagram_extension(elem_id))
                    
                    # 处理ConstraintBlock的specification
                    if elem_type == "ConstraintBlock" and 'specification' in elem_data:
                        self._add_constraint_specification(elem, elem_data, elem_id)
                
                elif elem_type == "Property":
                    self._handle_property(elem_data, parent_xml_element, elem_id, elem_name)
                
                elif elem_type == "ConstraintParameter":
                    self._handle_constraint_parameter(elem_data, parent_xml_element, elem_id, elem_name)
                
                elif elem_type == "BindingConnector":
                    self._handle_binding_connector(elem_data, parent_xml_element, elem_id)
                
                # 递归处理子元素
                if elem_id in self.xml_elements:
                    build_tree(elem_id, self.xml_elements[elem_id])
        
        # 开始构建树
        build_tree(model_data['id'], model)
        
        # 5. 添加所有构造型（Stereotypes）
        self._apply_stereotypes(root, elements_by_id)
        
        return ET.tostring(root, encoding='unicode', method='xml')
    
    def _add_constraint_specification(self, elem, elem_data, elem_id):
        """为ConstraintBlock添加specification"""
        spec_data = elem_data['specification']
        owned_rule = self.create_element('ownedRule', {
            'xmi:type': 'uml:Constraint', 
            'xmi:id': f"{elem_id}_constraint"
        }, elem)
        
        self.create_element('constrainedElement', {'xmi:idref': elem_id}, owned_rule)
        
        spec = self.create_element('specification', {
            'xmi:type': 'uml:OpaqueExpression', 
            'xmi:id': f"{elem_id}_spec"
        }, owned_rule)
        
        self.create_element('body', {}, spec, text=spec_data.get('expression', ''))
        self.create_element('language', {}, spec, text=spec_data.get('language', ''))
    
    def _handle_property(self, elem_data, parent_xml_element, elem_id, elem_name):
        """处理Property元素"""
        prop_kind = elem_data.get('propertyKind', 'value')
        type_id = elem_data.get('typeId')
        
        attrs = {
            'xmi:type': 'uml:Property', 
            'xmi:id': elem_id, 
            'name': elem_name,
            'aggregation': 'composite'
        }
        
        if prop_kind == "constraint":
            attrs['visibility'] = 'private'
        
        elem = self.create_element('ownedAttribute', attrs, parent_xml_element)
        self.xml_elements[elem_id] = elem
        
        # 处理类型引用 - 关键修正点
        if type_id:
            if type_id.startswith('http'):
                self.create_element('type', {'href': type_id}, elem)
            else:
                elem.set('type', type_id)
    
    def _handle_constraint_parameter(self, elem_data, parent_xml_element, elem_id, elem_name):
        """处理ConstraintParameter元素"""
        type_id = elem_data.get('typeId')
        
        elem = self.create_element('ownedAttribute', {
            'xmi:type': 'uml:Port', 
            'xmi:id': elem_id, 
            'name': elem_name,
            'visibility': 'private', 
            'aggregation': 'composite'
        }, parent_xml_element)
        
        # 处理类型引用
        if type_id:
            if type_id.startswith('http'):
                self.create_element('type', {'href': type_id}, elem)
            else:
                elem.set('type', type_id)
        
        self.xml_elements[elem_id] = elem
    
    def _handle_binding_connector(self, elem_data, parent_xml_element, elem_id):
        """处理BindingConnector元素"""
        elem = self.create_element('ownedConnector', {
            'xmi:type': 'uml:Connector', 
            'xmi:id': elem_id, 
            'visibility': 'public'
        }, parent_xml_element)
        self.xml_elements[elem_id] = elem
        
        # 处理连接器的两端
        end1_data = elem_data.get('end1', {})
        end2_data = elem_data.get('end2', {})
        
        end1_id = f"{elem_id}_end1"
        end2_id = f"{elem_id}_end2"
        
        # 创建end1
        if 'propertyRefId' in end1_data:
            self.create_element('end', {
                'xmi:type': 'uml:ConnectorEnd', 
                'xmi:id': end1_id, 
                'role': end1_data['propertyRefId']
            }, elem)
        
        # 创建end2
        if 'partRefId' in end2_data and 'portRefId' in end2_data:
            self.create_element('end', {
                'xmi:type': 'uml:ConnectorEnd', 
                'xmi:id': end2_id, 
                'partWithPort': end2_data['partRefId'], 
                'role': end2_data['portRefId']
            }, elem)
            
            # 记录需要添加NestedConnectorEnd构造型的端点
            self.nested_connector_ends.append((end2_id, end2_data['partRefId']))
        
        # 保存端点引用
        self.xml_elements[end1_id] = elem.find('end[1]')
        self.xml_elements[end2_id] = elem.find('end[2]')
    
    def _apply_stereotypes(self, root, elements_by_id):
        """应用SysML构造型"""
        for elem_id, elem_data in elements_by_id.items():
            elem_type = elem_data['type']
            
            if elem_type == "Block":
                self.create_element('sysml:Block', {
                    'xmi:id': f"{elem_id}_stereotype", 
                    'base_Class': elem_id
                }, root)
            
            elif elem_type == "ConstraintBlock":
                self.create_element('sysml:ConstraintBlock', {
                    'xmi:id': f"{elem_id}_stereotype", 
                    'base_Class': elem_id
                }, root)
            
            elif elem_type == "ValueType":
                self.create_element('sysml:ValueType', {
                    'xmi:id': f"{elem_id}_stereotype", 
                    'base_DataType': elem_id
                }, root)
            
            elif elem_type == "Property":
                prop_kind = elem_data.get('propertyKind', 'value')
                if prop_kind == 'value':
                    self.create_element('MD_Customization_for_SysML__additional_stereotypes:ValueProperty', {
                        'xmi:id': f"{elem_id}_stereotype", 
                        'base_Property': elem_id
                    }, root)
                elif prop_kind == 'constraint':
                    self.create_element('MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty', {
                        'xmi:id': f"{elem_id}_stereotype", 
                        'base_Property': elem_id
                    }, root)
            
            elif elem_type == "ConstraintParameter":
                self.create_element('MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter', {
                    'xmi:id': f"{elem_id}_stereotype", 
                    'base_Port': elem_id
                }, root)
            
            elif elem_type == "BindingConnector":
                self.create_element('sysml:BindingConnector', {
                    'xmi:id': f"{elem_id}_stereotype", 
                    'base_Connector': elem_id
                }, root)
        
        # 添加NestedConnectorEnd构造型
        for end_id, prop_path_id in self.nested_connector_ends:
            self.create_element('sysml:NestedConnectorEnd', {
                'xmi:id': f"{end_id}_stereotype", 
                'base_ConnectorEnd': end_id, 
                'propertyPath': prop_path_id
            }, root)
    
    def prettify_xml(self, xml_string):
        """格式化XML输出"""
        try:
            reparsed = minidom.parseString(xml_string)
            pretty_xml = reparsed.toprettyxml(indent="  ", encoding="UTF-8").decode()
            return "\n".join([line for line in pretty_xml.splitlines() if line.strip()])
        except Exception as e:
            print(f"XML格式化失败: {e}")
            return xml_string
