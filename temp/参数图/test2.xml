<xmi:XMI xmi:version="2.5" xmlns:xmi="http://www.omg.org/XMI" xmlns:uml="http://www.omg.org/spec/UML/20090901" xmlns:sysml="http://www.omg.org/spec/SysML/20131001" xmlns:MD_Customization_for_SysML__additional_stereotypes="http://www.magicdraw.com/spec/Customization/190/SysML"><uml:Model xmi:type="uml:Model" xmi:id="model-cycling" name="CyclingModel"><packagedElement xmi:type="uml:Package" xmi:id="pkg-cycling" name="ParametricDiagram">
		<profileApplication xmi:type='uml:ProfileApplication' xmi:id='SysML_Profile_App'>
			<appliedProfile href='http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML'>
				<xmi:Extension extender='MagicDraw UML 2021x'>
					<referenceExtension referentPath='SysML' referentType='Profile' originalID='_11_5EAPbeta_be00301_1147434586638_637562_1900'/>
				</xmi:Extension>
			</appliedProfile>
		</profileApplication>
<packagedElement xmi:type="uml:Class" xmi:id="block1" name="CyclingSystem"><xmi:Extension extender="MagicDraw UML 2021x"><modelExtension><ownedDiagram xmi:type="uml:Diagram" xmi:id="_block1_diagram" name="参数图" visibility="public" context="block1" ownerOfDiagram="block1"><xmi:Extension extender="MagicDraw UML 2021x"><diagramRepresentation><diagram:DiagramRepresentationObject ID="_block1_rep" initialFrameSizeSet="true" requiredFeature="com.nomagic.magicdraw.plugins.impl.sysml#SysML;MD_customization_for_SysML.mdzip;UML_Standard_Profile.mdzip" type="SysML Parametric Diagram" umlType="Composite Structure Diagram" xmi:id="_block1_xmi" xmi:version="2.0" xmlns:binary="http://www.nomagic.com/ns/cameo/client/binary/1.0" xmlns:diagram="http://www.nomagic.com/ns/magicdraw/core/diagram/1.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><diagramContents contentHash="bfe6e2a79c6e668f74eded668a40a36b357487c5" exporterName="MagicDraw UML" exporterVersion="2021x" xmi:id="_block1_contents" /></diagram:DiagramRepresentationObject></diagramRepresentation></xmi:Extension></ownedDiagram></modelExtension></xmi:Extension><ownedAttribute xmi:type="uml:Property" xmi:id="prop1" name="v" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop2" name="d" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop3" name="t" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop4" name="C" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop5" name="C0" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop6" name="C_used" 
aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop7" name="P" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop8" name="F" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop9" 
name="gear" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop10" name="T" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop11" name="k" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop12" name="D" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="prop13" name="E" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Property" xmi:id="constraint1" name="VelocityEquation" aggregation="composite" visibility="private" type="cb1" /><ownedAttribute xmi:type="uml:Property" xmi:id="constraint2" name="BatteryCapacityEquation" aggregation="composite" visibility="private" type="cb2" /><ownedAttribute xmi:type="uml:Property" xmi:id="constraint3" name="PowerEquation" aggregation="composite" visibility="private" type="cb3" /><ownedAttribute xmi:type="uml:Property" xmi:id="constraint4" name="TorqueEquation" aggregation="composite" visibility="private" type="cb4" /><ownedAttribute xmi:type="uml:Property" xmi:id="constraint5" name="RangeEquation" aggregation="composite" visibility="private" type="cb5" /><ownedConnector xmi:type="uml:Connector" xmi:id="conn1" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn1_end1" role="prop1" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn1_end2" partWithPort="constraint1" role="param1" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn2" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn2_end1" role="prop2" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn2_end2" partWithPort="constraint1" role="param2" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn3" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn3_end1" role="prop3" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn3_end2" partWithPort="constraint1" role="param3" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn4" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn4_end1" role="prop4" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn4_end2" partWithPort="constraint2" role="param4" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn5" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn5_end1" role="prop5" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn5_end2" partWithPort="constraint2" role="param5" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn6" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn6_end1" role="prop6" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn6_end2" partWithPort="constraint2" role="param6" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn7" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn7_end1" role="prop7" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn7_end2" partWithPort="constraint3" role="param7" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn8" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn8_end1" role="prop8" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn8_end2" partWithPort="constraint3" role="param8" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn9" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn9_end1" role="prop1" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn9_end2" partWithPort="constraint3" role="param9" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn10" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn10_end1" role="prop10" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn10_end2" partWithPort="constraint4" role="param10" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn11" visibility="public"><end xmi:type="uml:ConnectorEnd" 
xmi:id="conn11_end1" role="prop11" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn11_end2" partWithPort="constraint4" role="param11" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn12" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn12_end1" role="prop9" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn12_end2" partWithPort="constraint4" role="param12" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn13" visibility="public"><end 
xmi:type="uml:ConnectorEnd" xmi:id="conn13_end1" role="prop12" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn13_end2" partWithPort="constraint5" role="param13" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn14" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn14_end1" role="prop4" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn14_end2" partWithPort="constraint5" role="param14" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn15" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn15_end1" role="prop13" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn15_end2" partWithPort="constraint5" role="param15" /></ownedConnector></packagedElement><packagedElement xmi:type="uml:Class" xmi:id="cb1" name="VelocityEquation"><ownedRule xmi:type="uml:Constraint" xmi:id="cb1_constraint"><constrainedElement xmi:idref="cb1" /><specification xmi:type="uml:OpaqueExpression" xmi:id="cb1_spec"><body>v = d / t</body><language>English</language></specification></ownedRule><ownedAttribute xmi:type="uml:Port" xmi:id="param1" name="v" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param2" name="d" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param3" name="t" visibility="private" aggregation="composite" type="Real" /></packagedElement><packagedElement xmi:type="uml:Class" xmi:id="cb2" name="BatteryCapacityEquation"><ownedRule xmi:type="uml:Constraint" xmi:id="cb2_constraint"><constrainedElement xmi:idref="cb2" /><specification xmi:type="uml:OpaqueExpression" xmi:id="cb2_spec"><body>C = C0 - C_used</body><language>English</language></specification></ownedRule><ownedAttribute xmi:type="uml:Port" 
xmi:id="param4" name="C" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param5" name="C0" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param6" name="C_used" visibility="private" aggregation="composite" type="Real" /></packagedElement><packagedElement xmi:type="uml:Class" xmi:id="cb3" name="PowerEquation"><ownedRule xmi:type="uml:Constraint" xmi:id="cb3_constraint"><constrainedElement xmi:idref="cb3" /><specification xmi:type="uml:OpaqueExpression" xmi:id="cb3_spec"><body>P = 
F * v</body><language>English</language></specification></ownedRule><ownedAttribute xmi:type="uml:Port" xmi:id="param7" name="P" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param8" name="F" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param9" name="v" visibility="private" aggregation="composite" type="Real" /></packagedElement><packagedElement xmi:type="uml:Class" xmi:id="cb4" name="TorqueEquation"><ownedRule xmi:type="uml:Constraint" xmi:id="cb4_constraint"><constrainedElement xmi:idref="cb4" /><specification xmi:type="uml:OpaqueExpression" xmi:id="cb4_spec"><body>T = 
k * gear</body><language>English</language></specification></ownedRule><ownedAttribute xmi:type="uml:Port" xmi:id="param10" name="T" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param11" name="k" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param12" name="gear" visibility="private" aggregation="composite" type="Real" /></packagedElement><packagedElement xmi:type="uml:Class" xmi:id="cb5" name="RangeEquation"><ownedRule xmi:type="uml:Constraint" xmi:id="cb5_constraint"><constrainedElement xmi:idref="cb5" /><specification xmi:type="uml:OpaqueExpression" xmi:id="cb5_spec"><body>D = C / E</body><language>English</language></specification></ownedRule><ownedAttribute xmi:type="uml:Port" xmi:id="param13" name="D" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param14" name="C" visibility="private" aggregation="composite" type="Real" /><ownedAttribute xmi:type="uml:Port" xmi:id="param15" name="E" visibility="private" aggregation="composite" type="Real" /></packagedElement></packagedElement></uml:Model><sysml:Block xmi:id="block1_stereotype" base_Class="block1" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop1_stereotype" base_Property="prop1" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop2_stereotype" base_Property="prop2" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop3_stereotype" base_Property="prop3" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop4_stereotype" base_Property="prop4" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop5_stereotype" base_Property="prop5" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop6_stereotype" base_Property="prop6" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop7_stereotype" base_Property="prop7" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop8_stereotype" base_Property="prop8" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop9_stereotype" base_Property="prop9" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop10_stereotype" base_Property="prop10" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop11_stereotype" base_Property="prop11" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop12_stereotype" base_Property="prop12" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop13_stereotype" base_Property="prop13" /><sysml:ConstraintBlock xmi:id="cb1_stereotype" base_Class="cb1" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param1_stereotype" base_Port="param1" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param2_stereotype" base_Port="param2" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param3_stereotype" base_Port="param3" /><sysml:ConstraintBlock xmi:id="cb2_stereotype" base_Class="cb2" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param4_stereotype" base_Port="param4" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param5_stereotype" base_Port="param5" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param6_stereotype" base_Port="param6" /><sysml:ConstraintBlock xmi:id="cb3_stereotype" base_Class="cb3" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param7_stereotype" base_Port="param7" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param8_stereotype" base_Port="param8" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param9_stereotype" base_Port="param9" /><sysml:ConstraintBlock 
xmi:id="cb4_stereotype" base_Class="cb4" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param10_stereotype" base_Port="param10" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param11_stereotype" base_Port="param11" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param12_stereotype" base_Port="param12" /><sysml:ConstraintBlock xmi:id="cb5_stereotype" base_Class="cb5" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param13_stereotype" base_Port="param13" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param14_stereotype" base_Port="param14" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param15_stereotype" base_Port="param15" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty xmi:id="constraint1_stereotype" base_Property="constraint1" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty xmi:id="constraint2_stereotype" base_Property="constraint2" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty xmi:id="constraint3_stereotype" base_Property="constraint3" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty xmi:id="constraint4_stereotype" base_Property="constraint4" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty xmi:id="constraint5_stereotype" base_Property="constraint5" /><sysml:BindingConnector xmi:id="conn1_stereotype" base_Connector="conn1" /><sysml:BindingConnector xmi:id="conn2_stereotype" base_Connector="conn2" /><sysml:BindingConnector xmi:id="conn3_stereotype" base_Connector="conn3" /><sysml:BindingConnector xmi:id="conn4_stereotype" base_Connector="conn4" /><sysml:BindingConnector xmi:id="conn5_stereotype" base_Connector="conn5" /><sysml:BindingConnector xmi:id="conn6_stereotype" base_Connector="conn6" /><sysml:BindingConnector 
xmi:id="conn7_stereotype" base_Connector="conn7" /><sysml:BindingConnector xmi:id="conn8_stereotype" base_Connector="conn8" /><sysml:BindingConnector xmi:id="conn9_stereotype" base_Connector="conn9" /><sysml:BindingConnector xmi:id="conn10_stereotype" base_Connector="conn10" /><sysml:BindingConnector xmi:id="conn11_stereotype" base_Connector="conn11" /><sysml:BindingConnector xmi:id="conn12_stereotype" base_Connector="conn12" /><sysml:BindingConnector xmi:id="conn13_stereotype" base_Connector="conn13" /><sysml:BindingConnector xmi:id="conn14_stereotype" base_Connector="conn14" /><sysml:BindingConnector xmi:id="conn15_stereotype" base_Connector="conn15" /><sysml:NestedConnectorEnd xmi:id="conn1_end2_stereotype" base_ConnectorEnd="conn1_end2" propertyPath="constraint1" /><sysml:NestedConnectorEnd xmi:id="conn2_end2_stereotype" base_ConnectorEnd="conn2_end2" propertyPath="constraint1" /><sysml:NestedConnectorEnd xmi:id="conn3_end2_stereotype" base_ConnectorEnd="conn3_end2" propertyPath="constraint1" /><sysml:NestedConnectorEnd xmi:id="conn4_end2_stereotype" base_ConnectorEnd="conn4_end2" propertyPath="constraint2" /><sysml:NestedConnectorEnd xmi:id="conn5_end2_stereotype" 
base_ConnectorEnd="conn5_end2" propertyPath="constraint2" /><sysml:NestedConnectorEnd xmi:id="conn6_end2_stereotype" base_ConnectorEnd="conn6_end2" propertyPath="constraint2" /><sysml:NestedConnectorEnd xmi:id="conn7_end2_stereotype" base_ConnectorEnd="conn7_end2" propertyPath="constraint3" /><sysml:NestedConnectorEnd xmi:id="conn8_end2_stereotype" base_ConnectorEnd="conn8_end2" propertyPath="constraint3" /><sysml:NestedConnectorEnd xmi:id="conn9_end2_stereotype" base_ConnectorEnd="conn9_end2" propertyPath="constraint3" /><sysml:NestedConnectorEnd xmi:id="conn10_end2_stereotype" base_ConnectorEnd="conn10_end2" propertyPath="constraint4" /><sysml:NestedConnectorEnd xmi:id="conn11_end2_stereotype" base_ConnectorEnd="conn11_end2" propertyPath="constraint4" /><sysml:NestedConnectorEnd 
xmi:id="conn12_end2_stereotype" base_ConnectorEnd="conn12_end2" propertyPath="constraint4" /><sysml:NestedConnectorEnd xmi:id="conn13_end2_stereotype" base_ConnectorEnd="conn13_end2" propertyPath="constraint5" /><sysml:NestedConnectorEnd xmi:id="conn14_end2_stereotype" base_ConnectorEnd="conn14_end2" propertyPath="constraint5" /><sysml:NestedConnectorEnd xmi:id="conn15_end2_stereotype" base_ConnectorEnd="conn15_end2" propertyPath="constraint5" /></xmi:XMI>