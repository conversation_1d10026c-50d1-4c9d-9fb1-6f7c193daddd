<xmi:XMI xmi:version="2.5" xmlns:xmi="http://www.omg.org/XMI" xmlns:uml="http://www.omg.org/spec/UML/20090901" xmlns:sysml="http://www.omg.org/spec/SysML/20131001" xmlns:MD_Customization_for_SysML__additional_stereotypes="http://www.magicdraw.com/spec/Customization/190/SysML"><uml:Model xmi:type="uml:Model" xmi:id="model-cms-unique-id" name="CircularMotionModel"><packagedElement xmi:type="uml:Package" xmi:id="pkg-cms-unique-id" name="ParametricDiagram"><packagedElement xmi:type="uml:Class" xmi:id="block1" name="CircularMotionSystem"><xmi:Extension extender="MagicDraw UML 2021x"><modelExtension><ownedDiagram xmi:type="uml:Diagram" xmi:id="_block1_diagram" name="参数图" visibility="public" context="block1" ownerOfDiagram="block1"><xmi:Extension extender="MagicDraw UML 2021x"><diagramRepresentation><diagram:DiagramRepresentationObject ID="_block1_rep" initialFrameSizeSet="true" 
requiredFeature="com.nomagic.magicdraw.plugins.impl.sysml#SysML;MD_customization_for_SysML.mdzip;UML_Standard_Profile.mdzip" type="SysML Parametric 
Diagram" umlType="Composite Structure Diagram" xmi:id="_block1_xmi" xmi:version="2.0" xmlns:binary="http://www.nomagic.com/ns/cameo/client/binary/1.0" xmlns:diagram="http://www.nomagic.com/ns/magicdraw/core/diagram/1.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><diagramContents contentHash="bfe6e2a79c6e668f74eded668a40a36b357487c5" exporterName="MagicDraw UML" exporterVersion="2021x" xmi:id="_block1_contents" /></diagram:DiagramRepresentationObject></diagramRepresentation></xmi:Extension></ownedDiagram></modelExtension></xmi:Extension><ownedAttribute xmi:type="uml:Property" xmi:id="prop1" name="r" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Property" xmi:id="prop2" name="ω" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Property" xmi:id="prop3" name="v" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Property" xmi:id="prop4" name="F" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Property" xmi:id="prop5" name="m" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Property" xmi:id="constraint1" name="VelocityEquation" aggregation="composite" visibility="private" type="constraintBlock1" /><ownedAttribute xmi:type="uml:Property" xmi:id="constraint2" name="CentrifugalForceEquation" aggregation="composite" visibility="private" type="constraintBlock2" /><ownedConnector xmi:type="uml:Connector" xmi:id="conn1" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn1_end1" role="prop3" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn1_end2" partWithPort="constraint1" role="param1" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn2" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn2_end1" role="prop2" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn2_end2" partWithPort="constraint1" role="param2" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn3" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn3_end1" role="prop1" /><end 
xmi:type="uml:ConnectorEnd" xmi:id="conn3_end2" partWithPort="constraint1" role="param3" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn4" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn4_end1" role="prop4" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn4_end2" partWithPort="constraint2" role="param4" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn5" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn5_end1" role="prop5" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn5_end2" partWithPort="constraint2" role="param5" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn6" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn6_end1" role="prop3" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn6_end2" partWithPort="constraint2" role="param6" /></ownedConnector><ownedConnector xmi:type="uml:Connector" xmi:id="conn7" visibility="public"><end xmi:type="uml:ConnectorEnd" xmi:id="conn7_end1" role="prop1" /><end xmi:type="uml:ConnectorEnd" xmi:id="conn7_end2" partWithPort="constraint2" role="param7" /></ownedConnector></packagedElement><packagedElement xmi:type="uml:Class" xmi:id="constraintBlock1" name="VelocityEquation"><ownedRule xmi:type="uml:Constraint" xmi:id="constraintBlock1_constraint"><constrainedElement xmi:idref="constraintBlock1" /><specification xmi:type="uml:OpaqueExpression" xmi:id="constraintBlock1_spec"><body>v = ω * r</body><language>English</language></specification></ownedRule><ownedAttribute xmi:type="uml:Port" xmi:id="param1" name="v" visibility="private" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Port" xmi:id="param2" name="ω" visibility="private" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Port" xmi:id="param3" name="r" visibility="private" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute></packagedElement><packagedElement xmi:type="uml:Class" xmi:id="constraintBlock2" name="CentrifugalForceEquation"><ownedRule xmi:type="uml:Constraint" xmi:id="constraintBlock2_constraint"><constrainedElement xmi:idref="constraintBlock2" /><specification xmi:type="uml:OpaqueExpression" xmi:id="constraintBlock2_spec"><body>F = m * v^2 / r</body><language>English</language></specification></ownedRule><ownedAttribute xmi:type="uml:Port" xmi:id="param4" name="F" visibility="private" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Port" xmi:id="param5" name="m" visibility="private" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Port" xmi:id="param6" name="v" visibility="private" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute><ownedAttribute xmi:type="uml:Port" xmi:id="param7" name="r" visibility="private" aggregation="composite"><type href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real" /></ownedAttribute></packagedElement></packagedElement></uml:Model><sysml:Block xmi:id="block1_stereotype" base_Class="block1" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop1_stereotype" base_Property="prop1" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop2_stereotype" base_Property="prop2" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop3_stereotype" base_Property="prop3" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop4_stereotype" base_Property="prop4" /><MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop5_stereotype" base_Property="prop5" /><sysml:ConstraintBlock xmi:id="constraintBlock1_stereotype" base_Class="constraintBlock1" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param1_stereotype" base_Port="param1" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param2_stereotype" base_Port="param2" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param3_stereotype" base_Port="param3" /><sysml:ConstraintBlock xmi:id="constraintBlock2_stereotype" base_Class="constraintBlock2" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param4_stereotype" base_Port="param4" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param5_stereotype" base_Port="param5" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param6_stereotype" base_Port="param6" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter xmi:id="param7_stereotype" base_Port="param7" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty xmi:id="constraint1_stereotype" base_Property="constraint1" /><MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty xmi:id="constraint2_stereotype" base_Property="constraint2" /><sysml:BindingConnector xmi:id="conn1_stereotype" base_Connector="conn1" /><sysml:BindingConnector xmi:id="conn2_stereotype" base_Connector="conn2" /><sysml:BindingConnector xmi:id="conn3_stereotype" base_Connector="conn3" /><sysml:BindingConnector xmi:id="conn4_stereotype" base_Connector="conn4" /><sysml:BindingConnector xmi:id="conn5_stereotype" base_Connector="conn5" /><sysml:BindingConnector xmi:id="conn6_stereotype" base_Connector="conn6" /><sysml:BindingConnector xmi:id="conn7_stereotype" base_Connector="conn7" /><sysml:NestedConnectorEnd xmi:id="conn1_end2_stereotype" base_ConnectorEnd="conn1_end2" propertyPath="constraint1" /><sysml:NestedConnectorEnd xmi:id="conn2_end2_stereotype" base_ConnectorEnd="conn2_end2" propertyPath="constraint1" /><sysml:NestedConnectorEnd xmi:id="conn3_end2_stereotype" base_ConnectorEnd="conn3_end2" propertyPath="constraint1" /><sysml:NestedConnectorEnd xmi:id="conn4_end2_stereotype" base_ConnectorEnd="conn4_end2" propertyPath="constraint2" /><sysml:NestedConnectorEnd xmi:id="conn5_end2_stereotype" base_ConnectorEnd="conn5_end2" propertyPath="constraint2" /><sysml:NestedConnectorEnd xmi:id="conn6_end2_stereotype" base_ConnectorEnd="conn6_end2" propertyPath="constraint2" /><sysml:NestedConnectorEnd xmi:id="conn7_end2_stereotype" base_ConnectorEnd="conn7_end2" propertyPath="constraint2" /></xmi:XMI>