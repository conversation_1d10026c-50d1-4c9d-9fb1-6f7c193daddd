<?xml version='1.0' encoding='UTF-8'?>
<xmi:XMI xmlns:uml='http://www.omg.org/spec/UML/20131001' xmlns:xmi='http://www.omg.org/spec/XMI/20131001' xmlns:Validation_Profile='http://www.magicdraw.com/schemas/Validation_Profile.xmi' xmlns:StandardProfile='http://www.omg.org/spec/UML/20131001/StandardProfile' xmlns:DSL_Customization='http://www.magicdraw.com/schemas/DSL_Customization.xmi' xmlns:MagicDraw_Profile='http://www.omg.org/spec/UML/20131001/MagicDrawProfile'>
	<xmi:Documentation>
		<xmi:exporter>MagicDraw UML</xmi:exporter>
		<xmi:exporterVersion>2021x</xmi:exporterVersion>
	</xmi:Documentation>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<req_resource resourceID='1440' resourceName='SysML' resourceValueName='SysML Parametric Diagram'/>
	</xmi:Extension>
	<uml:Model xmi:type='uml:Model' xmi:id='model-unique-id' name='EbikeParameterModel'>
		<packagedElement xmi:type='uml:Package' xmi:id='pkg-unique-id' name='ParametricDiagram'>
			<packagedElement xmi:type='uml:Class' xmi:id='block1' name='EbikeSystem'>
				<xmi:Extension extender='MagicDraw UML 2021x'>
					<modelExtension>
						<ownedDiagram xmi:type='uml:Diagram' xmi:id='_block1_diagram' name='参数图' visibility='public' context='block1' ownerOfDiagram='block1'>
							<xmi:Extension extender='MagicDraw UML 2021x'>
								<diagramRepresentation>
									<diagram:DiagramRepresentationObject ID='_block1_rep' initialFrameSizeSet='true' requiredFeature='com.nomagic.magicdraw.plugins.impl.sysml#SysML;MD_customization_for_SysML.mdzip;UML_Standard_Profile.mdzip' type='SysML Parametric Diagram' umlType='Composite Structure Diagram' xmi:id='_block1_xmi' xmi:version='2.0' xmlns:diagram='http://www.nomagic.com/ns/magicdraw/core/diagram/1.0' xmlns:xmi='http://www.omg.org/XMI'>
										<diagramContents contentHash='bfe6e2a79c6e668f74eded668a40a36b357487c5' exporterName='MagicDraw UML' exporterVersion='2021x' xmi:id='_block1_contents'/>
									</diagram:DiagramRepresentationObject>
								</diagramRepresentation>
							</xmi:Extension>
						</ownedDiagram>
					</modelExtension>
				</xmi:Extension>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop1' name='v' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_810519_97' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_685087_98' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop2' name='d' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_349392_83' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_878844_84' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop3' name='t' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_594530_81' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_695261_82' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop4' name='C' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_984942_23' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_843467_24' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop5' name='P' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_715941_13' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_347133_14' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop6' name='gear' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_956298_35' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_673647_36' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop7' name='k' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_933080_57' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_165584_58' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop8' name='E' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_15802_91' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_10830_92' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='prop9' name='D' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639819_795846_1' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_259631_2' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='constraint1_property' name='SpeedEquation' visibility='private' aggregation='composite' type='constraint1'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_264163_3' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_995083_4' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='constraint2_property' name='RemainingCapacity' visibility='private' aggregation='composite' type='constraint2'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_759716_95' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_786231_96' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='constraint3_property' name='PowerCalculation' visibility='private' aggregation='composite' type='constraint3'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_516255_21' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_747144_22' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='constraint4_property' name='TorqueGearRelation' visibility='private' aggregation='composite' type='constraint4'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639824_809071_103' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639824_739815_104' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Property' xmi:id='constraint5_property' name='RangeDistance' visibility='private' aggregation='composite' type='constraint5'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_498462_27' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_170616_28' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn1' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn1_end1' role='prop3'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_56663_87' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_746069_88' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn1_end2' role='param1'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_61218_41' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_419061_42' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn2' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn2_end1' role='prop2'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_545312_85' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_395384_86' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn2_end2' role='param2'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_381242_31' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_489544_32' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn3' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn3_end1' role='prop1'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_341915_33' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_465394_34' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn3_end2' role='param3'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639824_775401_107' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639824_575782_108' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn4' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn4_end1' role='prop4'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_204282_19' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_420790_20' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn4_end2' role='param4'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_924494_73' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_343688_74' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn5' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn5_end1' role='prop4'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_556430_69' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_975383_70' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn5_end2' role='param9'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_380981_59' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_535710_60' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn6' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn6_end1' role='prop5'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_867302_53' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_905961_54' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn6_end2' role='param6'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_110658_61' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_967565_62' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn7' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn7_end1' role='prop6'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_294984_49' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_969921_50' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn7_end2' role='param11'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_448849_15' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_758334_16' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn8' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn8_end1' role='prop7'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_57460_29' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_748979_30' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn8_end2' role='param12'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639824_738691_105' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639824_317849_106' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn9' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn9_end1' role='prop8'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_319002_75' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_383831_76' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn9_end2' role='param7'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_43754_43' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_984196_44' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn10' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn10_end1' role='prop9'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_75521_37' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_885604_38' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn10_end2' role='param9'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_236922_25' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_497429_26' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn11' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn11_end1' role='prop4'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_54965_77' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_511659_78' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn11_end2' role='param13'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_746126_71' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_419774_72' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn12' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn12_end1' role='prop5'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_664640_63' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_769879_64' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn12_end2' role='param14'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_619146_5' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_604450_6' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
				<ownedConnector xmi:type='uml:Connector' xmi:id='conn13' visibility='public'>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn13_end1' role='prop4'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_798362_9' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_366123_10' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
					<end xmi:type='uml:ConnectorEnd' xmi:id='conn13_end2' role='param15'>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639824_177471_109' value='1'/>
							</modelExtension>
						</xmi:Extension>
						<xmi:Extension extender='MagicDraw UML 2021x'>
							<modelExtension>
								<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639824_594476_110' value='1'/>
							</modelExtension>
						</xmi:Extension>
					</end>
				</ownedConnector>
			</packagedElement>
			<packagedElement xmi:type='uml:Class' xmi:id='constraint1' name='SpeedEquation'>
				<xmi:Extension extender='MagicDraw UML 2021x'>
					<modelExtension/>
				</xmi:Extension>
				<ownedRule xmi:type='uml:Constraint' xmi:id='constraint1_constraint'>
					<constrainedElement xmi:idref='constraint1'/>
					<specification xmi:type='uml:OpaqueExpression' xmi:id='constraint1_spec'>
						<body>v = d / t</body>
						<language>English</language>
					</specification>
				</ownedRule>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param1' name='v' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_584771_39' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_348080_40' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param2' name='d' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_356992_89' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_95841_90' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param3' name='t' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639821_287877_45' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639821_163369_46' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
			</packagedElement>
			<packagedElement xmi:type='uml:Class' xmi:id='constraint2' name='RemainingCapacity'>
				<xmi:Extension extender='MagicDraw UML 2021x'>
					<modelExtension/>
				</xmi:Extension>
				<ownedRule xmi:type='uml:Constraint' xmi:id='constraint2_constraint'>
					<constrainedElement xmi:idref='constraint2'/>
					<specification xmi:type='uml:OpaqueExpression' xmi:id='constraint2_spec'>
						<body>C = C_initial - consumed</body>
						<language>English</language>
					</specification>
				</ownedRule>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param4' name='C' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_803060_99' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_508838_100' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param5' name='C_initial' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_589683_101' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_584026_102' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param6' name='consumed' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_542576_93' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_473823_94' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
			</packagedElement>
			<packagedElement xmi:type='uml:Class' xmi:id='constraint3' name='PowerCalculation'>
				<xmi:Extension extender='MagicDraw UML 2021x'>
					<modelExtension/>
				</xmi:Extension>
				<ownedRule xmi:type='uml:Constraint' xmi:id='constraint3_constraint'>
					<constrainedElement xmi:idref='constraint3'/>
					<specification xmi:type='uml:OpaqueExpression' xmi:id='constraint3_spec'>
						<body>P = F * v</body>
						<language>English</language>
					</specification>
				</ownedRule>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param7' name='P' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_134094_55' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_110191_56' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param8' name='F' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_644306_67' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_137058_68' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param9' name='v' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_117204_51' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_440014_52' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
			</packagedElement>
			<packagedElement xmi:type='uml:Class' xmi:id='constraint4' name='TorqueGearRelation'>
				<xmi:Extension extender='MagicDraw UML 2021x'>
					<modelExtension/>
				</xmi:Extension>
				<ownedRule xmi:type='uml:Constraint' xmi:id='constraint4_constraint'>
					<constrainedElement xmi:idref='constraint4'/>
					<specification xmi:type='uml:OpaqueExpression' xmi:id='constraint4_spec'>
						<body>T = k * gear</body>
						<language>English</language>
					</specification>
				</ownedRule>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param10' name='T' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_148623_11' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_23099_12' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param11' name='gear' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_950872_65' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_744470_66' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param12' name='k' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_694140_7' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_674825_8' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
			</packagedElement>
			<packagedElement xmi:type='uml:Class' xmi:id='constraint5' name='RangeDistance'>
				<xmi:Extension extender='MagicDraw UML 2021x'>
					<modelExtension/>
				</xmi:Extension>
				<ownedRule xmi:type='uml:Constraint' xmi:id='constraint5_constraint'>
					<constrainedElement xmi:idref='constraint5'/>
					<specification xmi:type='uml:OpaqueExpression' xmi:id='constraint5_spec'>
						<body>D = C / E</body>
						<language>English</language>
					</specification>
				</ownedRule>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param13' name='E' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639820_780609_17' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639820_253129_18' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param14' name='C' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639823_621720_79' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639823_656345_80' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
				<ownedAttribute xmi:type='uml:Port' xmi:id='param15' name='D' visibility='private' aggregation='composite'>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<lowerValue xmi:type='uml:LiteralInteger' xmi:id='_2021x_2_320032_1750834639822_699996_47' value='1'/>
						</modelExtension>
					</xmi:Extension>
					<xmi:Extension extender='MagicDraw UML 2021x'>
						<modelExtension>
							<upperValue xmi:type='uml:LiteralUnlimitedNatural' xmi:id='_2021x_2_320032_1750834639822_4865_48' value='1'/>
						</modelExtension>
					</xmi:Extension>
				</ownedAttribute>
			</packagedElement>
		</packagedElement>
	</uml:Model>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<stereotypesHREFS/>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='proxy.local__PROJECT$h877558e9224f114d50dea1f39a1c119_resource_com$dnomagic$dci$dmetamodel$dproject$dsnapshot' type='XML' header='&lt;?xml version=&quot;1.0&quot; encoding=&quot;ASCII&quot;?&gt;'>
<project:Project xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:project="http://www.nomagic.com/ns/cameo/client/project/1.0" xmi:id="_VWTUgU_TEeCNOP_jel_PNg" description="" id="PROJECT-877558e9224f114d50dea1f39a1c119">
  <ownedSections xmi:id="_VWY0EU_TEeCNOP_jel_PNg" name="model" featuredBy="_VWY0EE_TEeCNOP_jel_PNg"/>
  <ownedSections xmi:id="_VabmkU_TEeCNOP_jel_PNg" name="commonprojectoptions" shared="true" featuredBy="_VabmkE_TEeCNOP_jel_PNg"/>
  <ownedSections xmi:id="_VacNoU_TEeCNOP_jel_PNg" name="personalprojectoptions" shared="true" belongsTo="_VacNok_TEeCNOP_jel_PNg" featuredBy="_VabmkE_TEeCNOP_jel_PNg"/>
  <ownedSections xmi:id="_VbQF8E_TEeCNOP_jel_PNg" name="shared_model" shared="true" featuredBy="_VWY0EE_TEeCNOP_jel_PNg">
    <sharePoints xmi:id="_VbhywE_TEeCNOP_jel_PNg" ID="magicdraw_uml_standard_profile_v_0001">
      <object href="PROJECT-877558e9224f114d50dea1f39a1c119?resource=com.nomagic.magicdraw.uml_umodel.shared_umodel#magicdraw_uml_standard_profile_v_0001"/>
      <options xmi:id="_Bnb0gH02EeCAfIowCLYtPA" key="preferredPath" value=""/>
    </sharePoints>
  </ownedSections>
  <ownedSections xmi:id="_VfpdwU_TEeCNOP_jel_PNg" name="commoncodeengineering" featuredBy="_VfpdwE_TEeCNOP_jel_PNg"/>
  <userParts xmi:id="_VacNok_TEeCNOP_jel_PNg" user="_kpyFAVxdEeC_ZNJv4YLpoQ" sections="_VacNoU_TEeCNOP_jel_PNg"/>
  <features xmi:id="_VWY0EE_TEeCNOP_jel_PNg" name="UML Model" namespace="com.nomagic.magicdraw.uml_model" version="17.0" sections="_VWY0EU_TEeCNOP_jel_PNg _VbQF8E_TEeCNOP_jel_PNg" internalVersion="1"/>
  <features xmi:id="_VabmkE_TEeCNOP_jel_PNg" name="Project Options" namespace="com.nomagic.magicdraw.core.project.options" version="1.0" sections="_VabmkU_TEeCNOP_jel_PNg _VacNoU_TEeCNOP_jel_PNg" internalVersion="1"/>
  <features xmi:id="_Vfo2sE_TEeCNOP_jel_PNg" name="Language Properties" namespace="com.nomagic.magicdraw.ce.languageproperties" version="1.0" internalVersion="1"/>
  <features xmi:id="_VfpdwE_TEeCNOP_jel_PNg" name="Code Engineering" namespace="com.nomagic.magicdraw.ce" version="1.0" sections="_VfpdwU_TEeCNOP_jel_PNg" internalVersion="1"/>
  <features xmi:id="_x_Z_IFUEEee8oPCPVi3wjg" name="DMN Code Engineering" namespace="com.nomagic.magicdraw.ce.dmn" version="1.0" internalVersion="1"/>
  <properties xmi:id="_Vbhywk_TEeCNOP_jel_PNg" key="standardProfile" value="true"/>
  <properties xmi:id="_VbiZ0E_TEeCNOP_jel_PNg" key="internalVersion" value="2021x Refresh2"/>
  <properties xmi:id="_srSgUFnLEeCNG86yLj6xYg" key="MODULES_DIRS" value="&lt;install.root>\templates&lt;>&lt;install.root>\profiles&lt;>&lt;project.dir>\&lt;>&lt;install.root>\modelLibraries&lt;>\&lt;install.root>\profiles"/>
  <properties xmi:id="_94jvAH01EeCAfIowCLYtPA" key="exporterDescription" value="ac ed 0 5 73 72 0 2e 63 6f 6d 2e 6e 6f 6d 61 67 69 63 2e 70 65 72 73 69 73 74 65 6e 63 65 2e 58 6d 69 45 78 70 6f 72 74 65 72 44 65 73 63 72 69 70 74 69 6f 6e f5 3e fd c8 e7 3c 3b d7 2 0 5 4c 0 5 6d 4e 61 6d 65 74 0 12 4c 6a 61 76 61 2f 6c 61 6e 67 2f 53 74 72 69 6e 67 3b 4c 0 12 6d 52 65 71 75 69 72 65 64 50 6c 75 67 69 6e 4d 61 70 74 0 f 4c 6a 61 76 61 2f 75 74 69 6c 2f 4d 61 70 3b 4c 0 12 6d 52 65 71 75 69 72 65 64 52 65 73 6f 75 72 63 65 73 74 0 10 4c 6a 61 76 61 2f 75 74 69 6c 2f 4c 69 73 74 3b 4c 0 10 6d 55 4d 4c 4e 61 6d 65 73 70 61 63 65 55 52 49 71 0 7e 0 1 4c 0 8 6d 56 65 72 73 69 6f 6e 71 0 7e 0 1 78 70 74 0 d 4d 61 67 69 63 44 72 61 77 20 55 4d 4c 73 72 0 11 6a 61 76 61 2e 75 74 69 6c 2e 48 61 73 68 4d 61 70 5 7 da c1 c3 16 60 d1 3 0 2 46 0 a 6c 6f 61 64 46 61 63 74 6f 72 49 0 9 74 68 72 65 73 68 6f 6c 64 78 70 3f 40 0 0 0 0 0 0 77 8 0 0 0 10 0 0 0 0 78 73 72 0 13 6a 61 76 61 2e 75 74 69 6c 2e 41 72 72 61 79 4c 69 73 74 78 81 d2 1d 99 c7 61 9d 3 0 1 49 0 4 73 69 7a 65 78 70 0 0 0 0 77 4 0 0 0 0 78 70 74 0 5 32 30 32 31 78 "/>
  <properties xmi:id="_DnjcsMQEEeCVy5mnlxX55w" key="fileVersion" value="1111"/>
  <properties xmi:id="_Doc0kMQEEeCVy5mnlxX55w" key="MODEL_ROOT_HREF" value="local:/PROJECT-877558e9224f114d50dea1f39a1c119?resource=com.nomagic.magicdraw.uml_umodel.model#eee_1045467100313_135436_1"/>
  <properties xmi:id="_BwA7QADBEeK92_v4nw3vUw" key="CI_VERSION" value="V1702_SP1"/>
  <properties xmi:id="_z_AY0MGpEeS9Iojkvg0NZg" key="fileFormatVersion" value="18.1 beta.esi"/>
  <properties xmi:id="_SDFrYW6cEeWDd-3OrajLsA" key="saveID" value="9c6f9862-461c-430c-b9eb-2a8b7d90822c"/>
  <properties xmi:id="_dhIioFHEEeiUvMqDA7b6Sw" key="compatibleVersion" value="2021x"/>
  <properties xmi:id="_6OTmYRgBEem5GakRwxW-Zg" key="requiredFeature" value=""/>
  <properties xmi:id="_t7WuQCjeEeuqEq6eEcii7w" key="pre2021xStructuredExpressionsConverted" value="true"/>
  <projectUsers xmi:id="_kpyFAVxdEeC_ZNJv4YLpoQ" userId="default"/>
</project:Project>
</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='proxy.local__PROJECT$h877558e9224f114d50dea1f39a1c119_resource_com$dnomagic$dmagicdraw$duml_umodel$dshared_umodel$dsnapshot' type='XML' header='&lt;?xml version=&quot;1.0&quot; encoding=&quot;ASCII&quot;?&gt;'>
<uml:Package xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:uml="http://www.nomagic.com/magicdraw/UML/2.5.1.1" xmi:id="magicdraw_uml_standard_profile_v_0001" ID="magicdraw_uml_standard_profile_v_0001" appliedStereotype="_12_1_8f90291_1173963323875_662612_98" name="UML Standard Profile">
  <packagedElement xsi:type="uml:Profile" xmi:id="_be00301_1073394351331_445580_2" ID="_be00301_1073394351331_445580_2" name="MagicDraw Profile">
    <packagedElement xsi:type="uml:Stereotype" xmi:id="_12_1_8f90291_1173963323875_662612_98" ID="_12_1_8f90291_1173963323875_662612_98" name="auxiliaryResource" _extensionEndOfType="_12_1_8f90291_1173963939937_399630_100">
      <generalization xmi:id="_15_0_8f90291_1196866634537_680603_98" ID="_15_0_8f90291_1196866634537_680603_98" general="_9_0_be00301_1108044721245_236588_411"/>
      <ownedAttribute xmi:id="_12_1_8f90291_1173963939937_52316_99" ID="_12_1_8f90291_1173963939937_52316_99" name="base_Element" visibility="private" type="_9_0_62a020a_1105704884807_371561_7741" association="_12_1_8f90291_1173963939937_323574_98"/>
      <ownedAttribute xmi:id="_19_0_8760276_1515156810551_352594_4505" ID="_19_0_8760276_1515156810551_352594_4505" name="treatAsAuxiliaryInOwningProject" visibility="private" type="_12_0EAPbeta_be00301_1157529792739_987548_11"/>
    </packagedElement>
    <packagedElement xsi:type="uml:Extension" xmi:id="_10_0EAPbeta_be00301_1123081772098_323897_274" ID="_10_0EAPbeta_be00301_1123081772098_323897_274" memberEnd="_10_0EAPbeta_be00301_1123081772108_624594_276 _10_0EAPbeta_be00301_1123081772098_411862_275">
      <ownedEnd xsi:type="uml:ExtensionEnd" xmi:id="_10_0EAPbeta_be00301_1123081772108_624594_276" ID="_10_0EAPbeta_be00301_1123081772108_624594_276" name="extension_InvisibleStereotype" visibility="private" type="_9_0_be00301_1108044721245_236588_411" aggregation="composite" association="_10_0EAPbeta_be00301_1123081772098_323897_274"/>
    </packagedElement>
    <packagedElement xsi:type="uml:Extension" xmi:id="_12_1_8f90291_1173963939937_323574_98" ID="_12_1_8f90291_1173963939937_323574_98" memberEnd="_12_1_8f90291_1173963939937_399630_100 _12_1_8f90291_1173963939937_52316_99">
      <ownedEnd xsi:type="uml:ExtensionEnd" xmi:id="_12_1_8f90291_1173963939937_399630_100" ID="_12_1_8f90291_1173963939937_399630_100" name="extension_auxiliaryResource" visibility="private" type="_12_1_8f90291_1173963323875_662612_98" aggregation="composite" association="_12_1_8f90291_1173963939937_323574_98"/>
    </packagedElement>
    <packagedElement xsi:type="uml:Stereotype" xmi:id="_9_0_be00301_1108044721245_236588_411" ID="_9_0_be00301_1108044721245_236588_411" name="InvisibleStereotype" _extensionEndOfType="_10_0EAPbeta_be00301_1123081772108_624594_276">
      <ownedAttribute xmi:id="_10_0EAPbeta_be00301_1123081772098_411862_275" ID="_10_0EAPbeta_be00301_1123081772098_411862_275" name="base_Element" visibility="private" type="_9_0_62a020a_1105704884807_371561_7741" association="_10_0EAPbeta_be00301_1123081772098_323897_274"/>
    </packagedElement>
  </packagedElement>
  <packagedElement xsi:type="uml:Model" xmi:id="_9_0_be00301_1108053761194_467635_11463" ID="_9_0_be00301_1108053761194_467635_11463" appliedStereotype="magicdraw_1046861421236_601240_36" name="UML2 Metamodel">
    <packagedElement xsi:type="uml:Class" xmi:id="_9_0_62a020a_1105704884807_371561_7741" ID="_9_0_62a020a_1105704884807_371561_7741" name="Element"/>
    <packagedElement xsi:type="uml:Class" xmi:id="_9_0_62a020a_1105704920340_825592_9329" ID="_9_0_62a020a_1105704920340_825592_9329" name="Model"/>
    <packagedElement xsi:type="uml:Package" xmi:id="_12_0EAPbeta_be00301_1157529392394_202602_1" ID="_12_0EAPbeta_be00301_1157529392394_202602_1" name="PrimitiveTypes">
      <packagedElement xsi:type="uml:PrimitiveType" xmi:id="_12_0EAPbeta_be00301_1157529792739_987548_11" ID="_12_0EAPbeta_be00301_1157529792739_987548_11" name="Boolean"/>
    </packagedElement>
  </packagedElement>
  <packagedElement xsi:type="uml:Profile" xmi:id="_9_0_be00301_1108050582343_527400_10847" ID="_9_0_be00301_1108050582343_527400_10847" name="StandardProfile">
    <packagedElement xsi:type="uml:Stereotype" xmi:id="magicdraw_1046861421236_601240_36" ID="magicdraw_1046861421236_601240_36" name="Metamodel" _extensionEndOfType="_10_0EAPbeta_be00301_1123081771136_580423_99">
      <ownedAttribute xmi:id="_10_0EAPbeta_be00301_1123081771136_271406_98" ID="_10_0EAPbeta_be00301_1123081771136_271406_98" name="base_Model" visibility="private" type="_9_0_62a020a_1105704920340_825592_9329" association="_10_0EAPbeta_be00301_1123081771136_824883_97"/>
    </packagedElement>
    <packagedElement xsi:type="uml:Extension" xmi:id="_10_0EAPbeta_be00301_1123081771136_824883_97" ID="_10_0EAPbeta_be00301_1123081771136_824883_97" memberEnd="_10_0EAPbeta_be00301_1123081771136_580423_99 _10_0EAPbeta_be00301_1123081771136_271406_98">
      <ownedEnd xsi:type="uml:ExtensionEnd" xmi:id="_10_0EAPbeta_be00301_1123081771136_580423_99" ID="_10_0EAPbeta_be00301_1123081771136_580423_99" name="extension_metamodel" visibility="private" type="magicdraw_1046861421236_601240_36" aggregation="composite" association="_10_0EAPbeta_be00301_1123081771136_824883_97"/>
    </packagedElement>
  </packagedElement>
</uml:Package>
</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='Binaries.properties' type='BINARY'>H4sIAAAAAAAAAFPm5VIOT01R8CrNUzAyVTA0sTI1tzI2VHAODlEwMjAy5eUCACAtjpYiAAAA</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='PROJECT-119c989f537fe98c1fef755a7db21a' type='BINARY'>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='com.nomagic.ci.persistence.local.proxy.privatedependencylist' type='XML' header='&lt;?xml version=&quot;1.0&quot; encoding=&quot;ASCII&quot;?&gt;'>
<snapshot:PrivateDependenciesContainer xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:snapshot="http://www.nomagic.com/ns/cameo/client/snapshot/1.0" xmi:id="_qgqu4FGREfC_6slgGCT39w">
  <referencedResources xmi:id="_qgrV8FGREfC_6slgGCT39w" originalResourceURI="local:/PROJECT-877558e9224f114d50dea1f39a1c119?resource=com.nomagic.magicdraw.uml_umodel.shared_umodel">
    <referencedObjects>magicdraw_uml_standard_profile_v_0001</referencedObjects>
  </referencedResources>
</snapshot:PrivateDependenciesContainer>
</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='personal-Binaries.properties' type='BINARY'>H4sIAAAAAAAAAFPm5VIOT01R8CrNUzAyVTA0sTI1tzI2VHAODlEwMjAy5eUCACAtjpYiAAAA</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='com.nomagic.magicdraw.core.project.options.commonprojectoptions' type='XML' header='&lt;?xml version=&quot;1.0&quot; encoding=&quot;ASCII&quot;?&gt;'>
<project.options:CommonProjectOptions xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:project.options="http://www.nomagic.com/ns/magicdraw/core/project/options/1.0" xmi:id="_oy1UElGREfC_6slgGCT39w" optionsString="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" modelElementStyleString="UEsDBBQACAgIAAAAIQAAAAAAAAAAAAAAAAAAAAkAVVQFAAEAAAAAdY5BC4IwGEDP+it226msW8RUJOdJK9gMOsnQzxhsU9wU/fdpQbdO7/LgPRLPWqEJBis7E+Lj/oARmLprpHmFuOTZ7oTjyCf3oethcMtDWum6Ialr6FdGvkd0QxVoMA7BlxclrA0xk7pXwNyiAK+eR4zQEDH+zGlV3FKaVzSnBb3yKqVZUuackeCjbG4DrRiVQ7OW50moEULcCmUBB1sy+DXXteDf2xtQSwcIv2d6F6cAAADcAAAAUEsBAhQAFAAICAgAAAAhAL9nehenAAAA3AAAAAAACQAAAAAAAAAAAAAAAAAAAFVUBQABAAAAAFBLBQYAAAAAAQABADcAAADeAAAAAAA=" symbolStylesString="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"/>
</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='com.nomagic.ci.proxy.snapshot.descriptor.descriptors' type='XML' header='&lt;?xml version=&quot;1.0&quot; encoding=&quot;ASCII&quot;?&gt;'>
<snapshot:SnapshotInfoContainer xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:snapshot="http://www.nomagic.com/ns/cameo/client/snapshot/1.0" xmi:id="_qg1uAFGREfC_6slgGCT39w">
  <snaphotDescriptors xmi:id="_qg2VEFGREfC_6slgGCT39w" originalResourceURI="local:/PROJECT-877558e9224f114d50dea1f39a1c119?resource=com.nomagic.magicdraw.uml_umodel.shared_umodel">
    <sharePointObjects>magicdraw_uml_standard_profile_v_0001</sharePointObjects>
  </snaphotDescriptors>
</snapshot:SnapshotInfoContainer>
</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='personal-com.nomagic.magicdraw.core.project.options.personalprojectoptions' type='XML' header='&lt;?xml version=&quot;1.0&quot; encoding=&quot;ASCII&quot;?&gt;'>
<project.options:UserProjectOptions xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:project.options="http://www.nomagic.com/ns/magicdraw/core/project/options/1.0" xmi:id="_oy17IVGREfC_6slgGCT39w" optionsString="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" diagramViewStyleString="UEsDBBQACAgIAAAAIQAAAAAAAAAAAAAAAAAAAAkAVVQFAAEAAAAAdY5PC4IwHIbP+im87VSrW8RUpCyEInEWdJKhP2OwP7JN0W+fFnTr9BzeB56XxKMUwQDGcq1CtF1vUACq1g1XrxDdy9Nqh+LIJ7nRHRg3PbjlTpukrqGbGfkekU0qQIJyAXx5EMzaEFEuOwHUTQLQ7HlEMQkRLZ+XtDpmyblIrlVe3PK0KLOUEvyZF6+BlvXCBaPk+4GJHkLUMmEB4SWHf735Fv736w1QSwcInLS8UKcAAADYAAAAUEsBAhQAFAAICAgAAAAhAJy0vFCnAAAA2AAAAAAACQAAAAAAAAAAAAAAAAAAAFVUBQABAAAAAFBLBQYAAAAAAQABADcAAADeAAAAAAA="/>
</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='com.nomagic.ci.metamodel.project' type='XML' header='&lt;?xml version=&quot;1.0&quot; encoding=&quot;ASCII&quot;?&gt;'>
<project:Project xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:project="http://www.nomagic.com/ns/cameo/client/project/1.0" xmi:id="_ou1k4FGREfC_6slgGCT39w" description="" id="PROJECT-119c989f537fe98c1fef755a7db21a">
  <ownedSections xmi:id="_ou4BIFGREfC_6slgGCT39w" name="model" featuredBy="_ou3aEFGREfC_6slgGCT39w"/>
  <ownedSections xmi:id="_oy1UEVGREfC_6slgGCT39w" name="commonprojectoptions" shared="true" featuredBy="_oy1UEFGREfC_6slgGCT39w"/>
  <ownedSections xmi:id="_oy1UFVGREfC_6slgGCT39w" name="personalprojectoptions" shared="true" belongsTo="_oy1UFFGREfC_6slgGCT39w" featuredBy="_oy1UEFGREfC_6slgGCT39w"/>
  <userParts xmi:id="_oy1UFFGREfC_6slgGCT39w" user="_oy1UE1GREfC_6slgGCT39w" sections="_oy1UFVGREfC_6slgGCT39w"/>
  <projectUsages xmi:id="_ovOmcVGREfC_6slgGCT39w" usedProjectURI="file:/D:/Develop/DevelopTools/Cameo%20Systems%20Modeler%20Demo/profiles/UML_Standard_Profile.mdzip" readonly="true" loadedAutomatically="true" withPrivateDependencies="true">
    <usedProject href="PROJECT-877558e9224f114d50dea1f39a1c119?resource=com.nomagic.ci.metamodel.project#_VWTUgU_TEeCNOP_jel_PNg"/>
    <mountPoints xmi:id="_o1LdcFGREfC_6slgGCT39w" sharePointID="magicdraw_uml_standard_profile_v_0001" containmentFeatureID="63" featureName="UML Model" containmentIndex="-1" containmentFeatureName="packagedElement">
      <mountedPoint href="PROJECT-877558e9224f114d50dea1f39a1c119?resource=com.nomagic.magicdraw.uml_umodel.shared_umodel#magicdraw_uml_standard_profile_v_0001"/>
      <mountedOn href="PROJECT-119c989f537fe98c1fef755a7db21a?resource=com.nomagic.magicdraw.uml_umodel.model#model-unique-id"/>
      <options xmi:id="_qfGooFGREfC_6slgGCT39w" key="preferredPath" value="::"/>
      <mountedSharePoint href="PROJECT-877558e9224f114d50dea1f39a1c119?resource=com.nomagic.ci.metamodel.project#_VbhywE_TEeCNOP_jel_PNg"/>
    </mountPoints>
    <properties xmi:id="_ovOmclGREfC_6slgGCT39w" key="LOCAL_PROJECT_ID" value="PROJECT-877558e9224f114d50dea1f39a1c119"/>
    <properties xmi:id="_qfAiAFGREfC_6slgGCT39w" key="usedVersion" value="2021x Refresh2"/>
  </projectUsages>
  <features xmi:id="_ou3aEFGREfC_6slgGCT39w" name="UML Model" namespace="com.nomagic.magicdraw.uml_model" version="17.0" sections="_ou4BIFGREfC_6slgGCT39w" internalVersion="1"/>
  <features xmi:id="_oy1UEFGREfC_6slgGCT39w" name="Project Options" namespace="com.nomagic.magicdraw.core.project.options" version="1.0" sections="_oy1UEVGREfC_6slgGCT39w _oy1UFVGREfC_6slgGCT39w" internalVersion="1"/>
  <properties xmi:id="_ovAkAFGREfC_6slgGCT39w" key="standardProfile" value="false"/>
  <properties xmi:id="_ovAkAVGREfC_6slgGCT39w" key="internalVersion" value="-1"/>
  <properties xmi:id="_qfAiAVGREfC_6slgGCT39w" key="exporterDescription" value="ac ed 0 5 73 72 0 2e 63 6f 6d 2e 6e 6f 6d 61 67 69 63 2e 70 65 72 73 69 73 74 65 6e 63 65 2e 58 6d 69 45 78 70 6f 72 74 65 72 44 65 73 63 72 69 70 74 69 6f 6e f5 3e fd c8 e7 3c 3b d7 2 0 5 4c 0 5 6d 4e 61 6d 65 74 0 12 4c 6a 61 76 61 2f 6c 61 6e 67 2f 53 74 72 69 6e 67 3b 4c 0 12 6d 52 65 71 75 69 72 65 64 50 6c 75 67 69 6e 4d 61 70 74 0 f 4c 6a 61 76 61 2f 75 74 69 6c 2f 4d 61 70 3b 4c 0 12 6d 52 65 71 75 69 72 65 64 52 65 73 6f 75 72 63 65 73 74 0 10 4c 6a 61 76 61 2f 75 74 69 6c 2f 4c 69 73 74 3b 4c 0 10 6d 55 4d 4c 4e 61 6d 65 73 70 61 63 65 55 52 49 71 0 7e 0 1 4c 0 8 6d 56 65 72 73 69 6f 6e 71 0 7e 0 1 78 70 74 0 d 4d 61 67 69 63 44 72 61 77 20 55 4d 4c 73 72 0 11 6a 61 76 61 2e 75 74 69 6c 2e 48 61 73 68 4d 61 70 5 7 da c1 c3 16 60 d1 3 0 2 46 0 a 6c 6f 61 64 46 61 63 74 6f 72 49 0 9 74 68 72 65 73 68 6f 6c 64 78 70 3f 40 0 0 0 0 0 0 77 8 0 0 0 10 0 0 0 0 78 73 72 0 13 6a 61 76 61 2e 75 74 69 6c 2e 41 72 72 61 79 4c 69 73 74 78 81 d2 1d 99 c7 61 9d 3 0 1 49 0 4 73 69 7a 65 78 70 0 0 0 1 77 4 0 0 0 1 73 72 0 32 63 6f 6d 2e 6e 6f 6d 61 67 69 63 2e 70 65 72 73 69 73 74 65 6e 63 65 2e 52 65 71 75 69 72 65 64 52 65 73 6f 75 72 63 65 44 65 73 63 72 69 70 74 6f 72 da cf 46 1f 32 8 f0 c5 2 0 3 49 0 3 6d 49 64 4c 0 5 6d 4e 61 6d 65 71 0 7e 0 1 4c 0 6 6d 56 61 6c 75 65 71 0 7e 0 1 78 70 0 0 5 a0 74 0 5 53 79 73 4d 4c 74 0 18 53 79 73 4d 4c 20 50 61 72 61 6d 65 74 72 69 63 20 44 69 61 67 72 61 6d 78 70 74 0 5 32 30 32 31 78 "/>
  <properties xmi:id="_qfC-QFGREfC_6slgGCT39w" key="fileVersion" value="0"/>
  <properties xmi:id="_qfC-QVGREfC_6slgGCT39w" key="saveID" value="63c337db-f3ac-40c6-bf6b-08405cf993be"/>
  <properties xmi:id="_qfEMYFGREfC_6slgGCT39w" key="MODEL_ROOT_HREF" value="local:/PROJECT-119c989f537fe98c1fef755a7db21a?resource=com.nomagic.magicdraw.uml_umodel.model#model-unique-id"/>
  <properties xmi:id="_qfqCQFGREfC_6slgGCT39w" key="MODULES_DIRS" value="&lt;project.dir>&lt;>&lt;install.root>\profiles&lt;>&lt;install.root>\modelLibraries"/>
  <properties xmi:id="_qhKeIFGREfC_6slgGCT39w" key="CI_VERSION" value="V1702_SP1"/>
  <projectUsers xmi:id="_oy1UE1GREfC_6slgGCT39w" userId="default"/>
</project:Project>
</filePart>
	</xmi:Extension>
	<xmi:Extension extender='MagicDraw UML 2021x'>
		<filePart name='Records.properties' type='BINARY'>H4sIAAAAAAAAAL1TQU+DMBS+k/AfSMZ1zcpGgCVcXLzsotElHknXPlwNtKQtKv/eyticYzMsTi9N+/q9973va99oIcuKGL7mBTeNB8KoxnVGT8C8ZS28IPTwbB5G8yn2Fo8rL5gEoetQWSIhS/LMKWpXpsgbqssiKyWDArVrOgjlOpWS7w0qJCVFlt0/3C1vFyt/E0dRGMaQBMEsx3jGwgkDgvNpQjDFOMkUaFkrCpll8VlH4zPKfVaCIW1tn9naL0CNz7Qgld5Ik/4r25XF7U302aeLdUerN0QB+zr+idTLuF3nhguiOGhku6hAGbtNT8Rcp2tsbLlpEid5OI1ySGKKc8htpyRi6wCTdBjs+9ekHFkazbUBQWHrBNq6Uin+SgwwqEAwe9sUFpX+Jtm+tYVLQYrxKe0/XZ4bKCoVoO5TIVkZLoW2wbKUoot2wTOjNji/b1urc/eaiIGmildGqoOt7tk1JOnApgua3uUcyb5eqZ4D+7HeFThW2wO4zgfW6UPWTAUAAA==</filePart>
	</xmi:Extension>
</xmi:XMI>