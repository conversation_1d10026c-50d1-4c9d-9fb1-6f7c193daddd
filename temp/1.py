import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
import traceback
from collections import defaultdict

class SysMLGenerator:
    """
    一个统一的、详尽的生成器，用于将包含多种图表信息的、扁平化的JSON数据
    转换为一个完整的、结构正确的SysML XMI文件。
    该类深度整合了针对需求图、块定义/内部块图、状态机图、序列图、
    活动图、用例图和参数图的全部转换逻辑。
    """
    NAMESPACES = {
        "xmi": "http://www.omg.org/spec/XMI/20131001",
        "uml": "http://www.omg.org/spec/UML/20131001",
        "sysml": "http://www.omg.org/spec/SysML/20181001/SysML",
        "StandardProfile": "http://www.omg.org/spec/UML/20131001/StandardProfile",
        "MagicDraw_Profile": "http://www.omg.org/spec/UML/20131001/MagicDrawProfile",
        "MD_Customization_for_SysML__additional_stereotypes": "http://www.magicdraw.com/spec/Customization/180/SysML",
        "DSL_Customization": "http://www.magicdraw.com/schemas/DSL_Customization.xmi",
        "diagram": "http://www.nomagic.com/ns/magicdraw/core/diagram/1.0"
    }
    PRIMITIVE_TYPE_MAP = {
        "Real": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real",
        "Integer": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Integer",
        "String": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.String",
        "Boolean": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Boolean",
        "VerdictKind": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"
    }

    def __init__(self, json_data_str):
        self.json_data = json.loads(json_data_str)
        self.elements_by_id = {}
        self.children_by_parent = defaultdict(list)
        self.xml_elements = {}
        self.stereotypes_to_apply = defaultdict(list)
        self.lifelines_to_update = defaultdict(list)

        self.nested_connector_ends_to_apply = []
        ### 关键修正点: 为需要后期处理的元素创建专门列表 ###
        self.associations_to_apply = []
        self.diagrams_to_apply = []

        self.model_id = None
        self.model_name = None
        self.xmi_root = None
    def _generate_id(self, base_id, suffix):
        clean_base = str(base_id).replace("-", "_")
        clean_suffix = str(suffix).replace("-", "_").replace(":", "_")
        return f"_{clean_base}_{clean_suffix}"

    def _preprocess_data(self):
        print("--- 阶段 1: 预处理JSON数据 ---")
        if "elements" not in self.json_data:
            raise ValueError("错误: JSON数据必须包含 'elements' 键。")

        self.elements_by_id = {elem["id"]: elem for elem in self.json_data["elements"]}

        model_value = self.json_data.get("model")
        model_data = {}
        if isinstance(model_value, list):
            if model_value: model_data = model_value[0]
        elif isinstance(model_value, dict):
            model_data = model_value

        self.model_id = model_data.get("id", "default-model-id")
        self.model_name = model_data.get("name", "DefaultModel")
        if self.model_id not in self.elements_by_id:
            self.elements_by_id[self.model_id] = model_data

        for elem_id, elem_data in self.elements_by_id.items():
            parent_id = elem_data.get("parentId")
            if parent_id: self.children_by_parent[parent_id].append(elem_id)

            elem_type = elem_data.get("type")
            stereotype_category = self._get_stereotype_category(elem_type, elem_data)
            if stereotype_category:
                self.stereotypes_to_apply[stereotype_category].append(elem_data)
                ### 关键修正点: 在预处理阶段识别出需要后处理的关联和图表 ###
            if elem_type == "Association": self.associations_to_apply.append(elem_data)
            if elem_type == "Diagram": self.diagrams_to_apply.append(elem_data)

        print(f"数据预处理完成。共找到 {len(self.elements_by_id)} 个元素。")

    def _get_stereotype_category(self, elem_type, elem_data):
        category_map = {
            "Requirement": "Requirement", "Block": "Block", "TestCase": "TestCase",
            "ValueType": "ValueType", "Unit": "Unit", "ConstraintBlock": "ConstraintBlock",
            "InterfaceBlock": "InterfaceBlock", "FullPort": "FullPort", "ProxyPort": "ProxyPort",
            "ConstraintParameter": "ConstraintParameter", "ActivityPartition": "ActivityPartition",
            "BindingConnector": "BindingConnector", "DeriveReqt": "Abstraction",
            "Satisfy": "Abstraction", "Verify": "Abstraction"
        }
        if elem_type in category_map:
            return category_map[elem_type]
        if elem_type == "Property" and "propertyKind" in elem_data:
            return "Property"
        return None

    def generate_xmi(self):
        """执行从JSON到XMI的完整转换流程。"""
        try:
            self._preprocess_data()
            self._create_base_structure()
            self._post_process_structure()
            self._apply_stereotypes()
            return self._prettify_xml()
        except Exception:
            print("在生成XMI过程中发生严重错误。")
            traceback.print_exc()
            return None

    def _create_base_structure(self):
        """阶段2: 创建XMI根、模型并递归创建所有元素。"""
        print("--- 阶段 2: 创建完整的UML结构 ---")
        self.xmi_root = ET.Element("xmi:XMI", attrib={"xmi:version": "2.5"})
        for prefix, uri in self.NAMESPACES.items():
            self.xmi_root.set(f"xmlns:{prefix}", uri)

        model_attrs = {"xmi:type": "uml:Model", "xmi:id": self.model_id, "name": self.model_name}
        model_xml = ET.SubElement(self.xmi_root, "uml:Model", attrib=model_attrs)
        self.xml_elements[self.model_id] = model_xml

        self._create_element_recursive(self.model_id, model_xml)

    def _create_element_recursive(self, parent_id, parent_xml_node):
        """递归地为给定的父节点创建所有子元素。"""
        for child_id in sorted(self.children_by_parent.get(parent_id, [])):
            if child_id in self.xml_elements: continue
            child_data = self.elements_by_id.get(child_id)
            if not child_data: continue

            elem_type = child_data.get("type")

            # --- BDD/IBD-FIX: 在主创建阶段跳过关联，留给后处理 ---
            if elem_type == "Association": continue

            elem_name = child_data.get("name")
            attrs = {"xmi:id": child_id}
            if elem_name: attrs["name"] = elem_name

            tag_name, xmi_type = self._determine_tag_and_type(child_data)
            if not tag_name: continue

            attrs["xmi:type"] = xmi_type

            current_parent_xml = parent_xml_node
            if elem_type in ["Include", "Extend", "Generalization"]:
                source_id = child_data.get("sourceId")
                if source_id in self.xml_elements:
                    current_parent_xml = self.xml_elements[source_id]
                else:
                    # This case should be rare with the current single-pass recursive approach
                    print(f"警告：关系 '{child_id}' 的源节点 '{source_id}' 在创建时未找到。")
                    continue

            xml_elem = ET.SubElement(current_parent_xml, tag_name, attrs)
            self.xml_elements[child_id] = xml_elem
            self._populate_element_details(xml_elem, child_data)

            # --- ACTIVITY-DIAGRAM-FIX: 如果是Activity，立即处理其内部内容 ---
            if elem_type == "Activity":
                self._populate_activity_content(xml_elem, child_data)
            else:
                self._create_element_recursive(child_id, xml_elem)
    def _populate_activity_content(self, activity_xml_elem, activity_data):
        """专门用于填充Activity内部所有元素的函数。"""
        activity_id = activity_data["id"]

        # 1. 查找此活动的所有子元素
        node_ids = [cid for cid in self.children_by_parent.get(activity_id, []) if self.elements_by_id[cid]['type'] not in ['ControlFlow', 'ObjectFlow', 'ActivityPartition']]
        edge_ids = [cid for cid in self.children_by_parent.get(activity_id, []) if self.elements_by_id[cid]['type'] in ['ControlFlow', 'ObjectFlow']]
        group_ids = [cid for cid in self.children_by_parent.get(activity_id, []) if self.elements_by_id[cid]['type'] == 'ActivityPartition']

        # 2. 按顺序创建：分区(group)、边(edge)、节点(node)
        for group_id in group_ids:
            self._create_activity_element(activity_xml_elem, group_id)
        for edge_id in edge_ids:
            self._create_activity_element(activity_xml_elem, edge_id)
        for node_id in node_ids:
            self._create_activity_element(activity_xml_elem, node_id)

    def _create_activity_element(self, parent_xml_element, elem_id):
        """为活动创建单个内部元素（节点、边、分区等）。"""
        if elem_id in self.xml_elements: return # 避免重复创建

        elem_data = self.elements_by_id[elem_id]
        tag_name, xmi_type = self._determine_tag_and_type(elem_data)
        if not tag_name: return

        attrs = {"xmi:id": elem_id, "xmi:type": xmi_type}
        if "name" in elem_data: attrs["name"] = elem_data["name"]

        xml_elem = ET.SubElement(parent_xml_element, tag_name, attrs)
        self.xml_elements[elem_id] = xml_elem

        # 填充特定属性
        elem_type = elem_data["type"]
        if elem_type in ["ControlFlow", "ObjectFlow"]:
            if "sourceId" in elem_data: xml_elem.set("source", elem_data["sourceId"])
            if "targetId" in elem_data: xml_elem.set("target", elem_data["targetId"])
            if "guard" in elem_data:
                guard = ET.SubElement(xml_elem, "guard", {"xmi:type": "uml:OpaqueExpression", "xmi:id": self._generate_id(elem_id, "guard")})
                ET.SubElement(guard, "body").text = elem_data["guard"]
        elif elem_type == "CallBehaviorAction":
            if "behavior" in elem_data: xml_elem.set("behavior", elem_data["behavior"])
            # 创建引脚
            for pin_id in self.children_by_parent.get(elem_id, []):
                self._create_activity_element(xml_elem, pin_id)
        elif elem_type == "ActivityPartition":
            if "representsId" in elem_data: xml_elem.set("represents", elem_data["representsId"])
            # 添加分区内的节点和边引用
            for node_edge_id in elem_data.get("nodeIds", []):
                node_edge_type = self.elements_by_id[node_edge_id].get("type")
                ref_tag = "edge" if node_edge_type in ["ControlFlow", "ObjectFlow"] else "node"
                ET.SubElement(xml_elem, ref_tag, {"xmi:idref": node_edge_id})
        elif elem_type in ["InputPin", "OutputPin", "CentralBufferNode", "ActivityParameterNode"]:
            if "typeId" in elem_data: self._add_type_ref_to_element(xml_elem, elem_data["typeId"])
    def _determine_tag_and_type(self, elem_data):
        elem_type = elem_data.get("type")
        parent_type = self.elements_by_id.get(elem_data.get("parentId"), {}).get("type")
        tag, xtype = 'packagedElement', f"uml:{elem_type}"


        if elem_type == "Interaction":
            tag = 'ownedBehavior' if parent_type in ["Block", "Class"] else 'packagedElement'
        elif elem_type == "Lifeline": tag = 'lifeline'
        elif elem_type == "Message": tag = 'message'
        elif elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification", "CombinedFragment"]:
            tag = 'fragment'
        elif elem_type == "InteractionOperand": tag = 'operand'
        elif elem_type == "InteractionConstraint": tag = 'guard'
        elif elem_type == "StateMachine":
            tag = 'ownedBehavior' if parent_type == "Block" else 'packagedElement'
        elif elem_type == "Region": tag = 'region'
        elif elem_type == "State": tag = 'subvertex'
        elif elem_type == "FinalState": tag = 'subvertex'; xtype = "uml:FinalState"
        elif elem_type == "Pseudostate":
            tag = 'connectionPoint' if parent_type == "State" else 'subvertex'
        elif elem_type == "Transition": tag = 'transition'
        elif elem_type == "Include": tag = 'include'; xtype = "uml:Include"
        elif elem_type == "Extend": tag = 'extend'; xtype = "uml:Extend"
        elif elem_type == "Generalization": tag = 'generalization'; xtype = "uml:Generalization"
        elif elem_type == "Association": tag = 'packagedElement'; xtype = "uml:Association"
        elif elem_type in ["Block", "Requirement", "ConstraintBlock", "InterfaceBlock", "Actor"]: xtype = "uml:Class" if elem_type != "Actor" else "uml:Actor"
        elif elem_type == "UseCase": xtype = "uml:UseCase"
        elif elem_type == "TestCase": xtype = "uml:Activity"
        elif elem_type == "ValueType": xtype = "uml:DataType"
        elif elem_type == "Unit": xtype = "uml:InstanceSpecification"
        elif elem_type == "Enumeration": xtype = "uml:Enumeration"
        elif elem_type == "Property": tag = 'ownedAttribute'; xtype = "uml:Property"
        elif elem_type in ["FullPort", "ProxyPort", "ConstraintParameter", "Port"]: tag = 'ownedAttribute'; xtype = "uml:Port"
        elif elem_type == "Operation": tag = 'ownedOperation'
        elif elem_type == "Reception": tag = 'ownedReception'
        elif elem_type == "Parameter": tag = 'ownedParameter'
        elif elem_type == "EnumerationLiteral": tag = 'ownedLiteral'
        elif elem_type in ["StateMachine", "Interaction", "Activity"]:
            tag = 'ownedBehavior' if parent_type == "Block" else 'packagedElement'
        elif elem_type == "Region": tag = 'region'
        elif elem_type in ["State", "FinalState"]: tag = 'subvertex'
        elif elem_type == "Pseudostate": tag = 'connectionPoint' if parent_type == "State" else 'subvertex'
        elif elem_type == "Transition": tag = 'transition'
        elif elem_type == "Lifeline": tag = 'lifeline'
        elif elem_type == "Message": tag = 'message'
        elif elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification", "CombinedFragment"]: tag = 'fragment'
        elif elem_type == "InteractionOperand": tag = 'operand'
        elif elem_type in ["InitialNode", "ActivityFinalNode", "FlowFinalNode", "DecisionNode", "MergeNode", "ForkNode", "JoinNode", "CallBehaviorAction", "CentralBufferNode", "ActivityParameterNode"]: tag = 'node'
        elif elem_type in ["InputPin", "OutputPin"]: tag = 'argument' if elem_type == 'InputPin' else 'result'
        elif elem_type in ["ControlFlow", "ObjectFlow"]: tag = 'edge'
        elif elem_type == "ActivityPartition": tag = 'group'
        elif elem_type in ["DeriveReqt", "Satisfy", "Verify"]: xtype = "uml:Abstraction"
        elif elem_type in ["AssemblyConnector", "BindingConnector"]: tag = 'ownedConnector'; xtype = "uml:Connector"
        elif elem_type == "Diagram": return None, None

        return tag, xtype

    def _populate_element_details(self, xml_elem, elem_data):
        elem_id, elem_type = elem_data.get("id"), elem_data.get("type")

        if elem_type == "Property":
            xml_elem.set("aggregation", elem_data.get("aggregation", "none"))
            # 关联属性将在后处理阶段设置
            self._add_multiplicity(elem_data, xml_elem)
        elif elem_type in ["FullPort", "ProxyPort"]:
            xml_elem.set("aggregation", "composite") # Ports are always composite
            if elem_data.get("isBehavior"): xml_elem.set("isBehavior", str(elem_data["isBehavior"]).lower())
            self._add_multiplicity(elem_data, xml_elem)
        elif elem_type == "Operation":
            for param_data in elem_data.get("parameters", []):
                param_attrs = {"xmi:type": "uml:Parameter", "xmi:id": param_data["id"], "name": param_data["name"], "direction": param_data.get("direction", "in")}
                param_xml = ET.SubElement(xml_elem, "ownedParameter", param_attrs)
                self._add_type_ref_to_element(param_xml, param_data.get("typeId"))
        elif elem_type == "Reception":
            if "signalId" in elem_data: xml_elem.set("signal", elem_data["signalId"])
        elif elem_type in ["AssemblyConnector", "BindingConnector"]:
            for end_key in ["end1", "end2"]:
                end_data = elem_data.get(end_key)
                if not end_data or "id" not in end_data: continue

                end_attrs = {"xmi:type": "uml:ConnectorEnd", "xmi:id": end_data["id"]}
                role_id = end_data.get("portRefId") or end_data.get("propertyRefId")
                part_id = end_data.get("partRefId")

                if role_id: end_attrs["role"] = role_id
                if part_id:
                    end_attrs["partWithPort"] = part_id
                    # 只有当 partWithPort 存在时，NestedConnectorEnd才有意义
                    self.nested_connector_ends_to_apply.append((end_data["id"], part_id))
                ET.SubElement(xml_elem, "end", end_attrs)
        elif elem_type == "Lifeline":
            if "representsId" in elem_data: xml_elem.set("represents", elem_data["representsId"])
        elif elem_type == "Message":
            if "sendEventId" in elem_data: xml_elem.set("sendEvent", elem_data["sendEventId"])
            if "receiveEventId" in elem_data: xml_elem.set("receiveEvent", elem_data["receiveEventId"])
            if "messageSort" in elem_data: xml_elem.set("messageSort", elem_data["messageSort"])
            if "signatureId" in elem_data: xml_elem.set("signature", elem_data["signatureId"])
            for arg_data in elem_data.get("arguments", []):
                arg_attrs = {"xmi:type": "uml:OpaqueExpression"}
                if "id" in arg_data: arg_attrs["xmi:id"] = arg_data["id"]
                arg_elem = ET.SubElement(xml_elem, "argument", arg_attrs)
                if "body" in arg_data: ET.SubElement(arg_elem, "body").text = str(arg_data["body"])
                if "language" in arg_data: ET.SubElement(arg_elem, "language").text = arg_data["language"]
        elif elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification"]:
            covered_id = elem_data.get("coveredId")
            if covered_id:
                xml_elem.set("covered", covered_id)
                self.lifelines_to_update[covered_id].append(elem_id)
            if elem_type == "MessageOccurrenceSpecification" and "messageId" in elem_data:
                xml_elem.set("message", elem_data["messageId"])
        elif elem_type == "CombinedFragment":
            if "interactionOperator" in elem_data: xml_elem.set("interactionOperator", elem_data["interactionOperator"])
            for ll_id in elem_data.get("coveredLifelineIds", []):
                ET.SubElement(xml_elem, "covered", {"xmi:idref": ll_id})
                self.lifelines_to_update[ll_id].append(elem_id)
        elif elem_type == "InteractionOperand":
            if "guardId" in elem_data and elem_data["guardId"]:
                xml_elem.set("guard", elem_data["guardId"])
        elif elem_type == "InteractionConstraint":
            spec_data = elem_data.get("specification")
            if isinstance(spec_data, dict):
                spec_attrs = {"xmi:type": "uml:OpaqueExpression"}
                if "id" in spec_data: spec_attrs["xmi:id"] = spec_data["id"]
                spec_elem = ET.SubElement(xml_elem, "specification", spec_attrs)
                if "body" in spec_data: ET.SubElement(spec_elem, "body").text = spec_data["body"]
                if "language" in spec_data: ET.SubElement(spec_elem, "language").text = spec_data["language"]
        elif elem_type == "State":
            for behavior_type in ["entry", "exit", "doActivity"]:
                if behavior_type in elem_data and elem_data[behavior_type]:
                    self._create_behavior_activity(xml_elem, behavior_type, elem_data[behavior_type])
        elif elem_type == "Transition":
            if "sourceId" in elem_data: xml_elem.set("source", elem_data["sourceId"])
            if "targetId" in elem_data: xml_elem.set("target", elem_data["targetId"])

            if "triggerIds" in elem_data:
                for i, event_id in enumerate(elem_data["triggerIds"]):
                    trigger_attrs = {"xmi:id": self._generate_id(elem_id, f"trigger_{i}"), "event": event_id}
                    ET.SubElement(xml_elem, "trigger", trigger_attrs)

            if "guard" in elem_data and elem_data["guard"]:
                guard_attrs = {"xmi:type": "uml:Constraint", "xmi:id": self._generate_id(elem_id, "guard")}
                guard_elem = ET.SubElement(xml_elem, "guard", guard_attrs)
                spec_attrs = {"xmi:type": "uml:OpaqueExpression", "xmi:id": self._generate_id(elem_id, "guard_spec")}
                spec_elem = ET.SubElement(guard_elem, "specification", spec_attrs)
                ET.SubElement(spec_elem, "body").text = elem_data["guard"].get("expression", "")
                if "language" in elem_data["guard"]:
                    ET.SubElement(spec_elem, "language").text = elem_data["guard"]["language"]

            if "effect" in elem_data and elem_data["effect"]:
                self._create_behavior_activity(xml_elem, "effect", elem_data["effect"])
        elif elem_type == "Pseudostate":
            if "kind" in elem_data: xml_elem.set("kind", elem_data["kind"])
        elif elem_type == "SignalEvent":
            if "signalId" in elem_data: xml_elem.set("signal", elem_data["signalId"])
        elif elem_type == "Property":
            xml_elem.set("aggregation", elem_data.get("aggregation", "none"))
            self._add_multiplicity(elem_data, xml_elem)
        elif elem_type in ["FullPort", "ProxyPort"]:
            xml_elem.set("aggregation", "composite") # Ports are always composite
            if elem_data.get("isBehavior"): xml_elem.set("isBehavior", str(elem_data["isBehavior"]).lower())
            self._add_multiplicity(elem_data, xml_elem)
        elif elem_type == "Operation":
            for param_data in elem_data.get("parameters", []):
                param_attrs = {"xmi:type": "uml:Parameter", "xmi:id": param_data["id"], "name": param_data["name"], "direction": param_data.get("direction", "in")}
                param_xml = ET.SubElement(xml_elem, "ownedParameter", param_attrs)
                self._add_type_ref_to_element(param_xml, param_data.get("typeId"))
        elif elem_type == "Reception":
            if "signalId" in elem_data: xml_elem.set("signal", elem_data["signalId"])
        elif elem_type == "ConstraintBlock" and "specification" in elem_data:
            spec_data = elem_data["specification"]
            # 创建 ownedRule (uml:Constraint)
            rule_attrs = {"xmi:type": "uml:Constraint", "xmi:id": self._generate_id(elem_id, "rule")}
            rule_elem = ET.SubElement(xml_elem, "ownedRule", rule_attrs)

            # 约束规则总是约束自身这个块
            ET.SubElement(rule_elem, "constrainedElement", {"xmi:idref": elem_id})

            # 创建 specification (uml:OpaqueExpression)
            spec_attrs = {"xmi:type": "uml:OpaqueExpression", "xmi:id": self._generate_id(elem_id, "spec")}
            spec_elem = ET.SubElement(rule_elem, "specification", spec_attrs)

            # 填充表达式的 body 和 language
            ET.SubElement(spec_elem, "body").text = spec_data.get("expression", "")
            if "language" in spec_data:
                ET.SubElement(spec_elem, "language").text = spec_data.get("language")

        elif elem_type in ["AssemblyConnector", "BindingConnector"]:
            for end_key in ["end1", "end2"]:
                end_data = elem_data.get(end_key)
                if not end_data: continue

                end_id = end_data.get("id")
                # 关键修复点：确保所有属性值在设置前都不是None
                if not end_id: continue

                end_attrs = {"xmi:type": "uml:ConnectorEnd", "xmi:id": end_id}

                # 获取 role 和 part ID
                role_id = end_data.get("portRefId") or end_data.get("propertyRefId")
                part_id = end_data.get("partRefId")

                # 只有在值不为 None 的情况下才设置属性
                if role_id is not None:
                    end_attrs["role"] = role_id

                if part_id is not None:
                    end_attrs["partWithPort"] = part_id
                    # 只有当 partWithPort 存在时，NestedConnectorEnd才有意义
                    self.nested_connector_ends_to_apply.append((end_id, part_id))

                ET.SubElement(xml_elem, "end", end_attrs)

        # (保留所有其他图的逻辑)
        elif elem_type == "Generalization": xml_elem.set("general", elem_data.get("targetId"))
        elif elem_type == "Include": xml_elem.set("addition", elem_data.get("targetId"))
        elif elem_type == "Extend": xml_elem.set("extendedCase", elem_data.get("targetId"))
        elif elem_type == "Association":
            source_id, target_id = elem_data.get("sourceId"), elem_data.get("targetId")
            if source_id and target_id:
                end1_id = self._generate_id(elem_id, "source_end"); end2_id = self._generate_id(elem_id, "target_end")
                ET.SubElement(xml_elem, 'ownedEnd', {'xmi:type': 'uml:Property', 'xmi:id': end1_id, 'type': source_id, 'association': elem_id})
                ET.SubElement(xml_elem, 'ownedEnd', {'xmi:type': 'uml:Property', 'xmi:id': end2_id, 'type': target_id, 'association': elem_id})
                ET.SubElement(xml_elem, 'memberEnd', {'xmi:idref': end1_id}); ET.SubElement(xml_elem, 'memberEnd', {'xmi:idref': end2_id})
                ET.SubElement(xml_elem, 'navigableOwnedEnd', {'xmi:idref': end2_id})
        elif elem_type == "TestCase":
            p_id = self._generate_id(elem_id, "verdict_param"); n_id = self._generate_id(elem_id, "verdict_node")
            p_xml = ET.SubElement(xml_elem, "ownedParameter", {"xmi:type": "uml:Parameter", "xmi:id": p_id, "name": "verdict", "direction": "return"})
            ET.SubElement(p_xml, "type", {"href": self.PRIMITIVE_TYPE_MAP["VerdictKind"]})
            ET.SubElement(xml_elem, "node", {"xmi:type": "uml:ActivityParameterNode", "xmi:id": n_id, "name": "verdict", "parameter": p_id})
        elif elem_type in ["DeriveReqt", "Satisfy", "Verify"]:
            client_map = {"DeriveReqt": "derivedRequirementId", "Satisfy": "blockId", "Verify": "testCaseId"}
            supplier_map = {"DeriveReqt": "sourceRequirementId", "Satisfy": "requirementId", "Verify": "requirementId"}
            if elem_data.get(client_map.get(elem_type)): ET.SubElement(xml_elem, "client", {"xmi:idref": elem_data.get(client_map.get(elem_type))})
            if elem_data.get(supplier_map.get(elem_type)): ET.SubElement(xml_elem, "supplier", {"xmi:idref": elem_data.get(supplier_map.get(elem_type))})

        # 通用属性填充
        if "visibility" in elem_data and elem_data["visibility"] is not None:
            xml_elem.set("visibility", elem_data["visibility"])
        if "isAbstract" in elem_data and elem_data["isAbstract"] is not None:
            xml_elem.set("isAbstract", str(elem_data["isAbstract"]).lower())
        if "typeId" in elem_data and elem_data["typeId"] is not None:
            self._add_type_ref_to_element(xml_elem, elem_data["typeId"])

        if elem_type == "Reception":
            signal_id = elem_data.get("signalId")
            if signal_id is not None:
                xml_elem.set("signal", signal_id)
        if elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification"]:
            covered_id = elem_data.get("coveredId")
            if covered_id:
                xml_elem.set("covered", covered_id)
                self.lifelines_to_update[covered_id].append(elem_id)
    def _add_multiplicity(self, element_data, parent_xml_element):
        multiplicity = element_data.get('multiplicity')
        if multiplicity is None: return
        lower_val, upper_val = '1', '1'
        if isinstance(multiplicity, str) and multiplicity.startswith('[') and multiplicity.endswith(']'):
            parts = multiplicity[1:-1].split('..')
            if len(parts) == 2: lower_val, upper_val = parts[0].strip(), parts[1].strip()
            elif len(parts) == 1: lower_val = upper_val = parts[0].strip()
        lower_attrs = {'xmi:type': 'uml:LiteralInteger', 'xmi:id': self._generate_id(element_data['id'], 'lower'), 'value': lower_val}
        upper_attrs = {'xmi:id': self._generate_id(element_data['id'], 'upper'), 'value': upper_val}
        upper_attrs['xmi:type'] = 'uml:LiteralUnlimitedNatural' if upper_val == '*' else 'uml:LiteralInteger'
        ET.SubElement(parent_xml_element, "lowerValue", lower_attrs)
        ET.SubElement(parent_xml_element, "upperValue", upper_attrs)


    def _create_behavior_activity(self, parent_xml, tag, data):
        """辅助函数：为状态机创建内嵌的Activity行为。"""
        if not data or "wrapperActivityId" not in data: return
        wrapper_id, called_id = data["wrapperActivityId"], data.get("calledBehaviorId")
        called_name = self.elements_by_id.get(called_id, {}).get("name")
        attrs = {"xmi:type": "uml:Activity", "xmi:id": wrapper_id}
        if called_name: attrs["name"] = called_name
        act_xml = ET.SubElement(parent_xml, tag, attrs)
        if called_id:
            cba_attrs = {"xmi:type": "uml:CallBehaviorAction", "xmi:id": self._generate_id(wrapper_id, "cba"), "behavior": called_id}
            ET.SubElement(act_xml, "node", cba_attrs)

    def _populate_activity_content(self, activity_xml_elem, activity_data):
        activity_id = activity_data["id"]
        node_ids = [cid for cid in self.children_by_parent.get(activity_id, []) if self.elements_by_id[cid]['type'] not in ['ControlFlow', 'ObjectFlow', 'ActivityPartition']]
        edge_ids = [cid for cid in self.children_by_parent.get(activity_id, []) if self.elements_by_id[cid]['type'] in ['ControlFlow', 'ObjectFlow']]
        group_ids = [cid for cid in self.children_by_parent.get(activity_id, []) if self.elements_by_id[cid]['type'] == 'ActivityPartition']

        for group_id in group_ids: self._create_activity_element(activity_xml_elem, group_id)
        for edge_id in edge_ids: self._create_activity_element(activity_xml_elem, edge_id)
        for node_id in node_ids: self._create_activity_element(activity_xml_elem, node_id)

    def _create_activity_element(self, parent_xml_element, elem_id):
        if elem_id in self.xml_elements: return
        elem_data = self.elements_by_id[elem_id]
        tag_name, xmi_type = self._determine_tag_and_type(elem_data)
        if not tag_name: return

        attrs = {"xmi:id": elem_id, "xmi:type": xmi_type}
        if "name" in elem_data: attrs["name"] = elem_data["name"]

        xml_elem = ET.SubElement(parent_xml_element, tag_name, attrs)
        self.xml_elements[elem_id] = xml_elem

        elem_type = elem_data["type"]
        if elem_type in ["ControlFlow", "ObjectFlow"]:
            if "sourceId" in elem_data: xml_elem.set("source", elem_data["sourceId"])
            if "targetId" in elem_data: xml_elem.set("target", elem_data["targetId"])
            if "guard" in elem_data:
                guard = ET.SubElement(xml_elem, "guard", {"xmi:type": "uml:OpaqueExpression", "xmi:id": self._generate_id(elem_id, "guard")})
                ET.SubElement(guard, "body").text = elem_data["guard"]
        elif elem_type == "CallBehaviorAction":
            if "behavior" in elem_data: xml_elem.set("behavior", elem_data["behavior"])
            for pin_id in self.children_by_parent.get(elem_id, []):
                self._create_activity_element(xml_elem, pin_id)
        elif elem_type == "ActivityPartition":
            if "representsId" in elem_data: xml_elem.set("represents", elem_data["representsId"])
            for node_edge_id in elem_data.get("nodeIds", []):
                node_edge_type = self.elements_by_id[node_edge_id].get("type")
                ref_tag = "edge" if node_edge_type in ["ControlFlow", "ObjectFlow"] else "node"
                ET.SubElement(xml_elem, ref_tag, {"xmi:idref": node_edge_id})
        elif elem_type in ["InputPin", "OutputPin", "CentralBufferNode", "ActivityParameterNode"]:
            if "typeId" in elem_data: self._add_type_ref_to_element(xml_elem, elem_data["typeId"])


    def _add_type_ref_to_element(self, parent_xml, type_id):
        if not type_id: return
        # 检查是否是完整的URL
        if str(type_id).startswith("http://") or str(type_id).startswith("https://"):
            ET.SubElement(parent_xml, "type", {"href": type_id})
        # 检查是否是预定义的简写基础类型
        elif type_id in self.PRIMITIVE_TYPE_MAP:
            ET.SubElement(parent_xml, "type", {"href": self.PRIMITIVE_TYPE_MAP[type_id]})
        # 否则，假定为模型内部的ID引用
        elif type_id in self.elements_by_id:
            parent_xml.set("type", type_id)
        else:
            print(f"警告: 类型 '{type_id}' 未在JSON中定义，也非预定义类型。")

    def _post_process_references(self):
        print("--- 阶段 3: 后期处理引用 (如 Lifeline.coveredBy) ---")
        for ll_id, frag_ids in self.lifelines_to_update.items():
            if ll_id in self.xml_elements:
                ll_xml = self.xml_elements[ll_id]
                for frag_id in frag_ids:
                    ET.SubElement(ll_xml, "coveredBy", {"xmi:idref": frag_id})
    def _post_process_structure(self):
        print("--- 阶段 3: 后期处理关系和图表 ---")
        self._apply_associations()
        self._apply_diagrams()
        self._post_process_lifeline_coverage()

    def _post_process_lifeline_coverage(self):
        print("--- 阶段 3.3: 后期处理 Lifeline.coveredBy ---")
        for ll_id, frag_ids in self.lifelines_to_update.items():
            if ll_id in self.xml_elements:
                ll_xml = self.xml_elements[ll_id]
                for frag_id in frag_ids:
                    ET.SubElement(ll_xml, "coveredBy", {"xmi:idref": frag_id})

    def _apply_associations(self):
        """后处理：创建Association元素并更新其两端的Property。"""
        print(f"--- 阶段 3.1: 应用 {len(self.associations_to_apply)} 个关联 ---")
        for assoc_data in self.associations_to_apply:
            parent_xml = self.xml_elements.get(assoc_data["parentId"])
            if not parent_xml:
                print(f"警告: 关联 '{assoc_data['id']}' 的父节点 '{assoc_data['parentId']}' 未找到。")
                continue

            # BDD/IBD 的关联通常没有sourceId/targetId，而是有memberEndIds
            member_ends = assoc_data.get("memberEndIds", [])
            # 用例图的关联有sourceId/targetId
            source_id = assoc_data.get("sourceId")
            target_id = assoc_data.get("targetId")

            if member_ends and len(member_ends) == 2:
                # BDD/IBD 关联逻辑
                if member_ends[0] in self.xml_elements and member_ends[1] in self.xml_elements:
                    assoc_xml = ET.SubElement(parent_xml, "packagedElement", {"xmi:type": "uml:Association", "xmi:id": assoc_data["id"]})
                    ET.SubElement(assoc_xml, "memberEnd", {"xmi:idref": member_ends[0]})
                    ET.SubElement(assoc_xml, "memberEnd", {"xmi:idref": member_ends[1]})
                    self.xml_elements[member_ends[0]].set("association", assoc_data["id"])
                    self.xml_elements[member_ends[1]].set("association", assoc_data["id"])
            elif source_id and target_id:
                # 用例图关联逻辑
                assoc_xml = ET.SubElement(parent_xml, "packagedElement", {"xmi:type": "uml:Association", "xmi:id": assoc_data["id"]})
                end1_id, end2_id = self._generate_id(assoc_data["id"], "end1"), self._generate_id(assoc_data["id"], "end2")
                ET.SubElement(assoc_xml, 'ownedEnd', {'xmi:type': 'uml:Property', 'xmi:id': end1_id, 'type': source_id, 'association': assoc_data["id"]})
                ET.SubElement(assoc_xml, 'ownedEnd', {'xmi:type': 'uml:Property', 'xmi:id': end2_id, 'type': target_id, 'association': assoc_data["id"]})
                ET.SubElement(assoc_xml, 'memberEnd', {'xmi:idref': end1_id}); ET.SubElement(assoc_xml, 'memberEnd', {'xmi:idref': end2_id})
    def _apply_diagrams(self):
        print(f"--- 阶段 3.2: 应用 {len(self.diagrams_to_apply)} 个图表 ---")
        for diag_data in self.diagrams_to_apply:
            owner_id = diag_data.get("contextId") or diag_data.get("parentId")
            owner_xml = self.xml_elements.get(owner_id)
            if owner_xml:
                self._add_diagram_extension(diag_data, owner_xml)

    def _add_diagram_extension(self, diagram_data, owner_xml_element):
        pass # 此处省略详细的图表XML结构，因为它非常冗长且与核心逻辑关系不大
    def _apply_stereotypes(self):
        print("--- 阶段 4: 应用SysML构造型 ---")
        stereotype_map = {
            "Requirement": ("sysml:Requirement", "base_Class"),
            "Block": ("sysml:Block", "base_Class"),
            "InterfaceBlock": ("sysml:InterfaceBlock", "base_Class"),
            "TestCase": ("sysml:TestCase", "base_Behavior"),
            "Abstraction": {"DeriveReqt": ("sysml:DeriveReqt", "base_Abstraction"), "Satisfy": ("sysml:Satisfy", "base_Abstraction"),
                            "Verify": ("sysml:Verify", "base_Abstraction")},
            "ValueType": ("sysml:ValueType", "base_DataType"),
            "Unit": ("MD_Customization_for_SysML__additional_stereotypes:Unit", "base_InstanceSpecification"),
            "ConstraintBlock": ("sysml:ConstraintBlock", "base_Class"),
            "ConstraintParameter": ("MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter", "base_Port"),
            "BindingConnector": ("sysml:BindingConnector", "base_Connector"),
            "ActivityPartition": ("sysml:AllocateActivityPartition", "base_ActivityPartition"),
            "FullPort": ("sysml:FullPort", "base_Port"),
            "ProxyPort": ("sysml:ProxyPort", "base_Port")
        }
        for category, elements in self.stereotypes_to_apply.items():
            for data in elements:
                elem_id = data.get("id")
                if elem_id not in self.xml_elements: continue

                mapping = stereotype_map.get(category)
                if not mapping:
                    # --- PARAMETRIC-FIX: 处理 Property 的构造型 ---
                    if category == "Property":
                        kind = data.get("propertyKind")
                        prop_kind_map = {
                            "value": ("MD_Customization_for_SysML__additional_stereotypes:ValueProperty", "base_Property"),
                            "part": ("MD_Customization_for_SysML__additional_stereotypes:PartProperty", "base_Property"),
                            "reference": ("MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty", "base_Property"),
                            "constraint": ("MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty", "base_Property")
                        }
                        if kind in prop_kind_map:
                            mapping = prop_kind_map[kind]
                        else: continue
                    else: continue

                tag, base_attr = None, None
                if category == "Abstraction":
                    original_type = data.get("type")
                    if original_type in mapping: tag, base_attr = mapping[original_type]
                else:
                    tag, base_attr = mapping

                if tag and base_attr:
                    attrs = {"xmi:id": self._generate_id(elem_id, "stereo"), base_attr: elem_id}
                    if data.get("type") == "Requirement":
                        attrs["Id"] = data.get("reqId", ""); attrs["Text"] = data.get("text", "")
                    ET.SubElement(self.xmi_root, tag, attrs)

        ### 修正点 3: 添加应用 NestedConnectorEnd 构造型的逻辑 ###
        print("--- 阶段 4.5: 应用 NestedConnectorEnd 构造型 ---")
        for end_id, prop_path_id in self.nested_connector_ends_to_apply:
            attrs = {
                "xmi:id": self._generate_id(end_id, "nested_stereo"),
                "base_ConnectorEnd": end_id,
                "propertyPath": prop_path_id
            }
            ET.SubElement(self.xmi_root, "sysml:NestedConnectorEnd", attrs)

    def _prettify_xml(self):
        print("--- 阶段 5: 格式化XML输出 ---")
        rough_string = ET.tostring(self.xmi_root, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ", encoding="UTF-8").decode()
        return "\n".join([line for line in pretty_xml.splitlines() if line.strip()])

if __name__ == '__main__':
    # 测试用的JSON，专门聚焦于参数图的各种元素
    parametric_focused_json_str ={
        "model": {
            "id": "model-master-system-uuid",
            "name": "Integrated Vehicle AirConditioner System"
        },
        "elements": [
            {
                "id": "pkg-root-uuid",
                "type": "Package",
                "name": "SystemModelPackage",
                "parentId": "model-master-system-uuid"
            },
            {
                "id": "pkg-reqs-uuid",
                "type": "Package",
                "name": "Requirements",
                "parentId": "pkg-root-uuid"
            },
            {
                "id": "pkg-usecases-uuid",
                "type": "Package",
                "name": "UseCases",
                "parentId": "pkg-root-uuid"
            },
            {
                "id": "pkg-structure-uuid",
                "type": "Package",
                "name": "SystemStructure",
                "parentId": "pkg-root-uuid"
            },
            {
                "id": "pkg-behavior-uuid",
                "type": "Package",
                "name": "SystemBehavior",
                "parentId": "pkg-root-uuid"
            },
            {
                "id": "pkg-libraries-uuid",
                "type": "Package",
                "name": "Libraries",
                "parentId": "pkg-root-uuid"
            },
            {
                "id": "pkg-bicycle-func-uuid",
                "type": "Package",
                "name": "自行车功能包",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "req-ride-control-uuid",
                "type": "Requirement",
                "name": "骑行控制",
                "reqId": "REQ-1",
                "text": "系统应允许用户骑行自行车，提供前后轮驱动。用户可以通过踏板控制前后轮转动。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-brake-system-uuid",
                "type": "Requirement",
                "name": "刹车系统",
                "reqId": "REQ-2",
                "text": "备刹车系统，用户可通过手柄操作停止自行车。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-max-load-uuid",
                "type": "Requirement",
                "name": "最大载重",
                "reqId": "REQ-3",
                "text": "车辆最大载重为100公斤。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-durability-uuid",
                "type": "Requirement",
                "name": "耐用性",
                "reqId": "REQ-4",
                "text": "耐用性满足5年使用。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-max-speed-uuid",
                "type": "Requirement",
                "name": "最大速度",
                "reqId": "REQ-5",
                "text": "最大速度限制为25公里/小时，确保安全。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-structure-uuid",
                "type": "Requirement",
                "name": "结构标准",
                "reqId": "REQ-6",
                "text": "结构符合ISO 4210安全标准。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-frame-material-uuid",
                "type": "Requirement",
                "name": "车架材料",
                "reqId": "REQ-7",
                "text": "车架材料为铝合金，重量不超过15公斤。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-wheel-diameter-uuid",
                "type": "Requirement",
                "name": "轮径",
                "reqId": "REQ-8",
                "text": "轮径为26英寸。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-performance-test-uuid",
                "type": "Requirement",
                "name": "性能验证",
                "reqId": "REQ-9",
                "text": "通过速度测验、载重测试和刹车距离测试。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "req-safety-priority-uuid",
                "type": "Requirement",
                "name": "安全优先",
                "reqId": "REQ-10",
                "text": "安全性为最高优先级，功能依次类推。",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "blk-control-system-uuid",
                "type": "Block",
                "name": "自动控制系统",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "tc-speed-test-uuid",
                "type": "TestCase",
                "name": "速度测试",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "tc-load-test-uuid",
                "type": "TestCase",
                "name": "载重测试",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "tc-brake-distance-uuid",
                "type": "TestCase",
                "name": "刹车距离测试",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "rel-derive-structure-durability-uuid",
                "type": "DeriveReqt",
                "sourceRequirementId": "req-structure-uuid",
                "derivedRequirementId": "req-durability-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "rel-derive-performance-maxspeed-uuid",
                "type": "DeriveReqt",
                "sourceRequirementId": "req-performance-test-uuid",
                "derivedRequirementId": "req-max-speed-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "rel-satisfy-control-ride-uuid",
                "type": "Satisfy",
                "blockId": "blk-control-system-uuid",
                "requirementId": "req-ride-control-uuid",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "rel-satisfy-control-brake-uuid",
                "type": "Satisfy",
                "blockId": "blk-control-system-uuid",
                "requirementId": "req-brake-system-uuid",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "rel-verify-speed-maxspeed-uuid",
                "type": "Verify",
                "testCaseId": "tc-speed-test-uuid",
                "requirementId": "req-max-speed-uuid",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "rel-verify-load-maxload-uuid",
                "type": "Verify",
                "testCaseId": "tc-load-test-uuid",
                "requirementId": "req-max-load-uuid",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "rel-verify-brake-system-uuid",
                "type": "Verify",
                "testCaseId": "tc-brake-distance-uuid",
                "requirementId": "req-brake-system-uuid",
                "parentId": "pkg-reqs-uuid"
            },
            {
                "id": "pkg-bicycle-uuid",
                "type": "Package",
                "name": "自行车包",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "unit-mps-uuid",
                "type": "Unit",
                "name": "m/s",
                "parentId": "pkg-libraries-uuid"
            },
            {
                "id": "vt-speed-uuid",
                "type": "ValueType",
                "name": "Speed",
                "parentId": "pkg-libraries-uuid",
                "baseType": "Real",
                "unitId": "unit-mps-uuid"
            },
            {
                "id": "enum-gear-uuid",
                "type": "Enumeration",
                "name": "GearPosition",
                "parentId": "pkg-libraries-uuid"
            },
            {
                "id": "sig-speed-uuid",
                "type": "Signal",
                "name": "SpeedSignal",
                "parentId": "pkg-libraries-uuid"
            },
            {
                "id": "if-handlebar-uuid",
                "type": "InterfaceBlock",
                "name": "HandlebarInterface",
                "parentId": "pkg-structure-uuid",
                "isAbstract": True
            },
            {
                "id": "blk-frame-uuid",
                "type": "Block",
                "name": "Frame",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-wheels-uuid",
                "type": "Block",
                "name": "Wheels",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-pedalsystem-uuid",
                "type": "Block",
                "name": "PedalSystem",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-brakingsystem-uuid",
                "type": "Block",
                "name": "BrakingSystem",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-drivchain-uuid",
                "type": "Block",
                "name": "DriveChain",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-handlebar-uuid",
                "type": "Block",
                "name": "Handlebar",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-speedsensor-uuid",
                "type": "Block",
                "name": "SpeedSensor",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-bicycle-uuid",
                "type": "Block",
                "name": "Bicycle",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "prop-bicycle-frame",
                "type": "Property",
                "name": "frame",
                "parentId": "blk-bicycle-uuid",
                "visibility": "public",
                "propertyKind": "part",
                "aggregation": "composite",
                "typeId": "blk-frame-uuid",
                "associationId": "assoc-bicycle-frame",
                "multiplicity": "[1..1]"
            },
            {
                "id": "prop-bicycle-wheels",
                "type": "Property",
                "name": "wheels",
                "parentId": "blk-bicycle-uuid",
                "visibility": "public",
                "propertyKind": "part",
                "aggregation": "composite",
                "typeId": "blk-wheels-uuid",
                "associationId": "assoc-bicycle-wheels",
                "multiplicity": "[1..1]"
            },
            {
                "id": "prop-bicycle-pedalsystem",
                "type": "Property",
                "name": "pedalSystem",
                "parentId": "blk-pedalsystem-uuid",
                "visibility": "public",
                "propertyKind": "part",
                "aggregation": "composite",
                "typeId": "blk-pedalsystem-uuid",
                "associationId": "assoc-bicycle-pedalsystem",
                "multiplicity": "[1..1]"
            },
            {
                "id": "prop-bicycle-brakingsystem",
                "type": "Property",
                "name": "brakingSystem",
                "parentId": "blk-brakingsystem-uuid",
                "visibility": "public",
                "propertyKind": "part",
                "aggregation": "composite",
                "typeId": "blk-brakingsystem-uuid",
                "associationId": "assoc-bicycle-brakingsystem",
                "multiplicity": "[1..1]"
            },
            {
                "id": "prop-bicycle-drivchain",
                "type": "Property",
                "name": "driveChain",
                "parentId": "blk-drivchain-uuid",
                "visibility": "public",
                "propertyKind": "part",
                "aggregation": "composite",
                "typeId": "blk-drivchain-uuid",
                "associationId": "assoc-bicycle-drivchain",
                "multiplicity": "[1..1]"
            },
            {
                "id": "prop-bicycle-handlebar",
                "type": "Property",
                "name": "_handlebar",
                "parentId": "blk-handlebar-uuid",
                "visibility": "public",
                "propertyKind": "reference",
                "aggregation": "none",
                "typeId": "blk-handlebar-uuid",
                "associationId": "assoc-bicycle-handlebar"
            },
            {
                "id": "prop-speedsensor-current",
                "type": "Property",
                "name": "currentSpeed",
                "parentId": "blk-speedsensor-uuid",
                "visibility": "public",
                "propertyKind": "value",
                "aggregation": "none",
                "typeId": "vt-speed-uuid",
                "multiplicity": "[1..1]"
            },
            {
                "id": "prop-speedsensor",
                "type": "Property",
                "name": "speedOutput",
                "parentId": "blk-speedsensor-uuid",
                "visibility": "public",
                "propertyKind": "value",
                "aggregation": "none",
                "typeId": "sig-speed-uuid",
                "multiplicity": "[1..1]"
            },
            {
                "id": "port-handlebar-brake",
                "type": "FullPort",
                "name": "brakeControl",
                "parentId": "pkg-structure-uuid",
                "visibility": "public",
                "typeId": "if-handlebar-uuid",
                "isBehavior": False
            },
            {
                "id": "port-handlebar-steer",
                "type": "FullPort",
                "name": "steeringControl",
                "parentId": "pkg-structure-uuid",
                "visibility": "public",
                "typeId": "if-handlebar-uuid",
                "isBehavior": False
            },
            {
                "id": "port-speedsensor-speedout",
                "type": "ProxyPort",
                "name": "speedOutput",
                "parentId": "blk-speedsensor-uuid",
                "visibility": "public",
                "typeId": "sig-speed-uuid",
                "isBehavior": True
            },
            {
                "id": "op-fan-setspeed",
                "type": "Operation",
                "name": "setSpeedLevel",
                "parentId": "pkg-structure-uuid",
                "visibility": "public",
                "parameters": [
                    {
                        "id": "p-fss-lvl",
                        "name": "level",
                        "typeId": "enum-gear-uuid",
                        "direction": "in"
                    }
                ]
            },
            {
                "id": "op-remote-sendcmd",
                "type": "Operation",
                "name": "sendCommand",
                "parentId": "pkg-structure-uuid",
                "visibility": "public",
                "parameters": [
                    {
                        "id": "p-rsc-cmd",
                        "name": "command",
                        "typeId": "enum-gear-uuid",
                        "direction": "in"
                    }
                ]
            },
            {
                "id": "recp-bicycle-speed",
                "type": "Reception",
                "name": "speedSignalReceived",
                "parentId": "pkg-structure-uuid",
                "visibility": "public",
                "signalId": "sig-speed-uuid"
            },
            {
                "id": "lit-off",
                "type": "EnumerationLiteral",
                "name": "Off",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "lit-low",
                "type": "EnumerationLiteral",
                "name": "Low",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "lit-medium",
                "type": "EnumerationLiteral",
                "name": "Medium",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "lit-high",
                "type": "EnumerationLiteral",
                "name": "High",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-bicycle-frame",
                "type": "Association",
                "parentId": "pkg-structure-uuid",
                "memberEndIds": [
                    "prop-bicycle-frame",
                    "prop-bicycle-wheels"
                ]
            },
            {
                "id": "assoc-bicycle-wheels",
                "type": "Association",
                "parentId": "pkg-structure-uuid",
                "memberEndIds": [
                    "prop-bicycle-wheels",
                    "prop-bicycle-wheels"
                ]
            },
            {
                "id": "assoc-bicycle-pedalsystem",
                "type": "Association",
                "parentId": "pkg-structure-uuid",
                "memberEndIds": [
                    "prop-bicycle-pedalsystem",
                    "prop-pedalsystem"
                ]
            },
            {
                "id": "assoc-bicycle-brakingsystem",
                "type": "Association",
                "parentId": "pkg-structure-uuid",
                "memberEndIds": [
                    "prop-bicycle-brakingsystem",
                    "prop-brakingsystem"
                ]
            },
            {
                "id": "assoc-bicycle-drivchain",
                "type": "Association",
                "parentId": "pkg-structure-uuid",
                "memberEndIds": [
                    "prop-bicycle-drivchain",
                    "prop-drivchain"
                ]
            },
            {
                "id": "assoc-bicycle-handlebar",
                "type": "Association",
                "parentId": "pkg-structure-uuid",
                "memberEndIds": [
                    "prop-bicycle-handlebar",
                    "prop-handlebar"
                ]
            },
            {
                "id": "conn-bicycle-frame-wheels",
                "type": "AssemblyConnector",
                "parentId": "pkg-structure-uuid",
                "kind": "assembly",
                "end1": {
                    "partRefId": "prop-bicycle-frame",
                    "portRefId": None
                },
                "end2": {
                    "partRefId": "prop-bicycle-wheels",
                    "portRefId": None
                }
            },
            {
                "id": "conn-bicycle-pedalsystem-drivechain",
                "type": "AssemblyConnector",
                "parentId": "pkg-structure-uuid",
                "kind": "assembly",
                "end1": {
                    "partRefId": "prop-bicycle-pedalsystem",
                    "portRefId": None
                },
                "end2": {
                    "partRefId": "prop-bicycle-drivchain",
                    "portRefId": None
                }
            },
            {
                "id": "conn-bicycle-handlebar-brake",
                "type": "AssemblyConnector",
                "parentId": "pkg-structure-uuid",
                "kind": "assembly",
                "end1": {
                    "partRefId": "prop-bicycle-handlebar",
                    "portRefId": "port-handlebar-brake"
                },
                "end2": {
                    "partRefId": None,
                    "portRefId": None
                }
            },
            {
                "id": "pkg-bike-system-uuid",
                "type": "Package",
                "name": "自行车骑行系统",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-speed-data-uuid",
                "type": "Block",
                "name": "SpeedData",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-speed-limit-uuid",
                "type": "Block",
                "name": "SpeedLimitCommand",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-brake-status-uuid",
                "type": "Block",
                "name": "BrakeStatus",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-user-uuid",
                "type": "Block",
                "name": "用户",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-bike-uuid",
                "type": "Block",
                "name": "自行车系统",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-speed-sensor-uuid",
                "type": "Block",
                "name": "速度传感器",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "blk-brake-system-uuid",
                "type": "Block",
                "name": "刹车系统",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "act-bike-ride-uuid",
                "type": "Activity",
                "name": "自行车骑行流程",
                "parentId": "pkg-behavior-uuid",
                "nodes": [
                    "node-start-uuid",
                    "node-user-get-on",
                    "node-bike-start",
                    "node-pedal",
                    "node-drive-chain",
                    "node-monitor-speed",
                    "node-speed-fork",
                    "node-speed-decision",
                    "node-limit-acceleration",
                    "node-activate-brake",
                    "node-decelerate",
                    "node-user-get-off",
                    "node-standby",
                    "node-end-uuid"
                ],
                "edges": [
                    "edge-cf-start-to-user",
                    "edge-cf-user-to-start",
                    "edge-cf-start-to-bike",
                    "edge-cf-bike-to-pedal",
                    "edge-cf-pedal-to-drive",
                    "edge-cf-drive-to-monitor",
                    "edge-cf-monitor-to-fork",
                    "edge-cf-fork-to-speedA",
                    "edge-cf-fork-to-speedB",
                    "edge-cf-speedA-to-join",
                    "edge-cf-speedA-to-join",
                    "edge-cf-join-to-consolidate",
                    "edge-cf-consolidate-to-decision",
                    "edge-cf-decision-to-limit",
                    "edge-cf-decision-to-brake",
                    "edge-cf-limit-to-monitor",
                    "edge-cf-brake-to-decelerate",
                    "edge-cf-decelerate-to-standby",
                    "edge-cf-standby-to-end"
                ],
                "groups": [
                    "grp-user-uuid",
                    "grp-bike-uuid",
                    "grp-speed-uuid",
                    "grp-brake-uuid"
                ]
            },
            {
                "id": "act-user-get-on",
                "type": "Activity",
                "name": "用户上车",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-bike-start",
                "type": "Activity",
                "name": "启动自行车",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-pedal",
                "type": "Activity",
                "name": "踏板",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-drive-chain",
                "type": "Activity",
                "name": "传动链带动轮子旋转",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-monitor-speed",
                "type": "Activity",
                "name": "监测速度",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-limit-acceleration",
                "type": "Activity",
                "name": "限制加速",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-activate-brake",
                "type": "Activity",
                "name": "激活刹车系统",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-decelerate",
                "type": "Activity",
                "name": "减速直至停止",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-user-get-off",
                "type": "Activity",
                "name": "用户下车",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-standby",
                "type": "Activity",
                "name": "系统待命",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "node-start-uuid",
                "type": "InitialNode",
                "name": "开始",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-end-uuid",
                "type": "ActivityFinalNode",
                "name": "流程结束",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-speed-fork",
                "type": "ForkNode",
                "name": "速度监测并行",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-speed-decision",
                "type": "DecisionNode",
                "name": "速度是否超过25公里/小时?",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-join",
                "type": "JoinNode",
                "name": "等待速度限制完成",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-consolidate",
                "type": "CallBehaviorAction",
                "name": "汇总速度信息",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-limit-acceleration",
                "type": "CallBehaviorAction",
                "name": "限制加速",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-activate-brake",
                "type": "CallBehaviorAction",
                "name": "激活刹车",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-decelerate",
                "type": "CallBehaviorAction",
                "name": "减速直至停止",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-user-get-on",
                "type": "CallBehaviorAction",
                "name": "用户上车",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-user-get-off",
                "type": "CallBehaviorAction",
                "name": "用户下车",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "node-standby",
                "type": "CallBehaviorAction",
                "name": "系统待命",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "obj-speed-sensor-uuid",
                "type": "ObjectNode",
                "name": "速度传感器数据",
                "typeId": "blk-speed-data-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "obj-speed-limit-uuid",
                "type": "ObjectNode",
                "name": "速度限制指令",
                "typeId": "blk-speed-limit-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "obj-brake-status-uuid",
                "type": "ObjectNode",
                "name": "刹车状态",
                "typeId": "blk-brake-status-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "node-user-operation",
                "type": "CallBehaviorAction",
                "name": "用户操作刹车",
                "behavior": None,
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "pin-in-user",
                "type": "InputPin",
                "name": "用户输入",
                "typeId": "blk-user-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-in-speed-data",
                "type": "InputPin",
                "name": "速度传感器数据",
                "typeId": "blk-speed-data-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-in-speed-limit",
                "type": "InputPin",
                "name": "速度限制指令",
                "typeId": "blk-speed-limit-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-in-brake",
                "type": "InputPin",
                "name": "刹车操作",
                "typeId": "blk-brake-status-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-in-decelerate",
                "type": "InputPin",
                "name": "减速指令",
                "typeId": "blk-brake-status-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-in-user-off",
                "type": "InputPin",
                "name": "用户下车",
                "typeId": "blk-user-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-in-standby",
                "type": "InputPin",
                "name": "系统待命",
                "typeId": "blk-bike-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-out-wheel-rotation",
                "type": "OutputPin",
                "name": "轮子旋转状态",
                "typeId": "blk-speed-data-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-out-speed-data",
                "type": "OutputPin",
                "name": "速度数据",
                "typeId": "blk-speed-data-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-out-speed-limit",
                "type": "OutputPin",
                "name": "速度限制指令",
                "typeId": "blk-speed-limit-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-out-brake-status",
                "type": "OutputPin",
                "name": "刹车状态",
                "typeId": "blk-brake-status-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "pin-out-system-state",
                "type": "OutputPin",
                "name": "系统状态",
                "typeId": "blk-bike-uuid",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "edge-cf-start-to-user",
                "type": "ControlFlow",
                "sourceId": "node-start-uuid",
                "targetId": "node-user-get-on",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-user-to-start",
                "type": "ControlFlow",
                "sourceId": "node-user-get-on",
                "targetId": "node-bike-start",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-start-to-bike",
                "type": "ControlFlow",
                "sourceId": "node-start-uuid",
                "targetId": "node-bike-start",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-bike-to-pedal",
                "type": "ControlFlow",
                "sourceId": "node-bike-start",
                "targetId": "node-pedal",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-pedal-to-drive",
                "type": "ControlFlow",
                "sourceId": "node-pedal",
                "targetId": "node-drive-chain",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-drive-to-monitor",
                "type": "ControlFlow",
                "sourceId": "node-drive-chain",
                "targetId": "node-monitor-speed",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-monitor-to-fork",
                "type": "ControlFlow",
                "sourceId": "node-monitor-speed",
                "targetId": "node-speed-fork",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-fork-to-speedA",
                "type": "ControlFlow",
                "sourceId": "node-speed-fork",
                "targetId": "node-speed-decision",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-speedA-to-join",
                "type": "ControlFlow",
                "sourceId": "node-speed-decision",
                "targetId": "node-join",
                "guard": "[未超速]",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-join-to-consolidate",
                "type": "ControlFlow",
                "sourceId": "node-join",
                "targetId": "node-consolidate",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-consolidate-to-decision",
                "type": "ControlFlow",
                "sourceId": "node-consolidate",
                "targetId": "node-speed-decision",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-decision-to-limit",
                "type": "ControlFlow",
                "sourceId": "node-speed-decision",
                "targetId": "node-limit-acceleration",
                "guard": "[超速]",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-decision-to-brake",
                "type": "ControlFlow",
                "sourceId": "node-speed-decision",
                "targetId": "node-activate-brake",
                "guard": "[刹车]",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-limit-to-monitor",
                "type": "ControlFlow",
                "sourceId": "node-limit-acceleration",
                "targetId": "node-monitor-speed",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-brake-to-decelerate",
                "type": "ControlFlow",
                "sourceId": "node-activate-brake",
                "targetId": "node-decelerate",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-decelerate-to-standby",
                "type": "ControlFlow",
                "sourceId": "node-decelerate",
                "targetId": "node-standby",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "edge-cf-standby-to-end",
                "type": "ControlFlow",
                "sourceId": "node-standby",
                "targetId": "node-end-uuid",
                "parentId": "act-bike-ride-uuid"
            },
            {
                "id": "pkg-vehicle-control-uuid",
                "type": "Package",
                "name": "车辆控制包",
                "parentId": "model-vehicle-statemachine-uuid"
            },
            {
                "id": "pkg-vehicle-behaviors-uuid",
                "type": "Package",
                "name": "车辆行为库",
                "parentId": "model-vehicle-statemachine-uuid"
            },
            {
                "id": "blk-vehicle-controller-uuid",
                "type": "Block",
                "name": "车辆控制器",
                "parentId": "pkg-structure-uuid",
                "classifierBehaviorId": "sm-vehicle-operation-uuid"
            },
            {
                "id": "sm-vehicle-operation-uuid",
                "type": "StateMachine",
                "name": "车辆操作状态机",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "region-vehicle-main-uuid",
                "type": "Region",
                "name": "主操作区域",
                "parentId": "sm-vehicle-operation-uuid"
            },
            {
                "id": "ps-main-initial-uuid",
                "type": "Pseudostate",
                "kind": "initial",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "state-idle-uuid",
                "type": "State",
                "name": "空闲",
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "state-riding-uuid",
                "type": "State",
                "name": "骑行中",
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "state-braking-uuid",
                "type": "State",
                "name": "刹车中",
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "state-stopped-uuid",
                "type": "State",
                "name": "停止",
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "act-start-speed-monitor-uuid",
                "type": "Activity",
                "name": "启动速度监测",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-continue-speed-monitor-uuid",
                "type": "Activity",
                "name": "持续速度监测",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-stop-speed-monitor-uuid",
                "type": "Activity",
                "name": "停止速度监测",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-activate-brake-uuid",
                "type": "Activity",
                "name": "激活刹车",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "act-stop-wheels-uuid",
                "type": "Activity",
                "name": "停止轮子",
                "parentId": "pkg-behavior-uuid"
            },
            {
                "id": "trans-idle-to-riding-uuid",
                "type": "Transition",
                "sourceId": "ps-main-initial-uuid",
                "targetId": "state-riding-uuid",
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "trans-riding-to-braking-uuid",
                "type": "Transition",
                "sourceId": "state-riding-uuid",
                "targetId": "state-braking-uuid",
                "triggerIds": [
                    "event-brake-uuid"
                ],
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "trans-braking-to-stopped-uuid",
                "type": "Transition",
                "sourceId": "state-braking-uuid",
                "targetId": "state-stopped-uuid",
                "triggerIds": [
                    "event-vehicle-stopped-uuid"
                ],
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "trans-stopped-to-idle-uuid",
                "type": "Transition",
                "sourceId": "state-stopped-uuid",
                "targetId": "state-idle-uuid",
                "triggerIds": [
                    "event-user-exit-uuid"
                ],
                "parentId": "region-vehicle-main-uuid"
            },
            {
                "id": "pkg-uc-001",
                "type": "Package",
                "name": "主用例包",
                "parentId": "model-uc-001"
            },
            {
                "id": "actor-001",
                "type": "Actor",
                "name": "用户（骑行者）",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "actor-002",
                "type": "Actor",
                "name": "系统（自行车硬件）",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-001",
                "type": "UseCase",
                "name": "上车",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-002",
                "type": "UseCase",
                "name": "骑行",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-003",
                "type": "UseCase",
                "name": "踏板",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-004",
                "type": "UseCase",
                "name": "控制速度",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-005",
                "type": "UseCase",
                "name": "刹车",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-006",
                "type": "UseCase",
                "name": "减速停",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-007",
                "type": "UseCase",
                "name": "停止",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-008",
                "type": "UseCase",
                "name": "进入待命",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-009",
                "type": "UseCase",
                "name": "提示安全警告",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "usecase-010",
                "type": "UseCase",
                "name": "自动激活限制",
                "parentId": "pkg-usecases-uuid"
            },
            {
                "id": "assoc-001",
                "type": "Association",
                "sourceId": "actor-001",
                "targetId": "usecase-001",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-002",
                "type": "Association",
                "sourceId": "actor-001",
                "targetId": "usecase-002",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-003",
                "type": "Association",
                "sourceId": "actor-001",
                "targetId": "usecase-003",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-004",
                "type": "Association",
                "sourceId": "actor-001",
                "targetId": "usecase-004",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-005",
                "type": "Association",
                "sourceId": "actor-001",
                "targetId": "usecase-005",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-006",
                "type": "Association",
                "sourceId": "actor-001",
                "targetId": "usecase-006",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-007",
                "type": "Association",
                "sourceId": "actor-001",
                "targetId": "usecase-007",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-008",
                "type": "Association",
                "sourceId": "actor-002",
                "targetId": "usecase-008",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-009",
                "type": "Association",
                "sourceId": "actor-002",
                "targetId": "usecase-009",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "assoc-010",
                "type": "Association",
                "sourceId": "actor-002",
                "targetId": "usecase-010",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "include-001",
                "type": "Include",
                "sourceId": "usecase-001",
                "targetId": "usecase-003",
                "parentId": "usecase-001"
            },
            {
                "id": "include-002",
                "type": "Include",
                "sourceId": "usecase-001",
                "targetId": "usecase-004",
                "parentId": "usecase-001"
            },
            {
                "id": "include-003",
                "type": "Include",
                "sourceId": "usecase-005",
                "targetId": "usecase-006",
                "parentId": "usecase-005"
            },
            {
                "id": "include-004",
                "type": "Include",
                "sourceId": "usecase-007",
                "targetId": "usecase-008",
                "parentId": "usecase-007"
            },
            {
                "id": "extend-001",
                "type": "Extend",
                "sourceId": "usecase-009",
                "targetId": "usecase-005",
                "parentId": "usecase-009"
            },
            {
                "id": "extend-002",
                "type": "Extend",
                "sourceId": "usecase-010",
                "targetId": "usecase-004",
                "parentId": "usecase-010"
            },
            {
                "id": "pkg-unique-id",
                "type": "Package",
                "name": "ParametricDiagram",
                "parentId": "model-unique-id"
            },
            {
                "id": "block-vehicle",
                "type": "Block",
                "name": "ElectricVehicle",
                "parentId": "pkg-structure-uuid"
            },
            {
                "id": "prop-Speed",
                "type": "Property",
                "name": "Speed",
                "propertyKind": "value",
                "parentId": "block-vehicle",
                "typeId": "Real"
            },
            {
                "id": "prop-MaxSpeed",
                "type": "Property",
                "name": "MaxSpeed",
                "propertyKind": "value",
                "parentId": "block-vehicle",
                "typeId": "Real"
            },
            {
                "id": "prop-WeightLimit",
                "type": "Property",
                "name": "WeightLimit",
                "propertyKind": "value",
                "parentId": "block-vehicle",
                "typeId": "Real"
            },
            {
                "id": "prop-FrameMaterial",
                "type": "Property",
                "name": "FrameMaterial",
                "propertyKind": "value",
                "parentId": "block-vehicle",
                "typeId": "String"
            },
            {
                "id": "prop-WheelSpeed",
                "type": "Property",
                "name": "WheelSpeed",
                "propertyKind": "value",
                "parentId": "block-vehicle",
                "typeId": "Real"
            },
            {
                "id": "prop-WheelRadius",
                "type": "Property",
                "name": "WheelRadius",
                "propertyKind": "value",
                "parentId": "block-vehicle",
                "typeId": "Real"
            },
            {
                "id": "prop-π",
                "type": "Property",
                "name": "Pi",
                "propertyKind": "value",
                "parentId": "block-vehicle",
                "typeId": "Real"
            },
            {
                "id": "constraint-CalculateSpeed",
                "type": "ConstraintBlock",
                "name": "SpeedCalculation",
                "parentId": "pkg-libraries-uuid",
                "specification": {
                    "expression": "Speed = (WheelSpeed * WheelRadius * Pi) / 60",
                    "language": "English"
                }
            },
            {
                "id": "param-Speed",
                "type": "ConstraintParameter",
                "name": "Speed",
                "parentId": "constraint-CalculateSpeed",
                "typeId": "Real"
            },
            {
                "id": "param-WheelSpeed",
                "type": "ConstraintParameter",
                "name": "WheelSpeed",
                "parentId": "constraint-CalculateSpeed",
                "typeId": "Real"
            },
            {
                "id": "param-WheelRadius",
                "type": "ConstraintParameter",
                "name": "WheelRadius",
                "parentId": "constraint-CalculateSpeed",
                "typeId": "Real"
            },
            {
                "id": "param-Pi",
                "type": "ConstraintParameter",
                "name": "Pi",
                "parentId": "constraint-CalculateSpeed",
                "typeId": "Real"
            },
            {
                "id": "constraint-SpeedLimit",
                "type": "ConstraintBlock",
                "name": "SpeedLimitConstraint",
                "parentId": "pkg-libraries-uuid",
                "specification": {
                    "expression": "if Speed > MaxSpeed then LimitSpeed = MaxSpeed else LimitSpeed = Speed",
                    "language": "English"
                }
            },
            {
                "id": "param-LimitSpeed",
                "type": "ConstraintParameter",
                "name": "LimitSpeed",
                "parentId": "constraint-SpeedLimit",
                "typeId": "Real"
            },
            {
                "id": "constraint-AccelerationTime",
                "type": "ConstraintBlock",
                "name": "AccelerationTimeConstraint",
                "parentId": "pkg-libraries-uuid",
                "specification": {
                    "expression": "Time = 5 seconds for 0-25 km/h",
                    "language": "English"
                }
            },
            {
                "id": "param-Time",
                "type": "ConstraintParameter",
                "name": "Time",
                "parentId": "constraint-AccelerationTime",
                "typeId": "Real"
            },
            {
                "id": "constraint-BrakingDistance",
                "type": "ConstraintBlock",
                "name": "BrakingDistanceConstraint",
                "parentId": "pkg-libraries-uuid",
                "specification": {
                    "expression": "Distance <= 3 meters at 10 km/h",
                    "language": "English"
                }
            },
            {
                "id": "param-Distance",
                "type": "ConstraintParameter",
                "name": "Distance",
                "parentId": "constraint-BrakingDistance",
                "typeId": "Real"
            },
            {
                "id": "connection-Speed-WheelSpeed",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-WheelSpeed"
                },
                "end2": {
                    "partRefId": "constraint-CalculateSpeed",
                    "portRefId": "param-WheelSpeed"
                }
            },
            {
                "id": "connection-WheelRadius-WheelRadius",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-WheelRadius"
                },
                "end2": {
                    "partRefId": "constraint-CalculateSpeed",
                    "portRefId": "param-WheelRadius"
                }
            },
            {
                "id": "connection-Pi-Pi",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-π"
                },
                "end2": {
                    "partRefId": "constraint-CalculateSpeed",
                    "portRefId": "param-Pi"
                }
            },
            {
                "id": "connection-Speed-Speed",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-Speed"
                },
                "end2": {
                    "partRefId": "constraint-CalculateSpeed",
                    "portRefId": "param-Speed"
                }
            },
            {
                "id": "connection-SpeedLimit-SpeedLimit",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-Speed"
                },
                "end2": {
                    "partRefId": "constraint-SpeedLimit",
                    "portRefId": "param-LimitSpeed"
                }
            },
            {
                "id": "connection-MaxSpeed-MaxSpeed",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-MaxSpeed"
                },
                "end2": {
                    "partRefId": "constraint-SpeedLimit",
                    "portRefId": "param-MaxSpeed"
                }
            },
            {
                "id": "connection-AccelerationTime-Time",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-Time"
                },
                "end2": {
                    "partRefId": "constraint-AccelerationTime",
                    "portRefId": "param-Time"
                }
            },
            {
                "id": "connection-BrakingDistance-Distance",
                "type": "BindingConnector",
                "parentId": "pkg-structure-uuid",
                "end1": {
                    "propertyRefId": "prop-Distance"
                },
                "end2": {
                    "partRefId": "constraint-BrakingDistance",
                    "portRefId": "param-Distance"
                }
            }
        ]
    }
    print("开始生成完整的SysML XMI文件 (聚焦于参数图)...")

    generator = SysMLGenerator(json.dumps(parametric_focused_json_str))
    final_xmi = generator.generate_xmi()

    if final_xmi:
        output_filename = "bicycle_system_model_parametric_fixed.xmi"
        with open(output_filename, "w", encoding="utf-8") as f:
            f.write(final_xmi)
        print(f"\n成功！完整的XMI文件已保存到: {output_filename}")
        print("\n--- XMI 预览 ---")
        print(final_xmi)
    else:
        print("\n生成失败。请检查控制台输出的错误信息。")