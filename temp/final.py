import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
import traceback
from typing import Dict, List, Any, Optional

class UnifiedJsonToXmiConverter:
    """
    一个统一的转换器，能够将一个完整的、合并后的SysML JSON模型转换为一个XMI文件。
    """
    def __init__(self, json_data_str: str):
        self.json_data: Dict[str, Any] = json.loads(json_data_str)
        
        # 定义命名空间
        self.namespaces = {
            'xmi': "http://www.omg.org/spec/XMI/20131001",
            'uml': "http://www.omg.org/spec/UML/20131001",
            'sysml': "http://www.omg.org/spec/SysML/20181001/SysML"
        }
        
        # 内部状态
        self.elements_by_id: Dict[str, Any] = {}
        self.children_by_parent: Dict[str, List[str]] = {}
        self.xml_elements: Dict[str, ET.Element] = {} # 存储已创建的XML节点
        self.stereotypes_to_apply: List[Dict[str, Any]] = []

    def _register_namespaces(self):
        for prefix, uri in self.namespaces.items():
            ET.register_namespace(prefix, uri)

    def _preprocess_data(self):
        """预处理JSON数据，构建ID和父子关系映射。"""
        print("--- [1] Preprocessing Data ---")
        self.elements_by_id = {elem['id']: elem for elem in self.json_data.get('elements', [])}
        
        # 将顶层model也加入查找字典
        model_data = self.json_data.get('model', {})
        if model_data and 'id' in model_data:
            self.elements_by_id[model_data['id']] = model_data

        for elem_id, elem_data in self.elements_by_id.items():
            parent_id = elem_data.get("parentId")
            if parent_id:
                if parent_id not in self.children_by_parent:
                    self.children_by_parent[parent_id] = []
                self.children_by_parent[parent_id].append(elem_id)
        print(f"Data preprocessed. Found {len(self.elements_by_id)} total elements.")

    def _create_element(self, parent_xml_node: ET.Element, elem_id: str):
        """
        核心的递归创建函数。根据元素类型分发到不同的处理逻辑。
        """
        if elem_id in self.xml_elements:  # 避免重复创建
            return
            
        if elem_id not in self.elements_by_id:
            print(f"  [Warning] Element with ID '{elem_id}' not found in JSON data. Skipping.")
            return

        elem_data = self.elements_by_id[elem_id]
        elem_type = elem_data.get('type')
        
        # 根据类型选择合适的创建函数
        # 基础元素（包、块、Actor等）通常作为 packagedElement
        if elem_type in ["Package", "Block", "InterfaceBlock", "Actor", "UseCase", "Activity", "StateMachine", "Interaction", "Signal", "ValueType", "Enumeration", "ConstraintBlock", "Unit"]:
            self._create_packaged_element(parent_xml_node, elem_data)
        # 关系型元素，通常也是 packagedElement
        elif elem_type in ["Association", "Satisfy", "Verify", "DeriveReqt", "Include", "Extend"]:
            self._create_relationship_element(parent_xml_node, elem_data)
        # 其他元素类型，可能是嵌套元素，由其父元素在创建时处理
        # 例如 Property, Port, State, Transition 等
        else:
            # print(f"  [Info] Skipping element type '{elem_type}' in main creation loop (handled by parent).")
            pass

    def _create_packaged_element(self, parent_xml_node: ET.Element, elem_data: Dict[str, Any]):
        """创建作为 uml:Package 或 uml:Model 的直接子元素的节点。"""
        elem_id = elem_data['id']
        if elem_id in self.xml_elements: return
        
        elem_type = elem_data['type']
        attrs = {'xmi:id': elem_id, 'name': elem_data.get('name', '')}
        
        # 映射JSON类型到UML XMI类型
        type_map = {
            "Package": "uml:Package", "Block": "uml:Class", "InterfaceBlock": "uml:Class",
            "Actor": "uml:Actor", "UseCase": "uml:UseCase", "Activity": "uml:Activity",
            "StateMachine": "uml:StateMachine", "Interaction": "uml:Interaction",
            "Signal": "uml:Signal", "ValueType": "uml:DataType", "Enumeration": "uml:Enumeration",
            "ConstraintBlock": "uml:Class" # ConstraintBlock也是一种Class
        }
        
        if elem_type not in type_map:
             print(f"  [Warning] Unhandled packaged element type: {elem_type}. Skipping {elem_id}.")
             return
             
        attrs['xmi:type'] = type_map[elem_type]
        if elem_data.get('isAbstract'): attrs['isAbstract'] = 'true'

        xml_elem = ET.SubElement(parent_xml_node, 'packagedElement', attrs)
        self.xml_elements[elem_id] = xml_elem
        print(f"  Created <packagedElement> for {elem_type} '{elem_data.get('name')}' ({elem_id})")

        # 添加构造型应用到待处理列表
        self.stereotypes_to_apply.append({'base_id': elem_id, 'json_type': elem_type})
        
        # --- 递归处理特定元素的内部结构 ---
        if elem_type == 'Block' or elem_type == 'InterfaceBlock':
            self._create_block_content(xml_elem, elem_data)
        elif elem_type == 'Activity':
            self._create_activity_content(xml_elem, elem_data)
        elif elem_type == 'StateMachine':
            self._create_statemachine_content(xml_elem, elem_data)
        elif elem_type == 'Interaction':
            self._create_interaction_content(xml_elem, elem_data)
        elif elem_type == 'ConstraintBlock':
            self._create_constraintblock_content(xml_elem, elem_data)

        # --- 递归创建子包/元素 ---
        if elem_id in self.children_by_parent:
            for child_id in self.children_by_parent[elem_id]:
                self._create_element(xml_elem, child_id)

    def _create_relationship_element(self, parent_xml_node: ET.Element, elem_data: Dict[str, Any]):
        """创建关系型元素，如 Association, Abstraction 等。"""
        elem_id = elem_data['id']
        if elem_id in self.xml_elements: return
        
        elem_type = elem_data['type']
        attrs = {'xmi:id': elem_id, 'name': elem_data.get('name', '')}
        
        if elem_type == "Association":
            attrs['xmi:type'] = "uml:Association"
            xml_elem = ET.SubElement(parent_xml_node, 'packagedElement', attrs)
            
            for end_id in elem_data.get('memberEndIds', []):
                ET.SubElement(xml_elem, 'memberEnd', {'xmi:idref': end_id})
                # We need to find the Property element and set its 'association' attribute
                if end_id in self.xml_elements:
                    self.xml_elements[end_id].set('association', elem_id)
            print(f"  Created <Association> '{elem_data.get('name')}' ({elem_id})")
        
        elif elem_type in ["Satisfy", "Verify", "DeriveReqt", "Include", "Extend"]:
            attrs['xmi:type'] = "uml:Abstraction" if elem_type in ["Satisfy", "Verify", "DeriveReqt"] else f"uml:{elem_type}"
            
            # Include/Extend are owned by the source UseCase, not the package
            source_id_for_rel = elem_data.get('sourceId') or elem_data.get('sourceRequirementId')
            actual_parent_xml = self.xml_elements.get(source_id_for_rel) if elem_type in ["Include", "Extend"] else parent_xml_node
            
            if actual_parent_xml is None:
                print(f"  [Error] Parent for {elem_type} '{elem_id}' not found. Skipping.")
                return

            tag_name_map = {"Satisfy": "packagedElement", "Verify": "packagedElement", "DeriveReqt": "packagedElement", "Include": "include", "Extend": "extend"}
            tag = tag_name_map[elem_type]

            xml_elem = ET.SubElement(actual_parent_xml, tag, attrs)
            
            # Client/Supplier for Abstractions
            if elem_type in ["Satisfy", "Verify", "DeriveReqt"]:
                client_id = elem_data.get('blockId') or elem_data.get('testCaseId') or elem_data.get('derivedRequirementId')
                supplier_id = elem_data.get('requirementId') or elem_data.get('sourceRequirementId')
                if client_id: ET.SubElement(xml_elem, 'client', {'xmi:idref': client_id})
                if supplier_id: ET.SubElement(xml_elem, 'supplier', {'xmi:idref': supplier_id})
            # extendedCase/addition for UseCase relationships
            elif elem_type == "Extend":
                xml_elem.set('extendedCase', elem_data['targetId'])
            elif elem_type == "Include":
                xml_elem.set('addition', elem_data['targetId'])
            print(f"  Created <{tag}> for {elem_type} ({elem_id})")
        
        else:
            return # Should not happen

        self.xml_elements[elem_id] = xml_elem
        self.stereotypes_to_apply.append({'base_id': elem_id, 'json_type': elem_type})

    # --- Content Creation Helpers for Complex Elements ---
    
    def _create_block_content(self, block_xml_node: ET.Element, block_data: Dict[str, Any]):
        """为Block/Class创建内部元素：Properties, Ports, Operations, Connectors。"""
        block_id = block_data['id']
        child_ids = self.children_by_parent.get(block_id, [])

        for child_id in child_ids:
            child_data = self.elements_by_id.get(child_id)
            if not child_data: continue
            
            child_type = child_data['type']
            child_attrs = {'xmi:id': child_id, 'name': child_data.get('name')}
            tag = None
            
            if child_type == "Property":
                tag = 'ownedAttribute'
                child_attrs['xmi:type'] = 'uml:Property'
                child_attrs['visibility'] = child_data.get('visibility', 'public')
                child_attrs['aggregation'] = child_data.get('aggregation', 'none')
                if child_data.get('typeId'): child_attrs['type'] = child_data['typeId']
                
            elif child_type in ["FullPort", "ProxyPort"]:
                tag = 'ownedAttribute' # Ports are properties
                child_attrs['xmi:type'] = 'uml:Port'
                child_attrs['visibility'] = child_data.get('visibility', 'public')
                child_attrs['aggregation'] = 'composite'
                if child_data.get('typeId'): child_attrs['type'] = child_data['typeId']
                
            elif child_type == "Operation":
                tag = 'ownedOperation'
                child_attrs['xmi:type'] = 'uml:Operation'
                child_attrs['visibility'] = child_data.get('visibility', 'public')
                
            elif child_type in ["AssemblyConnector", "BindingConnector"]:
                tag = 'ownedConnector'
                child_attrs['xmi:type'] = 'uml:Connector'
                child_attrs['visibility'] = 'public'

            if tag:
                child_xml_elem = ET.SubElement(block_xml_node, tag, child_attrs)
                self.xml_elements[child_id] = child_xml_elem
                # Add stereotypes for properties/ports
                self.stereotypes_to_apply.append({'base_id': child_id, 'json_type': child_type})

                # Handle specifics
                if child_type == "Operation":
                    for param_data in child_data.get('parameters', []):
                        param_attrs = {'xmi:id': param_data['id'], 'name': param_data.get('name'), 'xmi:type': 'uml:Parameter', 'direction': param_data.get('direction', 'in')}
                        param_xml = ET.SubElement(child_xml_elem, 'ownedParameter', param_attrs)
                        if param_data.get('typeId'): ET.SubElement(param_xml, 'type', {'xmi:idref': param_data['typeId']})

                elif child_type in ["AssemblyConnector", "BindingConnector"]:
                    self._create_connector_ends(child_xml_elem, child_data)


    def _create_connector_ends(self, connector_xml_node: ET.Element, conn_data: Dict[str, Any]):
        """为Connector创建端点。"""
        for end_key in ['end1', 'end2']:
            end_data = conn_data.get(end_key)
            if not end_data: continue
            end_attrs = {'xmi:id': end_data['id'], 'xmi:type': 'uml:ConnectorEnd'}
            
            part_ref = end_data.get('partRefId')
            prop_ref = end_data.get('propertyRefId') # For Binding
            port_ref = end_data.get('portRefId')
            
            if part_ref: end_attrs['partWithPort'] = part_ref
            
            # Role is the port for assembly, or the property for binding
            role_id = port_ref if conn_data['type'] == 'AssemblyConnector' else prop_ref
            if role_id:
                end_attrs['role'] = role_id
            
            end_xml = ET.SubElement(connector_xml_node, 'end', end_attrs)
            self.xml_elements[end_data['id']] = end_xml

            # Add NestedConnectorEnd stereotype if needed
            if part_ref and port_ref:
                self.stereotypes_to_apply.append({
                    'base_id': end_data['id'], 'json_type': 'NestedConnectorEnd',
                    'extra_attrs': {'propertyPath': part_ref}
                })

    def _create_constraintblock_content(self, cb_xml_node: ET.Element, cb_data: Dict[str, Any]):
        """为ConstraintBlock创建内部内容，主要是约束和参数端口。"""
        # Create the constraint rule
        spec_data = cb_data.get('specification', {})
        if 'expression' in spec_data:
            rule_id = f"{cb_data['id']}_rule"
            rule_xml = ET.SubElement(cb_xml_node, 'ownedRule', {'xmi:id': rule_id, 'xmi:type': 'uml:Constraint'})
            ET.SubElement(rule_xml, 'constrainedElement', {'xmi:idref': cb_data['id']})
            spec_xml = ET.SubElement(rule_xml, 'specification', {'xmi:id': f"{rule_id}_spec", 'xmi:type': 'uml:OpaqueExpression'})
            ET.SubElement(spec_xml, 'body').text = spec_data['expression']

        # Create parameter ports
        child_ids = self.children_by_parent.get(cb_data['id'], [])
        for child_id in child_ids:
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get('type') == 'ConstraintParameter':
                port_attrs = {'xmi:id': child_id, 'name': child_data.get('name'), 'xmi:type': 'uml:Port', 'aggregation': 'composite'}
                port_xml = ET.SubElement(cb_xml_node, 'ownedAttribute', port_attrs) # Parameters are modeled as ports
                self.xml_elements[child_id] = port_xml
                self.stereotypes_to_apply.append({'base_id': child_id, 'json_type': 'ConstraintParameter'})
                if child_data.get('typeId') == "Real": # Handle primitive types
                    ET.SubElement(port_xml, 'type', {'href': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real'})


    def _create_activity_content(self, activity_xml_node: ET.Element, activity_data: Dict[str, Any]):
        """为Activity创建内部内容：Nodes, Edges, Groups。"""
        # 1. 创建所有节点（除了Pin）
        for node_id in activity_data.get('nodes', []):
            node_data = self.elements_by_id.get(node_id)
            if node_data and node_data['type'] not in ['InputPin', 'OutputPin']:
                self._create_activity_node(activity_xml_node, node_data)
        
        # 2. 创建所有边
        for edge_id in activity_data.get('edges', []):
            edge_data = self.elements_by_id.get(edge_id)
            if edge_data: self._create_activity_edge(activity_xml_node, edge_data)

        # 3. 创建所有分区
        for group_id in activity_data.get('groups', []):
            group_data = self.elements_by_id.get(group_id)
            if group_data: self._create_activity_partition(activity_xml_node, group_data)
            

    def _create_activity_node(self, activity_xml_node, node_data):
        """为Activity创建单个节点。"""
        node_id = node_data['id']
        if node_id in self.xml_elements: return
        
        node_type = node_data['type']
        attrs = {'xmi:id': node_id, 'name': node_data.get('name'), 'xmi:type': f"uml:{node_type}", 'visibility': 'public'}
        
        if node_type == "CallBehaviorAction" and node_data.get('behavior'):
            attrs['behavior'] = node_data['behavior']
        
        node_xml = ET.SubElement(activity_xml_node, 'node', attrs)
        self.xml_elements[node_id] = node_xml

        # 为 CallBehaviorAction 创建 Pins
        if node_type == "CallBehaviorAction":
            child_ids = self.children_by_parent.get(node_id, [])
            for pin_id in child_ids:
                pin_data = self.elements_by_id.get(pin_id)
                if pin_data and pin_data['type'] in ['InputPin', 'OutputPin']:
                    pin_tag = 'argument' if pin_data['type'] == 'InputPin' else 'result'
                    pin_attrs = {'xmi:id': pin_id, 'name': pin_data.get('name'), 'xmi:type': f"uml:{pin_data['type']}", 'visibility': 'public'}
                    if pin_data.get('typeId'): pin_attrs['type'] = pin_data['typeId']
                    ET.SubElement(node_xml, pin_tag, pin_attrs)

    def _create_activity_edge(self, activity_xml_node, edge_data):
        """为Activity创建单个边。"""
        edge_id = edge_data['id']
        if edge_id in self.xml_elements: return
        attrs = {'xmi:id': edge_id, 'name': edge_data.get('name'), 'xmi:type': f"uml:{edge_data['type']}", 'visibility': 'public'}
        attrs['source'] = edge_data.get('sourceId')
        attrs['target'] = edge_data.get('targetId')
        edge_xml = ET.SubElement(activity_xml_node, 'edge', attrs)
        self.xml_elements[edge_id] = edge_xml

        # 添加Guard
        guard_text = edge_data.get('guard')
        if guard_text:
            guard_xml = ET.SubElement(edge_xml, 'guard', {'xmi:id': f"{edge_id}_guard", 'xmi:type': 'uml:Constraint'})
            spec_xml = ET.SubElement(guard_xml, 'specification', {'xmi:id': f"{edge_id}_spec", 'xmi:type': 'uml:OpaqueExpression'})
            ET.SubElement(spec_xml, 'body').text = guard_text
            
    def _create_activity_partition(self, activity_xml_node, group_data):
        """为Activity创建单个分区。"""
        group_id = group_data['id']
        if group_id in self.xml_elements: return
        attrs = {'xmi:id': group_id, 'name': group_data.get('name'), 'xmi:type': 'uml:ActivityPartition', 'isDimension': 'true'}
        if group_data.get('representsId'): attrs['represents'] = group_data['representsId']
        group_xml = ET.SubElement(activity_xml_node, 'group', attrs)
        self.xml_elements[group_id] = group_xml
        self.stereotypes_to_apply.append({'base_id': group_id, 'json_type': 'ActivityPartition'})

        # 添加分区包含的节点
        for node_id in group_data.get('nodeIds', []):
            if node_id in self.xml_elements: # 确保节点已经被创建
                ET.SubElement(group_xml, 'node', {'xmi:idref': node_id})

    def _create_statemachine_content(self, sm_xml_node: ET.Element, sm_data: Dict[str, Any]):
        # 状态机的内容主要是Region
        child_ids = self.children_by_parent.get(sm_data['id'], [])
        for child_id in child_ids:
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data['type'] == 'Region':
                self._create_region_content(sm_xml_node, child_data)
    
    def _create_region_content(self, parent_xml_node: ET.Element, region_data: Dict[str, Any]):
        region_id = region_data['id']
        attrs = {'xmi:id': region_id, 'name': region_data.get('name'), 'xmi:type': 'uml:Region', 'visibility': 'public'}
        region_xml = ET.SubElement(parent_xml_node, 'region', attrs)
        self.xml_elements[region_id] = region_xml

        # 1. 创建所有 Subvertices (State, Pseudostate)
        child_ids = self.children_by_parent.get(region_id, [])
        for child_id in child_ids:
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data['type'] in ['State', 'Pseudostate', 'FinalState']:
                self._create_vertex_content(region_xml, child_data)
        
        # 2. 创建所有 Transitions
        for child_id in child_ids:
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data['type'] == 'Transition':
                self._create_transition_content(region_xml, child_data)

    def _create_vertex_content(self, region_xml_node: ET.Element, vertex_data: Dict[str, Any]):
        vertex_id = vertex_data['id']
        if vertex_id in self.xml_elements: return
        
        vertex_type = vertex_data['type']
        attrs = {'xmi:id': vertex_id, 'name': vertex_data.get('name'), 'xmi:type': f"uml:{vertex_type}", 'visibility': 'public'}

        if vertex_type == 'Pseudostate':
            attrs['kind'] = vertex_data.get('kind', 'initial')

        vertex_xml = ET.SubElement(region_xml_node, 'subvertex', attrs)
        self.xml_elements[vertex_id] = vertex_xml

        # 如果是复合状态，处理其内部
        if vertex_type == 'State' and vertex_data.get('isComposite'):
            child_ids = self.children_by_parent.get(vertex_id, [])
            for child_id in child_ids:
                child_data = self.elements_by_id.get(child_id)
                if child_data and child_data['type'] == 'Region':
                    self._create_region_content(vertex_xml, child_data)
        
        # 处理 Entry/Exit/Do
        if vertex_type == 'State':
            if 'entry' in vertex_data: self._create_behavior_call(vertex_xml, 'entry', vertex_data['entry'])
            if 'exit' in vertex_data: self._create_behavior_call(vertex_xml, 'exit', vertex_data['exit'])
            if 'doActivity' in vertex_data: self._create_behavior_call(vertex_xml, 'doActivity', vertex_data['doActivity'])


    def _create_transition_content(self, region_xml_node: ET.Element, trans_data: Dict[str, Any]):
        trans_id = trans_data['id']
        if trans_id in self.xml_elements: return

        attrs = {'xmi:id': trans_id, 'name': trans_data.get('name'), 'xmi:type': 'uml:Transition', 'visibility': 'public'}
        attrs['source'] = trans_data['sourceId']
        attrs['target'] = trans_data['targetId']

        trans_xml = ET.SubElement(region_xml_node, 'transition', attrs)
        self.xml_elements[trans_id] = trans_xml

        # Guard
        if 'guard' in trans_data:
             guard_xml = ET.SubElement(trans_xml, 'guard', {'xmi:id': f"{trans_id}_guard", 'xmi:type': 'uml:Constraint'})
             spec_xml = ET.SubElement(guard_xml, 'specification', {'xmi:id': f"{trans_id}_spec", 'xmi:type': 'uml:OpaqueExpression'})
             ET.SubElement(spec_xml, 'body').text = trans_data['guard']['expression']

        # Effect
        if 'effect' in trans_data:
            self._create_behavior_call(trans_xml, 'effect', trans_data['effect'])
            
        # Trigger
        for i, trigger_id in enumerate(trans_data.get('triggerIds', [])):
            trigger_xml = ET.SubElement(trans_xml, 'trigger', {'xmi:id': f"{trans_id}_trigger_{i}", 'xmi:type': 'uml:Trigger'})
            ET.SubElement(trigger_xml, 'event', {'xmi:idref': trigger_id})
            

    def _create_behavior_call(self, parent_xml_node: ET.Element, tag: str, behavior_data: Dict[str, Any]):
        """为State或Transition创建Entry/Exit/Do/Effect行为。"""
        # 这个逻辑在SysML XMI中比较复杂，通常是创建一个匿名的Activity来调用一个已定义的Behavior
        # 简化处理：如果JSON中有wrapperActivityId和calledBehaviorId，我们创建它
        wrapper_id = behavior_data.get('wrapperActivityId')
        called_id = behavior_data.get('calledBehaviorId')
        if not (wrapper_id and called_id): return
        
        activity_attrs = {'xmi:id': wrapper_id, 'xmi:type': 'uml:Activity'}
        activity_xml = ET.SubElement(parent_xml_node, tag, activity_attrs)
        
        # 内部结构：Initial -> CallBehaviorAction -> Final
        cba_id = f"{wrapper_id}_cba"
        cba_xml = ET.SubElement(activity_xml, 'node', {'xmi:id': cba_id, 'xmi:type': 'uml:CallBehaviorAction', 'behavior': called_id})


    def _create_interaction_content(self, interaction_xml_node: ET.Element, inter_data: Dict[str, Any]):
        # 创建 Lifelines, Messages, Fragments
        child_ids = self.children_by_parent.get(inter_data['id'], [])
        for child_id in child_ids:
            child_data = self.elements_by_id.get(child_id)
            if not child_data: continue

            tag, attrs = None, {'xmi:id': child_id, 'name': child_data.get('name')}
            
            if child_data['type'] == 'Lifeline':
                tag = 'lifeline'; attrs['xmi:type'] = 'uml:Lifeline'
                if child_data.get('representsId'): attrs['represents'] = child_data['representsId']
            elif child_data['type'] == 'Message':
                tag = 'message'; attrs['xmi:type'] = 'uml:Message'
                if child_data.get('messageSort'): attrs['messageSort'] = child_data['messageSort']
                if child_data.get('sendEventId'): attrs['sendEvent'] = child_data['sendEventId']
                if child_data.get('receiveEventId'): attrs['receiveEvent'] = child_data['receiveEventId']
                if child_data.get('signatureId'): attrs['signature'] = child_data['signatureId']
            elif child_data['type'] in ['MessageOccurrenceSpecification', 'DestructionOccurrenceSpecification', 'CombinedFragment']:
                tag = 'fragment'; attrs['xmi:type'] = f"uml:{child_data['type']}"
                if child_data.get('coveredId'): attrs['covered'] = child_data['coveredId']
                if child_data.get('messageId'): attrs['message'] = child_data['messageId']
                if child_data.get('interactionOperator'): attrs['interactionOperator'] = child_data['interactionOperator']
            
            if tag:
                child_xml = ET.SubElement(interaction_xml_node, tag, attrs)
                self.xml_elements[child_id] = child_xml
                
                # 为Lifeline后期添加coveredBy做准备
                if child_data['type'] == 'Lifeline':
                    lifeline_covered_by = []
                    for frag_id in inter_data.get('fragmentIds',[]):
                        frag_data = self.elements_by_id.get(frag_id)
                        if frag_data and frag_data.get('coveredId') == child_id:
                            lifeline_covered_by.append(frag_id)
                    child_xml.set("_covered_by_temp", ",".join(lifeline_covered_by))


    def _apply_stereotypes(self, xmi_root: ET.Element):
        """在所有基础元素创建后，统一应用构造型。"""
        print("--- [3] Applying Stereotypes ---")
        stereo_map = {
            # BDD/IBD
            "Block": ("sysml:Block", "base_Class"),
            "InterfaceBlock": ("sysml:Block", "base_Class"), # InterfaceBlock也应用sysml:Block
            "ValueType": ("sysml:ValueType", "base_DataType"),
            "Property": ("sysml:PartProperty", "base_Property"), # 默认
            "FullPort": ("sysml:FlowPort", "base_Port"), # 简化映射
            "ProxyPort": ("sysml:FlowPort", "base_Port"), # 简化映射
            "AssemblyConnector": ("sysml:BindingConnector", "base_Connector"), # 简化映射
            "BindingConnector": ("sysml:BindingConnector", "base_Connector"),
            "NestedConnectorEnd": ("sysml:NestedConnectorEnd", "base_ConnectorEnd"),
            # REQ
            "Requirement": ("sysml:Requirement", "base_Class"),
            "TestCase": ("sysml:TestCase", "base_Activity"),
            "Satisfy": ("sysml:Satisfy", "base_Abstraction"),
            "Verify": ("sysml:Verify", "base_Abstraction"),
            "DeriveReqt": ("sysml:DeriveReqt", "base_Abstraction"),
            # ACT
            "ActivityPartition": ("sysml:AllocateActivityPartition", "base_ActivityPartition"),
            # PARAM
            "ConstraintBlock": ("sysml:ConstraintBlock", "base_Class"),
            "ConstraintParameter": ("sysml:ConstraintParameter", "base_Port")
        }
        for item in self.stereotypes_to_apply:
            base_id = item['base_id']
            json_type = item['json_type']
            
            # 特殊处理Property的多种构造型
            if json_type == "Property":
                prop_data = self.elements_by_id.get(base_id, {})
                prop_kind = prop_data.get('propertyKind')
                if prop_kind == 'value': stereo_map['Property'] = ("sysml:ValueProperty", "base_Property")
                elif prop_kind == 'part': stereo_map['Property'] = ("sysml:PartProperty", "base_Property")
                else: stereo_map['Property'] = ("sysml:ReferenceProperty", "base_Property")

            if json_type in stereo_map:
                stereo_tag, base_attr = stereo_map[json_type]
                stereo_attrs = {
                    'xmi:id': f"{base_id}_stereotype_app",
                    base_attr: base_id
                }
                # 添加额外属性
                if 'extra_attrs' in item:
                    stereo_attrs.update(item['extra_attrs'])
                
                # 为Requirement添加Id和Text
                if json_type == "Requirement":
                    req_data = self.elements_by_id.get(base_id)
                    if req_data:
                        stereo_attrs['Id'] = req_data.get('reqId', '')
                        stereo_attrs['Text'] = req_data.get('text', '')
                
                ET.SubElement(xmi_root, stereo_tag, stereo_attrs)


    def generate_xmi(self) -> Optional[str]:
        """主生成函数。"""
        self._register_namespaces()
        self._preprocess_data()
        
        xmi_root = ET.Element('xmi:XMI', {'xmi:version': '2.5'})

        model_data = self.json_data.get('model', {})
        if not model_data:
             print("[Error] JSON is missing 'model' object.")
             return None
        
        model_attrs = {'xmi:id': model_data['id'], 'name': model_data.get('name'), 'xmi:type': 'uml:Model'}
        model_xml_node = ET.SubElement(xmi_root, 'uml:Model', model_attrs)
        self.xml_elements[model_data['id']] = model_xml_node

        print("--- [2] Creating XML Element Structure ---")
        # 从模型的子元素开始递归创建
        root_element_ids = self.children_by_parent.get(model_data['id'], [])
        for root_id in sorted(root_element_ids): # 排序以保证确定性
            self._create_element(model_xml_node, root_id)

        # 应用构造型
        self._apply_stereotypes(xmi_root)
        
        # --- Pretty Print ---
        print("--- [4] Finalizing and Formatting XML ---")
        try:
            ET.indent(xmi_root, space="  ")
            rough_string = ET.tostring(xmi_root, encoding='utf-8', method='xml')
            reparsed = minidom.parseString(rough_string)
            pretty_xml = reparsed.toprettyxml(indent="  ", encoding="utf-8").decode('utf-8')
            
            xml_declaration = '<?xml version="1.0" encoding="UTF-8"?>\n'
            if pretty_xml.startswith('<?xml'):
                pretty_xml = pretty_xml.split('\n', 1)[1]

            return xml_declaration + pretty_xml
        except Exception as e:
            print(f"Error during XML pretty printing: {e}")
            traceback.print_exc()
            return ET.tostring(xmi_root, encoding='unicode')

if __name__ == '__main__':
    # 假设 'merged_json_output.json' 文件包含了之前步骤生成的完整JSON
    try:

        full_json_str = """

{
  "model": {
    "id": "model-master-system-uuid",
    "name": "Integrated Vehicle AirConditioner System"
  },
  "elements": [
    {
      "id": "pkg-reqs-uuid",
      "type": "Package",
      "name": "Requirements",
      "parentId": "model-master-system-uuid"
    },
    {
      "id": "pkg-usecases-uuid",
      "type": "Package",
      "name": "UseCases",
      "parentId": "model-master-system-uuid"
    },
    {
      "id": "pkg-structure-uuid",
      "type": "Package",
      "name": "SystemStructure",
      "parentId": "model-master-system-uuid"
    },
    {
      "id": "pkg-behavior-uuid",
      "type": "Package",
      "name": "SystemBehavior",
      "parentId": "model-master-system-uuid"
    },
    {
      "id": "pkg-libraries-uuid",
      "type": "Package",
      "name": "Libraries",
      "parentId": "model-master-system-uuid"
    },
    {
      "id": "req-air-quality-control-uuid",
      "type": "Requirement",
      "name": "空气质量监测与净化",
      "reqId": "R1",
      "text": "系统检测PM2.5、VOC、CO2指标范围，超标时自动启用空气净化并提示用户更换滤芯",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-smart-comfort-uuid",
      "type": "Requirement",
      "name": "支持“智能舒适”模式",
      "reqId": "R2",
      "text": "系统根据传感器数据自动调节温度、风速和空气循环，目标参数范围",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-heat-pump-uuid",
      "type": "Requirement",
      "name": "热泵技术应用",
      "reqId": "R3",
      "text": "采用热泵技术，制冷/制热能力、响应时间、能效比要求",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-fan-speed-uuid",
      "type": "Requirement",
      "name": "多档位与风速调节",
      "reqId": "R4",
      "text": "提供4个档位和自动模式，风速范围及响应时间",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-temperature-control-uuid",
      "type": "Requirement",
      "name": "温度控制",
      "reqId": "R5",
      "text": "调节目标温度范围、误差、响应时间，采用PID算法",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-air-cycle-switch-uuid",
      "type": "Requirement",
      "name": "空气循环切换",
      "reqId": "R6",
      "text": "支持外循环和内循环切换，自动或手动，切换延迟",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-environment-stability-uuid",
      "type": "Requirement",
      "name": "环境适应性",
      "reqId": "R7",
      "text": "在-20°C至50°C环境下稳定运行，启动时间、响应时间、连续运行时间",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-CAN-compatibility-uuid",
      "type": "Requirement",
      "name": "CAN总线兼容",
      "reqId": "R8",
      "text": "支持CAN 2.0B协议，数据速率，标准和扩展帧",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-safety-standards-uuid",
      "type": "Requirement",
      "name": "安全标准符合",
      "reqId": "R9",
      "text": "符合ISO 26262标准，故障检测与隔离",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-vibration-resistance-uuid",
      "type": "Requirement",
      "name": "振动与冲击适应性",
      "reqId": "R10",
      "text": "在振动频率和振幅范围内正常工作，符合相关标准",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-hardware-size-uuid",
      "type": "Requirement",
      "name": "硬件尺寸限制",
      "reqId": "R11",
      "text": "尺寸不超过规定范围，便于安装",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-reliability-uuid",
      "type": "Requirement",
      "name": "MTBF与故障响应",
      "reqId": "R12",
      "text": "MTBF≥10,000小时，关键故障响应时间≤1秒",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-performance-uuid",
      "type": "Requirement",
      "name": "能耗与噪声指标",
      "reqId": "R13",
      "text": "最大能耗≤1500W，噪声≤45 dB(A)",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "req-user-interface-uuid",
      "type": "Requirement",
      "name": "用户界面与远程诊断",
      "reqId": "R14",
      "text": "多语言界面，支持远程诊断和维护",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-air-quality-verify-uuid",
      "type": "TestCase",
      "name": "空气质量检测与净化验证",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-smart-comfort-verify-uuid",
      "type": "TestCase",
      "name": "智能舒适模式调节验证",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-heat-pump-verify-uuid",
      "type": "TestCase",
      "name": "热泵性能验证",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-fan-speed-verify-uuid",
      "type": "TestCase",
      "name": "风速调节响应验证",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-temperature-verify-uuid",
      "type": "TestCase",
      "name": "温度控制精度验证",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-air-cycle-verify-uuid",
      "type": "TestCase",
      "name": "空气循环切换验证",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-environment-verify-uuid",
      "type": "TestCase",
      "name": "环境适应性测试",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-CAN-compatibility-verify-uuid",
      "type": "TestCase",
      "name": "CAN通信兼容性测试",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "tc-safety-verify-uuid",
      "type": "TestCase",
      "name": "安全标准符合性验证",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-derive-airquality-smartmode-uuid",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-air-quality-control-uuid",
      "derivedRequirementId": "req-smart-comfort-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-derive-airquality-heatpump-uuid",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-air-quality-control-uuid",
      "derivedRequirementId": "req-heat-pump-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-derive-airquality-fanspeed-uuid",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-air-quality-control-uuid",
      "derivedRequirementId": "req-fan-speed-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-derive-airquality-temp-uuid",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-air-quality-control-uuid",
      "derivedRequirementId": "req-temperature-control-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-derive-airquality-aircycle-uuid",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-air-quality-control-uuid",
      "derivedRequirementId": "req-air-cycle-switch-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-derive-system-environment-uuid",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-environment-stability-uuid",
      "derivedRequirementId": "req-smart-comfort-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-derive-communication-cansupport-uuid",
      "type": "DeriveReqt",
      "sourceRequirementId": "req-CAN-compatibility-uuid",
      "derivedRequirementId": "req-safety-standards-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "actor-001",
      "type": "Actor",
      "name": "驾驶员",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-001",
      "type": "UseCase",
      "name": "调节温度",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-002",
      "type": "UseCase",
      "name": "监测温度",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-003",
      "type": "UseCase",
      "name": "比较温度",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-004",
      "type": "UseCase",
      "name": "启动制冷",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-005",
      "type": "UseCase",
      "name": "调节风速",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-006",
      "type": "UseCase",
      "name": "PID调节",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-007",
      "type": "UseCase",
      "name": "自动调节",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-008",
      "type": "UseCase",
      "name": "显示调节状态",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-009",
      "type": "UseCase",
      "name": "进入维持状态",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-010",
      "type": "UseCase",
      "name": "快速调节",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-011",
      "type": "UseCase",
      "name": "检测传感器故障",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-012",
      "type": "UseCase",
      "name": "切换到故障状态",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-013",
      "type": "UseCase",
      "name": "提示维护",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-014",
      "type": "UseCase",
      "name": "切换到手动加热",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-015",
      "type": "UseCase",
      "name": "启动电加热器",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-016",
      "type": "UseCase",
      "name": "调节加热输出",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-017",
      "type": "UseCase",
      "name": "监测压缩机故障",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-018",
      "type": "UseCase",
      "name": "断开压缩机",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-019",
      "type": "UseCase",
      "name": "进入Fault状态",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-020",
      "type": "UseCase",
      "name": "提示用户",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-021",
      "type": "UseCase",
      "name": "监测空气净化器故障",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-022",
      "type": "UseCase",
      "name": "提示更换滤芯",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "usecase-023",
      "type": "UseCase",
      "name": "暂停净化功能",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-001",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-001",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-002",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-002",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-003",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-003",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-004",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-014",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-005",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-015",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-006",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-016",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-007",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-020",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-008",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-021",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-009",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-022",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "assoc-010",
      "type": "Association",
      "sourceId": "actor-001",
      "targetId": "usecase-023",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "include-001",
      "type": "Include",
      "sourceId": "usecase-001",
      "targetId": "usecase-002",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "include-002",
      "type": "Include",
      "sourceId": "usecase-001",
      "targetId": "usecase-003",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "include-003",
      "type": "Include",
      "sourceId": "usecase-002",
      "targetId": "usecase-003",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "include-004",
      "type": "Include",
      "sourceId": "usecase-001",
      "targetId": "usecase-004",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "include-005",
      "type": "Include",
      "sourceId": "usecase-005",
      "targetId": "usecase-006",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "extend-001",
      "type": "Extend",
      "sourceId": "usecase-010",
      "targetId": "usecase-004",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "extend-002",
      "type": "Extend",
      "sourceId": "usecase-010",
      "targetId": "usecase-015",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "extend-003",
      "type": "Extend",
      "sourceId": "usecase-011",
      "targetId": "usecase-012",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "extend-004",
      "type": "Extend",
      "sourceId": "usecase-011",
      "targetId": "usecase-020",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "extend-005",
      "type": "Extend",
      "sourceId": "usecase-017",
      "targetId": "usecase-019",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "extend-006",
      "type": "Extend",
      "sourceId": "usecase-021",
      "targetId": "usecase-022",
      "parentId": "pkg-usecases-uuid"
    },
    {
      "id": "unit-percent-uuid",
      "type": "Unit",
      "name": "%",
      "parentId": "pkg-libraries-uuid",
      "symbol": "%"
    },
    {
      "id": "unit-degree-uuid",
      "type": "Unit",
      "name": "Degree Celsius",
      "parentId": "pkg-libraries-uuid",
      "symbol": "°C"
    },
    {
      "id": "vt-temperature-uuid",
      "type": "ValueType",
      "name": "Temperature",
      "parentId": "pkg-libraries-uuid",
      "baseType": "Real",
      "unitId": "unit-degree-uuid"
    },
    {
      "id": "enum-pm25-uuid",
      "type": "Enumeration",
      "name": "PM2.5Level",
      "parentId": "pkg-libraries-uuid",
      "literals": [
        "0-50",
        "51-100",
        "101-150",
        "151-200",
        "201-300",
        "301-500"
      ]
    },
    {
      "id": "enum-voc-uuid",
      "type": "Enumeration",
      "name": "VOCLevel",
      "parentId": "pkg-libraries-uuid",
      "literals": [
        "0-200",
        "201-400",
        "401-600",
        "601-800",
        "801-1000",
        "1001-2000"
      ]
    },
    {
      "id": "enum-co2-uuid",
      "type": "Enumeration",
      "name": "CO2Level",
      "parentId": "pkg-libraries-uuid",
      "literals": [
        "0-400",
        "401-800",
        "801-1200",
        "1201-1600",
        "1601-2000"
      ]
    },
    {
      "id": "enum-tempsensor-uuid",
      "type": "Enumeration",
      "name": "TemperatureSensorType",
      "parentId": "pkg-libraries-uuid",
      "literals": [
        "TypeA",
        "TypeB"
      ]
    },
    {
      "id": "enum-heatpower-uuid",
      "type": "Enumeration",
      "name": "HeaterPower",
      "parentId": "pkg-libraries-uuid",
      "literals": [
        ">=3.8kW"
      ]
    },
    {
      "id": "enum-coolpower-uuid",
      "type": "Enumeration",
      "name": "CoolerPower",
      "parentId": "pkg-libraries-uuid",
      "literals": [
        ">=3.5kW"
      ]
    },
    {
      "id": "sig-airquality-uuid",
      "type": "Signal",
      "name": "AirQualityData",
      "parentId": "pkg-libraries-uuid"
    },
    {
      "id": "sig-system-start-uuid",
      "type": "Signal",
      "name": "系统启动命令",
      "parentId": "pkg-libraries-uuid"
    },
    {
      "id": "blk-car-air-conditioner-system-uuid",
      "type": "Block",
      "name": "CarAirConditionerSystem",
      "parentId": "pkg-structure-uuid",
      "classifierBehaviorId": "sm-aircon-uuid"
    },
    {
      "id": "blk-TemperatureControlModule-uuid",
      "type": "Block",
      "name": "TemperatureControlModule",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-AirFlowControlModule-uuid",
      "type": "Block",
      "name": "AirFlowControlModule",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-AirQualityMonitoringModule-uuid",
      "type": "Block",
      "name": "AirQualityMonitoringModule",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-UserInterfaceModule-uuid",
      "type": "Block",
      "name": "UserInterfaceModule",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-PowerManagementModule-uuid",
      "type": "Block",
      "name": "PowerManagementModule",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-SafetyControlModule-uuid",
      "type": "Block",
      "name": "SafetyControlModule",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-CommunicationInterface-uuid",
      "type": "Block",
      "name": "CommunicationInterface",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-temperature-data-uuid",
      "type": "Block",
      "name": "TemperatureData",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-humidity-data-uuid",
      "type": "Block",
      "name": "HumidityData",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-air-quality-data-uuid",
      "type": "Block",
      "name": "AirQualityData",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-target-temp-uuid",
      "type": "Block",
      "name": "TargetTemperature",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-current-temp-uuid",
      "type": "Block",
      "name": "CurrentTemperature",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-air-quality-index-uuid",
      "type": "Block",
      "name": "AirQualityIndex",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-fault-info-uuid",
      "type": "Block",
      "name": "FaultInfo",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "blk-control-command-uuid",
      "type": "Block",
      "name": "ControlCommand",
      "parentId": "pkg-structure-uuid"
    },
    {
      "id": "prop-tempSensor-uuid",
      "type": "Property",
      "name": "TemperatureSensor",
      "parentId": "blk-TemperatureControlModule-uuid",
      "propertyKind": "part",
      "typeId": "enum-tempsensor-uuid",
      "associationId": "assoc-tempSensor"
    },
    {
      "id": "prop-heater-uuid",
      "type": "Property",
      "name": "Heater",
      "parentId": "blk-TemperatureControlModule-uuid",
      "propertyKind": "part",
      "typeId": "enum-heatpower-uuid",
      "associationId": "assoc-heater"
    },
    {
      "id": "prop-cooler-uuid",
      "type": "Property",
      "name": "Cooler",
      "parentId": "blk-TemperatureControlModule-uuid",
      "propertyKind": "part",
      "typeId": "enum-coolpower-uuid",
      "associationId": "assoc-cooler"
    },
    {
      "id": "prop-tempValue-uuid",
      "type": "Property",
      "name": "CurrentTemperature",
      "parentId": "blk-TemperatureControlModule-uuid",
      "propertyKind": "value",
      "typeId": "vt-temperature-uuid"
    },
    {
      "id": "prop-pm25-uuid",
      "type": "Property",
      "name": "PM2.5",
      "parentId": "blk-AirQualityMonitoringModule-uuid",
      "propertyKind": "value",
      "typeId": "enum-pm25-uuid"
    },
    {
      "id": "prop-voc-uuid",
      "type": "Property",
      "name": "VOC",
      "parentId": "blk-AirQualityMonitoringModule-uuid",
      "propertyKind": "value",
      "typeId": "enum-voc-uuid"
    },
    {
      "id": "prop-co2-uuid",
      "type": "Property",
      "name": "CO2",
      "parentId": "blk-AirQualityMonitoringModule-uuid",
      "propertyKind": "value",
      "typeId": "enum-co2-uuid"
    },
    {
      "id": "prop-target-temp-uuid",
      "type": "Property",
      "name": "目标温度",
      "parentId": "interaction-temp-adjust-uuid",
      "typeId": "blk-target-temp-uuid"
    },
    {
      "id": "prop-air-quality-uuid",
      "type": "Property",
      "name": "空气质量值",
      "parentId": "interaction-air-quality-uuid",
      "typeId": "blk-air-quality-data-uuid"
    },
    {
      "id": "rel-satisfy-purification-ctrl-uuid",
      "type": "Satisfy",
      "blockId": "blk-AirQualityMonitoringModule-uuid",
      "requirementId": "req-air-quality-control-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-satisfy-temp-ctrl-uuid",
      "type": "Satisfy",
      "blockId": "blk-TemperatureControlModule-uuid",
      "requirementId": "req-temperature-control-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-satisfy-fan-speed-uuid",
      "type": "Satisfy",
      "blockId": "blk-AirFlowControlModule-uuid",
      "requirementId": "req-fan-speed-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-satisfy-air-cycle-uuid",
      "type": "Satisfy",
      "blockId": "blk-AirFlowControlModule-uuid",
      "requirementId": "req-air-cycle-switch-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-satisfy-heat-pump-uuid",
      "type": "Satisfy",
      "blockId": "blk-TemperatureControlModule-uuid",
      "requirementId": "req-heat-pump-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-satisfy-CAN-uuid",
      "type": "Satisfy",
      "blockId": "blk-CommunicationInterface-uuid",
      "requirementId": "req-CAN-compatibility-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-satisfy-safety-uuid",
      "type": "Satisfy",
      "blockId": "blk-SafetyControlModule-uuid",
      "requirementId": "req-safety-standards-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-satisfy-user-interface-uuid",
      "type": "Satisfy",
      "blockId": "blk-UserInterfaceModule-uuid",
      "requirementId": "req-user-interface-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-airquality-uuid",
      "type": "Verify",
      "testCaseId": "tc-air-quality-verify-uuid",
      "requirementId": "req-air-quality-control-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-smartmode-uuid",
      "type": "Verify",
      "testCaseId": "tc-smart-comfort-verify-uuid",
      "requirementId": "req-smart-comfort-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-heatpump-uuid",
      "type": "Verify",
      "testCaseId": "tc-heat-pump-verify-uuid",
      "requirementId": "req-heat-pump-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-fanspeed-uuid",
      "type": "Verify",
      "testCaseId": "tc-fan-speed-verify-uuid",
      "requirementId": "req-fan-speed-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-temp-uuid",
      "type": "Verify",
      "testCaseId": "tc-temperature-verify-uuid",
      "requirementId": "req-temperature-control-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-aircycle-uuid",
      "type": "Verify",
      "testCaseId": "tc-air-cycle-verify-uuid",
      "requirementId": "req-air-cycle-switch-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-environment-uuid",
      "type": "Verify",
      "testCaseId": "tc-environment-verify-uuid",
      "requirementId": "req-environment-stability-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-CAN-uuid",
      "type": "Verify",
      "testCaseId": "tc-CAN-compatibility-verify-uuid",
      "requirementId": "req-CAN-compatibility-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "rel-verify-safety-uuid",
      "type": "Verify",
      "testCaseId": "tc-safety-verify-uuid",
      "requirementId": "req-safety-standards-uuid",
      "parentId": "pkg-reqs-uuid"
    },
    {
      "id": "assoc-airquality-sensor",
      "type": "Association",
      "name": "AirQualitySensorAssociation",
      "parentId": "pkg-structure-uuid",
      "memberEndIds": [
        "prop-pm25-uuid",
        "prop-voc-uuid",
        "prop-co2-uuid"
      ]
    },
    {
      "id": "conn-airquality-sensor",
      "type": "AssemblyConnector",
      "name": "AirQualitySensorConnection",
      "parentId": "blk-AirQualityMonitoringModule-uuid",
      "kind": "assembly",
      "end1": {
        "partRefId": "prop-pm25-uuid"
      },
      "end2": {
        "portRefId": "port-airquality-data"
      }
    },
    {
      "id": "conn-temp-sensor",
      "type": "AssemblyConnector",
      "name": "TemperatureSensorConnection",
      "parentId": "blk-TemperatureControlModule-uuid",
      "kind": "assembly",
      "end1": {
        "partRefId": "prop-tempSensor-uuid"
      },
      "end2": {
        "portRefId": "port-temp-sensor"
      }
    },
    {
      "id": "act-main-control-uuid",
      "type": "Activity",
      "name": "空调控制流程",
      "parentId": "pkg-behavior-uuid",
      "nodes": [
        "node-start",
        "node-detect-power",
        "node-init-modules",
        "node-wait-user-input",
        "node-receive-target-temp",
        "node-sample-environment",
        "node-env-sample-fork",
        "node-env-sample-join",
        "node-compare-temp",
        "node-decision-temp",
        "node-start-cooling-heating",
        "node-adjust-fan-speed",
        "node-pid-control",
        "node-monitor-air-quality",
        "node-decision-air-quality",
        "node-activate-purifier",
        "node-adjust-circulation",
        "node-monitor-air-quality-recovery",
        "node-auto-adjust-fan",
        "node-user-adjust-fan",
        "node-fault-detection",
        "node-decision-fault",
        "node-switch-fault",
        "node-notify-user",
        "node-shutdown",
        "node-shutdown-devices",
        "node-save-state",
        "node-shutdown-final",
        "cbuf-env-data",
        "cbuf-air-quality"
      ],
      "edges": [
        "edge-cf-start-detect",
        "edge-cf-detect-init",
        "edge-cf-init-wait",
        "edge-cf-wait-sample",
        "edge-cf-sample-fork",
        "edge-cf-fork-join",
        "edge-cf-join-compare",
        "edge-cf-compare-decision",
        "edge-cf-decision-start-cooling",
        "edge-cf-decision-start-reject",
        "edge-cf-start-cooling-merge",
        "edge-cf-reject-merge",
        "edge-cf-merge-notify",
        "edge-cf-notify-final",
        "edge-of-start-detect-power",
        "edge-of-detect-init",
        "edge-of-init-wait",
        "edge-of-wait-sample",
        "edge-of-sample-fork",
        "edge-of-fork-join",
        "edge-of-join-compare",
        "edge-of-compare-decision",
        "edge-of-decision-start-cooling",
        "edge-of-decision-start-reject",
        "edge-of-start-cooling-merge",
        "edge-of-reject-merge",
        "edge-of-merge-notify",
        "edge-of-notify-final"
      ],
      "groups": [
        "grp-controller-uuid",
        "grp-sensor-uuid",
        "grp-ui-uuid",
        "grp-cooling-uuid",
        "grp-purifier-uuid",
        "grp-display-uuid",
        "grp-fault-uuid"
      ]
    },
    {
      "id": "grp-controller-uuid",
      "type": "ActivityPartition",
      "name": "ControllerModule",
      "representsId": "blk-TemperatureControlModule-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "grp-sensor-uuid",
      "type": "ActivityPartition",
      "name": "SensorModule",
      "representsId": "blk-AirQualityMonitoringModule-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "grp-ui-uuid",
      "type": "ActivityPartition",
      "name": "UserInterface",
      "representsId": "blk-UserInterfaceModule-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "grp-cooling-uuid",
      "type": "ActivityPartition",
      "name": "CoolingSystem",
      "representsId": "blk-TemperatureControlModule-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "grp-purifier-uuid",
      "type": "ActivityPartition",
      "name": "AirPurifier",
      "representsId": "blk-AirQualityMonitoringModule-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "grp-display-uuid",
      "type": "ActivityPartition",
      "name": "DisplayModule",
      "representsId": "blk-UserInterfaceModule-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "grp-fault-uuid",
      "type": "ActivityPartition",
      "name": "AlarmSystem",
      "representsId": "blk-SafetyControlModule-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-start",
      "type": "InitialNode",
      "name": "系统启动",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-detect-power",
      "type": "CallBehaviorAction",
      "name": "检测电源状态",
      "behavior": "act-detect-power-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-detect-power-uuid",
      "type": "Activity",
      "name": "检测电源状态",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-init-modules",
      "type": "CallBehaviorAction",
      "name": "初始化子模块",
      "behavior": "act-init-modules-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-init-modules-uuid",
      "type": "Activity",
      "name": "初始化子模块",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-wait-user-input",
      "type": "CallBehaviorAction",
      "name": "等待用户设定目标温度",
      "behavior": "act-wait-user-input-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-wait-user-input-uuid",
      "type": "Activity",
      "name": "等待用户设定目标温度",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-receive-target-temp",
      "type": "CallBehaviorAction",
      "name": "接收用户目标温度",
      "behavior": "act-receive-target-temp-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-receive-target-temp-uuid",
      "type": "Activity",
      "name": "接收用户目标温度",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-sample-environment",
      "type": "CallBehaviorAction",
      "name": "采集环境数据",
      "behavior": "act-sample-environment-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-sample-environment-uuid",
      "type": "Activity",
      "name": "采集环境数据",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-env-sample-fork",
      "type": "ForkNode",
      "name": "环境采样并行",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-env-sample-join",
      "type": "JoinNode",
      "name": "环境采样完成",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-compare-temp",
      "type": "CallBehaviorAction",
      "name": "比较温度偏差",
      "behavior": "act-compare-temp-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-compare-temp-uuid",
      "type": "Activity",
      "name": "比较温度偏差",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-decision-temp",
      "type": "DecisionNode",
      "name": "温度偏差是否大于1°C",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-start-cooling-heating",
      "type": "CallBehaviorAction",
      "name": "启动制冷/加热",
      "behavior": "act-start-cooling-heating-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-start-cooling-heating-uuid",
      "type": "Activity",
      "name": "启动制冷/加热",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-adjust-fan-speed",
      "type": "CallBehaviorAction",
      "name": "调节风速",
      "behavior": "act-adjust-fan-speed-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-adjust-fan-speed-uuid",
      "type": "Activity",
      "name": "调节风速",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-pid-control",
      "type": "CallBehaviorAction",
      "name": "应用PID控制调节压缩机和风扇",
      "behavior": "act-pid-control-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-pid-control-uuid",
      "type": "Activity",
      "name": "应用PID控制调节压缩机和风扇",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-monitor-air-quality",
      "type": "CallBehaviorAction",
      "name": "检测空气质量指标",
      "behavior": "act-monitor-air-quality-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-monitor-air-quality-uuid",
      "type": "Activity",
      "name": "检测空气质量指标",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-decision-air-quality",
      "type": "DecisionNode",
      "name": "空气质量是否超标",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-activate-purifier",
      "type": "CallBehaviorAction",
      "name": "启用空气净化",
      "behavior": "act-activate-purifier-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-activate-purifier-uuid",
      "type": "Activity",
      "name": "启用空气净化",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-adjust-circulation",
      "type": "CallBehaviorAction",
      "name": "调整空气循环模式",
      "behavior": "act-adjust-circulation-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-adjust-circulation-uuid",
      "type": "Activity",
      "name": "调整空气循环模式",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-monitor-air-quality-recovery",
      "type": "CallBehaviorAction",
      "name": "监测空气质量恢复",
      "behavior": "act-monitor-air-quality-recovery-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-monitor-air-quality-recovery-uuid",
      "type": "Activity",
      "name": "监测空气质量恢复",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-auto-adjust-fan",
      "type": "CallBehaviorAction",
      "name": "自动调节风速",
      "behavior": "act-auto-adjust-fan-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-auto-adjust-fan-uuid",
      "type": "Activity",
      "name": "自动调节风速",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-user-adjust-fan",
      "type": "CallBehaviorAction",
      "name": "响应用户手动调节",
      "behavior": "act-user-adjust-fan-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-user-adjust-fan-uuid",
      "type": "Activity",
      "name": "响应用户手动调节",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-fault-detection",
      "type": "CallBehaviorAction",
      "name": "异常检测",
      "behavior": "act-fault-detection-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-fault-detection-uuid",
      "type": "Activity",
      "name": "异常检测",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-decision-fault",
      "type": "DecisionNode",
      "name": "是否检测到故障",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-switch-fault",
      "type": "CallBehaviorAction",
      "name": "切换到FAULT状态",
      "behavior": "act-switch-fault-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-switch-fault-uuid",
      "type": "Activity",
      "name": "切换到FAULT状态",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-notify-user",
      "type": "CallBehaviorAction",
      "name": "通知用户",
      "behavior": "act-notify-user-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "node-shutdown",
      "type": "CallBehaviorAction",
      "name": "系统关闭",
      "behavior": "act-shutdown-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-shutdown-uuid",
      "type": "Activity",
      "name": "系统关闭",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-shutdown-devices",
      "type": "CallBehaviorAction",
      "name": "逐步关闭设备",
      "behavior": "act-shutdown-devices-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-shutdown-devices-uuid",
      "type": "Activity",
      "name": "逐步关闭设备",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-save-state",
      "type": "CallBehaviorAction",
      "name": "保存状态",
      "behavior": "act-save-state-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "act-save-state-uuid",
      "type": "Activity",
      "name": "保存状态",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "node-shutdown-final",
      "type": "ActivityFinalNode",
      "name": "流程结束",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "cbuf-env-data",
      "type": "CentralBufferNode",
      "name": "环境数据缓冲区",
      "typeId": "blk-temperature-data-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "cbuf-air-quality",
      "type": "CentralBufferNode",
      "name": "空气质量缓冲区",
      "typeId": "blk-air-quality-data-uuid",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "pin-in-target-temp",
      "type": "InputPin",
      "name": "in_目标温度",
      "typeId": "blk-target-temp-uuid",
      "parentId": "node-receive-target-temp"
    },
    {
      "id": "pin-out-target-temp",
      "type": "OutputPin",
      "name": "out_目标温度",
      "typeId": "blk-target-temp-uuid",
      "parentId": "node-receive-target-temp"
    },
    {
      "id": "pin-in-env-data",
      "type": "InputPin",
      "name": "in_环境数据",
      "typeId": "blk-temperature-data-uuid",
      "parentId": "node-sample-environment"
    },
    {
      "id": "pin-out-env-data",
      "type": "OutputPin",
      "name": "out_环境数据",
      "typeId": "blk-temperature-data-uuid",
      "parentId": "node-sample-environment"
    },
    {
      "id": "pin-in-current-temp",
      "type": "InputPin",
      "name": "in_当前温度",
      "typeId": "blk-current-temp-uuid",
      "parentId": "node-compare-temp"
    },
    {
      "id": "pin-in-target-temp-compare",
      "type": "InputPin",
      "name": "in_目标温度",
      "typeId": "blk-target-temp-uuid",
      "parentId": "node-compare-temp"
    },
    {
      "id": "pin-out-temp-deviation",
      "type": "OutputPin",
      "name": "out_温度偏差",
      "typeId": "blk-fault-info-uuid",
      "parentId": "node-compare-temp"
    },
    {
      "id": "pin-in-air-quality",
      "type": "InputPin",
      "name": "in_空气质量指标",
      "typeId": "blk-air-quality-index-uuid",
      "parentId": "node-monitor-air-quality"
    },
    {
      "id": "pin-out-air-quality",
      "type": "OutputPin",
      "name": "out_空气质量指标",
      "typeId": "blk-air-quality-index-uuid",
      "parentId": "node-monitor-air-quality"
    },
    {
      "id": "pin-in-control-command",
      "type": "InputPin",
      "name": "in_控制命令",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-start-cooling-heating"
    },
    {
      "id": "pin-out-control-command",
      "type": "OutputPin",
      "name": "out_控制命令",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-start-cooling-heating"
    },
    {
      "id": "pin-in-fan-speed",
      "type": "InputPin",
      "name": "in_风速调节",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-adjust-fan-speed"
    },
    {
      "id": "pin-out-fan-speed",
      "type": "OutputPin",
      "name": "out_风速调节",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-adjust-fan-speed"
    },
    {
      "id": "pin-in-pid-params",
      "type": "InputPin",
      "name": "in_PID参数",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-pid-control"
    },
    {
      "id": "pin-out-pid-params",
      "type": "OutputPin",
      "name": "out_PID参数",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-pid-control"
    },
    {
      "id": "pin-in-air-quality-check",
      "type": "InputPin",
      "name": "in_空气质量指标",
      "typeId": "blk-air-quality-index-uuid",
      "parentId": "node-activate-purifier"
    },
    {
      "id": "pin-out-air-quality-check",
      "type": "OutputPin",
      "name": "out_空气质量指标",
      "typeId": "blk-air-quality-index-uuid",
      "parentId": "node-activate-purifier"
    },
    {
      "id": "pin-in-circulation-mode",
      "type": "InputPin",
      "name": "in_空气循环模式",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-adjust-circulation"
    },
    {
      "id": "pin-out-circulation-mode",
      "type": "OutputPin",
      "name": "out_空气循环模式",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-adjust-circulation"
    },
    {
      "id": "pin-in-air-quality-recovery",
      "type": "InputPin",
      "name": "in_空气质量恢复",
      "typeId": "blk-air-quality-index-uuid",
      "parentId": "node-monitor-air-quality-recovery"
    },
    {
      "id": "pin-out-air-quality-recovery",
      "type": "OutputPin",
      "name": "out_空气质量恢复",
      "typeId": "blk-air-quality-index-uuid",
      "parentId": "node-monitor-air-quality-recovery"
    },
    {
      "id": "pin-in-user-fan-adjust",
      "type": "InputPin",
      "name": "in_用户调节风速",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-user-adjust-fan"
    },
    {
      "id": "pin-out-user-fan-adjust",
      "type": "OutputPin",
      "name": "out_用户调节风速",
      "typeId": "blk-control-command-uuid",
      "parentId": "node-user-adjust-fan"
    },
    {
      "id": "pin-in-fault-info",
      "type": "InputPin",
      "name": "in_故障信息",
      "typeId": "blk-fault-info-uuid",
      "parentId": "node-fault-detection"
    },
    {
      "id": "pin-out-fault-info",
      "type": "OutputPin",
      "name": "out_故障信息",
      "typeId": "blk-fault-info-uuid",
      "parentId": "node-fault-detection"
    },
    {
      "id": "edge-cf-start-detect",
      "type": "ControlFlow",
      "sourceId": "node-start",
      "targetId": "node-detect-power",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-detect-init",
      "type": "ControlFlow",
      "sourceId": "node-detect-power",
      "targetId": "node-init-modules",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-init-wait",
      "type": "ControlFlow",
      "sourceId": "node-init-modules",
      "targetId": "node-wait-user-input",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-wait-sample",
      "type": "ControlFlow",
      "sourceId": "node-wait-user-input",
      "targetId": "node-sample-environment",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-sample-fork",
      "type": "ControlFlow",
      "sourceId": "node-sample-environment",
      "targetId": "node-env-sample-fork",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-fork-join",
      "type": "ControlFlow",
      "sourceId": "node-env-sample-fork",
      "targetId": "node-env-sample-join",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-join-compare",
      "type": "ControlFlow",
      "sourceId": "node-env-sample-join",
      "targetId": "node-compare-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-compare-decision",
      "type": "ControlFlow",
      "sourceId": "node-compare-temp",
      "targetId": "node-decision-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-decision-start-cooling",
      "type": "ControlFlow",
      "sourceId": "node-decision-temp",
      "targetId": "node-start-cooling-heating",
      "guard": "[>1°C]",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-decision-start-reject",
      "type": "ControlFlow",
      "sourceId": "node-decision-temp",
      "targetId": "node-adjust-fan-speed",
      "guard": "[≤1°C]",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-start-cooling-merge",
      "type": "ControlFlow",
      "sourceId": "node-start-cooling-heating",
      "targetId": "node-pid-control",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-reject-merge",
      "type": "ControlFlow",
      "sourceId": "node-adjust-fan-speed",
      "targetId": "node-pid-control",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-merge-notify",
      "type": "ControlFlow",
      "sourceId": "node-pid-control",
      "targetId": "node-monitor-air-quality",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-cf-notify-final",
      "type": "ControlFlow",
      "sourceId": "node-monitor-air-quality",
      "targetId": "node-shutdown",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-start-detect-power",
      "type": "ObjectFlow",
      "sourceId": "node-start",
      "targetId": "pin-in-target-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-detect-init",
      "type": "ObjectFlow",
      "sourceId": "node-detect-power",
      "targetId": "pin-in-target-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-init-wait",
      "type": "ObjectFlow",
      "sourceId": "node-init-modules",
      "targetId": "pin-in-target-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-wait-sample",
      "type": "ObjectFlow",
      "sourceId": "node-wait-user-input",
      "targetId": "pin-in-target-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-sample-fork",
      "type": "ObjectFlow",
      "sourceId": "node-sample-environment",
      "targetId": "pin-in-env-data",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-fork-join",
      "type": "ObjectFlow",
      "sourceId": "node-env-sample-fork",
      "targetId": "node-env-sample-join",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-join-compare",
      "type": "ObjectFlow",
      "sourceId": "node-env-sample-join",
      "targetId": "pin-in-current-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-compare-decision",
      "type": "ObjectFlow",
      "sourceId": "node-compare-temp",
      "targetId": "pin-in-current-temp",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-decision-start-cooling",
      "type": "ObjectFlow",
      "sourceId": "node-start-cooling-heating",
      "targetId": "pin-in-control-command",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-decision-start-reject",
      "type": "ObjectFlow",
      "sourceId": "node-adjust-fan-speed",
      "targetId": "pin-in-control-command",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-start-cooling-merge",
      "type": "ObjectFlow",
      "sourceId": "node-pid-control",
      "targetId": "pin-in-control-command",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-reject-merge",
      "type": "ObjectFlow",
      "sourceId": "node-pid-control",
      "targetId": "pin-in-control-command",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-merge-notify",
      "type": "ObjectFlow",
      "sourceId": "node-pid-control",
      "targetId": "pin-in-air-quality-check",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "edge-of-notify-final",
      "type": "ObjectFlow",
      "sourceId": "node-monitor-air-quality",
      "targetId": "pin-in-air-quality-check",
      "parentId": "act-main-control-uuid"
    },
    {
      "id": "sm-aircon-uuid",
      "type": "StateMachine",
      "name": "空调状态机",
      "parentId": "blk-car-air-conditioner-system-uuid"
    },
    {
      "id": "region-aircon-main-uuid",
      "type": "Region",
      "name": "主区域",
      "parentId": "sm-aircon-uuid"
    },
    {
      "id": "ps-main-initial-uuid",
      "type": "Pseudostate",
      "kind": "initial",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "state-idle-uuid",
      "type": "State",
      "name": "Idle",
      "parentId": "region-aircon-main-uuid",
      "entry": {
        "wrapperActivityId": "wrapper-entry-idle-uuid",
        "calledBehaviorId": "act-perform-idle-setup-uuid"
      }
    },
    {
      "id": "act-perform-idle-setup-uuid",
      "type": "Activity",
      "name": "PerformIdleSetup",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "state-initializing-uuid",
      "type": "State",
      "name": "Initializing",
      "parentId": "region-aircon-main-uuid",
      "entry": {
        "wrapperActivityId": "wrapper-entry-initializing-uuid",
        "calledBehaviorId": "act-perform-initializing-uuid"
      }
    },
    {
      "id": "act-perform-initializing-uuid",
      "type": "Activity",
      "name": "PerformInitializing",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "state-cooling-uuid",
      "type": "State",
      "name": "Cooling",
      "parentId": "region-aircon-main-uuid",
      "isComposite": true,
      "connectionPoints": [
        "ps-cooling-entry-uuid",
        "ps-cooling-exit-uuid"
      ],
      "regions": [
        "region-cooling-uuid"
      ]
    },
    {
      "id": "ps-cooling-entry-uuid",
      "type": "Pseudostate",
      "kind": "entryPoint",
      "name": "进入Cooling",
      "parentId": "state-cooling-uuid"
    },
    {
      "id": "ps-cooling-exit-uuid",
      "type": "Pseudostate",
      "kind": "exitPoint",
      "name": "退出Cooling",
      "parentId": "state-cooling-uuid"
    },
    {
      "id": "region-cooling-uuid",
      "type": "Region",
      "name": "Cooling子区域",
      "parentId": "state-cooling-uuid"
    },
    {
      "id": "ps-cooling-sub-initial-uuid",
      "type": "Pseudostate",
      "kind": "initial",
      "parentId": "region-cooling-uuid"
    },
    {
      "id": "state-cooling-active-uuid",
      "type": "State",
      "name": "CoolingActive",
      "parentId": "region-cooling-uuid"
    },
    {
      "id": "state-heating-uuid",
      "type": "State",
      "name": "Heating",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "state-purify-uuid",
      "type": "State",
      "name": "AirPurifying",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "state-automode-uuid",
      "type": "State",
      "name": "AutoMode",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "state-fault-uuid",
      "type": "State",
      "name": "Fault",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "state-safeshutdown-uuid",
      "type": "State",
      "name": "SafeShutdown",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "ps-fault-final-uuid",
      "type": "FinalState",
      "name": "Final",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-initial-to-idle",
      "type": "Transition",
      "sourceId": "ps-main-initial-uuid",
      "targetId": "state-idle-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-idle-to-initializing",
      "type": "Transition",
      "sourceId": "state-idle-uuid",
      "targetId": "state-initializing-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-initializing-to-automode",
      "type": "Transition",
      "sourceId": "state-initializing-uuid",
      "targetId": "state-automode-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-automode-to-cooling",
      "type": "Transition",
      "sourceId": "state-automode-uuid",
      "targetId": "state-cooling-uuid",
      "parentId": "region-aircon-main-uuid",
      "triggerIds": [
        "event-system-start-uuid"
      ]
    },
    {
      "id": "trans-automode-to-heating",
      "type": "Transition",
      "sourceId": "state-automode-uuid",
      "targetId": "state-heating-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-automode-to-fault",
      "type": "Transition",
      "sourceId": "state-automode-uuid",
      "targetId": "state-fault-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-any-to-fault",
      "type": "Transition",
      "sourceId": "state-idle-uuid",
      "targetId": "state-fault-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-any-to-safeshutdown",
      "type": "Transition",
      "sourceId": "state-idle-uuid",
      "targetId": "state-safeshutdown-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-cooling-to-purify",
      "type": "Transition",
      "sourceId": "state-cooling-uuid",
      "targetId": "state-purify-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-purify-to-cooling",
      "type": "Transition",
      "sourceId": "state-purify-uuid",
      "targetId": "state-cooling-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-any-to-fault-2",
      "type": "Transition",
      "sourceId": "state-cooling-uuid",
      "targetId": "state-fault-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-any-to-fault-3",
      "type": "Transition",
      "sourceId": "state-heating-uuid",
      "targetId": "state-fault-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-idle-to-fault",
      "type": "Transition",
      "sourceId": "state-idle-uuid",
      "targetId": "state-fault-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "trans-safeshutdown-to-idle",
      "type": "Transition",
      "sourceId": "state-safeshutdown-uuid",
      "targetId": "state-idle-uuid",
      "parentId": "region-aircon-main-uuid"
    },
    {
      "id": "act-start-compressor-uuid",
      "type": "Activity",
      "name": "启动变频压缩机",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-adjust-fan-medium-uuid",
      "type": "Activity",
      "name": "调节风扇到中高速",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-adjust-air-valve-uuid",
      "type": "Activity",
      "name": "调整空气调节阀",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-stop-compressor-uuid",
      "type": "Activity",
      "name": "关闭压缩机",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-lower-fan-speed-uuid",
      "type": "Activity",
      "name": "降低风扇速度",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-adjust-air-circulation-uuid",
      "type": "Activity",
      "name": "调整空气循环",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-enable-hepa-uuid",
      "type": "Activity",
      "name": "启用HEPA过滤器",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-switch-internal-circulation-uuid",
      "type": "Activity",
      "name": "切换空气循环为内循环",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-disable-hepa-uuid",
      "type": "Activity",
      "name": "关闭空气净化器",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-normal-airflow-uuid",
      "type": "Activity",
      "name": "恢复正常空气流通",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-activate-fault-indicator-uuid",
      "type": "Activity",
      "name": "激活故障指示灯",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-lock-output-uuid",
      "type": "Activity",
      "name": "锁定输出",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-notify-user-uuid",
      "type": "Activity",
      "name": "通知用户",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-restart-system-uuid",
      "type": "Activity",
      "name": "重启系统",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "act-return-idle-uuid",
      "type": "Activity",
      "name": "返回Idle",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-system-start-uuid",
      "type": "SignalEvent",
      "name": "系统启动事件",
      "signalId": "sig-system-start-uuid",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-initialization-complete-uuid",
      "type": "TimeEvent",
      "name": "初始化完成事件",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-target-lower-uuid",
      "type": "ChangeEvent",
      "name": "目标低于当前温度",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-target-higher-uuid",
      "type": "ChangeEvent",
      "name": "目标高于当前温度",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-fault-detected-uuid",
      "type": "ChangeEvent",
      "name": "故障检测事件",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-sensor-failure-uuid",
      "type": "ChangeEvent",
      "name": "传感器故障",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-serious-fault-uuid",
      "type": "ChangeEvent",
      "name": "严重故障",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-shutdown-uuid",
      "type": "SignalEvent",
      "name": "关闭系统",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-activate-purify-uuid",
      "type": "ChangeEvent",
      "name": "激活空气净化",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "event-deactivate-purify-uuid",
      "type": "ChangeEvent",
      "name": "关闭空气净化",
      "parentId": "pkg-behavior-uuid"
    },
    {
      "id": "interaction-temp-adjust-uuid",
      "type": "Interaction",
      "name": "温度调节流程",
      "parentId": "pkg-behavior-uuid",
      "classifierBehaviorFor": "blk-car-air-conditioner-system-uuid"
    },
    {
      "id": "interaction-air-quality-uuid",
      "type": "Interaction",
      "name": "空气质量监测流程",
      "parentId": "pkg-behavior-uuid",
      "classifierBehaviorFor": "blk-car-air-conditioner-system-uuid"
    },
    {
      "id": "interaction-fault-uuid",
      "type": "Interaction",
      "name": "故障检测流程",
      "parentId": "pkg-behavior-uuid",
      "classifierBehaviorFor": "blk-car-air-conditioner-system-uuid"
    },
    {
      "id": "ll-user-uuid",
      "type": "Lifeline",
      "name": "用户",
      "parentId": "interaction-temp-adjust-uuid",
      "representsId": "actor-001"
    },
    {
      "id": "ll-ui-uuid",
      "type": "Lifeline",
      "name": "界面",
      "parentId": "interaction-temp-adjust-uuid",
      "representsId": "blk-UserInterfaceModule-uuid"
    },
    {
      "id": "ll-temp-sensor-uuid",
      "type": "Lifeline",
      "name": "温度传感器",
      "parentId": "interaction-temp-adjust-uuid",
      "representsId": "prop-tempSensor-uuid"
    },
    {
      "id": "ll-control-uuid",
      "type": "Lifeline",
      "name": "控制模块",
      "parentId": "interaction-temp-adjust-uuid",
      "representsId": "blk-TemperatureControlModule-uuid"
    },
    {
      "id": "ll-purifier-uuid",
      "type": "Lifeline",
      "name": "空气净化器",
      "parentId": "interaction-air-quality-uuid",
      "representsId": "blk-AirQualityMonitoringModule-uuid"
    },
    {
      "id": "ll-monitor-uuid",
      "type": "Lifeline",
      "name": "监测模块",
      "parentId": "interaction-air-quality-uuid",
      "representsId": "blk-AirQualityMonitoringModule-uuid"
    },
    {
      "id": "ll-fault-uuid",
      "type": "Lifeline",
      "name": "故障检测",
      "parentId": "interaction-fault-uuid",
      "representsId": "blk-SafetyControlModule-uuid"
    },
    {
      "id": "msg-user-input-uuid",
      "type": "Message",
      "name": "用户输入22°C",
      "parentId": "interaction-temp-adjust-uuid",
      "sendEventId": "send-user-input-ev-uuid",
      "receiveEventId": "recv-ui-receive-ev-uuid",
      "messageSort": "synchCall",
      "signatureId": "op-setTargetTemp-uuid",
      "arguments": [
        {
          "body": "22°C",
          "language": "text"
        }
      ]
    },
    {
      "id": "op-setTargetTemp-uuid",
      "type": "Operation",
      "name": "setTargetTemperature",
      "parentId": "blk-UserInterfaceModule-uuid"
    },
    {
      "id": "msg-pass-target-uuid",
      "type": "Message",
      "name": "传递目标温度",
      "parentId": "interaction-temp-adjust-uuid",
      "sendEventId": "send-ui-to-control-ev-uuid",
      "receiveEventId": "recv-control-receive-ev-uuid",
      "messageSort": "synchCall",
      "signatureId": "op-receiveTargetTemp-uuid",
      "arguments": [
        {
          "body": "22°C",
          "language": "text"
        }
      ]
    },
    {
      "id": "op-receiveTargetTemp-uuid",
      "type": "Operation",
      "name": "receiveTargetTemperature",
      "parentId": "blk-TemperatureControlModule-uuid"
    },
    {
      "id": "msg-read-temp-uuid",
      "type": "Message",
      "name": "读取温度",
      "parentId": "interaction-temp-adjust-uuid",
      "sendEventId": "send-sensor-read-ev-uuid",
      "receiveEventId": "recv-control-temp-ev-uuid",
      "messageSort": "synchCall",
      "signatureId": "op-readTemperature-uuid",
      "arguments": []
    },
    {
      "id": "op-readTemperature-uuid",
      "type": "Operation",
      "name": "readTemperature",
      "parentId": "blk-TemperatureControlModule-uuid"
    },
    {
      "id": "msg-start-adjust-uuid",
      "type": "Message",
      "name": "启动调节",
      "parentId": "interaction-temp-adjust-uuid",
      "sendEventId": "send-control-start-ev-uuid",
      "receiveEventId": "recv-purifier-start-ev-uuid",
      "messageSort": "synchCall",
      "signatureId": "op-startCooling-uuid",
      "arguments": []
    },
    {
      "id": "op-startCooling-uuid",
      "type": "Operation",
      "name": "startCooling",
      "parentId": "blk-TemperatureControlModule-uuid"
    },
    {
      "id": "msg-temp-reached-uuid",
      "type": "Message",
      "name": "温度达到目标",
      "parentId": "interaction-temp-adjust-uuid",
      "sendEventId": "sensor-temp-reached-ev-uuid",
      "receiveEventId": "control-temp-reached-ev-uuid",
      "messageSort": "reply",
      "arguments": []
    },
    {
      "id": "msg-adjust-uuid",
      "type": "Message",
      "name": "调节风速/关闭压缩机",
      "parentId": "interaction-temp-adjust-uuid",
      "sendEventId": "control-adjust-ev-uuid",
      "receiveEventId": "purifier-adjust-ev-uuid",
      "messageSort": "synchCall",
      "signatureId": "op-adjustFanAndCompressor-uuid",
      "arguments": [
        "降低风速或关闭压缩机"
      ]
    },
    {
      "id": "op-adjustFanAndCompressor-uuid",
      "type": "Operation",
      "name": "adjustFanAndCompressor",
      "parentId": "blk-AirFlowControlModule-uuid"
    },
    {
      "id": "msg-display-uuid",
      "type": "Message",
      "name": "显示已调节",
      "parentId": "interaction-temp-adjust-uuid",
      "sendEventId": "control-display-ev-uuid",
      "receiveEventId": "ui-display-ev-uuid",
      "messageSort": "reply",
      "arguments": [
        "已调节至22°C"
      ]
    },
    {
      "id": "send-user-input-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-user-uuid",
      "messageId": "msg-user-input-uuid"
    },
    {
      "id": "recv-ui-receive-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-ui-uuid",
      "messageId": "msg-user-input-uuid"
    },
    {
      "id": "send-ui-to-control-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-ui-uuid",
      "messageId": "msg-pass-target-uuid"
    },
    {
      "id": "recv-control-receive-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-pass-target-uuid"
    },
    {
      "id": "send-sensor-read-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-temp-sensor-uuid",
      "messageId": "msg-read-temp-uuid"
    },
    {
      "id": "recv-control-temp-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-read-temp-uuid"
    },
    {
      "id": "send-control-start-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-start-adjust-uuid"
    },
    {
      "id": "recv-purifier-start-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-purifier-uuid",
      "messageId": "msg-start-adjust-uuid"
    },
    {
      "id": "sensor-temp-reached-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-temp-sensor-uuid",
      "messageId": "msg-temp-reached-uuid"
    },
    {
      "id": "control-temp-reached-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-temp-reached-uuid"
    },
    {
      "id": "control-adjust-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-adjust-uuid"
    },
    {
      "id": "purifier-adjust-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-purifier-uuid",
      "messageId": "msg-adjust-uuid"
    },
    {
      "id": "control-display-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-display-uuid"
    },
    {
      "id": "ui-display-ev-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-ui-uuid",
      "messageId": "msg-display-uuid"
    },
    {
      "id": "cf-balancecheck-alt-uuid",
      "type": "CombinedFragment",
      "name": "温度调节成功",
      "parentId": "interaction-temp-adjust-uuid",
      "interactionOperator": "alt",
      "coveredLifelineIds": [
        "ll-control-uuid",
        "ll-user-uuid"
      ],
      "operandIds": [
        "operand-sufficient-uuid",
        "operand-insufficient-uuid"
      ]
    },
    {
      "id": "operand-sufficient-uuid",
      "type": "InteractionOperand",
      "parentId": "cf-balancecheck-alt-uuid",
      "guardId": "guard-sufficient-uuid",
      "fragmentIds": [
        "frag-temp-reached-uuid"
      ]
    },
    {
      "id": "guard-sufficient-uuid",
      "type": "InteractionConstraint",
      "parentId": "operand-sufficient-uuid",
      "specification": {
        "body": "温度在22±1°C范围内",
        "language": "English"
      }
    },
    {
      "id": "frag-temp-reached-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "operand-sufficient-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-display-uuid"
    },
    {
      "id": "operand-insufficient-uuid",
      "type": "InteractionOperand",
      "parentId": "cf-balancecheck-alt-uuid",
      "guardId": null,
      "fragmentIds": [
        "frag-temp-not-reached-uuid"
      ]
    },
    {
      "id": "frag-temp-not-reached-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "operand-insufficient-uuid",
      "coveredId": "ll-control-uuid",
      "messageId": "msg-display-uuid"
    },
    {
      "id": "fragment-destroy-temp-sensor-uuid",
      "type": "DestructionOccurrenceSpecification",
      "parentId": "interaction-temp-adjust-uuid",
      "coveredId": "ll-temp-sensor-uuid"
    }
  ]
}
"""
        converter = UnifiedJsonToXmiConverter(full_json_str)
        final_xmi = converter.generate_xmi()
        
        if final_xmi:
            print("\n--- Unified XMI Generation Successful ---")
            # print(final_xmi) # 输出可能非常长，可以选择不打印
            
            output_filename = "unified_model_output.xmi"
            with open(output_filename, "w", encoding="utf-8") as f_out:
                f_out.write(final_xmi)
            print(f"\n✅ Unified XMI has been saved to '{output_filename}'")
        else:
            print("\n❌ Unified XMI Generation Failed.")
            
    except FileNotFoundError:
        print("Error: 'merged_json_output.json' not found. Please run the merge agent first to generate this file.")
    except Exception as main_exc:
        print(f"An unexpected error occurred: {main_exc}")
        traceback.print_exc()