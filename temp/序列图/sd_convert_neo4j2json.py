import json
from neo4j import GraphDatabase
import traceback
from collections import defaultdict

# ==== Neo4j 连接配置 ====
URI = "bolt://localhost:7687"
USER = "neo4j"
PASSWORD = "123456789" # 请替换为您的密码

class Neo4jSequenceDiagramToJsonConverter:
    def __init__(self, uri, user, password):
        self._driver = None
        self.elements_map = {} # 存储所有重建的JSON元素对象，以ID为键
        self.model_info = {}

        try:
            self._driver = GraphDatabase.driver(uri, auth=(user, password),
                                               connection_timeout=30, max_connection_lifetime=3600)
            self._driver.verify_connectivity()
            print("成功连接到 Neo4j 并验证连接。")
        except Exception as e:
            print(f"错误：无法连接到 Neo4j 或验证失败 - {e}")
            self._driver = None
            raise

    def close(self):
        if self._driver:
            self._driver.close()
            print("Neo4j 连接已关闭。")

    def _execute_read_tx(self, tx, query, parameters=None):
        try:
            result = tx.run(query, parameters if parameters else {})
            return [record.data() for record in result]
        except Exception as e:
            print(f"错误：执行 Cypher 读取查询失败\n查询: {query}\n参数: {parameters}\n错误: {e}")
            traceback.print_exc()
            raise

    def _fetch_all_nodes_as_json_skeletons(self, session):
        """步骤 1: 获取所有节点并创建基础JSON对象骨架。"""
        print("步骤 1：获取所有节点并创建JSON骨架...")
        self.elements_map.clear()
        self.model_info.clear()

        query = """
        MATCH (n)
        WHERE n.elementId IS NOT NULL
        RETURN n.elementId AS id, labels(n) AS labels, properties(n) AS props
        """
        node_records = session.execute_read(self._execute_read_tx, query)
        print(f"  - 成功获取 {len(node_records)} 个节点记录。")

        if not node_records:
            print("错误：数据库中未找到任何带elementId的节点。")
            return False

        for record in node_records:
            props = record['props']
            element_id = record['id']
            
            # --- 确保 element_type_json 总是有值 ---
            element_type_json = props.get('type') 
            if not element_type_json:
                valid_labels = [lbl for lbl in record['labels'] if lbl not in ["_Neo4jDesktop_VisualNode", "Database", "_ sympathique_VisualNode"]] # 添加了您之前日志中出现的另一个内部标签
                if valid_labels:
                    element_type_json = valid_labels[0] # 取第一个有效标签作为类型
                    # print(f"信息: 节点 {element_id} 缺少 'type' 属性，使用标签 '{element_type_json}' 作为类型。")
                else:
                    element_type_json = "Unknown" # 最后的备用
                    print(f"警告: 节点 {element_id} 既无 'type' 属性也无有效标签。设为 'Unknown'。属性: {props}, 标签: {record['labels']}")
            # --- 结束确保 ---


            element_json = {"id": element_id, "type": element_type_json}
            if "name" in props: element_json["name"] = props["name"]
            if "visibility" in props: element_json["visibility"] = props["visibility"]


            if element_type_json == "Model":
                self.model_info = element_json 
                # 不从 elements_map 中移除模型，因为它可能仍被parentId引用或需要属性恢复
            
            # 特定类型属性恢复 - 这个 if/elif 结构是正确的，因为 element_type_json 在上面已定义
            if element_type_json in ["Class", "Block"]:
                if "classifierBehaviorId_ref" in props: element_json["classifierBehaviorId"] = props["classifierBehaviorId_ref"]
            elif element_type_json == "Property":
                if "typeId_ref" in props: element_json["typeId"] = props["typeId_ref"]
                if "typeHref" in props: element_json["typeHref"] = props["typeHref"]
                if "aggregation" in props: element_json["aggregation"] = props["aggregation"]
                if "associationId_ref" in props: element_json["associationId"] = props["associationId_ref"]
            elif element_type_json == "Parameter":
                if "direction" in props: element_json["direction"] = props["direction"]
                if "typeId_ref" in props: element_json["typeId"] = props["typeId_ref"]
                if "typeHref" in props: element_json["typeHref"] = props["typeHref"]
            elif element_type_json == "Lifeline":
                if "representsId_ref" in props: element_json["representsId"] = props["representsId_ref"]
            elif element_type_json == "Message":
                if "messageSort" in props: element_json["messageSort"] = props["messageSort"]
                if "signatureId_ref" in props: element_json["signatureId"] = props["signatureId_ref"]
                if "arguments_json" in props:
                    try: element_json["arguments"] = json.loads(props["arguments_json"])
                    except json.JSONDecodeError: print(f"警告: 无法解析消息 {element_id} 的参数JSON: {props['arguments_json']}")
            elif element_type_json == "MessageOccurrenceSpecification":
                if "coveredId_ref" in props: element_json["coveredId"] = props["coveredId_ref"]
                if "messageId_ref" in props: element_json["messageId"] = props["messageId_ref"]
            elif element_type_json == "DestructionOccurrenceSpecification":
                 if "coveredId_ref" in props: element_json["coveredId"] = props["coveredId_ref"]
            elif element_type_json == "CombinedFragment":
                if "interactionOperator" in props: element_json["interactionOperator"] = props["interactionOperator"]
            elif element_type_json == "InteractionConstraint":
                spec = {}
                if "spec_body" in props: spec["body"] = props["spec_body"]
                if "spec_language" in props: spec["language"] = props["spec_language"]
                if "spec_id" in props: spec["id"] = props["spec_id"]
                if spec: element_json["specification"] = spec
            
            self.elements_map[element_id] = element_json
        
        # 确认模型信息
        model_id_from_loop = None
        if self.model_info and "id" in self.model_info : # 如果 Model 标签的节点被正确识别为模型
            model_id_from_loop = self.model_info["id"]
            # print(f"  - 模型信息已从 :Model 标签节点确认: ID={model_id_from_loop}")
        else: # 备用方案：从 elements_map 中找 type="Model"
            for eid, edata in self.elements_map.items():
                if edata.get("type") == "Model":
                    self.model_info = edata
                    model_id_from_loop = eid
                    print(f"  - 备用方案: 使用元素 {eid} (type='Model') 作为模型信息。")
                    break
        
        if not self.model_info or not model_id_from_loop:
            print("错误: 无法确定有效的模型信息。")
            return False

        print(f"  - 解析了 {len(self.elements_map)} 个元素到JSON骨架。最终模型ID: {self.model_info.get('id')}")
        return True
    def _reconstruct_hierarchies_and_id_lists(self, session):
        """步骤 2: 查询关系，重建parentId和各种ID列表。"""
        print("步骤 2：重建层级关系和ID引用列表...")
        model_id = self.model_info.get("id")
        if not model_id: print("错误: 模型ID缺失，无法进行层级重建。"); return

        # 2a. ParentId and Owned...Ids (based on direct ownership relations)
        # (注意：一个元素只有一个parentId，但可以有多个owned...Ids数组)
        ownership_queries = {
            # ParentId settings
            "Model":        {"rel_type": "OWNS_PACKAGE", "child_key_in_parent": None},
            "Package":      {"rel_type": "OWNS_ELEMENT", "child_key_in_parent": None}, # For Actor, Class, Assoc
            "Class":        [
                {"rel_type": "OWNS_ATTRIBUTE", "child_key_in_parent": "ownedAttributeIds"},
                {"rel_type": "OWNS_OPERATION", "child_key_in_parent": "ownedOperationIds"},
                {"rel_type": "HAS_CLASSIFIER_BEHAVIOR", "child_key_in_parent": None}, # Sets parentId for Interaction
                {"rel_type": "OWNS_BEHAVIOR", "child_key_in_parent": None} # Sets parentId for Interaction
            ],
            "Block":        [ # Similar to Class
                {"rel_type": "OWNS_ATTRIBUTE", "child_key_in_parent": "ownedAttributeIds"},
                {"rel_type": "OWNS_OPERATION", "child_key_in_parent": "ownedOperationIds"},
                {"rel_type": "HAS_CLASSIFIER_BEHAVIOR", "child_key_in_parent": None},
                {"rel_type": "OWNS_BEHAVIOR", "child_key_in_parent": None}
            ],
            "Operation":    {"rel_type": "OWNS_PARAMETER", "child_key_in_parent": "parameterIds"},
            "Interaction":  [
                {"rel_type": "OWNS_ATTRIBUTE", "child_key_in_parent": "ownedAttributeIds"}, # Interaction-owned props
                {"rel_type": "OWNS_LIFELINE", "child_key_in_parent": "lifelineIds"},
                {"rel_type": "OWNS_MESSAGE", "child_key_in_parent": "messageIds"},
                {"rel_type": "OWNS_FRAGMENT", "child_key_in_parent": "fragmentIds"}
            ],
            "CombinedFragment": {"rel_type": "OWNS_OPERAND", "child_key_in_parent": "operandIds"},
            "InteractionOperand": [
                 {"rel_type": "HAS_GUARD_CONSTRAINT", "child_key_in_parent": "guardId"}, # guardId is single
                 {"rel_type": "OWNS_FRAGMENT", "child_key_in_parent": "fragmentIds"}
            ],
            "Association": {"rel_type": "HAS_MEMBER_END", "child_key_in_parent": "memberEndIds"} # memberEnd
            # Note: OWNS_ELEMENT for Package to Package is implicitly handled by generic Package rule
        }

        for parent_label, configs in ownership_queries.items():
            if not isinstance(configs, list): configs = [configs]
            for config in configs:
                rel_type = config["rel_type"]
                child_key = config["child_key_in_parent"]
                
                query = f"""
                MATCH (p:{parent_label})-[r:{rel_type}]->(c)
                WHERE p.elementId IS NOT NULL AND c.elementId IS NOT NULL
                RETURN p.elementId AS parentId, c.elementId AS childId, labels(c)[0] AS childLabel
                """
                # labels(c)[0] might be too simplistic if multiple functional labels exist
                records = session.execute_read(self._execute_read_tx, query)
                for record in records:
                    p_id, c_id = record["parentId"], record["childId"]
                    
                    if c_id in self.elements_map:
                        # Set parentId for child (if not model)
                        if p_id != model_id:
                            self.elements_map[c_id]["parentId"] = p_id
                        
                        # Add to parent's ...Ids list
                        if child_key and p_id in self.elements_map:
                            parent_elem = self.elements_map[p_id]
                            if child_key == "guardId": # Special case for single ID
                                parent_elem[child_key] = c_id
                            else:
                                if child_key not in parent_elem:
                                    parent_elem[child_key] = []
                                if c_id not in parent_elem[child_key]: # Avoid duplicates
                                    parent_elem[child_key].append(c_id)
                    else:
                        print(f"警告: (Ownership) 子元素 {c_id} 未在 elements_map 中找到。")
        
        # 2b. Specific references (Lifeline.representsId, Message.send/receiveEventId/signatureId, etc.)
        # These were mostly loaded as node properties with "_ref" suffix. Now link them.
        # (Actually, this was done in _fetch_all_nodes_as_json_skeletons by directly assigning to JSON keys)
        # We just need to ensure the _ref was correctly mapped to the final JSON key.
        
        # Example: CoveredLifelineIds for CombinedFragment
        cf_covered_query = """
        MATCH (cf:CombinedFragment)-[:COVERS_LIFELINE]->(ll:Lifeline)
        RETURN cf.elementId AS cfId, ll.elementId AS llId
        """
        cf_covered_records = session.execute_read(self._execute_read_tx, cf_covered_query)
        temp_cf_covered = defaultdict(list)
        for rec in cf_covered_records: temp_cf_covered[rec['cfId']].append(rec['llId'])
        for cfid, llids in temp_cf_covered.items():
            if cfid in self.elements_map: self.elements_map[cfid]['coveredLifelineIds'] = llids
            
        # Example: Association navigableOwnedEndIds (if stored via a specific relationship)
        assoc_nav_query = """
        MATCH (a:Association)-[:HAS_NAVIGABLE_OWNED_END]->(p:Property)
        RETURN a.elementId AS assocId, p.elementId AS propId
        """
        assoc_nav_records = session.execute_read(self._execute_read_tx, assoc_nav_query)
        temp_assoc_nav = defaultdict(list)
        for rec in assoc_nav_records: temp_assoc_nav[rec['assocId']].append(rec['propId'])
        for aid, pids in temp_assoc_nav.items():
            if aid in self.elements_map: self.elements_map[aid]['navigableOwnedEndIds'] = pids


        print("  - 层级和ID列表重建完成。")


    def fetch_and_reconstruct_json(self):
        if not self._driver: print("错误：Neo4j 驱动未初始化。"); return None
        try:
            with self._driver.session(database="neo4j") as session:
                if not self._fetch_all_nodes_as_json_skeletons(session): return None
                self._reconstruct_hierarchies_and_id_lists(session)

            print("步骤 3：构建最终 JSON 对象...")
            
            final_elements_list = []
            model_id_val = self.model_info.get("id")

            for elem_id, elem_data in self.elements_map.items():
                if elem_id == model_id_val: continue # 模型对象不应在elements列表中
                
                # 清理parentId如果是模型ID（顶层元素在JSON中不显式写parentId）
                if elem_data.get("parentId") == model_id_val:
                    del elem_data["parentId"]
                final_elements_list.append(elem_data)
            
            # 确保模型信息是最新的（如果它在elements_map中被更新了）
            final_model_info = self.elements_map.get(model_id_val, self.model_info)


            final_json_output = {
                "model": [final_model_info] if final_model_info and "id" in final_model_info else [],
                "elements": final_elements_list
            }
            print("JSON 重建完成。")
            return final_json_output

        except Exception as e:
            print(f"错误：从 Neo4j 获取或重建数据时发生错误：{e}")
            traceback.print_exc()
            return None

# --- 主执行块 ---
if __name__ == "__main__":
    # 使用您提供的ATM序列图JSON字符串
    actual_atm_interaction_json_str_for_upload = """
{
  "model": [
    {
      "id": "model-atm-sys-uuid",
      "type": "Model",
      "name": "ATM系统模型"
    }
  ],
  "elements": [
    {
      "id": "pkg-banksvc-uuid",
      "type": "Package",
      "name": "银行服务",
      "parentId": "model-atm-sys-uuid"
    },
    {
      "id": "actor-customer-uuid",
      "type": "Actor",
      "name": "客户",
      "parentId": "pkg-banksvc-uuid"
    },
    {
      "id": "cls-atm-uuid",
      "type": "Class",
      "name": "ATM",
      "parentId": "pkg-banksvc-uuid",
      "classifierBehaviorId": "interaction-withdraw-uuid",
      "ownedOperationIds": ["op-execwithdraw-uuid"],
      "ownedAttributeIds": ["prop-atm-instance-uuid", "prop-db-connector-uuid"]
    },
    {
      "id": "cls-db-uuid",
      "type": "Class",
      "name": "后端数据库",
      "parentId": "pkg-banksvc-uuid",
      "ownedOperationIds": ["op-querybal-uuid"],
      "ownedAttributeIds": []
    },
    {
      "id": "prop-interaction-customer-uuid",
      "type": "Property",
      "name": "p_customer",
      "parentId": "interaction-withdraw-uuid",
      "typeId": "actor-customer-uuid",
      "aggregation": "none",
      "visibility": "public"
    },
    {
      "id": "prop-atm-instance-uuid",
      "type": "Property",
      "name": "atm_instance",
      "parentId": "cls-atm-uuid",
      "typeId": "cls-atm-uuid",
      "aggregation": "none",
      "visibility": "public"
    },
    {
      "id": "prop-db-connector-uuid",
      "type": "Property",
      "name": "db_connector",
      "parentId": "cls-atm-uuid",
      "typeId": "cls-db-uuid",
      "aggregation": "none",
      "visibility": "public"
    },
    {
      "id": "op-execwithdraw-uuid",
      "type": "Operation",
      "name": "执行取款",
      "parentId": "cls-atm-uuid",
      "parameterIds": [],
      "visibility": "public"
    },
    {
      "id": "op-querybal-uuid",
      "type": "Operation",
      "name": "查询余额",
      "parentId": "cls-db-uuid",
      "parameterIds": ["param-accountid-uuid"],
      "visibility": "public"
    },
    {
      "id": "param-accountid-uuid",
      "type": "Parameter",
      "name": "账户ID",
      "parentId": "op-querybal-uuid",
      "direction": "in",
      "typeId": null, 
      "visibility": "public"
    },
    {
      "id": "interaction-withdraw-uuid",
      "type": "Interaction",
      "name": "客户取钱",
      "parentId": "cls-atm-uuid",
      "lifelineIds": [
        "ll-customer-uuid",
        "ll-atm-uuid",
        "ll-db-uuid"
      ],
      "messageIds": [
        "msg-reqwithdraw-uuid",
        "msg-verifybal-uuid",
        "msg-balinfo-uuid",
        "msg-dispense-uuid",
        "msg-insufficient-uuid"
      ],
      "fragmentIds": [
        "fragment-send-reqwithdraw-uuid",
        "fragment-recv-reqwithdraw-uuid",
        "fragment-send-verifybal-uuid",
        "fragment-recv-verifybal-uuid",
        "fragment-send-balinfo-uuid",
        "fragment-recv-balinfo-uuid",
        "cf-balancecheck-alt-uuid",
        "fragment-destroy-db-uuid"
      ],
      "ownedAttributeIds": [
        "prop-interaction-customer-uuid"
      ]
    },
    {
      "id": "ll-customer-uuid",
      "type": "Lifeline",
      "name": "L1",
      "parentId": "interaction-withdraw-uuid",
      "representsId": "prop-interaction-customer-uuid",
      "visibility": "public"
    },
    {
      "id": "ll-atm-uuid",
      "type": "Lifeline",
      "name": "L2",
      "parentId": "interaction-withdraw-uuid",
      "representsId": "prop-atm-instance-uuid",
      "visibility": "public"
    },
    {
      "id": "ll-db-uuid",
      "type": "Lifeline",
      "name": "L3",
      "parentId": "interaction-withdraw-uuid",
      "representsId": "prop-db-connector-uuid",
      "visibility": "public"
    },
    {
      "id": "msg-reqwithdraw-uuid",
      "type": "Message",
      "name": "取款请求",
      "parentId": "interaction-withdraw-uuid",
      "sendEventId": "fragment-send-reqwithdraw-uuid",
      "receiveEventId": "fragment-recv-reqwithdraw-uuid",
      "messageSort": "synchCall",
      "signatureId": "op-execwithdraw-uuid",
      "arguments": [],
      "visibility": "public"
    },
    {
      "id": "msg-verifybal-uuid",
      "type": "Message",
      "name": "验证余额",
      "parentId": "interaction-withdraw-uuid",
      "sendEventId": "fragment-send-verifybal-uuid",
      "receiveEventId": "fragment-recv-verifybal-uuid",
      "messageSort": "synchCall",
      "signatureId": "op-querybal-uuid",
      "arguments": [
        {
          "id": "arg-acctid-for-verifybal-uuid",
          "body": "账户ID",
          "language": "natural"
        }
      ],
      "visibility": "public"
    },
    {
      "id": "msg-balinfo-uuid",
      "type": "Message",
      "name": "余额信息",
      "parentId": "interaction-withdraw-uuid",
      "sendEventId": "fragment-send-balinfo-uuid",
      "receiveEventId": "fragment-recv-balinfo-uuid",
      "messageSort": "reply",
      "visibility": "public"
    },
    {
      "id": "msg-dispense-uuid",
      "type": "Message",
      "name": "出钞",
      "parentId": "interaction-withdraw-uuid",  
      "sendEventId": "fragment-send-dispense-uuid",
      "receiveEventId": "fragment-recv-dispense-uuid",
      "messageSort": "reply",
      "visibility": "public"
    },
    {
      "id": "msg-insufficient-uuid",
      "type": "Message",
      "name": "余额不足",
      "parentId": "interaction-withdraw-uuid", 
      "sendEventId": "fragment-send-insufficient-uuid",
      "receiveEventId": "fragment-recv-insufficient-uuid",
      "messageSort": "reply",
      "visibility": "public"
    },
    {
      "id": "fragment-send-reqwithdraw-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-withdraw-uuid",
      "coveredId": "ll-customer-uuid",
      "messageId": "msg-reqwithdraw-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-recv-reqwithdraw-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-withdraw-uuid",
      "coveredId": "ll-atm-uuid",
      "messageId": "msg-reqwithdraw-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-send-verifybal-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-withdraw-uuid",
      "coveredId": "ll-atm-uuid",
      "messageId": "msg-verifybal-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-recv-verifybal-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-withdraw-uuid",
      "coveredId": "ll-db-uuid",
      "messageId": "msg-verifybal-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-send-balinfo-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-withdraw-uuid",
      "coveredId": "ll-db-uuid",
      "messageId": "msg-balinfo-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-recv-balinfo-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-withdraw-uuid",
      "coveredId": "ll-atm-uuid",
      "messageId": "msg-balinfo-uuid",
      "visibility": "public"
    },
    {
      "id": "cf-balancecheck-alt-uuid",
      "type": "CombinedFragment",
      "name": "AltFragment",
      "parentId": "interaction-withdraw-uuid",
      "interactionOperator": "alt",
      "coveredLifelineIds": [
        "ll-atm-uuid",
        "ll-customer-uuid"
      ],
      "operandIds": [
        "operand-sufficient-uuid",
        "operand-insufficient-uuid"
      ],
      "visibility": "public"
    },
    {
      "id": "operand-sufficient-uuid",
      "type": "InteractionOperand",
      "parentId": "cf-balancecheck-alt-uuid",
      "guardId": "guard-sufficient-uuid",
      "fragmentIds": [
        "fragment-send-dispense-uuid",
        "fragment-recv-dispense-uuid",
        "msg-dispense-uuid" 
      ],
      "visibility": "public"
    },
    {
      "id": "operand-insufficient-uuid",
      "type": "InteractionOperand",
      "parentId": "cf-balancecheck-alt-uuid",
      "guardId": null,
      "fragmentIds": [
        "fragment-send-insufficient-uuid",
        "fragment-recv-insufficient-uuid",
        "msg-insufficient-uuid" 
      ],
      "visibility": "public"
    },
    {
      "id": "guard-sufficient-uuid",
      "type": "InteractionConstraint",
      "parentId": "operand-sufficient-uuid",
      "specification": {
        "id": "spec-guard-sufficient-uuid",
        "body": "余额充足",
        "language": "natural"
      },
      "visibility": "public"
    },
    {
      "id": "fragment-send-dispense-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "operand-sufficient-uuid",
      "coveredId": "ll-atm-uuid",
      "messageId": "msg-dispense-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-recv-dispense-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "operand-sufficient-uuid",
      "coveredId": "ll-customer-uuid",
      "messageId": "msg-dispense-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-send-insufficient-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "operand-insufficient-uuid",
      "coveredId": "ll-atm-uuid",
      "messageId": "msg-insufficient-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-recv-insufficient-uuid",
      "type": "MessageOccurrenceSpecification",
      "parentId": "operand-insufficient-uuid",
      "coveredId": "ll-customer-uuid",
      "messageId": "msg-insufficient-uuid",
      "visibility": "public"
    },
    {
      "id": "fragment-destroy-db-uuid",
      "type": "DestructionOccurrenceSpecification",
      "parentId": "interaction-withdraw-uuid",
      "coveredId": "ll-db-uuid",
      "visibility": "public"
    }
  ]
}
"""
    # 示例：先上传，再尝试读取和重建
    uploader = Neo4jSequenceDiagramToJsonConverter(URI, USER, PASSWORD)
    # print("\n--- 清空数据库 ---")
    # uploader.clear_database_content() # 确保从干净的数据库开始，以进行准确测试
    # print("\n--- 开始上传ATM序列图数据到 Neo4j ---")
    # upload_success = uploader.upload_from_json_string(actual_atm_interaction_json_str_for_upload)
    # uploader.close()

    # if upload_success:
    #     print("--- ATM序列图数据上传成功 ---")
    converter = None
    try:
        converter = Neo4jSequenceDiagramToJsonConverter(URI, USER, PASSWORD)
        if converter._driver:
            print("\n--- 开始从Neo4j重建序列图JSON数据 ---")
            reconstructed_json_data = converter.fetch_and_reconstruct_json()
            if reconstructed_json_data:
                print("\n--- 重建的序列图 JSON 数据 ---")
                print(json.dumps(reconstructed_json_data, indent=2, ensure_ascii=False))
                
                # try:
                #     with open("reconstructed_atm_sequence_data.json", "w", encoding="utf-8") as f:
                #         json.dump(reconstructed_json_data, f, indent=2, ensure_ascii=False)
                #     print("\n已将重建的 JSON 保存到 reconstructed_atm_sequence_data.json")
                # except Exception as e_save:
                #     print(f"\n错误：保存重建的 JSON 到文件时出错：{e_save}")
            else:
                print("未能成功重建序列图 JSON 数据。")
    except Exception as main_e:
        print(f"主程序发生错误: {main_e}")
        traceback.print_exc()
    finally:
        if converter and converter._driver:
            converter.close()
    # else:
    #     print("--- ATM序列图数据上传失败，跳过重建测试 ---")