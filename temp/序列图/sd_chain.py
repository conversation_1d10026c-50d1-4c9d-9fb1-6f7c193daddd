from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-a4bed4a0b42f4f198e1ecb2604d0fa6f",
#     base_url='https://api.deepseek.com'
# )
chat = ChatOpenAI(
    model="gpt-4.1-mini",
    #model="gpt-4.1-nano-2025-04-14",
    temperature=0.0,
    #max_tokens=8192,
    timeout=None,
    api_key="sk-TP4VOmsDu2OdsE8H88BaBd09D1B74bD983C7Ca67E6E9AeBf",
    base_url='https://happyapi.org/v1'
)
# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-pdRe1ohnoKmJSCGWBrCBnPH9226q7hVEgC6ijVfhZPREGUz0",
#     base_url='https://api.chatfire.cn/v1'
# )
# chat = ChatOpenAI(
#     model="deepseek-chat",
#     temperature=0.0,
#     max_tokens=8192,
#     timeout=None,
#     api_key="sk-1XvJYiJNpvUQBFpUwdUorXQrcWOJOMYLWwKLIRVvW9ygsQN1",
#     base_url='https://opus.gptuu.com/v1'
# )
question1 = """"ATM系统模型包含一个“银行服务”包。包内有一个“客户”Actor和一个“ATM”类，以及一个“后端数据库”类。
“ATM”类有一个名为“客户取钱”的序列图（作为其分类器行为）。
在此“客户取钱”交互中：
1.  “客户”的实例（生命线L1，代表交互内的一个临时属性 `p_customer`，其类型为“客户”Actor）向“ATM”的实例（生命线L2，代表“ATM”类的一个属性 `atm_instance`）发送“取款请求”消息，该消息调用“ATM”类的“执行取款”操作。
2.  “ATM”（生命线L2）向“后端数据库”的实例（生命线L3，代表“ATM”类的属性 `db_connector`，其类型为“后端数据库”）发送“验证余额”消息，调用“后端数据库”的“查询余额”操作，参数为“账户ID”。
3.  “后端数据库”（生命线L3）回复“ATM”（生命线L2）“余额信息”消息。
4.  接下来是一个条件判断（alt组合片段）：
    a.  如果“余额充足”（守卫条件），则“ATM”（生命线L2）向“客户”（生命线L1）发送“出钞”回复消息。
    b.  否则，“ATM”（生命线L2）向“客户”（生命线L1）发送“余额不足”回复消息。
5.  在“验证余额”之后，“后端数据库”生命线（L3）被销毁。"""
question2 = """"ATM系统模型包含一个“银行服务”包。包内有一个“客户”Actor和一个“ATM”类，以及一个“后端数据库”类。
“ATM”类有一个名为“客户取钱”的序列图（作为其分类器行为）。
在此“客户取钱”交互中：
1.  “客户”的实例（生命线L1，代表交互内的一个临时属性 `p_customer`，其类型为“客户”Actor）向“ATM”的实例（生命线L2，代表“ATM”类的一个属性 `atm_instance`）发送“取款请求”消息，该消息调用“ATM”类的“执行取款”操作。
2.  “ATM”（生命线L2）向“后端数据库”的实例（生命线L3，代表“ATM”类的属性 `db_connector`，其类型为“后端数据库”）发送“验证余额”消息，调用“后端数据库”的“查询余额”操作，参数为“账户ID”。
3.  “后端数据库”（生命线L3）回复“ATM”（生命线L2）“余额信息”消息。
4.  接下来是一个条件判断（alt组合片段）：
    a.  如果“余额充足”（守卫条件），则“ATM”（生命线L2）向“客户”（生命线L1）发送“出钞”回复消息。
    b.  否则，“ATM”（生命线L2）向“客户”（生命线L1）发送“余额不足”回复消息。
5.  在“验证余额”之后，“后端数据库”生命线（L3）被销毁。"""
question3 = """
ATM系统模型包含一个“银行服务”包。包内有一个“客户”Actor和一个“ATM”类，以及一个“后端数据库”类。
“ATM”类有一个名为“客户取钱”的序列图（作为其分类器行为）。
在此“客户取钱”交互中：
1.  “客户”的实例（生命线L1，代表交互内的一个临时属性 `p_customer`，其类型为“客户”Actor）向“ATM”的实例（生命线L2，代表“ATM”类的一个属性 `atm_instance`）发送“取款请求”消息，该消息调用“ATM”类的“执行取款”操作。
2.  “ATM”（生命线L2）向“后端数据库”的实例（生命线L3，代表“ATM”类的属性 `db_connector`，其类型为“后端数据库”）发送“验证余额”消息，调用“后端数据库”的“查询余额”操作，参数为“账户ID”。
3.  “后端数据库”（生命线L3）回复“ATM”（生命线L2）“余额信息”消息。
4.  接下来是一个条件判断（alt组合片段）：
    a.  如果“余额充足”（守卫条件），则“ATM”（生命线L2）向“客户”（生命线L1）发送“出钞”回复消息。
    b.  否则，“ATM”（生命线L2）向“客户”（生命线L1）发送“余额不足”回复消息。
5.  在“验证余额”之后，“后端数据库”生命线（L3）被销毁。
"""

prompt1 = """
## 角色
你是一位专业的 SysML/UML 序列图建模专家。你精通序列图的规范，能够准确地从自然语言描述中提取出交互（Interaction）、生命线（Lifeline）及其代表（represents）、消息（Message）及其发送者/接收者、消息事件（MessageOccurrenceSpecification）、组合片段（CombinedFragment）及其操作数（InteractionOperand）和守卫（InteractionConstraint）、以及这些元素所属的包和上下文（如类或操作）。

## 规则
你的目标是根据输入的文本描述，分析并生成构建 SysML/UML 序列图所需的元素信息。请遵循以下步骤进行思考和分析，并生成中间的思考过程：

1.  **识别模型和顶层包 (Model & Top-Level Packages)**:
    *   确定文本描述的顶层模型名称。
    *   识别主要的包 (Package) 及其名称。所有其他元素（如定义参与者的类、交互本身）通常属于某个包或直接属于模型。
    *   为每个识别的元素分配合理的名称和临时ID（最终JSON中ID需全局唯一，可使用描述性名称加后缀，如 `-uuid`）。

2.  **识别交互上下文和交互 (Interaction Context & Interaction)**:
    *   确定序列图所描述的交互（Interaction）的名称。
    *   识别这个交互是哪个类（Class/Block）的分类器行为（classifierBehavior），或者是哪个操作（Operation）的具体实现。记录这个拥有交互的上下文元素的临时ID。
    *   为交互本身分配一个临时ID。交互的`parentId`应指向拥有它的类或操作。

3.  **识别参与者类/角色 (Participant Classes/Roles - Actors, Blocks, Classes)**:
    *   找出文本中明确提到的、将作为生命线基础的系统实体、用户角色或组件。这些通常是 `Class`、`Block` 或 `Actor`。
    *   为每个参与者类/角色提取其 `名称`。
    *   分配一个临时的唯一系统 ID。记录它们所属的包 (`parentId`)。

4.  **识别交互内部属性 (Properties owned by Interaction - for Lifeline representation)**:
    *   检查文本或上下文，看是否需要在交互（Interaction）内部定义属性（Property），这些属性将由生命线代表。这种情况通常发生在生命线代表的不是其拥有者（如Class）的直接部件，而是例如一个Actor的实例或一个临时对象。
    *   如果需要，为这些交互拥有的属性分配临时ID，设置其类型（`typeId` 指向对应的Actor或Class）。其`parentId`将是该Interaction的ID。

5.  **识别生命线 (Lifelines) 及其代表 (Represents)**:
    *   对于交互中的每一个参与者，创建一个生命线（Lifeline）。
    *   确定每个生命线代表（`representsId`）哪个之前识别的参与者类/角色的实例。这可能是直接引用一个Actor或Class的ID，或者更常见地是引用一个Property的ID（这个Property的类型是那个Actor或Class）。该Property可以是拥有交互的类的属性，也可以是交互自身拥有的属性（见上一步）。
    *   为每个生命线分配一个临时ID。生命线的`parentId`是其所属的Interaction的ID。
    *   （可选高级）如果文本提到生命线的创建或销毁，记录下来。

6.  **识别消息 (Messages)**:
    *   找出文本中描述的参与者之间的交互动作或通信。这些是 `Message` 元素。
    *   为每个消息提取其 `名称`（例如，操作调用名，信号名，或描述性短语）。
    *   确定消息的 `发送生命线` (sender lifeline) 和 `接收生命线` (receiver lifeline) 的临时ID。
    *   识别消息的 `类型 (messageSort)`：例如，同步调用 (`synchCall`)，异步调用 (`asynchCall`)，回复 (`reply`)，创建消息 (`createMessage`)，销毁消息 (`deleteMessage`)。如果未明确，同步调用是常见默认。
    *   （可选）识别消息调用的具体操作签名（`signatureId`，指向一个Operation的ID）和消息参数（`arguments`，每个参数包含`body`和`language`）。
    *   为每个消息分配一个临时ID。消息的`parentId`是其所属的Interaction的ID。

7.  **识别消息发生规约和销毁规约 (MessageOccurrenceSpecification, DestructionOccurrenceSpecification - as Fragments)**:
    *   每个消息都有一个发送事件和一个接收事件，它们发生在各自的生命线上。这些是 `MessageOccurrenceSpecification`。
    *   为每个发送事件和接收事件分配一个临时ID。
    *   记录每个事件覆盖（`coveredId`）的生命线ID，以及它关联的（`messageId`）消息ID。
    *   如果提到了生命线的销毁，创建一个 `DestructionOccurrenceSpecification`，记录其覆盖的（`coveredId`）生命线ID。
    *   这些片段的`parentId`是它们所属的`Interaction`或`InteractionOperand`。

8.  **识别组合片段 (CombinedFragments), 操作数 (InteractionOperands), 和守卫 (InteractionConstraints)**:
    *   找出文本中描述条件分支（如 "如果...那么...否则..."对应 `alt`）、可选部分（"如果满足条件则..."对应 `opt`）、循环（"重复直到..."对应 `loop`）等控制流结构。这些是 `CombinedFragment`。
    *   为每个 `CombinedFragment` 提取其 `交互操作符 (interactionOperator)` (alt, opt, loop等)。
    *   识别此组合片段覆盖了哪些生命线 (`coveredLifelineIds`)。
    *   分配一个临时的唯一系统ID。其`parentId`是其所属的`Interaction`或父`InteractionOperand`。
    *   对于组合片段中的每个分支或部分，创建一个 `InteractionOperand`。
    *   为每个 `InteractionOperand` 分配临时ID。其`parentId`是所属的`CombinedFragment`。
    *   为每个 `InteractionOperand` 提取其 `守卫条件 (guard)`。守卫条件是一个 `InteractionConstraint`。
        *   为 `InteractionConstraint` 分配临时ID。其`parentId`是所属的`InteractionOperand`。
        *   `InteractionConstraint` 的规约 (`specification`) 是一个包含 `body` (条件表达式) 和 `language` 的对象。
    *   识别每个 `InteractionOperand` 内部包含哪些片段 (`fragmentIds`，通常是 `MessageOccurrenceSpecification`，也可能是嵌套的 `CombinedFragment`)。

9.  **识别类/Actor的属性和操作 (Properties and Operations of Classes/Actors)**:
    *   如果消息调用了特定的操作，或者生命线代表特定的属性/部件（这些属性属于类/Actor，而不是交互本身），确保这些操作和属性也被识别出来。
    *   操作属于其定义的类/Actor (`parentId` 指向类/Actor的ID)。
        *   为操作的参数（`Parameter`）分配临时ID，记录其名称、方向和类型（`typeId` 或 `typeHref`）。参数的`parentId`是其所属的操作。
    *   属性属于其定义的类/Actor (`parentId` 指向类/Actor的ID)。
        *   记录属性的名称、类型（`typeId` 或 `typeHref`）、聚合方式（`aggregation`）以及可能的关联（`associationId`）。
    *   为它们分配临时ID和名称。

10. **识别关联 (Associations)**:
    *   如果文本描述了类/Actor之间的静态关系（通常由属性的`association`端点体现），识别这些`Association`。
    *   记录关联的成员端点ID (`memberEndIds`)，这些端点是`Property`的ID。
    *   为`Association`分配临时ID，其`parentId`是它们所属的包。

11. **编译和整理输出**:
    *   汇总所有识别出的元素（模型、包、类/Actor、交互、属性、生命线、消息、消息发生规约、销毁规约、组合片段、操作数、交互约束、操作、参数、关联）及其属性和引用关系。
    *   准备一个清晰的、结构化的中间表示（“整理优化输出”），概述提取到的所有信息。确保所有临时ID都是唯一的，并且`parentId`关系正确。

## 样例

### 输入样例：
"ATM系统模型包含一个“银行服务”包。包内有一个“客户”Actor和一个“ATM”类，以及一个“后端数据库”类。
“ATM”类有一个名为“客户取钱”的序列图（作为其分类器行为）。
在此“客户取钱”交互中：
1.  “客户”的实例（生命线L1，代表交互内的一个临时属性 `p_customer`，其类型为“客户”Actor）向“ATM”的实例（生命线L2，代表“ATM”类的一个属性 `atm_instance`）发送“取款请求”消息，该消息调用“ATM”类的“执行取款”操作。
2.  “ATM”（生命线L2）向“后端数据库”的实例（生命线L3，代表“ATM”类的属性 `db_connector`，其类型为“后端数据库”）发送“验证余额”消息，调用“后端数据库”的“查询余额”操作，参数为“账户ID”。
3.  “后端数据库”（生命线L3）回复“ATM”（生命线L2）“余额信息”消息。
4.  接下来是一个条件判断（alt组合片段）：
    a.  如果“余额充足”（守卫条件），则“ATM”（生命线L2）向“客户”（生命线L1）发送“出钞”回复消息。
    b.  否则，“ATM”（生命线L2）向“客户”（生命线L1）发送“余额不足”回复消息。
5.  在“验证余额”之后，“后端数据库”生命线（L3）被销毁。"

### 输出文本 (CoT):
请你按照如下的11步进行思考推理并输出：

#### 第一步：识别模型和顶层包
- 模型名称: "ATM系统模型" (model-atm-sys-uuid)
- 主要包: "银行服务" (pkg-banksvc-uuid)
  - parentId: model-atm-sys-uuid

#### 第二步：识别交互上下文和交互
- 交互名称: "客户取钱" (interaction-withdraw-uuid)
- 拥有交互的上下文元素: "ATM" 类 (cls-atm-uuid) 的分类器行为。
- 交互的parentId: cls-atm-uuid

#### 第三步：识别参与者类/角色
- 参与者1 (Actor):
    - 名称: "客户"
    - 临时系统 ID: actor-customer-uuid
    - parentId: pkg-banksvc-uuid
- 参与者2 (Class):
    - 名称: "ATM"
    - 临时系统 ID: cls-atm-uuid
    - parentId: pkg-banksvc-uuid
- 参与者3 (Class):
    - 名称: "后端数据库"
    - 临时系统 ID: cls-db-uuid
    - parentId: pkg-banksvc-uuid

#### 第四步：识别交互内部属性
- 交互内部属性1 (为L1代表):
    - 名称: "p_customer" (根据文本推断或默认生成)
    - 类型 (typeId): actor-customer-uuid
    - 临时系统 ID: prop-interaction-customer-uuid
    - parentId: interaction-withdraw-uuid

#### 第五步：识别生命线及其代表
- 生命线1 (L1):
    - 代表 (representsId): prop-interaction-customer-uuid
    - 临时系统 ID: ll-customer-uuid
    - parentId: interaction-withdraw-uuid
- 生命线2 (L2):
    - 代表 (representsId): prop-atm-instance-uuid (假设ATM类有一个名为atm_instance的属性，类型为ATM自身，或直接是cls-atm-uuid如果简化)
    - 临时系统 ID: ll-atm-uuid
    - parentId: interaction-withdraw-uuid
- 生命线3 (L3):
    - 代表 (representsId): prop-db-connector-uuid (假设ATM类有一个名为db_connector的属性，类型为后端数据库)
    - 临时系统 ID: ll-db-uuid
    - parentId: interaction-withdraw-uuid
    - 销毁事件关联: fragment-destroy-db-uuid

#### 第六步：识别消息
- 消息1: "取款请求" (msg-reqwithdraw-uuid)
    - 发送者: ll-customer-uuid, 接收者: ll-atm-uuid, 类型: synchCall, 签名: op-execwithdraw-uuid
    - parentId: interaction-withdraw-uuid
- 消息2: "验证余额" (msg-verifybal-uuid)
    - 发送者: ll-atm-uuid, 接收者: ll-db-uuid, 类型: synchCall, 签名: op-querybal-uuid, 参数: [{"body": "账户ID"}]
    - parentId: interaction-withdraw-uuid
- 消息3: "余额信息" (msg-balinfo-uuid)
    - 发送者: ll-db-uuid, 接收者: ll-atm-uuid, 类型: reply
    - parentId: interaction-withdraw-uuid
- 消息4 (alt分支1): "出钞" (msg-dispense-uuid)
    - 发送者: ll-atm-uuid, 接收者: ll-customer-uuid, 类型: reply
    - parentId: interaction-withdraw-uuid (或其所属的InteractionOperand)
- 消息5 (alt分支2): "余额不足" (msg-insufficient-uuid)
    - 发送者: ll-atm-uuid, 接收者: ll-customer-uuid, 类型: reply
    - parentId: interaction-withdraw-uuid (或其所属的InteractionOperand)

#### 第七步：识别消息发生规约和销毁规约
- (为每条消息的发送和接收创建MessageOccurrenceSpecification，并关联消息和生命线。parentId是Interaction或Operand)
- ... (例如: fragment-send-reqwithdraw, fragment-recv-reqwithdraw, etc.) ...
- 销毁规约1: (fragment-destroy-db-uuid)
    - 类型: DestructionOccurrenceSpecification, 覆盖: ll-db-uuid
    - parentId: interaction-withdraw-uuid

#### 第八步：识别组合片段, 操作数, 和守卫
- 组合片段1 (alt): (cf-balancecheck-alt-uuid)
    - 操作符: "alt", 覆盖生命线: [ll-atm-uuid, ll-customer-uuid]
    - parentId: interaction-withdraw-uuid
    - 操作数:
        - 操作数1.1: (operand-sufficient-uuid, parentId: cf-balancecheck-alt-uuid)
            - 守卫: (guard-sufficient-uuid, parentId: operand-sufficient-uuid, spec: {"body": "余额充足"})
            - 包含片段: [fragment-send-dispense, fragment-recv-dispense] (对应消息4的事件)
        - 操作数1.2: (operand-insufficient-uuid, parentId: cf-balancecheck-alt-uuid)
            - 守卫: (隐式else或无守卫)
            - 包含片段: [fragment-send-insufficient, fragment-recv-insufficient] (对应消息5的事件)

#### 第九步：识别类/Actor的属性和操作
- "ATM" 类 (cls-atm-uuid):
    - 操作1: "执行取款" (op-execwithdraw-uuid, parentId: cls-atm-uuid)
    - 属性1: "atm_instance" (prop-atm-instance-uuid, parentId: cls-atm-uuid, typeId: cls-atm-uuid) - *如果生命线L2代表这个*
    - 属性2: "db_connector" (prop-db-connector-uuid, parentId: cls-atm-uuid, typeId: cls-db-uuid) - *如果生命线L3代表这个*
- "后端数据库" 类 (cls-db-uuid):
    - 操作1: "查询余额" (op-querybal-uuid, parentId: cls-db-uuid)
        - 参数1: (param-accountid-uuid, parentId: op-querybal-uuid, name: "账户ID", direction: "in")

#### 第十步：识别关联
- (如果atm_instance和db_connector是ATM类的属性，则它们与ATM类之间存在组合关联。这些通常通过Property的aggregation和associationId体现，但这里简化，仅作为属性列出。如果文本明确提到关联，则创建Association元素。)

#### 第十一步：整理优化输出
---
模型: ATM系统模型 (model-atm-sys-uuid)
  包: 银行服务 (pkg-banksvc-uuid, parentId: model-atm-sys-uuid)
    Actor: 客户 (actor-customer-uuid, parentId: pkg-banksvc-uuid)
    类: ATM (cls-atm-uuid, parentId: pkg-banksvc-uuid)
      操作: 执行取款 (op-execwithdraw-uuid, parentId: cls-atm-uuid)
      属性: atm_instance (prop-atm-instance-uuid, parentId: cls-atm-uuid, typeId: cls-atm-uuid)
      属性: db_connector (prop-db-connector-uuid, parentId: cls-atm-uuid, typeId: cls-db-uuid)
    类: 后端数据库 (cls-db-uuid, parentId: pkg-banksvc-uuid)
      操作: 查询余额 (op-querybal-uuid, parentId: cls-db-uuid)
        参数: 账户ID (param-accountid-uuid, parentId: op-querybal-uuid)

    交互: 客户取钱 (interaction-withdraw-uuid, parentId: cls-atm-uuid, classifierBehaviorFor: cls-atm-uuid)
      属性 (Interaction-owned for Lifeline): p_customer (prop-interaction-customer-uuid, parentId: interaction-withdraw-uuid, typeId: actor-customer-uuid)
      生命线: L1 (ll-customer-uuid, parentId: interaction-withdraw-uuid, representsId: prop-interaction-customer-uuid)
      生命线: L2 (ll-atm-uuid, parentId: interaction-withdraw-uuid, representsId: prop-atm-instance-uuid)
      生命线: L3 (ll-db-uuid, parentId: interaction-withdraw-uuid, representsId: prop-db-connector-uuid)
        销毁于: fragment-destroy-db-uuid

      消息: 取款请求 (msg-reqwithdraw-uuid, parentId: interaction-withdraw-uuid, send: ll-customer-uuid, recv: ll-atm-uuid, sort: synchCall, sig: op-execwithdraw-uuid)
      消息: 验证余额 (msg-verifybal-uuid, parentId: interaction-withdraw-uuid, send: ll-atm-uuid, recv: ll-db-uuid, sort: synchCall, sig: op-querybal-uuid, args: ["账户ID"])
      消息: 余额信息 (msg-balinfo-uuid, parentId: interaction-withdraw-uuid, send: ll-db-uuid, recv: ll-atm-uuid, sort: reply)

      组合片段 (alt): cf-balancecheck-alt-uuid (parentId: interaction-withdraw-uuid, op: alt, covered: [ll-atm-uuid, ll-customer-uuid])
        操作数: operand-sufficient-uuid (parentId: cf-balancecheck-alt-uuid)
          守卫: guard-sufficient-uuid (parentId: operand-sufficient-uuid, spec: "余额充足")
          消息: 出钞 (msg-dispense-uuid, parentId: operand-sufficient-uuid, send: ll-atm-uuid, recv: ll-customer-uuid, sort: reply)
          片段 (MessageOccurrences for 出钞): ...
        操作数: operand-insufficient-uuid (parentId: cf-balancecheck-alt-uuid)
          守卫: (无)
          消息: 余额不足 (msg-insufficient-uuid, parentId: operand-insufficient-uuid, send: ll-atm-uuid, recv: ll-customer-uuid, sort: reply)
          片段 (MessageOccurrences for 余额不足): ...

      片段 (MessageOccurrenceSpecifications not in CF): ...
      片段 (DestructionOccurrenceSpecification): fragment-destroy-db-uuid (covered: ll-db-uuid, parentId: interaction-withdraw-uuid)
---


## 具体任务
输入：          
""" + question2  + """输出：请你一步一步进行推理思考。"""

messages = [
    HumanMessage(content=prompt1),
]

print("⭐⭐⭐正在执行任务：", question2)
response = chat.invoke(messages)

print("😊😊😊推理结果：", response.content)
print("😊😊😊使用消耗", response.usage_metadata)

prompt2 = prompt1 + response.content + """
根据以上详细的推理和“整理优化输出”，请严格按照以下 JSON 格式生成 SysML/UML 序列图的完整描述。请确保：
1.  所有 `id` 字段都是全局唯一的（可以使用推理中建议的临时系统 ID）。
2.  `parentId` 正确反映了元素的包含关系。
3.  生命线的 `representsId` 指向其所代表的属性（Property）的ID，该属性的类型（typeId）再指向对应的类、Actor。
4.  消息的 `sendEventId` 和 `receiveEventId` 指向对应的 `MessageOccurrenceSpecification` ID。
5.  消息的 `signatureId` 指向被调用的操作的ID（如果适用）。
6.  `MessageOccurrenceSpecification` 和 `DestructionOccurrenceSpecification` 的 `coveredId` 指向被覆盖的生命线ID，`messageId` (仅用于MessageOccurrenceSpecification) 指向关联的消息ID。它们的 `parentId` 是所属的 `Interaction` 或 `InteractionOperand`。
7.  `CombinedFragment` 包含 `interactionOperator`, `coveredLifelineIds`, 和 `operandIds`。其`parentId`是所属的`Interaction`或父`InteractionOperand`。
8.  `InteractionOperand` 包含 `guardId` (可选) 和 `fragmentIds` (其内部的片段)。其`parentId`是所属的`CombinedFragment`。
9.  `InteractionConstraint` (守卫) 包含 `specification` 对象（含 `body` 和 `language`）。其`parentId`是所属的`InteractionOperand`。
10. `Property` 元素可以作为类的属性，也可以作为交互内部定义的属性（用于生命线表示）。

```json
{
  "model": [
    {
      "id": "model-unique-id",
      "type": "Model",
      "name": "ModelName"
    }
  ],
  "elements": [
    // Packages
    {
      "id": "pkg-unique-id",
      "type": "Package",
      "name": "PackageName",
      "parentId": "model-unique-id"
    },
    // Actors, Classes, Blocks
    {
      "id": "actor-or-class-id",
      "type": "Actor", // or "Class", "Block"
      "name": "ElementName",
      "parentId": "pkg-unique-id",
      "classifierBehaviorId": "interaction-as-classifier-behavior-id",
      "ownedOperationIds": ["op-id-1"],
      "ownedAttributeIds": ["prop-id-1"] // Attributes of the Class/Actor
    },
    // Properties (Attributes of Classes/Actors, OR owned by Interaction for Lifelines)
    {
        "id": "prop-id-1",
        "type": "Property",
        "name": "propertyName",
        "parentId": "actor-or-class-id", // OR "interaction-id" if interaction-owned
        "typeId": "class-or-actor-type-id", // The type of this property
        "aggregation": "composite", // "composite", "shared", "none"
        "associationId": "assoc-id-1" // Optional
    },
    // Operations
    {
      "id": "op-id-1",
      "type": "Operation",
      "name": "OperationName",
      "parentId": "actor-or-class-id",
      "parameterIds": ["param-id-1"]
    },
    // Parameters
    {
        "id": "param-id-1",
        "type": "Parameter",
        "name": "ParamName",
        "parentId": "op-id-1",
        "direction": "in",
        "typeId": "some-type-id" // or typeHref
    },
    // Associations
    {
        "id": "assoc-id-1",
        "type": "Association",
        "parentId": "pkg-unique-id",
        "memberEndIds": ["prop-id-1", "prop-id-2-other-end"]
    },
    // Interactions
    {
      "id": "interaction-id-1",
      "type": "Interaction",
      "name": "InteractionName",
      "parentId": "actor-or-class-id", // if classifierBehavior or owned by Operation (then op's parent)
      "lifelineIds": ["ll-id-1"],
      "messageIds": ["msg-id-1"],
      "fragmentIds": ["frag-id-1", "cf-id-1"], // Includes all fragments (MOS, DOS, CF)
      "ownedAttributeIds": ["prop-interaction-owned-id"] // Properties defined inside interaction for lifelines
    },
    // Lifelines
    {
      "id": "ll-id-1",
      "type": "Lifeline",
      "name": "LifelineName",
      "parentId": "interaction-id-1",
      "representsId": "prop-id-1" // ID of a Property
    },
    // Messages
    {
      "id": "msg-id-1",
      "type": "Message",
      "name": "MessageName",
      "parentId": "interaction-id-1",
      "sendEventId": "frag-send-event-id-for-msg1",
      "receiveEventId": "frag-receive-event-id-for-msg1",
      "messageSort": "synchCall",
      "signatureId": "op-id-1",
      "arguments": [{"body": "argValue", "language": "text"}]
    },
    // Fragments - MessageOccurrenceSpecification
    {
      "id": "frag-send-event-id-for-msg1",
      "type": "MessageOccurrenceSpecification",
      "parentId": "interaction-id-1", // or "operand-id" if inside a CombinedFragment
      "coveredId": "ll-id-1",       // Lifeline ID
      "messageId": "msg-id-1"         // Message ID
    },
    // Fragments - DestructionOccurrenceSpecification
    {
      "id": "frag-destroy-id-1",
      "type": "DestructionOccurrenceSpecification",
      "parentId": "interaction-id-1", // or "operand-id"
      "coveredId": "ll-id-1"
    },
    // Fragments - CombinedFragment
    {
      "id": "cf-id-1",
      "type": "CombinedFragment",
      "name": "AltFragment",
      "parentId": "interaction-id-1", // or an outer operand-id
      "interactionOperator": "alt",
      "coveredLifelineIds": ["ll-id-1", "ll-id-2"],
      "operandIds": ["operand-id-1", "operand-id-2"]
    },
    // Fragments - InteractionOperand
    {
      "id": "operand-id-1",
      "type": "InteractionOperand",
      "parentId": "cf-id-1",
      "guardId": "constraint-id-1", // Optional
      "fragmentIds": ["frag-mos-inside-operand-id"] // Fragments inside this operand
    },
    // Fragments - InteractionConstraint (Guard)
    {
      "id": "constraint-id-1",
      "type": "InteractionConstraint",
      "parentId": "operand-id-1",
      "specification": {
        "body": "condition > value",
        "language": "English"
      }
    }
  ]
}
```

请严格按照上面的JSON结构输出结果。"""

print("⭐⭐⭐处理任务：", "复合COT")

messages = [
    HumanMessage(content=prompt2),
]

response = chat.invoke(messages)

print("😊😊😊处理结果", response.content)
print("😊😊😊使用消耗", response.usage_metadata)