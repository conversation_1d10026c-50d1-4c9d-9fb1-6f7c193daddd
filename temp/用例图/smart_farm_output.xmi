<?xml version='1.0' encoding='utf-8'?>
<xmi:XMI xmi:version="2.5" xmlns:xmi="http://www.omg.org/XMI" xmlns:uml="http://www.omg.org/spec/UML/20090901">
  <uml:Model xmi:type="uml:Model" xmi:id="model-smart-farm" name="智能农场用例模型">
    <packagedElement xmi:type="uml:Package" xmi:id="pkg-main-usecases" name="主要用例">
      <packagedElement xmi:type="uml:Actor" xmi:id="actor1" name="农场管理员" />
      <packagedElement xmi:type="uml:Actor" xmi:id="actor2" name="技术支持团队" />
      <packagedElement xmi:type="uml:Actor" xmi:id="actor3" name="自动播种机" />
      <packagedElement xmi:type="uml:Actor" xmi:id="actor4" name="采摘机器人" />
      <packagedElement xmi:type="uml:Actor" xmi:id="actor5" name="无人机" />
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase1" name="调节温室温度">
        <include xmi:type="uml:Include" xmi:id="rel-include-1" addition="useCase4" />
      </packagedElement>
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase2" name="调节湿度">
        <include xmi:type="uml:Include" xmi:id="rel-include-2" addition="useCase4" />
      </packagedElement>
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase3" name="调节光照">
        <include xmi:type="uml:Include" xmi:id="rel-include-3" addition="useCase4" />
      </packagedElement>
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase4" name="监控土壤湿度">
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-1" extendedCase="useCase5" />
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-2" extendedCase="useCase6" />
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-3" extendedCase="useCase7" />
      </packagedElement>
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase5" name="自动灌溉" />
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase6" name="自动施肥" />
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase7" name="自动施药" />
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase8" name="自动操作">
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-4" extendedCase="useCase3" />
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-5" extendedCase="useCase4" />
      </packagedElement>
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase9" name="监测作物健康">
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-6" extendedCase="useCase10" />
      </packagedElement>
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase10" name="发出预警" />
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase11" name="检查设备">
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-7" extendedCase="useCase12" />
        <extend xmi:type="uml:Extend" xmi:id="rel-extend-8" extendedCase="useCase13" />
      </packagedElement>
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase12" name="远程修复" />
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase13" name="派人修理" />
      <packagedElement xmi:type="uml:UseCase" xmi:id="useCase14" name="推送软件更新" />
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-1">
        <memberEnd xmi:idref="assoc-1_end1" />
        <memberEnd xmi:idref="assoc-1_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-1_end1" type="actor1" association="assoc-1" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-1_end2" type="useCase1" association="assoc-1" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-2">
        <memberEnd xmi:idref="assoc-2_end1" />
        <memberEnd xmi:idref="assoc-2_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-2_end1" type="actor1" association="assoc-2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-2_end2" type="useCase2" association="assoc-2" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-3">
        <memberEnd xmi:idref="assoc-3_end1" />
        <memberEnd xmi:idref="assoc-3_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-3_end1" type="actor1" association="assoc-3" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-3_end2" type="useCase3" association="assoc-3" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-4">
        <memberEnd xmi:idref="assoc-4_end1" />
        <memberEnd xmi:idref="assoc-4_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-4_end1" type="actor1" association="assoc-4" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-4_end2" type="useCase4" association="assoc-4" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-5">
        <memberEnd xmi:idref="assoc-5_end1" />
        <memberEnd xmi:idref="assoc-5_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-5_end1" type="actor1" association="assoc-5" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-5_end2" type="useCase5" association="assoc-5" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-6">
        <memberEnd xmi:idref="assoc-6_end1" />
        <memberEnd xmi:idref="assoc-6_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-6_end1" type="actor1" association="assoc-6" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-6_end2" type="useCase6" association="assoc-6" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-7">
        <memberEnd xmi:idref="assoc-7_end1" />
        <memberEnd xmi:idref="assoc-7_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-7_end1" type="actor1" association="assoc-7" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-7_end2" type="useCase7" association="assoc-7" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-8">
        <memberEnd xmi:idref="assoc-8_end1" />
        <memberEnd xmi:idref="assoc-8_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-8_end1" type="actor1" association="assoc-8" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-8_end2" type="useCase9" association="assoc-8" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-9">
        <memberEnd xmi:idref="assoc-9_end1" />
        <memberEnd xmi:idref="assoc-9_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-9_end1" type="actor1" association="assoc-9" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-9_end2" type="useCase10" association="assoc-9" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-10">
        <memberEnd xmi:idref="assoc-10_end1" />
        <memberEnd xmi:idref="assoc-10_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-10_end1" type="actor2" association="assoc-10" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-10_end2" type="useCase11" association="assoc-10" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-11">
        <memberEnd xmi:idref="assoc-11_end1" />
        <memberEnd xmi:idref="assoc-11_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-11_end1" type="actor2" association="assoc-11" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-11_end2" type="useCase12" association="assoc-11" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-12">
        <memberEnd xmi:idref="assoc-12_end1" />
        <memberEnd xmi:idref="assoc-12_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-12_end1" type="actor2" association="assoc-12" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-12_end2" type="useCase13" association="assoc-12" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-13">
        <memberEnd xmi:idref="assoc-13_end1" />
        <memberEnd xmi:idref="assoc-13_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-13_end1" type="actor2" association="assoc-13" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-13_end2" type="useCase14" association="assoc-13" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-14">
        <memberEnd xmi:idref="assoc-14_end1" />
        <memberEnd xmi:idref="assoc-14_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-14_end1" type="actor3" association="assoc-14" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-14_end2" type="useCase8" association="assoc-14" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-15">
        <memberEnd xmi:idref="assoc-15_end1" />
        <memberEnd xmi:idref="assoc-15_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-15_end1" type="actor4" association="assoc-15" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-15_end2" type="useCase8" association="assoc-15" />
      </packagedElement>
      <packagedElement xmi:type="uml:Association" xmi:id="assoc-16">
        <memberEnd xmi:idref="assoc-16_end1" />
        <memberEnd xmi:idref="assoc-16_end2" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-16_end1" type="actor5" association="assoc-16" />
        <ownedEnd xmi:type="uml:Property" xmi:id="assoc-16_end2" type="useCase9" association="assoc-16" />
      </packagedElement>
    </packagedElement>
  </uml:Model>
</xmi:XMI>