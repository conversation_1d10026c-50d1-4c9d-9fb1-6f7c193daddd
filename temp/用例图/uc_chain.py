from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

chat = ChatOpenAI(
    model="gpt-4o",
    temperature=0.0,
    max_tokens=8192,
    api_key="sk-TP4VOmsDu2OdsE8H88BaBd09D1B74bD983C7Ca67E6E9AeBf",
    base_url='https://happyapi.org/v1'
)

question1 = """在一个简单的车机空调案例中，司机可以通过车载系统开启空调，并设置相关参数，如温度、风向和湿度。与此同时，维修人员负责对空调进行维护和修理，确保其正常运行。"""
question2 = """在一个智能家居系统中，用户可以通过语音助手或者手机应用控制家中的各类设备。用户可以通过语音命令开启空调、调节温度、风速和风向；同时，用户也可以调节室内的灯光亮度、颜色温度，或者设置自动化场景，如“早晨模式”或者“回家模式”。此外，家中的安防系统也会根据用户的指令进行调整，比如开启或关闭监控摄像头、门窗传感器和报警系统。

与此同时，家庭技术支持团队负责定期检查和维护智能家居设备，确保所有设备的正常运行和连接稳定。在设备发生故障时，技术支持人员会远程进行故障诊断，或者派遣工作人员进行现场修复。对于设备的固件更新，用户会通过应用接收到通知，选择是否进行更新，以提高设备的性能和安全性。

这个系统还支持设备间的联动，比如当用户离开家时，系统自动关闭所有不必要的设备并启动安防系统；而在用户回家时，系统会根据预设场景自动开启灯光、空调以及其他设备。"""
question3 = """
在智能农业系统中，农场管理员可以远程调节温室的温度、湿度和光照，并监控土壤湿度，确保作物健康生长。系统自动进行灌溉、施肥和施药。

系统还包括自动播种机和采摘机器人，能够根据指令自动操作。无人机监测作物健康并发出预警。

技术支持团队定期检查设备，确保其正常运行，遇到问题时进行远程修复或派人修理。系统还会推送软件更新。
"""

prompt1 = """
## 角色
你是一位出色的用例图分析大师，你熟悉各种复杂语境，你可以精准的提取出文本中的参与者、用例以及它们之间复杂的关系。

## 规则
以下是我的提取输出规则：
- 参与者：actor1, actor2, actor3。
- 用例：用例A, 用例B, 用例C, 用例D, 用例E。
- 关系：扩展，包含，泛化，关联；例如：actor1-[泛化]->actor3、actor1->[关联]->用例A、用例A<-[扩展]-用例B、用例A<-[扩展]-用例C、用例D-[包含]->用例E。

### 提取规则：

- **参与者映射规则**：

  - 主语或动词前的名词短语映射为候选参与者。
  - 介词短语中的名词映射为候选参与者。
  - 抽象概念或系统本身不作为参与者。

- **用例映射规则**：

  - 谓语或动词短语映射为候选用例。
  - 过于抽象或泛化的动词短语不作为用例。
  - 用例均以动词开头
  - 一个用例不能出现两种截然不同的动作，例如开启和关闭。

- **关系提取规则**：

  - 主语和谓语之间存在关联关系。
  - 包含“包括”、“由...组成”等关键词的句子表示用例之间存在**包含关系**。
  - 包含指示继承或分类的词汇表示参与者或用例之间存在**泛化关系**。
  - 包含“扩展到”、“增加”等关键词的句子表示用例之间存在**扩展关系**。
  - 参与者与用例之间通常存在关联关系。

### 注意事项
1. 参与者与用例只有关联关系，并且是由参与者指向用例。
2. 用例之间有包含、扩展关系。
3. 参与者与参与者之间有泛化关系。例如：黄金会员-[泛化]->普通用户。
4. '包含关系'的要点：
    - 当可以从两个或两个以上的用例中提取公共行为时，应该使用包含关系。其中提取出来的公共用例成为抽象用例，而把原始用例变成基本用例或基础用例。箭头指向抽象用例。例如：借书-[包含]->登录，还书-[包含]->登录。
    - 一个用例的功能太多时，可以使用包含关系建立若干个更小的用例。例如：查询信息-[包含]->查看余额信息，查询信息-[包含]->查看交易记录，查询信息-[包含]->查看上机记录。
5. 扩展用例在扩展点上增加新的维护和含义。扩展用例为基用例添加新的行为，箭头指向基本用例。例如：导出Excel<-[扩展]-查看余额信息，导出Excel<-[扩展]-查看交易记录，导出Excel<-[扩展]-查看上机记录。
6. 系统本身不可以当作参与者。

## 样例

输入样例：
在一个简单的车机空调案例中，司机可以通过车载系统开启空调，并设置相关参数，如温度、风向和湿度。开启空调的操作必须包含打火启动这一前置条件。与此同时，维修人员负责对空调进行检查和修理，确保其正常运行，维修完毕后，维修人员可以打印维修报告。

### 第一步识别参与者
- 司机
- 维修人员

### 第二步：识别用例
- 开启空调
- 设置相关参数
- 设置温度
- 设置风向
- 设置湿度
- 打火启动
- 检查空调
- 修理空调
- 打印维修报告

### 第三步：确定用例之间的关系, 这里需要注意“注意事项”中里面对包含关系和扩展关系的要点描述
- 开启空调-[包含]->打火启动
- 设置相关参数-[包含]->设置温度
- 设置相关参数-[包含]->设置风向
- 设置相关参数-[包含]->设置湿度
- 修理空调<-[扩展]-打印维修报告

### 第四步：确定参与者与用例的关系
- 司机->[关联]->开启空调
- 司机->[关联]->打火启动
- 司机->[关联]->设置相关参数
- 维修人员->[关联]->检查空调
- 维修人员->[关联]->修理空调

### 第五步：综合输出
- 参与者：司机, 维修人员。
- 用例：开启空调, 设置温度, 设置风向, 设置湿度, 打火启动, 维护空调, 修理空调。        
- 关系：司机->[关联]->开启空调、司机->[关联]->打火启动、司机->[关联]->设置相关参数、维修人员->[关联]->检查空调、维修人员->[关联]->修理空调、开启空调-[包含]->打火启动、设置相关参数-[包含]->设置温度、设置相关参数-[包含]->设置风向、设置相关参数-[包含]->设置湿度、修理空调<-[扩展]-打印维修报告

### 第六步：优化输出
这一步目的主要检查上一步的输出关系是否含有冗余的关系连线，以及关系是否正确且合理。
例如: 参与者A关联了用例A，用例A扩展了用例B，参与者A又关联了用例B，这种情况下是含有冗余关系的。简而言之，扩展关系意味着用例B的行为是由用例A的执行条件决定的，因此不需要再次显式地关联用例B，A参与者与用例A的关联已经包含了用例B的参与。

## 具体任务
输入：          
""" + question3  + """输出：请你一步一步进行推理思考。"""

messages = [
    HumanMessage(content=prompt1),
]

response = chat.invoke(messages)

print(response.content)


prompt2 = prompt1 + response.content + """
因此，请从优化输出中，为我提取JSON对象，如下格式：
ID需要保证全局唯一。
  ```json
 {
  "model": [
    {
      "id": "model-uc-unique-id", // 模型的唯一ID
      "name": "模型名称" // 例如 "智能农业用例模型"
    }
  ],
  "elements": [
    {
      "id": "pkg-uc-unique-id",     // 包的唯一ID
      "type": "Package",           // 类型固定为 "Package"
      "name": "包名称",              // 例如 "主要用例"
      "parentId": "model-uc-unique-id" // 父ID，指向模型ID
    },
    {
      "id": "actor-unique-id",     // 参与者的唯一ID
      "type": "Actor",             // 类型固定为 "Actor"
      "name": "参与者名称",
      "parentId": "pkg-uc-unique-id" // 父ID，指向包ID
    },
    {
      "id": "usecase-unique-id",   // 用例的唯一ID
      "type": "UseCase",           // 类型固定为 "UseCase"
      "name": "用例名称",
      "parentId": "pkg-uc-unique-id" // 父ID，指向包ID
    },
    {
      "id": "assoc-unique-id",       // 关联关系的唯一ID
      "type": "Association",       // 类型
      "sourceId": "actor-unique-id", // 源ID
      "targetId": "usecase-unique-id",// 目标ID
      "parentId": "pkg-uc-unique-id" // 父ID，指向包ID
    },
    {
      "id": "include-rel-unique-id", // 包含关系的唯一ID
      "type": "Include",
      "sourceId": "source-uc-id",
      "targetId": "target-uc-id",
      "parentId": "pkg-uc-unique-id"
    },
    {
      "id": "extend-rel-unique-id",  // 扩展关系的唯一ID
      "type": "Extend",
      "sourceId": "source-uc-id",
      "targetId": "target-uc-id",
      "parentId": "pkg-uc-unique-id"
    },
    {
      "id": "gen-rel-unique-id",     // 泛化关系的唯一ID
      "type": "Generalization",
      "sourceId": "source-actor-id",
      "targetId": "target-actor-id",
      "parentId": "pkg-uc-unique-id"
    }
  ]
}
  ```"""

print(prompt2)

messages = [
    HumanMessage(content=prompt2),
]

response = chat.invoke(messages)

print(response.content)