import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
import traceback
from collections import defaultdict
from typing import Dict, List, Any, Optional, Union

class SysMLJsonToXmlConverter:
    """
    SysML JSON到XML转换器
    
    该类能够将SysML JSON格式的模型转换为标准的SysML XMI (XML)格式。
    支持多种SysML图表类型，包括：
    - 需求图 (Requirement Diagram)
    - 块定义图/内部块图 (Block Definition/Internal Block Diagram)
    - 状态机图 (State Machine Diagram)
    - 序列图 (Sequence Diagram)
    - 活动图 (Activity Diagram)
    - 用例图 (Use Case Diagram)
    - 参数图 (Parametric Diagram)
    """
    
    # 命名空间定义
    NAMESPACES = {
        "xmi": "http://www.omg.org/spec/XMI/20131001",
        "uml": "http://www.omg.org/spec/UML/20131001",
        "sysml": "http://www.omg.org/spec/SysML/20181001/SysML",
        "StandardProfile": "http://www.omg.org/spec/UML/20131001/StandardProfile",
        "MagicDraw_Profile": "http://www.omg.org/spec/UML/20131001/MagicDrawProfile",
        "MD_Customization_for_SysML__additional_stereotypes": "http://www.magicdraw.com/spec/Customization/180/SysML",
        "DSL_Customization": "http://www.magicdraw.com/schemas/DSL_Customization.xmi",
        "diagram": "http://www.nomagic.com/ns/magicdraw/core/diagram/1.0"
    }
    
    # 基本类型映射
    PRIMITIVE_TYPE_MAP = {
        "Real": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real",
        "Integer": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Integer",
        "String": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.String",
        "Boolean": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Boolean",
        "VerdictKind": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"
    }

    def __init__(self, json_data: Union[str, dict]):
        """
        初始化转换器
        
        参数:
            json_data: JSON字符串或已解析的字典对象
        """
        # 解析JSON数据
        if isinstance(json_data, str):
            self.json_data = json.loads(json_data)
        else:
            self.json_data = json_data
            
        # 初始化内部状态
        self.elements_by_id = {}  # 元素ID到元素的映射
        self.children_by_parent = defaultdict(list)  # 父元素ID到子元素ID列表的映射
        self.xml_elements = {}  # 元素ID到XML元素的映射
        self.stereotypes_to_apply = defaultdict(list)  # 构造型应用列表
        self.lifelines_to_update = defaultdict(list)  # 序列图中需要更新的生命线
        self.model_id = None  # 模型ID
        self.model_name = None  # 模型名称
        self.xmi_root = None  # XML根元素

    def _generate_id(self, base_id: str, suffix: str) -> str:
        """
        为内部或构造型元素生成唯一ID
        
        参数:
            base_id: 基础ID
            suffix: 后缀
            
        返回:
            生成的唯一ID
        """
        clean_base = str(base_id).replace("-", "_")
        clean_suffix = str(suffix).replace("-", "_").replace(":", "_")
        return f"_{clean_base}_{clean_suffix}"

    def _preprocess_data(self) -> bool:
        """
        预处理JSON数据，构建查找字典
        
        返回:
            处理是否成功
        """
        print("--- 阶段 1: 预处理JSON数据 ---")
        if "elements" not in self.json_data:
            print("错误: JSON数据必须包含 'elements' 键。")
            return False

        # 构建元素ID到元素的映射
        self.elements_by_id = {elem["id"]: elem for elem in self.json_data["elements"]}

        # 处理模型数据
        model_value = self.json_data.get("model")
        model_data = {}

        if isinstance(model_value, list):
            if model_value: 
                model_data = model_value[0]
        elif isinstance(model_value, dict):
            model_data = model_value
            
        self.model_id = model_data.get("id", "default-model-id")
        self.model_name = model_data.get("name", "DefaultModel")
        
        # 确保模型也在元素映射中
        if self.model_id not in self.elements_by_id:
            self.elements_by_id[self.model_id] = model_data

        # 构建父子关系映射
        for elem_id, elem_data in self.elements_by_id.items():
            parent_id = elem_data.get("parentId")
            if parent_id: 
                self.children_by_parent[parent_id].append(elem_id)

            # 将需要应用构造型的元素分类存储
            elem_type = elem_data.get("type")
            stereotype_category = self._get_stereotype_category(elem_type, elem_data)
            if stereotype_category:
                self.stereotypes_to_apply[stereotype_category].append(elem_data)
            
        print(f"数据预处理完成。共找到 {len(self.elements_by_id)} 个元素。")
        return True

    def _get_stereotype_category(self, elem_type: str, elem_data: Dict) -> Optional[str]:
        """
        根据元素类型返回其所属的构造型分类
        
        参数:
            elem_type: 元素类型
            elem_data: 元素数据
            
        返回:
            构造型分类或None
        """
        if elem_type in ["Requirement", "Block", "TestCase", "ValueType", "Unit", 
                         "ConstraintBlock", "InterfaceBlock", "FullPort", "ProxyPort", 
                         "ConstraintParameter", "ActivityPartition", "BindingConnector"]:
            return elem_type
        if elem_type in ["DeriveReqt", "Satisfy", "Verify"]:
            return "Abstraction"
        if elem_type == "Property" and "propertyKind" in elem_data:
            return "Property"
        return None

    def _create_base_structure(self) -> None:
        """
        创建XMI根、模型并递归创建所有元素
        """
        print("--- 阶段 2: 创建完整的UML结构 ---")
        self.xmi_root = ET.Element("xmi:XMI", attrib={"xmi:version": "2.5"})
        
        # 添加命名空间
        for prefix, uri in self.NAMESPACES.items():
            self.xmi_root.set(f"xmlns:{prefix}", uri)

        # 创建模型元素
        model_attrs = {"xmi:type": "uml:Model", "xmi:id": self.model_id, "name": self.model_name}
        model_xml = ET.SubElement(self.xmi_root, "uml:Model", attrib=model_attrs)
        self.xml_elements[self.model_id] = model_xml
        
        # 添加SysML配置文件应用
        profile_app = ET.SubElement(model_xml, "profileApplication", 
                                   {"xmi:type": "uml:ProfileApplication", 
                                    "xmi:id": self._generate_id(self.model_id, "sysml_profile_app")})
        ET.SubElement(profile_app, "appliedProfile", 
                     {"href": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML"})
        
        # 从根节点开始递归创建所有元素
        self._create_element_recursive(self.model_id, model_xml)

    def _create_element_recursive(self, parent_id: str, parent_xml_node: ET.Element) -> None:
        """
        递归地为给定的父节点创建所有子元素
        
        参数:
            parent_id: 父元素ID
            parent_xml_node: 父元素的XML节点
        """
        # 按ID排序以确保一致性
        for child_id in sorted(self.children_by_parent.get(parent_id, [])):
            # 避免重复创建
            if child_id in self.xml_elements:
                continue
                
            child_data = self.elements_by_id.get(child_id)
            if not child_data:
                continue

            elem_type = child_data.get("type")
            elem_name = child_data.get("name")
            
            # 基本属性
            attrs = {"xmi:id": child_id}
            if elem_name:
                attrs["name"] = elem_name
            
            # 确定标签和类型
            tag_name, xmi_type = self._determine_tag_and_type(child_data)
            if not tag_name:
                continue
            
            attrs["xmi:type"] = xmi_type
            
            # 处理特殊关系元素
            current_parent_xml = parent_xml_node
            if elem_type in ["Include", "Extend", "Generalization"]:
                source_id = child_data.get("sourceId")
                if source_id in self.xml_elements:
                    current_parent_xml = self.xml_elements[source_id]
                else:
                    print(f"警告：关系 '{child_id}' 的源节点 '{source_id}' 在创建时未找到。")
                    continue
            
            # 创建XML元素
            xml_elem = ET.SubElement(current_parent_xml, tag_name, attrs)
            self.xml_elements[child_id] = xml_elem
            
            # 填充元素细节
            self._populate_element_details(xml_elem, child_data)
            
            # 递归处理子元素
            self._create_element_recursive(child_id, xml_elem)

    def _determine_tag_and_type(self, elem_data: Dict) -> tuple:
        """
        根据元素类型和上下文决定XML标签和xmi:type
        
        参数:
            elem_data: 元素数据
            
        返回:
            (标签名, xmi类型)
        """
        elem_type = elem_data.get("type")
        parent_id = elem_data.get("parentId")
        parent_type = self.elements_by_id.get(parent_id, {}).get("type") if parent_id else None
        
        # 默认值
        tag, xtype = 'packagedElement', f"uml:{elem_type}"

        # 根据元素类型确定正确的标签和类型
        if elem_type in ["Block", "Requirement", "ConstraintBlock", "InterfaceBlock"]:
            xtype = "uml:Class"
        elif elem_type == "TestCase":
            xtype = "uml:Activity"
        elif elem_type == "ValueType":
            xtype = "uml:DataType"
        elif elem_type == "Unit":
            xtype = "uml:InstanceSpecification"
        elif elem_type == "Enumeration":
            xtype = "uml:Enumeration"
        elif elem_type == "Property":
            tag = 'ownedAttribute'
            xtype = "uml:Property"
        elif elem_type in ["FullPort", "ProxyPort", "ConstraintParameter", "Port"]:
            tag = 'ownedAttribute'
            xtype = "uml:Port"
        elif elem_type == "Operation":
            tag = 'ownedOperation'
        elif elem_type == "Reception":
            tag = 'ownedReception'
        elif elem_type == "Parameter":
            tag = 'ownedParameter'
        elif elem_type == "EnumerationLiteral":
            tag = 'ownedLiteral'
        elif elem_type in ["StateMachine", "Interaction", "Activity"]:
            tag = 'ownedBehavior' if parent_type == "Block" else 'packagedElement'
        elif elem_type == "Region":
            tag = 'region'
        elif elem_type in ["State", "FinalState"]:
            tag = 'subvertex'
        elif elem_type == "Pseudostate":
            tag = 'connectionPoint' if parent_type == "State" else 'subvertex'
        elif elem_type == "Transition":
            tag = 'transition'
        elif elem_type == "Lifeline":
            tag = 'lifeline'
        elif elem_type == "Message":
            tag = 'message'
        elif elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification", "CombinedFragment"]:
            tag = 'fragment'
        elif elem_type == "InteractionOperand":
            tag = 'operand'
        elif elem_type in ["InitialNode", "ActivityFinalNode", "FlowFinalNode", "DecisionNode", 
                          "MergeNode", "ForkNode", "JoinNode", "CallBehaviorAction", 
                          "CentralBufferNode", "ActivityParameterNode"]:
            tag = 'node'
        elif elem_type in ["InputPin", "OutputPin"]:
            tag = 'argument' if elem_type == 'InputPin' else 'result'
        elif elem_type in ["ControlFlow", "ObjectFlow"]:
            tag = 'edge'
        elif elem_type == "ActivityPartition":
            tag = 'group'
        elif elem_type in ["DeriveReqt", "Satisfy", "Verify"]:
            xtype = "uml:Abstraction"
        elif elem_type in ["AssemblyConnector", "BindingConnector"]:
            tag = 'ownedConnector'
            xtype = "uml:Connector"
        elif elem_type == "Include":
            tag = 'include'
        elif elem_type == "Extend":
            tag = 'extend'
        elif elem_type == "Generalization":
            tag = 'generalization'
        elif elem_type == "Diagram":
            return None, None
        
        return tag, xtype

    def _populate_element_details(self, xml_elem: ET.Element, elem_data: Dict) -> None:
        """
        填充元素的具体属性和非递归子元素
        
        参数:
            xml_elem: XML元素
            elem_data: 元素数据
        """
        elem_id = elem_data.get("id")
        elem_type = elem_data.get("type")
        
        # 处理不同类型元素的特定属性
        if elem_type == "Requirement":
            # 设置需求ID和文本
            if "requirementId" in elem_data:
                ET.SubElement(xml_elem, "ownedAttribute", {
                    "xmi:id": self._generate_id(elem_id, "id_attr"),
                    "name": "id",
                    "visibility": "private"
                })
            if "text" in elem_data:
                ET.SubElement(xml_elem, "ownedAttribute", {
                    "xmi:id": self._generate_id(elem_id, "text_attr"),
                    "name": "text",
                    "visibility": "private"
                })
        
        elif elem_type == "Property":
            # 设置属性类型、可见性、聚合等
            if "typeId" in elem_data:
                self._add_type_ref_to_element(xml_elem, elem_data["typeId"])
            if "visibility" in elem_data:
                xml_elem.set("visibility", elem_data["visibility"])
            if "aggregation" in elem_data:
                xml_elem.set("aggregation", elem_data["aggregation"])
            if "multiplicity" in elem_data:
                mult = elem_data["multiplicity"]
                lower_val = ET.SubElement(xml_elem, "lowerValue", {
                    "xmi:id": self._generate_id(elem_id, "lower"),
                    "xmi:type": "uml:LiteralInteger",
                    "value": str(mult.get("lower", "1"))
                })
                upper_val = ET.SubElement(xml_elem, "upperValue", {
                    "xmi:id": self._generate_id(elem_id, "upper"),
                    "xmi:type": "uml:LiteralUnlimitedNatural",
                    "value": str(mult.get("upper", "1"))
                })
        
        elif elem_type in ["Association", "Generalization"]:
            # 设置关联的源和目标
            if "sourceId" in elem_data:
                xml_elem.set("source", elem_data["sourceId"])
            if "targetId" in elem_data:
                xml_elem.set("target", elem_data["targetId"])
        
        elif elem_type == "Transition":
            # 设置转换的源和目标
            if "sourceId" in elem_data:
                xml_elem.set("source", elem_data["sourceId"])
            if "targetId" in elem_data:
                xml_elem.set("target", elem_data["targetId"])
            # 添加触发器
            if "trigger" in elem_data:
                trigger = ET.SubElement(xml_elem, "trigger", {
                    "xmi:id": self._generate_id(elem_id, "trigger"),
                    "xmi:type": "uml:Trigger",
                    "name": elem_data["trigger"]
                })
        
        elif elem_type == "Message":
            # 设置消息的发送和接收事件
            if "sendEventId" in elem_data:
                xml_elem.set("sendEvent", elem_data["sendEventId"])
            if "receiveEventId" in elem_data:
                xml_elem.set("receiveEvent", elem_data["receiveEventId"])
            # 设置消息排序
            if "sort" in elem_data:
                xml_elem.set("messageSort", elem_data["sort"])
        
        # 处理其他特定类型...

    def _add_type_ref_to_element(self, parent_xml: ET.Element, type_id: str) -> None:
        """
        为元素添加类型引用
        
        参数:
            parent_xml: 父XML元素
            type_id: 类型ID
        """
        if type_id in self.PRIMITIVE_TYPE_MAP:
            # 基本类型
            ET.SubElement(parent_xml, "type", {"href": self.PRIMITIVE_TYPE_MAP[type_id]})
        else:
            # 自定义类型
            parent_xml.set("type", type_id)

    def _post_process_references(self) -> None:
        """
        后处理阶段，处理元素间的引用关系
        """
        print("--- 阶段 3: 处理引用关系 ---")
        # 处理生命线引用
        for lifeline_id, lifeline_data in self.lifelines_to_update.items():
            if lifeline_id in self.xml_elements:
                lifeline_xml = self.xml_elements[lifeline_id]
                for ref_data in lifeline_data:
                    # 添加引用
                    pass

    def _apply_stereotypes(self) -> None:
        """
        应用SysML构造型
        """
        print("--- 阶段 4: 应用SysML构造型 ---")
        # 应用Block构造型
        for block_data in self.stereotypes_to_apply.get("Block", []):
            block_id = block_data["id"]
            if block_id in self.xml_elements:
                ET.SubElement(self.xmi_root, "sysml:Block", {
                    "xmi:id": self._generate_id(block_id, "block_stereotype"),
                    "base_Class": block_id
                })
        
        # 应用Requirement构造型
        for req_data in self.stereotypes_to_apply.get("Requirement", []):
            req_id = req_data["id"]
            if req_id in self.xml_elements:
                attrs = {
                    "xmi:id": self._generate_id(req_id, "req_stereotype"),
                    "base_Class": req_id
                }
                if "requirementId" in req_data:
                    attrs["id"] = req_data["requirementId"]
                if "text" in req_data:
                    attrs["text"] = req_data["text"]
                ET.SubElement(self.xmi_root, "sysml:Requirement", attrs)
        
        # 应用其他构造型...

    def _prettify_xml(self) -> str:
        """
        美化XML输出
        
        返回:
            格式化的XML字符串
        """
        rough_string = ET.tostring(self.xmi_root, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ")

    def convert(self) -> Optional[str]:
        """
        执行从JSON到XMI的完整转换流程
        
        返回:
            格式化的XMI字符串，如果转换失败则返回None
        """
        try:
            if not self._preprocess_data():
                return None
                
            self._create_base_structure()
            self._post_process_references()
            self._apply_stereotypes()
            
            return self._prettify_xml()
        except Exception as e:
            print(f"在生成XMI过程中发生错误: {str(e)}")
            traceback.print_exc()
            return None

def convert_sysml_json_to_xml(json_data: Union[str, dict]) -> Optional[str]:
    """
    将SysML JSON数据转换为XMI格式
    
    参数:
        json_data: JSON字符串或已解析的字典对象
        
    返回:
        格式化的XMI字符串，如果转换失败则返回None
    """
    converter = SysMLJsonToXmlConverter(json_data)
    return converter.convert()

# 示例用法
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 从文件读取JSON
        with open(sys.argv[1], 'r', encoding='utf-8') as f:
            json_str = f.read()
        
        # 转换为XML
        xml_str = convert_sysml_json_to_xml(json_str)
        
        if xml_str:
            # 输出到文件或标准输出
            if len(sys.argv) > 2:
                with open(sys.argv[2], 'w', encoding='utf-8') as f:
                    f.write(xml_str)
                print(f"XMI已保存到 {sys.argv[2]}")
            else:
                print(xml_str)
        else:
            print("转换失败")
    else:
        print("用法: python sysml_json2xml.py <input_json_file> [output_xml_file]") 