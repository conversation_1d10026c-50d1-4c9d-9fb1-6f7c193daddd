import json
from collections import defaultdict

def validate_and_fix_json(json_data):
    """
    验证并修复AI生成的SysML JSON数据中的常见错误。

    Args:
        json_data (dict or str): 从AI获取的JSON数据，可以是字典或字符串。

    Returns:
        dict: 经过验证和修复的JSON数据字典。返回的数据可以安全地
              用于后续的XML生成。
    """
    print("--- Running JSON Validation and Fix ---")
    
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON format. {e}")
            return None
    else:
        data = json_data

    if 'elements' not in data:
        print("Error: JSON is missing 'elements' list.")
        return data

    elements = data['elements']
    elements_by_id = {elem['id']: elem for elem in elements}
    
    # 额外将顶层Model加入查找字典，以处理对它的parentId引用
    if 'model' in data and 'id' in data['model']:
        elements_by_id[data['model']['id']] = data['model']

    all_ids = set(elements_by_id.keys())
    has_errors = False

    # --- 1. 修复 ValueType 使用不一致 ---
    valuetype_map = {
        elem.get('name', '').lower(): elem['id'] 
        for elem in elements 
        if elem.get('type') == 'ValueType' and 'name' in elem
    }
    
    for elem in elements:
        if (elem.get('type') == 'Property' and 
            elem.get('propertyKind') == 'value' and 
            elem.get('name', '').lower() in valuetype_map):
            
            prop_name_lower = elem['name'].lower()
            expected_type_id = valuetype_map[prop_name_lower]
            current_type_id = elem.get('typeId')

            # 如果当前类型是基本类型，但存在同名的ValueType，则修复
            if current_type_id in ['String', 'Real', 'Integer', 'Boolean'] and current_type_id != expected_type_id:
                print(f"[FIX] ValueType Mismatch: Property '{elem['id']}' typeId corrected from '{current_type_id}' to '{expected_type_id}'.")
                elem['typeId'] = expected_type_id
    
    # --- 2. 修复 Connector partRefId 引用错误 ---
    for elem in elements:
        if elem.get('type') in ['AssemblyConnector', 'BindingConnector']:
            container_block_id = elem.get('parentId')
            if not container_block_id or container_block_id not in elements_by_id:
                continue

            container_block = elements_by_id[container_block_id]
            
            # 获取容器块中所有部件属性的 {name: id} 映射
            container_parts_map = {}
            for prop_id in container_block.get('properties', []):
                if prop_id in elements_by_id and elements_by_id[prop_id].get('propertyKind') == 'part':
                    part_prop = elements_by_id[prop_id]
                    container_parts_map[part_prop.get('name')] = part_prop.get('id')

            # 兼容 end1/end2 和 end 列表
            ends = []
            if 'end1' in elem and 'end2' in elem: ends.extend([elem['end1'], elem['end2']])
            elif 'end' in elem and isinstance(elem['end'], list): ends.extend(elem['end'])
            
            for end in ends:
                if not end or not end.get('partRefId'): continue
                
                part_ref_id = end['partRefId']
                
                # 如果引用的部件ID不在容器的部件列表中
                if part_ref_id not in container_parts_map.values():
                    part_ref_elem = elements_by_id.get(part_ref_id)
                    # 如果能根据名称在容器中找到正确的部件ID
                    if part_ref_elem and part_ref_elem.get('name') in container_parts_map:
                        correct_part_id = container_parts_map[part_ref_elem.get('name')]
                        print(f"[FIX] Connector partRefId: In Connector '{elem['id']}', end '{end['id']}' corrected from '{part_ref_id}' to '{correct_part_id}'.")
                        end['partRefId'] = correct_part_id

    # --- 3. 通用悬空ID检查 (报告但不修复) ---
    id_ref_fields = ['parentId', 'typeId', 'associationId', 'generalId', 'signalId', 'unitId']
    
    for elem in elements:
        for field in id_ref_fields:
            if field in elem and elem[field] is not None and elem[field] not in all_ids:
                if not (field == 'typeId' and elem[field] in ['String', 'Real', 'Integer', 'Boolean']):
                    print(f"[WARN] Dangling ID: Element '{elem['id']}' has a dangling reference in field '{field}': '{elem[field]}' not found.")
                    has_errors = True
        
        # 检查 Association 的 memberEndIds
        if elem.get('type') == 'Association':
            for member_id in elem.get('memberEndIds', []):
                if member_id not in all_ids:
                    print(f"[WARN] Dangling ID: Association '{elem['id']}' has a dangling memberEndId: '{member_id}' not found.")
                    has_errors = True
        
        # 检查 Connector 的引用
        if elem.get('type') in ['AssemblyConnector', 'BindingConnector']:
            ends = []
            if 'end1' in elem and 'end2' in elem: ends.extend([elem['end1'], elem['end2']])
            elif 'end' in elem and isinstance(elem['end'], list): ends.extend(elem['end'])
            for end in ends:
                for ref_field in ['partRefId', 'portRefId', 'propertyRefId']:
                     if end and end.get(ref_field) and end[ref_field] not in all_ids:
                        print(f"[WARN] Dangling ID: Connector '{elem['id']}' end '{end['id']}' has a dangling reference in field '{ref_field}': '{end[ref_field]}' not found.")
                        has_errors = True


    if not has_errors:
        print("--- Validation Complete: No dangling IDs found. ---")
    else:
        print("--- Validation Complete: Warnings were reported. ---")
        
    return data

json_string_from_ai = """
 {
  "model": { "id": "model-vehicle-uuid", "name": "VehicleModel" },
  "elements": [
    {
      "id": "pkg-vehicle-uuid",
      "type": "Package",
      "name": "VehicleComponentsPackage",
      "parentId": None
    },
    {
      "id": "unit-mm-uuid",
      "type": "Unit",
      "name": "Millimeter",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "unit-kg-uuid",
      "type": "Unit",
      "name": "Kilogram",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "vt-material-uuid",
      "type": "ValueType",
      "name": "Material",
      "parentId": "pkg-vehicle-uuid",
      "baseType": "String"
    },
    {
      "id": "vt-weight-uuid",
      "type": "ValueType",
      "name": "Weight",
      "parentId": "pkg-vehicle-uuid",
      "baseType": "Real",
      "unitId": "unit-kg-uuid"
    },
    {
      "id": "vt-diameter-uuid",
      "type": "ValueType",
      "name": "Diameter",
      "parentId": "pkg-vehicle-uuid",
      "baseType": "Real",
      "unitId": "unit-mm-uuid"
    },
    {
      "id": "vt-width-uuid",
      "type": "ValueType",
      "name": "Width",
      "parentId": "pkg-vehicle-uuid",
      "baseType": "Real",
      "unitId": "unit-mm-uuid"
    },
    {
      "id": "blk-frames-uuid",
      "type": "Block",
      "name": "Frame",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-wheels-uuid",
      "type": "Block",
      "name": "Wheel",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-drivesystem-uuid",
      "type": "Block",
      "name": "DriveSystem",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-brakesystem-uuid",
      "type": "Block",
      "name": "BrakeSystem",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-controlhandle-uuid",
      "type": "Block",
      "name": "ControlHandle",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-battery-uuid",
      "type": "Block",
      "name": "Battery",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-sensormodule-uuid",
      "type": "Block",
      "name": "SensorModule",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-display-uuid",
      "type": "Block",
      "name": "Display",
      "parentId": "pkg-vehicle-uuid"
    },
    {
      "id": "blk-vehicle-uuid",
      "type": "Block",
      "name": "MainVehicle",
      "parentId": None
    },
    {
      "id": "prop-veh-frame-uuid",
      "type": "Property",
      "name": "frame",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-frames-uuid",
      "associationId": "assoc-veh-frame-uuid"
    },
    {
      "id": "prop-veh-wheel-uuid",
      "type": "Property",
      "name": "wheel",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-wheels-uuid",
      "associationId": "assoc-veh-wheel-uuid"
    },
    {
      "id": "prop-veh-drivesystem-uuid",
      "type": "Property",
      "name": "driveSystem",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-drivesystem-uuid",
      "associationId": "assoc-veh-drivesystem-uuid"
    },
    {
      "id": "prop-veh-brakesystem-uuid",
      "type": "Property",
      "name": "brakeSystem",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-brakesystem-uuid",
      "associationId": "assoc-veh-brakesystem-uuid"
    },
    {
      "id": "prop-veh-controlhandle-uuid",
      "type": "Property",
      "name": "controlHandle",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-controlhandle-uuid",
      "associationId": "assoc-veh-controlhandle-uuid"
    },
    {
      "id": "prop-veh-battery-uuid",
      "type": "Property",
      "name": "battery",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-battery-uuid",
      "associationId": "assoc-veh-battery-uuid"
    },
    {
      "id": "prop-veh-sensormodule-uuid",
      "type": "Property",
      "name": "sensorModule",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-sensormodule-uuid",
      "associationId": "assoc-veh-sensormodule-uuid"
    },
    {
      "id": "prop-veh-display-uuid",
      "type": "Property",
      "name": "display",
      "parentId": "blk-vehicle-uuid",
      "propertyKind": "part",
      "typeId": "blk-display-uuid",
      "associationId": "assoc-veh-display-uuid"
    },
    {
      "id": "op-veh-connectdrive-uuid",
      "type": "Operation",
      "name": "connectDriveToWheel",
      "parentId": "blk-vehicle-uuid"
    },
    {
      "id": "op-veh-connectbrake-uuid",
      "type": "Operation",
      "name": "connectBrakeToSystem",
      "parentId": "blk-vehicle-uuid"
    },
    {
      "id": "op-veh-connectsensor-display-uuid",
      "type": "Operation",
      "name": "connectSensorToDisplay",
      "parentId": "blk-vehicle-uuid"
    },
    {
      "id": "prop-frames-material-uuid",
      "type": "Property",
      "name": "material",
      "parentId": "blk-frames-uuid",
      "propertyKind": "value",
      "typeId": "vt-material-uuid"
    },
    {
      "id": "prop-frames-weight-uuid",
      "type": "Property",
      "name": "weight",
      "parentId": "blk-frames-uuid",
      "propertyKind": "value",
      "typeId": "vt-weight-uuid"
    },
    {
      "id": "prop-wheels-diameter-uuid",
      "type": "Property",
      "name": "diameter",
      "parentId": "blk-wheels-uuid",
      "propertyKind": "value",
      "typeId": "vt-diameter-uuid"
    },
    {
      "id": "prop-wheels-width-uuid",
      "type": "Property",
      "name": "width",
      "parentId": "blk-wheels-uuid",
      "propertyKind": "value",
      "typeId": "vt-width-uuid"
    },
    {
      "id": "prop-drivesystem-resp-uuid",
      "type": "Property",
      "name": "responsibility",
      "parentId": "blk-drivesystem-uuid",
      "propertyKind": "value",
      "typeId": "String"
    },
    {
      "id": "port-drivesystem-powerout-uuid",
      "type": "FullPort",
      "name": "powerOutput",
      "parentId": "blk-drivesystem-uuid",
      "typeId": "PowerPort"
    },
    {
      "id": "conn-ds-wheel-uuid",
      "type": "AssemblyConnector",
      "name": "connectsToWheel",
      "parentId": "blk-drivesystem-uuid",
      "kind": "assembly",
      "end1": {
        "id": "cend-ds-wheel-1-uuid",
        "partRefId": "prop-veh-wheel-uuid",
        "portRefId": "port-drivesystem-powerout-uuid"
      },
      "end2": {
        "id": "cend-ds-wheel-2-uuid",
        "partRefId": "prop-veh-wheel-uuid",
        "portRefId": None
      }
    },
    {
      "id": "prop-brakesystem-resp-uuid",
      "type": "Property",
      "name": "responsibility",
      "parentId": "blk-brakesystem-uuid",
      "propertyKind": "value",
      "typeId": "String"
    },
    {
      "id": "port-brakesystem-control-uuid",
      "type": "FullPort",
      "name": "brakeControl",
      "parentId": "blk-brakesystem-uuid",
      "typeId": "BrakeControlPort"
    },
    {
      "id": "conn-bs-brake-uuid",
      "type": "AssemblyConnector",
      "name": "connectsToBrake",
      "parentId": "blk-brakesystem-uuid",
      "kind": "assembly",
      "end1": {
        "id": "cend-bs-control-uuid",
        "partRefId": "prop-veh-brake-uuid",
        "portRefId": "port-brakesystem-control-uuid"
      },
      "end2": {
        "id": "cend-bs-brake-uuid",
        "partRefId": "prop-veh-brake-uuid",
        "portRefId": None
      }
    },
    {
      "id": "prop-controlhandle-userinput-uuid",
      "type": "Property",
      "name": "userInput",
      "parentId": "blk-controlhandle-uuid",
      "propertyKind": "part",
      "typeId": "UserInputPort"
    },
    {
      "id": "port-control-userinput-uuid",
      "type": "FullPort",
      "name": "userInput",
      "parentId": "blk-controlhandle-uuid",
      "typeId": "UserInputPort"
    },
    {
      "id": "op-control-changegear-uuid",
      "type": "Operation",
      "name": "changeGear",
      "parentId": "blk-controlhandle-uuid"
    },
    {
      "id": "op-control-applybrake-uuid",
      "type": "Operation",
      "name": "applyBrake",
      "parentId": "blk-controlhandle-uuid"
    },
    {
      "id": "prop-battery-capacity-uuid",
      "type": "Property",
      "name": "capacity",
      "parentId": "blk-battery-uuid",
      "propertyKind": "value",
      "typeId": "CapacityType"
    },
    {
      "id": "prop-sensormodule-speed-uuid",
      "type": "Property",
      "name": "speedSensor",
      "parentId": "blk-sensormodule-uuid",
      "propertyKind": "value",
      "typeId": "SpeedSensorType"
    },
    {
      "id": "prop-sensormodule-slope-uuid",
      "type": "Property",
      "name": "slopeSensor",
      "parentId": "blk-sensormodule-uuid",
      "propertyKind": "value",
      "typeId": "SlopeSensorType"
    },
    {
      "id": "prop-sensormodule-attitude-uuid",
      "type": "Property",
      "name": "attitudeSensor",
      "parentId": "blk-sensormodule-uuid",
      "propertyKind": "value",
      "typeId": "AttitudeSensorType"
    },
    {
      "id": "port-display-speed-uuid",
      "type": "ProxyPort",
      "name": "speedDisplay",
      "parentId": "blk-display-uuid"
    },
    {
      "id": "port-display-gear-uuid",
      "type": "ProxyPort",
      "name": "gearDisplay",
      "parentId": "blk-display-uuid"
    },
    {
      "id": "port-display-battery-uuid",
      "type": "ProxyPort",
      "name": "batteryStatus",
      "parentId": "blk-display-uuid"
    },
    {
      "id": "assoc-veh-frame-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-frame-uuid", "prop-veh-frame-uuid"]
    },
    {
      "id": "assoc-veh-wheel-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-wheel-uuid", "prop-veh-wheel-uuid"]
    },
    {
      "id": "assoc-veh-drivesystem-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-drivesystem-uuid", "prop-veh-drivesystem-uuid"]
    },
    {
      "id": "assoc-veh-brakesystem-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-brakesystem-uuid", "prop-veh-brakesystem-uuid"]
    },
    {
      "id": "assoc-veh-controlhandle-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-controlhandle-uuid", "prop-veh-controlhandle-uuid"]
    },
    {
      "id": "assoc-veh-battery-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-battery-uuid", "prop-veh-battery-uuid"]
    },
    {
      "id": "assoc-veh-sensormodule-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-sensormodule-uuid", "prop-veh-sensormodule-uuid"]
    },
    {
      "id": "assoc-veh-display-uuid",
      "type": "Association",
      "parentId": "pkg-vehicle-uuid",
      "memberEndIds": ["prop-veh-display-uuid", "prop-veh-display-uuid"]
    },
    {
      "id": "gen-stdhvac-hvac-uuid",
      "type": "Generalization",
      "parentId": "blk-standardhvac-uuid",
      "specificId": "blk-standardhvac-uuid",
      "generalId": "blk-hvaccontroller-uuid"
    },
    {
      "id": "gen-advhvac-hvac-uuid",
      "type": "Generalization",
      "parentId": "blk-advancedhvac-uuid",
      "specificId": "blk-advancedhvac-uuid",
      "generalId": "blk-hvaccontroller-uuid"
    },
    {
      "id": "conn-sensor-display-uuid",
      "type": "AssemblyConnector",
      "name": "connectsSensorToDisplay",
      "parentId": "blk-sensormodule-uuid",
      "kind": "assembly",
      "end1": {
        "id": "cend-sensor-speed-uuid",
        "partRefId": "prop-sensormodule-speed-uuid",
        "portRefId": "port-display-speed-uuid"
      },
      "end2": {
        "id": "cend-sensor-slope-uuid",
        "partRefId": "prop-sensormodule-slope-uuid",
        "portRefId": "port-display-gear-uuid"
      }
    },
    {
      "id": "conn-sensor-attitude-uuid",
      "type": "AssemblyConnector",
      "name": "connectsSensorToDisplay",
      "parentId": "blk-sensormodule-uuid",
      "kind": "assembly",
      "end1": {
        "partRefId": "prop-sensormodule-attitude-uuid",
      },
      "end2": {
        "id": "cend-sensor-attitude-uuid",
        "partRefId": "prop-sensormodule-attitude-uuid",
        "portRefId": "port-display-battery-uuid"
      }
    }
  ]
}

"""

corrected_json_data = validate_and_fix_json((json_string_from_ai))

print(corrected_json_data)