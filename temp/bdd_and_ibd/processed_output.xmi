<?xml version='1.0' encoding='UTF-8'?>
<xmi:XMI xmlns:MD_Customization_for_SysML__additional_stereotypes="http://www.magicdraw.com/spec/Customization/190/SysML" xmlns:sysml="http://www.omg.org/spec/SysML/20131001" xmlns:uml="http://www.omg.org/spec/UML/20090901" xmlns:xmi="http://www.omg.org/XMI" xmlns:diagram="http://www.nomagic.com/ns/magicdraw/core/diagram/1.0" xmi:version="2.5">
    <uml:Model xmi:type="uml:Model" xmi:id="model-vehicle-uuid" name="VehicleSystemModel">
        <packagedElement xmi:id="pkg-vehicle-uuid" xmi:type="uml:Package" name="VehicleSystemPackage">
            <packagedElement xmi:id="unit-ampere-uuid" xmi:type="uml:InstanceSpecification" name="%">
                <classifier href="http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML.Unit"/>
            </packagedElement>
            <packagedElement xmi:id="vt-capacity-uuid" xmi:type="uml:DataType" name="Capacity"/>
            <packagedElement xmi:id="vt-diameter-uuid" xmi:type="uml:DataType" name="Diameter"/>
            <packagedElement xmi:id="vt-width-uuid" xmi:type="uml:DataType" name="Width"/>
            <packagedElement xmi:id="sig-speed-uuid" xmi:type="uml:Signal" name="SpeedSignal"/>
            <packagedElement xmi:id="blk-Frame-uuid" xmi:type="uml:Class" name="Frame">
                <ownedAttribute xmi:id="prop-frame-material" xmi:type="uml:Property" name="材料" visibility="public" aggregation="none"/>
                <ownedAttribute xmi:id="prop-frame-weight" xmi:type="uml:Property" name="重量" visibility="public" aggregation="none"/>
            </packagedElement>
            <packagedElement xmi:id="blk-Wheel-uuid" xmi:type="uml:Class" name="Wheel">
                <ownedAttribute xmi:id="prop-wheel-diameter" xmi:type="uml:Property" name="直径" visibility="public" aggregation="none" type="vt-diameter-uuid"/>
                <ownedAttribute xmi:id="prop-wheel-width" xmi:type="uml:Property" name="宽度" visibility="public" aggregation="none" type="vt-width-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="blk-DriveSystem-uuid" xmi:type="uml:Class" name="DriveSystem">
                <ownedAttribute xmi:id="prop-drivesystem-chain" xmi:type="uml:Property" name="链条" visibility="public" aggregation="none"/>
                <ownedAttribute xmi:id="prop-drivesystem-flywheel" xmi:type="uml:Property" name="飞轮" visibility="public" aggregation="none"/>
                <ownedAttribute xmi:id="prop-drivesystem-gear" xmi:type="uml:Property" name="变速器" visibility="public" aggregation="none" type="enum-gear-uuid"/>
                <ownedAttribute xmi:id="port-drivesys-powerout" xmi:type="uml:Port" name="动力输出" visibility="public" aggregation="composite" type="sig-speed-uuid"/>
                <ownedOperation xmi:id="op-DriveSystem-transmit" xmi:type="uml:Operation" name="传递动力" visibility="public"/>
                <ownedOperation xmi:id="op-DriveSystem-connect" xmi:type="uml:Operation" name="连接传感器和显示" visibility="public"/>
                <ownedConnector xmi:id="conn-Drive-to-Wheel" xmi:type="uml:Connector" name="DriveSystem to Wheel" visibility="public">
                    <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Drive-Wheel" role="port-drivesys-powerout"/>
                    <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Wheel-Axle"/>
                </ownedConnector>
            </packagedElement>
            <packagedElement xmi:id="blk-BrakeSystem-uuid" xmi:type="uml:Class" name="BrakeSystem">
                <ownedAttribute xmi:id="prop-brakesystem-handbrake" xmi:type="uml:Property" name="手刹" visibility="public" aggregation="none" association="assoc-Brake-Connectors"/>
                <ownedAttribute xmi:id="prop-brakesystem-brakepads" xmi:type="uml:Property" name="刹车片" visibility="public" aggregation="none" association="assoc-Brake-Connectors"/>
                <ownedAttribute xmi:id="port-brakesystem-control" xmi:type="uml:Port" name="制动控制" visibility="public" aggregation="composite" type="sig-speed-uuid"/>
                <ownedConnector xmi:id="conn-Brake-to-Frame" xmi:type="uml:Connector" name="BrakeSystem to Frame" visibility="public">
                    <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Brake-Frame"/>
                    <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Frame-Brake"/>
                </ownedConnector>
            </packagedElement>
            <packagedElement xmi:id="blk-ControlHandle-uuid" xmi:type="uml:Class" name="ControlHandle">
                <ownedAttribute xmi:id="prop-control-handle" xmi:type="uml:Property" name="用户交互界面" visibility="public" aggregation="none"/>
                <ownedOperation xmi:id="port-control-handle-input" xmi:type="uml:Operation" name="用户输入接口" visibility="public"/>
                <ownedConnector xmi:id="conn-Control-Handle" xmi:type="uml:Connector" name="ControlHandle to Frame" visibility="public">
                    <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Control-Handle"/>
                    <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Frame-Control"/>
                </ownedConnector>
            </packagedElement>
            <packagedElement xmi:id="blk-Battery-uuid" xmi:type="uml:Class" name="Battery">
                <ownedAttribute xmi:id="prop-battery-capacity" xmi:type="uml:Property" name="容量" visibility="public" aggregation="none" type="vt-capacity-uuid"/>
            </packagedElement>
            <packagedElement xmi:id="blk-SensorModule-uuid" xmi:type="uml:Class" name="SensorModule">
                <ownedAttribute xmi:id="prop-sensor-speed" xmi:type="uml:Property" name="检测速度" visibility="public" aggregation="none"/>
                <ownedAttribute xmi:id="prop-sensor-slope" xmi:type="uml:Property" name="检测坡度" visibility="public" aggregation="none"/>
                <ownedAttribute xmi:id="prop-sensor-attitude" xmi:type="uml:Property" name="检测姿态" visibility="public" aggregation="none"/>
                <ownedOperation xmi:id="port-sensor-data" xmi:type="uml:Operation" name="数据采集接口" visibility="public"/>
            </packagedElement>
            <packagedElement xmi:id="blk-Display-uuid" xmi:type="uml:Class" name="Display">
                <ownedAttribute xmi:id="prop-display-speed" xmi:type="uml:Property" name="显示速度" visibility="public" aggregation="none"/>
                <ownedAttribute xmi:id="prop-display-gear" xmi:type="uml:Property" name="显示档位" visibility="public" aggregation="none"/>
                <ownedAttribute xmi:id="prop-display-battery" xmi:type="uml:Property" name="电池状态" visibility="public" aggregation="none"/>
            </packagedElement>
            <packagedElement xmi:id="sig-velocity-uuid" xmi:type="uml:Signal" name="VelocityData"/>
            <packagedElement xmi:id="sig-slope-uuid" xmi:type="uml:Signal" name="SlopeData"/>
            <packagedElement xmi:id="sig-attitude-uuid" xmi:type="uml:Signal" name="AttitudeData"/>
            <ownedConnector xmi:id="conn-Sensor-Display" xmi:type="uml:Connector" name="Sensor to Display" visibility="public">
                <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Sensor-Display" role="prop-sensor-speed"/>
                <end xmi:type="uml:ConnectorEnd" xmi:id="cEnd-Display-Speed" role="prop-display-speed"/>
            </ownedConnector>
            <packagedElement xmi:id="assoc-Brake-Connectors" xmi:type="uml:Association" name="BrakeSystem-Connections">
                <memberEnd xmi:idref="prop-brakesystem-handbrake"/>
                <memberEnd xmi:idref="prop-brakesystem-brakepads"/>
            </packagedElement>
        </packagedElement>
    </uml:Model>
    <MD_Customization_for_SysML__additional_stereotypes:Unit xmi:id="unit-ampere-uuid_application" base_InstanceSpecification="unit-ampere-uuid"/>
    <sysml:ValueType xmi:id="vt-capacity-uuid_application" base_DataType="vt-capacity-uuid" unit="unit-ampere-uuid"/>
    <sysml:ValueType xmi:id="vt-diameter-uuid_application" base_DataType="vt-diameter-uuid"/>
    <sysml:ValueType xmi:id="vt-width-uuid_application" base_DataType="vt-width-uuid"/>
    <sysml:Block xmi:id="blk-Frame-uuid_application" base_Class="blk-Frame-uuid"/>
    <sysml:Block xmi:id="blk-Wheel-uuid_application" base_Class="blk-Wheel-uuid"/>
    <sysml:Block xmi:id="blk-DriveSystem-uuid_application" base_Class="blk-DriveSystem-uuid"/>
    <sysml:Block xmi:id="blk-BrakeSystem-uuid_application" base_Class="blk-BrakeSystem-uuid"/>
    <sysml:Block xmi:id="blk-ControlHandle-uuid_application" base_Class="blk-ControlHandle-uuid"/>
    <sysml:Block xmi:id="blk-Battery-uuid_application" base_Class="blk-Battery-uuid"/>
    <sysml:Block xmi:id="blk-SensorModule-uuid_application" base_Class="blk-SensorModule-uuid"/>
    <sysml:Block xmi:id="blk-Display-uuid_application" base_Class="blk-Display-uuid"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-frame-material_application" base_Property="prop-frame-material"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-frame-weight_application" base_Property="prop-frame-weight"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-wheel-diameter_application" base_Property="prop-wheel-diameter"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-wheel-width_application" base_Property="prop-wheel-width"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-drivesystem-chain_application" base_Property="prop-drivesystem-chain"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-drivesystem-flywheel_application" base_Property="prop-drivesystem-flywheel"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-drivesystem-gear_application" base_Property="prop-drivesystem-gear"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-brakesystem-handbrake_application" base_Property="prop-brakesystem-handbrake"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-brakesystem-brakepads_application" base_Property="prop-brakesystem-brakepads"/>
    <MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty xmi:id="prop-control-handle_application" base_Property="prop-control-handle"/>
    <MD_Customization_for_SysML__additional_stereotypes:ValueProperty xmi:id="prop-battery-capacity_application" base_Property="prop-battery-capacity"/>
    <MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty xmi:id="prop-sensor-speed_application" base_Property="prop-sensor-speed"/>
    <MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty xmi:id="prop-sensor-slope_application" base_Property="prop-sensor-slope"/>
    <MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty xmi:id="prop-sensor-attitude_application" base_Property="prop-sensor-attitude"/>
    <MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty xmi:id="prop-display-speed_application" base_Property="prop-display-speed"/>
    <MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty xmi:id="prop-display-gear_application" base_Property="prop-display-gear"/>
    <MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty xmi:id="prop-display-battery_application" base_Property="prop-display-battery"/>
    <sysml:FullPort xmi:id="port-drivesys-powerout_application" base_Port="port-drivesys-powerout"/>
    <sysml:FullPort xmi:id="port-brakesystem-control_application" base_Port="port-brakesystem-control"/>
    <sysml:BindingConnector xmi:id="conn-Sensor-Display_application" base_Connector="conn-Sensor-Display"/>
</xmi:XMI>