import json

def validate_and_fix_json(json_data):
    """
    验证并修复AI生成的JSON中的特定已知错误。
    """
    if isinstance(json_data, str):
        data = json.loads(json_data)
    else:
        data = json_data

    elements = data.get('elements', [])
    elements_by_id = {elem['id']: elem for elem in elements}
    
    # 建立ValueType名称到ID的映射
    valuetype_map = {
        elem['name']: elem['id'] 
        for elem in elements 
        if elem.get('type') == 'ValueType'
    }

    for elem in elements:
        # --- 修复错误1: ValueType使用不一致 ---
        if elem.get('type') == 'Property' and elem.get('propertyKind') == 'value':
            prop_name = elem.get('name', '').capitalize()
            # 如果属性名（首字母大写）存在于ValueType映射中
            if prop_name in valuetype_map:
                expected_type_id = valuetype_map[prop_name]
                if elem.get('typeId') != expected_type_id:
                    print(f"Fixing ValueType: Property '{elem['id']}' typeId changed from '{elem.get('typeId')}' to '{expected_type_id}'")
                    elem['typeId'] = expected_type_id

        # --- 修复错误2: Connector partRefId 错误 ---
        if elem.get('type') == 'AssemblyConnector':
            container_block_id = elem.get('parentId')
            if not container_block_id: continue
            
            # 获取容器块的所有部件属性ID
            container_parts = {
                prop['name']: prop['id'] 
                for prop_id in elements_by_id.get(container_block_id, {}).get('properties', [])
                if (prop := elements_by_id.get(prop_id)) and prop.get('propertyKind') == 'part'
            }
            
            for end_key in ['end1', 'end2']:
                end = elem.get(end_key)
                if not end or not end.get('partRefId'): continue
                
                part_ref_id = end['partRefId']
                part_ref_elem = elements_by_id.get(part_ref_id)
                
                # 如果引用的ID本身不是容器的部件，而是某个块的内部属性
                if part_ref_elem and part_ref_elem['name'] in container_parts:
                    correct_part_id = container_parts[part_ref_elem['name']]
                    if part_ref_id != correct_part_id:
                        print(f"Fixing partRefId: Connector '{elem['id']}' end '{end['id']}' partRefId changed from '{part_ref_id}' to '{correct_part_id}'")
                        end['partRefId'] = correct_part_id
    
    return data

# --- 如何在您的代码中使用 ---
# ...
# json_response = chat.invoke(messages2)
# raw_json_data = json_response.content
#
# 在生成XML之前，先进行验证和修复

raw_json_data = {
  "model": {
    "id": "model-bicycle-uuid",
    "name": "BicycleModel",
    "diagrams": ["diag-bicycle-bdd-uuid", "diag-bicycle-ibd-uuid"]
  },
  "elements": [
    {
      "id": "pkg-bicycle-uuid",
      "type": "Package",
      "name": "BicyclePackage",
      "parentId": "model-bicycle-uuid"
    },
    {
      "id": "vt-material-uuid",
      "type": "ValueType",
      "name": "Material",
      "parentId": "pkg-bicycle-uuid",
      "baseType": "String"
    },
    {
      "id": "vt-weight-uuid",
      "type": "ValueType",
      "name": "Weight",
      "parentId": "pkg-bicycle-uuid",
      "baseType": "Real"
    },
    {
      "id": "vt-diameter-uuid",
      "type": "ValueType",
      "name": "Diameter",
      "parentId": "pkg-bicycle-uuid",
      "baseType": "Real"
    },
    {
      "id": "vt-width-uuid",
      "type": "ValueType",
      "name": "Width",
      "parentId": "pkg-bicycle-uuid",
      "baseType": "Real"
    },
    {
      "id": "vt-capacity-uuid",
      "type": "ValueType",
      "name": "Capacity",
      "parentId": "pkg-bicycle-uuid",
      "baseType": "Real"
    },
    {
      "id": "blk-frame-uuid",
      "type": "Block",
      "name": "Frame",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-wheel-uuid",
      "type": "Block",
      "name": "Wheel",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-drivesystem-uuid",
      "type": "Block",
      "name": "DriveSystem",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-brakesystem-uuid",
      "type": "Block",
      "name": "BrakeSystem",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-controlhandle-uuid",
      "type": "Block",
      "name": "ControlHandle",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-battery-uuid",
      "type": "Block",
      "name": "Battery",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-sensormodule-uuid",
      "type": "Block",
      "name": "SensorModule",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-display-uuid",
      "type": "Block",
      "name": "Display",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "blk-electroniccontrolunit-uuid",
      "type": "Block",
      "name": "ElectronicControlUnit",
      "parentId": "pkg-bicycle-uuid"
    },
    {
      "id": "prop-bs-frame-uuid",
      "type": "Property",
      "name": "frame",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-frame-uuid",
      "associationId": "assoc-bs-frame-uuid"
    },
    {
      "id": "prop-bs-rearwheel-uuid",
      "type": "Property",
      "name": "rearWheel",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-wheel-uuid",
      "associationId": "assoc-bs-rearwheel-uuid"
    },
    {
      "id": "prop-bs-drivesystem-uuid",
      "type": "Property",
      "name": "driveSystem",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-drivesystem-uuid",
      "associationId": "assoc-bs-drivesystem-uuid"
    },
    {
      "id": "prop-bs-brakesystem-uuid",
      "type": "Property",
      "name": "brakeSystem",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-brakesystem-uuid",
      "associationId": "assoc-bs-brakesystem-uuid"
    },
    {
      "id": "prop-bs-controlhandle-uuid",
      "type": "Property",
      "name": "controlHandle",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-controlhandle-uuid",
      "associationId": "assoc-bs-controlhandle-uuid"
    },
    {
      "id": "prop-bs-battery-uuid",
      "type": "Property",
      "name": "battery",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-battery-uuid",
      "associationId": "assoc-bs-battery-uuid"
    },
    {
      "id": "prop-bs-sensormodule-uuid",
      "type": "Property",
      "name": "sensorModule",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-sensormodule-uuid",
      "associationId": "assoc-bs-sensormodule-uuid"
    },
    {
      "id": "prop-bs-display-uuid",
      "type": "Property",
      "name": "display",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-display-uuid",
      "associationId": "assoc-bs-display-uuid"
    },
    {
      "id": "prop-bs-ecu-uuid",
      "type": "Property",
      "name": "ecu",
      "parentId": "blk-bicyclesystem-uuid",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-electroniccontrolunit-uuid",
      "associationId": "assoc-bs-ecu-uuid"
    },
    {
      "id": "prop-frame-material-uuid",
      "type": "Property",
      "name": "material",
      "parentId": "blk-frame-uuid",
      "propertyKind": "value",
      "typeId": "String"
    },
    {
      "id": "prop-frame-weight-uuid",
      "type": "Property",
      "name": "weight",
      "parentId": "blk-frame-uuid",
      "propertyKind": "value",
      "typeId": "Real"
    },
    {
      "id": "prop-wheel-diameter-uuid",
      "type": "Property",
      "name": "diameter",
      "parentId": "blk-wheel-uuid",
      "propertyKind": "value",
      "typeId": "Real"
    },
    {
      "id": "prop-wheel-width-uuid",
      "type": "Property",
      "name": "width",
      "parentId": "blk-wheel-uuid",
      "propertyKind": "value",
      "typeId": "Real"
    },
    {
      "id": "prop-battery-capacity-uuid",
      "type": "Property",
      "name": "capacity",
      "parentId": "blk-battery-uuid",
      "propertyKind": "value",
      "typeId": "vt-amperehour-uuid",
      "defaultValue": "10"
    },
    {
      "id": "port-wheel-powerin-uuid",
      "type": "ProxyPort",
      "name": "powerInput",
      "parentId": "blk-wheel-uuid"
    },
    {
      "id": "port-drivesys-powerout-uuid",
      "type": "ProxyPort",
      "name": "powerOutput",
      "parentId": "blk-drivesystem-uuid"
    },
    {
      "id": "port-sensor-dataout-uuid",
      "type": "ProxyPort",
      "name": "dataOut",
      "parentId": "blk-sensormodule-uuid"
    },
    {
      "id": "port-display-datain-uuid",
      "type": "ProxyPort",
      "name": "dataIn",
      "parentId": "blk-display-uuid"
    },
    {
      "id": "port-ecu-sensorin-uuid",
      "type": "ProxyPort",
      "name": "sensorInput",
      "parentId": "blk-electroniccontrolunit-uuid"
    },
    {
      "id": "port-ecu-displayout-uuid",
      "type": "ProxyPort",
      "name": "displayOutput",
      "parentId": "blk-electroniccontrolunit-uuid"
    },
    {
      "id": "assoc-bs-frame-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-frame-uuid", "prop-frame-owner-uuid"]
    },
    {
      "id": "assoc-bs-rearwheel-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-rearwheel-uuid", "prop-wheel-owner-uuid"]
    },
    {
      "id": "assoc-bs-drivesystem-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-drivesystem-uuid", "prop-drivesys-owner-uuid"]
    },
    {
      "id": "assoc-bs-brakesystem-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-brakesystem-uuid", "prop-brakesys-owner-uuid"]
    },
    {
      "id": "assoc-bs-controlhandle-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-controlhandle-uuid", "prop-controlhandle-owner-uuid"]
    },
    {
      "id": "assoc-bs-battery-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-battery-uuid", "prop-battery-owner-uuid"]
    },
    {
      "id": "assoc-bs-sensormodule-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-sensormodule-uuid", "prop-sensormod-owner-uuid"]
    },
    {
      "id": "assoc-bs-display-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-display-uuid", "prop-display-owner-uuid"]
    },
    {
      "id": "assoc-bs-ecu-uuid",
      "type": "Association",
      "parentId": "pkg-bicycle-uuid",
      "memberEndIds": ["prop-bs-ecu-uuid", "prop-ecu-owner-uuid"]
    },
    {
      "id": "conn-drive-wheel-uuid",
      "type": "AssemblyConnector",
      "name": "conn-drivesys-to-wheel",
      "parentId": "blk-bicyclesystem-uuid",
      "kind": "assembly",
      "end": [
        {
          "id": "cend-dw-1-uuid",
          "partRefId": "prop-bs-drivesystem-uuid",
          "portRefId": "port-drivesys-powerout-uuid"
        },
        {
          "id": "cend-dw-2-uuid",
          "partRefId": "prop-bs-rearwheel-uuid",
          "portRefId": "port-wheel-powerin-uuid"
        }
      ]
    },
    {
      "id": "conn-sensor-ecu-uuid",
      "type": "AssemblyConnector",
      "name": "conn-sensor-to-ecu",
      "parentId": "blk-bicyclesystem-uuid",
      "kind": "assembly",
      "end": [
        {
          "id": "cend-se-1-uuid",
          "partRefId": "prop-bs-sensormodule-uuid",
          "portRefId": "port-sensor-dataout-uuid"
        },
        {
          "id": "cend-se-2-uuid",
          "partRefId": "prop-bs-ecu-uuid",
          "portRefId": "port-ecu-sensorin-uuid"
        }
      ]
    },
    {
      "id": "conn-ecu-display-uuid",
      "type": "AssemblyConnector",
      "name": "conn-ecu-to-display",
      "parentId": "blk-bicyclesystem-uuid",
      "kind": "assembly",
      "end": [
        {
          "id": "cend-ed-1-uuid",
          "partRefId": "prop-bs-ecu-uuid",
          "portRefId": "port-ecu-displayout-uuid"
        },
        {
          "id": "cend-ed-2-uuid",
          "partRefId": "prop-bs-display-uuid",
          "portRefId": "port-display-datain-uuid"
        }
      ]
    },
    {
      "id": "diag-bicycle-bdd-uuid",
      "type": "Diagram",
      "name": "Bicycle System BDD",
      "parentId": "pkg-bicycle-uuid",
      "diagramType": "BDD",
      "contextId": "pkg-bicycle-uuid"
    },
    {
      "id": "diag-bicycle-ibd-uuid",
      "type": "Diagram",
      "name": "Bicycle System IBD",
      "parentId": "blk-bicyclesystem-uuid",
      "diagramType": "IBD",
      "contextId": "blk-bicyclesystem-uuid"
    }
  ]
}


corrected_json_data = validate_and_fix_json(raw_json_data) 

# 使用修复后的JSON生成XML
print(corrected_json_data)