<xmi:XMI xmi:version="2.5">
  <uml:Model xmi:type="uml:Model" xmi:id="model-vehicle-uuid" name="VehicleModel">
    <packagedElement xmi:id="pkg-vehicle-uuid" name="VehiclePackage" xmi:type="uml:Package">
      <packagedElement xmi:id="unit-amp-uuid" name="A" />
      <packagedElement xmi:id="vt-capacity-uuid" name="Capacity" xmi:type="uml:DataType" />
      <packagedElement xmi:id="vt-diameter-uuid" name="Diameter" xmi:type="uml:DataType" />
      <packagedElement xmi:id="vt-width-uuid" name="Width" xmi:type="uml:DataType" />
      <packagedElement xmi:id="vt-speed-uuid" name="Speed" xmi:type="uml:DataType" />
      <packagedElement xmi:id="vt-gear-uuid" name="Gear" xmi:type="uml:DataType" />
      <packagedElement xmi:id="vt-battery-capacity-uuid" name="BatteryCapacity" xmi:type="uml:DataType" />
      <packagedElement xmi:id="enum-gear-uuid" name="GearPositions" xmi:type="uml:Enumeration" />     
      <packagedElement xmi:id="enum-battstatus-uuid" name="BatteryStatus" xmi:type="uml:Enumeration" />
      <packagedElement xmi:id="sig-speed-uuid" name="SpeedSignal" xmi:type="uml:Signal" />
      <packagedElement xmi:id="sig-slope-uuid" name="SlopeSignal" xmi:type="uml:Signal" />
      <packagedElement xmi:id="sig-attitude-uuid" name="AttitudeSignal" xmi:type="uml:Signal" />      
      <packagedElement xmi:id="if-driveout-uuid" name="DriveOutputInterface" xmi:type="uml:Class" />  
      <packagedElement xmi:id="if-brakecontrol-uuid" name="BrakeControlInterface" xmi:type="uml:Class" />
      <packagedElement xmi:id="if-userinput-uuid" name="UserInputInterface" xmi:type="uml:Class" />   
      <packagedElement xmi:id="blk-battery-uuid" name="Battery" xmi:type="uml:Class">
        <ownedAttribute xmi:id="prop-battery-capacity" name="capacity" xmi:type="uml:Property" visibility="public" aggregation="none" type="vt-capacity-uuid">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
      </packagedElement>
      <packagedElement xmi:id="blk-drivesystem-uuid" name="DriveSystem" xmi:type="uml:Class">
        <ownedAttribute xmi:id="prop-drivesys-chain" name="chain" xmi:type="uml:Property" visibility="public" aggregation="composite" association="assoc-drivesys-chain">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-drivesys-flywheel" name="flywheel" xmi:type="uml:Property" visibility="public" aggregation="composite" association="assoc-drivesys-flywheel">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-drivesys-transmission" name="transmission" xmi:type="uml:Property" visibility="public" aggregation="composite" association="assoc-drivesys-transmission">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <packagedElement xmi:id="port-drivesys-powerout" name="powerOutput" />
        <packagedElement xmi:id="port-drivesys-drivein" name="driveInput" />
      </packagedElement>
      <packagedElement xmi:id="blk-brakesystem-uuid" name="BrakeSystem" xmi:type="uml:Class">
        <ownedAttribute xmi:id="prop-brakes-handbrake" name="handBrake" xmi:type="uml:Property" visibility="public" aggregation="composite" association="assoc-brakes-handbrake">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-brakes-brakepad" name="brakePad" xmi:type="uml:Property" visibility="public" aggregation="composite" association="assoc-brakes-brakepad">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <packagedElement xmi:id="port-brakes-control" name="brakeControl" />
        <ownedOperation xmi:id="op-brakes-apply" name="applyBrake" />
        <ownedOperation xmi:id="op-brakes-release" name="releaseBrake" />
      </packagedElement>
      <packagedElement xmi:id="blk-controlhandle-uuid" name="ControlHandle" xmi:type="uml:Class">     
        <packagedElement xmi:id="port-control-speed" name="speedControl" />
        <packagedElement xmi:id="port-control-brake" name="brakeControl" />
        <ownedOperation xmi:id="op-increase-speed" name="increaseSpeed" />
        <ownedOperation xmi:id="op-decrease-speed" name="decreaseSpeed" />
        <ownedOperation xmi:id="op-apply-brake" name="applyBrake" />
      </packagedElement>
      <packagedElement xmi:id="blk-sensormodule-uuid" name="SensorModule" xmi:type="uml:Class">       
        <packagedElement xmi:id="port-sensor-speed" name="speedSensor" />
        <packagedElement xmi:id="port-sensor-slope" name="slopeSensor" />
        <packagedElement xmi:id="port-sensor-attitude" name="attitudeSensor" />
      </packagedElement>
      <packagedElement xmi:id="blk-display-uuid" name="Display" xmi:type="uml:Class">
        <packagedElement xmi:id="port-display-speed" name="speedDisplay" />
        <packagedElement xmi:id="port-display-gear" name="gearDisplay" />
        <packagedElement xmi:id="port-display-batt" name="batteryStatusDisplay" />
      </packagedElement>
      <packagedElement xmi:id="blk-gear-uuid" name="Gear" xmi:type="uml:Class">
        <ownedAttribute xmi:id="prop-gear-position" name="position" xmi:type="uml:Property" visibility="public" aggregation="none" type="enum-gear-uuid">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
      </packagedElement>
      <packagedElement xmi:id="blk-wheel-uuid" name="Wheel" xmi:type="uml:Class">
        <ownedAttribute xmi:id="prop-wheel-diameter" name="diameter" xmi:type="uml:Property" visibility="public" aggregation="none" type="vt-diameter-uuid">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-wheel-width" name="width" xmi:type="uml:Property" visibility="public" aggregation="none" type="vt-width-uuid">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
      </packagedElement>
      <packagedElement xmi:id="blk-vehicle-uuid" name="Vehicle" xmi:type="uml:Class">
        <ownedAttribute xmi:id="prop-vehicle-drivesys" name="driveSystem" xmi:type="uml:Property" visibility="public" aggregation="composite" type="blk-drivesystem-uuid" association="assoc-vehicle-drivesys">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-brakesys" name="brakeSystem" xmi:type="uml:Property" visibility="public" aggregation="composite" type="blk-brakesystem-uuid" association="assoc-vehicle-brakesys">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-control" name="controlHandle" xmi:type="uml:Property" visibility="public" aggregation="composite" type="blk-controlhandle-uuid" association="assoc-vehicle-control">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-battery" name="battery" xmi:type="uml:Property" visibility="public" aggregation="composite" type="blk-battery-uuid" association="assoc-vehicle-battery">       
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-sensor" name="sensorModule" xmi:type="uml:Property" visibility="public" aggregation="composite" type="blk-sensormodule-uuid" association="assoc-vehicle-sensor">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-display" name="display" xmi:type="uml:Property" visibility="public" aggregation="composite" type="blk-display-uuid" association="assoc-vehicle-display">       
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-speed" name="currentSpeed" xmi:type="uml:Property" visibility="public" aggregation="none" type="vt-speed-uuid">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-gear" name="currentGear" xmi:type="uml:Property" visibility="public" aggregation="none" type="enum-gear-uuid">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <ownedAttribute xmi:id="prop-vehicle-battstatus" name="batteryStatus" xmi:type="uml:Property" 
visibility="public" aggregation="none" type="enum-battstatus-uuid">
          <lowerValue xmi:type="uml:LiteralInteger" xmi:id="None_lower" value="1" />
          <upperValue xmi:id="None_upper" value="1" xmi:type="uml:LiteralUnlimitedNatural" />
        </ownedAttribute>
        <packagedElement xmi:id="port-vehicle-driveout" name="driveOutput" />
        <packagedElement xmi:id="port-vehicle-brakectrl" name="brakeControl" />
        <packagedElement xmi:id="port-vehicle-userinput" name="userInput" />
        <packagedElement xmi:id="port-vehicle-powersupply" name="powerSupply" />
        <packagedElement xmi:id="port-vehicle-speedsensor" name="speedSensor" />
        <packagedElement xmi:id="port-vehicle-slopesensor" name="slopeSensor" />
        <packagedElement xmi:id="port-vehicle-attitudesensor" name="attitudeSensor" />
        <ownedOperation xmi:id="op-vehicle-changgear" name="changeGear">
          <packagedElement xmi:id="p-vehicle-gear" name="gear" />
        </ownedOperation>
        <ownedOperation xmi:id="op-vehicle-setspeed" name="setSpeed">
          <packagedElement xmi:id="p-vehicle-speed" name="speed" />
        </ownedOperation>
        <ownedOperation xmi:id="op-vehicle-brakes" name="applyBrakes" />
        <ownedOperation xmi:id="op-vehicle-stop" name="stop" />
      </packagedElement>
      <packagedElement xmi:id="assoc-vehicle-drivesys" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-vehicle-drivesys" />
      </packagedElement>
      <packagedElement xmi:id="assoc-vehicle-brakesys" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-vehicle-brakesys" />
      </packagedElement>
      <packagedElement xmi:id="assoc-vehicle-control" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-vehicle-control" />
      </packagedElement>
      <packagedElement xmi:id="assoc-vehicle-battery" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-vehicle-battery" />
      </packagedElement>
      <packagedElement xmi:id="assoc-vehicle-sensor" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-vehicle-sensor" />
      </packagedElement>
      <packagedElement xmi:id="assoc-vehicle-display" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-vehicle-display" />
      </packagedElement>
      <packagedElement xmi:id="assoc-gear-wheel" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-gear-position" />
      </packagedElement>
      <packagedElement xmi:id="assoc-drivesys-chain" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-drivesys-chain" />
      </packagedElement>
      <packagedElement xmi:id="assoc-drivesys-flywheel" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-drivesys-flywheel" />
      </packagedElement>
      <packagedElement xmi:id="assoc-drivesys-transmission" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-drivesys-transmission" />
      </packagedElement>
      <packagedElement xmi:id="assoc-brakes-handbrake" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-brakes-handbrake" />
      </packagedElement>
      <packagedElement xmi:id="assoc-brakes-brakepad" xmi:type="uml:Association">
        <memberEnd xmi:idref="prop-brakes-brakepad" />
      </packagedElement>
    </packagedElement>
  </uml:Model>
  <sysml:ValueType xmi:id="vt-capacity-uuid_stereotype" base_DataType="vt-capacity-uuid" />
  <sysml:ValueType xmi:id="vt-diameter-uuid_stereotype" base_DataType="vt-diameter-uuid" />
  <sysml:ValueType xmi:id="vt-width-uuid_stereotype" base_DataType="vt-width-uuid" />
  <sysml:ValueType xmi:id="vt-speed-uuid_stereotype" base_DataType="vt-speed-uuid" />
  <sysml:ValueType xmi:id="vt-gear-uuid_stereotype" base_DataType="vt-gear-uuid" />
  <sysml:ValueType xmi:id="vt-battery-capacity-uuid_stereotype" base_DataType="vt-battery-capacity-uuid" />
  <sysml:Block xmi:id="blk-battery-uuid_stereotype" base_Class="blk-battery-uuid" />
  <sysml:Block xmi:id="blk-drivesystem-uuid_stereotype" base_Class="blk-drivesystem-uuid" />
  <sysml:Block xmi:id="blk-brakesystem-uuid_stereotype" base_Class="blk-brakesystem-uuid" />
  <sysml:Block xmi:id="blk-controlhandle-uuid_stereotype" base_Class="blk-controlhandle-uuid" />      
  <sysml:Block xmi:id="blk-sensormodule-uuid_stereotype" base_Class="blk-sensormodule-uuid" />        
  <sysml:Block xmi:id="blk-display-uuid_stereotype" base_Class="blk-display-uuid" />
  <sysml:Block xmi:id="blk-gear-uuid_stereotype" base_Class="blk-gear-uuid" />
  <sysml:Block xmi:id="blk-wheel-uuid_stereotype" base_Class="blk-wheel-uuid" />
  <sysml:Block xmi:id="blk-vehicle-uuid_stereotype" base_Class="blk-vehicle-uuid" />
</xmi:XMI>