from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

chat = ChatOpenAI(
    model="gpt-4.1-nano-2025-04-14",
    #model="gpt-4o-mini",
    temperature=0.0,
    api_key="sk-TP4VOmsDu2OdsE8H88BaBd09D1B74bD983C7Ca67E6E9AeBf",
    base_url='https://happyapi.org/v1'
)

question1 = """"主要块包括：Frame（承载所有组件，属性包括材料、重量）、Wheel（轮胎、轮辋，属性包括直径、宽度）、DriveSystem（链条、飞轮、变速器，职责为传递动力）、BrakeSystem（手刹、刹车片，职责为减速和停车）、ControlHandle（用户交互界面，提供变速和刹车控制）、Battery（容量10Ah，为电子系统供电）、SensorModule（检测速度、坡度、姿态）、Display（显示速度、档位、电池状态）。接口定义：DriveSystem提供动力输出接口到车轮；BrakeSystem提供制动控制接口；ControlHandle提供用户输入接口；SensorModule提供数据采集接口。内部连接：变速器连接链条，驱动飞轮；电子控制单元连接传感器和显示屏。"""
question2 = """"主要块包括：Frame（承载所有组件，属性包括材料、重量）、Wheel（轮胎、轮辋，属性包括直径、宽度）、DriveSystem（链条、飞轮、变速器，职责为传递动力）、BrakeSystem（手刹、刹车片，职责为减速和停车）、ControlHandle（用户交互界面，提供变速和刹车控制）、Battery（容量10Ah，为电子系统供电）、SensorModule（检测速度、坡度、姿态）、Display（显示速度、档位、电池状态）。接口定义：DriveSystem提供动力输出接口到车轮；BrakeSystem提供制动控制接口；ControlHandle提供用户输入接口；SensorModule提供数据采集接口。内部连接：变速器连接链条，驱动飞轮；电子控制单元连接传感器和显示屏。"""
question3 = """
主要块包括：Frame（承载所有组件，属性包括材料、重量）、Wheel（轮胎、轮辋，属性包括直径、宽度）、DriveSystem（链条、飞轮、变速器，职责为传递动力）、BrakeSystem（手刹、刹车片，职责为减速和停车）、ControlHandle（用户交互界面，提供变速和刹车控制）、Battery（容量10Ah，为电子系统供电）、SensorModule（检测速度、坡度、姿态）、Display（显示速度、档位、电池状态）。接口定义：DriveSystem提供动力输出接口到车轮；BrakeSystem提供制动控制接口；ControlHandle提供用户输入接口；SensorModule提供数据采集接口。内部连接：变速器连接链条，驱动飞轮；电子控制单元连接传感器和显示屏。
"""

prompt1 = """
# prompt1 (v6 - 精炼一致版)
## 角色
你是一位顶级的系统建模专家，精通 SysML BDD 和 IBD 规范。你的任务是从输入的自然语言描述中，全面、精确地提取所有结构和行为元素，并组织成一个统一的、扁平化的思考结果列表。

## 核心建模原则与规则
1.  **ID生成**: 所有ID必须保证唯一。
2.  **顶层容器**: 系统必须由一个顶层的容器 `Block` 代表。所有主要组件都是这个容器块的 `part` 属性。
3.  **双向关联**: 每个 `part` 或 `reference` 属性都必须有对应的反向引用属性，并共同组成一个拥有两个端点的 `Association`。
4.  **全面提取**: 必须提取文本中明确或暗示的所有元素，如 `Block`, `Property`, `Port`, `Operation`, `Parameter`, `Enumeration`, `Literal`, `Generalization`, `Connector` 等。
5.  **完整性**: 所有被引用的元素都必须被定义。如果缺失，需根据上下文合理推断（如`ElectronicControlUnit`）。

## 输出格式与黄金标准样例 (智能温控器系统)
你的输出必须严格遵循以下思考过程的格式和逻辑。这个例子是你的唯一参考标准。

### 输入文本:
"设计一个`智能温控器系统包`。该系统由一个`主温控器`和一个`环境传感器`组成。`主温控器`是系统的核心，包含一个`显示单元`部件和一个`HVAC控制器`部件。`HVAC控制器`是一个抽象块，它有两个具体实现：`标准HVAC控制器`和`高级HVAC控制器`（继承自`HVAC控制器`）。`主温控器`有一个操作`设置目标温度(温度: Celsius)`，其中`Celsius`是`Real`类型的值，单位为`°C`。它还有一个值属性`当前模式`，类型为枚举`OperatingMode`（包含`Cooling`, `Heating`, `Off`）。`环境传感器`有一个全端口`p_powerIn`用于供电，类型为`PowerBlock`。在`主温控器`的内部，`环境传感器`的`p_dataOut`端口连接到`显示单元`的`p_tempIn`端口。"

### 思考过程输出:
1.  **顶层结构:**
    *   Model: id=`model-thermostat-uuid`, name=`ThermostatModel`
    *   Package: id=`pkg-thermostat-uuid`, name=`SmartThermostatPackage`, parentId=`model-thermostat-uuid`

2.  **核心类型定义:**
    *   Unit: id=`unit-celsius-uuid`, name=`°C`
    *   ValueType: id=`vt-celsius-uuid`, name=`Celsius`, parentId=`pkg-thermostat-uuid`, baseType=`Real`, unitId=`unit-celsius-uuid`
    *   Enumeration: id=`enum-opmode-uuid`, name=`OperatingMode`, parentId=`pkg-thermostat-uuid`
    *   Block: id=`blk-powerblock-uuid`, name=`PowerBlock`, parentId=`pkg-thermostat-uuid`
    *   Block: id=`blk-displayunit-uuid`, name=`DisplayUnit`, parentId=`pkg-thermostat-uuid`
    *   Block: id=`blk-hvaccontroller-uuid`, name=`HVACController`, parentId=`pkg-thermostat-uuid`, isAbstract=True
    *   Block: id=`blk-standardhvac-uuid`, name=`StandardHVACController`, parentId=`pkg-thermostat-uuid`
    *   Block: id=`blk-advancedhvac-uuid`, name=`AdvancedHVACController`, parentId=`pkg-thermostat-uuid`
    *   Block: id=`blk-environmentsensor-uuid`, name=`EnvironmentSensor`, parentId=`pkg-thermostat-uuid`

3.  **顶层系统块 (容器):**
    *   Block: id=`blk-thermostat-uuid`, name=`MainThermostat`, parentId=`pkg-thermostat-uuid`

4.  **内部成员与关系:**
    *   **For `MainThermostat` (id=`blk-thermostat-uuid`):**
        *   Property (Part): `sensor`: id=`prop-th-sensor-uuid`, parentId=`blk-thermostat-uuid`, typeId=`blk-environmentsensor-uuid`, assocId=`assoc-th-sensor-uuid`
        *   Property (Part): `display`: id=`prop-th-display-uuid`, parentId=`blk-thermostat-uuid`, typeId=`blk-displayunit-uuid`, assocId=`assoc-th-display-uuid`
        *   Property (Part): `hvac`: id=`prop-th-hvac-uuid`, parentId=`blk-thermostat-uuid`, typeId=`blk-hvaccontroller-uuid`, assocId=`assoc-th-hvac-uuid`
        *   Property (Value): `currentMode`: id=`prop-th-mode-uuid`, parentId=`blk-thermostat-uuid`, typeId=`enum-opmode-uuid`
        *   Operation: `setTargetTemperature`: id=`op-th-settemp-uuid`, parentId=`blk-thermostat-uuid`
    *   **For `setTargetTemperature` (id=`op-th-settemp-uuid`):**
        *   Parameter: `temperature`: id=`param-settemp-temp-uuid`, parentId=`op-th-settemp-uuid`, typeId=`vt-celsius-uuid`, direction=`in`
    *   **For `OperatingMode` (id=`enum-opmode-uuid`):**
        *   Literal: `Cooling`: id=`lit-opmode-cool-uuid`, parentId=`enum-opmode-uuid`
        *   Literal: `Heating`: id=`lit-opmode-heat-uuid`, parentId=`enum-opmode-uuid`
        *   Literal: `Off`: id=`lit-opmode-off-uuid`, parentId=`enum-opmode-uuid`
    *   **For `EnvironmentSensor` (id=`blk-environmentsensor-uuid`):**
        *   Port (FullPort): `p_powerIn`: id=`port-sensor-powerin-uuid`, parentId=`blk-environmentsensor-uuid`, typeId=`blk-powerblock-uuid`
        *   Port (ProxyPort): `p_dataOut`: id=`port-sensor-dataout-uuid`, parentId=`blk-environmentsensor-uuid`
        *   Property (Reference): `_thermostat`: id=`prop-sensor-owner-uuid`, parentId=`blk-environmentsensor-uuid`, typeId=`blk-thermostat-uuid`, assocId=`assoc-th-sensor-uuid`
    *   **For `DisplayUnit` (id=`blk-displayunit-uuid`):**
        *   Port (ProxyPort): `p_tempIn`: id=`port-display-tempin-uuid`, parentId=`blk-displayunit-uuid`
        *   Property (Reference): `_thermostat`: id=`prop-display-owner-uuid`, parentId=`blk-displayunit-uuid`, typeId=`blk-thermostat-uuid`, assocId=`assoc-th-display-uuid`
    *   **For `HVACController` (id=`blk-hvaccontroller-uuid`):**
        *   Property (Reference): `_thermostat`: id=`prop-hvac-owner-uuid`, parentId=`blk-hvaccontroller-uuid`, typeId=`blk-thermostat-uuid`, assocId=`assoc-th-hvac-uuid`
    *   **Generalizations:**
        *   Generalization: id=`gen-stdhvac-hvac-uuid`, parentId=`blk-standardhvac-uuid`, specificId=`blk-standardhvac-uuid`, generalId=`blk-hvaccontroller-uuid`
        *   Generalization: id=`gen-advhvac-hvac-uuid`, parentId=`blk-advancedhvac-uuid`, specificId=`blk-advancedhvac-uuid`, generalId=`blk-hvaccontroller-uuid`
    *   **Associations:**
        *   Association: id=`assoc-th-sensor-uuid`, parentId=`pkg-thermostat-uuid`, memberEndIds=[`prop-th-sensor-uuid`, `prop-sensor-owner-uuid`]
        *   Association: id=`assoc-th-display-uuid`, parentId=`pkg-thermostat-uuid`, memberEndIds=[`prop-th-display-uuid`, `prop-display-owner-uuid`]
        *   Association: id=`assoc-th-hvac-uuid`, parentId=`pkg-thermostat-uuid`, memberEndIds=[`prop-th-hvac-uuid`, `prop-hvac-owner-uuid`]

5.  **内部连接 (in `MainThermostat`):**
    *   Connector (Assembly): `sensor_to_display`: id=`conn-s-d-uuid`, parentId=`blk-thermostat-uuid`, End1(part:`prop-th-sensor-uuid`, port:`port-sensor-dataout-uuid`), End2(part:`prop-th-display-uuid`, port:`port-display-tempin-uuid`)

    
## 具体任务
输入：          
""" + question2  + """输出：按照上述方法，请你一步一步进行推理思考，只需给出思考过程、且完整无误。"""

messages = [
    HumanMessage(content=prompt1),
]

print("⭐⭐⭐正在执行任务：", question2)
response = chat.invoke(messages)

print("😊😊😊推理结果：", response.content)
print("😊😊😊使用消耗", response.usage_metadata)

prompt2 = prompt1 + response.content + """
# prompt2 (v6 - 精炼一致版)
## 角色
你是一位精确的数据转换工程师，负责将详细的、扁平化的 SysML 元素思考过程转化为严格符合规范的统一 JSON 格式。你**只输出JSON**，不能输出任何其他内容或注释。

## 核心规则
1.  **全面转换**: “思考过程”中的**每一个元素**都必须在`elements`列表中有一个对应的JSON对象。绝对不能遗漏。
2.  **引用完整性**: 仔细检查所有ID引用 (`parentId`, `typeId`, `associationId`,等)，确保它们都指向`elements`列表中存在的另一个元素的`id`。决不允许悬空ID。
3.  **关联逻辑**: `Association`的`memberEndIds`必须包含两个属性ID。`Connector`的`partRefId`必须引用顶层容器块中定义的部件属性。

## 黄金标准样例 (智能温控器系统)
```JSON
{
  "model": { "id": "model-thermostat-uuid", "name": "ThermostatModel" },
  "elements": [
    { "id": "pkg-thermostat-uuid", "type": "Package", "name": "SmartThermostatPackage", "parentId": "model-thermostat-uuid" },
    { "id": "unit-celsius-uuid", "type": "Unit", "name": "°C", "parentId": "pkg-thermostat-uuid" },
    { "id": "vt-celsius-uuid", "type": "ValueType", "name": "Celsius", "parentId": "pkg-thermostat-uuid", "baseType": "Real", "unitId": "unit-celsius-uuid" },
    { "id": "enum-opmode-uuid", "type": "Enumeration", "name": "OperatingMode", "parentId": "pkg-thermostat-uuid" },
    { "id": "lit-opmode-cool-uuid", "type": "EnumerationLiteral", "name": "Cooling", "parentId": "enum-opmode-uuid" },
    { "id": "lit-opmode-heat-uuid", "type": "EnumerationLiteral", "name": "Heating", "parentId": "enum-opmode-uuid" },
    { "id": "lit-opmode-off-uuid", "type": "EnumerationLiteral", "name": "Off", "parentId": "enum-opmode-uuid" },
    { "id": "blk-powerblock-uuid", "type": "Block", "name": "PowerBlock", "parentId": "pkg-thermostat-uuid" },
    { "id": "blk-displayunit-uuid", "type": "Block", "name": "DisplayUnit", "parentId": "pkg-thermostat-uuid", "ports": ["port-display-tempin-uuid"], "properties": ["prop-display-owner-uuid"] },
    { "id": "blk-hvaccontroller-uuid", "type": "Block", "name": "HVACController", "parentId": "pkg-thermostat-uuid", "isAbstract": true, "properties": ["prop-hvac-owner-uuid"] },
    { "id": "blk-standardhvac-uuid", "type": "Block", "name": "StandardHVACController", "parentId": "pkg-thermostat-uuid", "generalizations": ["gen-stdhvac-hvac-uuid"] },
    { "id": "blk-advancedhvac-uuid", "type": "Block", "name": "AdvancedHVACController", "parentId": "pkg-thermostat-uuid", "generalizations": ["gen-advhvac-hvac-uuid"] },
    { "id": "blk-environmentsensor-uuid", "type": "Block", "name": "EnvironmentSensor", "parentId": "pkg-thermostat-uuid", "ports": ["port-sensor-powerin-uuid", "port-sensor-dataout-uuid"], "properties": ["prop-sensor-owner-uuid"] },
    { "id": "blk-thermostat-uuid", "type": "Block", "name": "MainThermostat", "parentId": "pkg-thermostat-uuid", "properties": ["prop-th-sensor-uuid", "prop-th-display-uuid", "prop-th-hvac-uuid", "prop-th-mode-uuid"], "operations": ["op-th-settemp-uuid"], "connectors": ["conn-s-d-uuid"]},
    { "id": "op-th-settemp-uuid", "type": "Operation", "name": "setTargetTemperature", "parentId": "blk-thermostat-uuid", "parameters": [{ "id": "param-settemp-temp-uuid", "name": "temperature", "typeId": "vt-celsius-uuid", "direction": "in" }] },
    { "id": "prop-th-sensor-uuid", "type": "Property", "name": "sensor", "parentId": "blk-thermostat-uuid", "propertyKind": "part", "typeId": "blk-environmentsensor-uuid", "associationId": "assoc-th-sensor-uuid" },
    { "id": "prop-th-display-uuid", "type": "Property", "name": "display", "parentId": "blk-thermostat-uuid", "propertyKind": "part", "typeId": "blk-displayunit-uuid", "associationId": "assoc-th-display-uuid" },
    { "id": "prop-th-hvac-uuid", "type": "Property", "name": "hvac", "parentId": "blk-thermostat-uuid", "propertyKind": "part", "typeId": "blk-hvaccontroller-uuid", "associationId": "assoc-th-hvac-uuid" },
    { "id": "prop-th-mode-uuid", "type": "Property", "name": "currentMode", "parentId": "blk-thermostat-uuid", "propertyKind": "value", "typeId": "enum-opmode-uuid" },
    { "id": "prop-sensor-owner-uuid", "type": "Property", "name": "_thermostat", "parentId": "blk-environmentsensor-uuid", "propertyKind": "reference", "typeId": "blk-thermostat-uuid", "associationId": "assoc-th-sensor-uuid" },
    { "id": "prop-display-owner-uuid", "type": "Property", "name": "_thermostat", "parentId": "blk-displayunit-uuid", "propertyKind": "reference", "typeId": "blk-thermostat-uuid", "associationId": "assoc-th-display-uuid" },
    { "id": "prop-hvac-owner-uuid", "type": "Property", "name": "_thermostat", "parentId": "blk-hvaccontroller-uuid", "propertyKind": "reference", "typeId": "blk-thermostat-uuid", "associationId": "assoc-th-hvac-uuid" },
    { "id": "port-sensor-powerin-uuid", "type": "FullPort", "name": "p_powerIn", "parentId": "blk-environmentsensor-uuid", "typeId": "blk-powerblock-uuid" },
    { "id": "port-sensor-dataout-uuid", "type": "ProxyPort", "name": "p_dataOut", "parentId": "blk-environmentsensor-uuid" },
    { "id": "port-display-tempin-uuid", "type": "ProxyPort", "name": "p_tempIn", "parentId": "blk-displayunit-uuid" },
    { "id": "assoc-th-sensor-uuid", "type": "Association", "parentId": "pkg-thermostat-uuid", "memberEndIds": ["prop-th-sensor-uuid", "prop-sensor-owner-uuid"] },
    { "id": "assoc-th-display-uuid", "type": "Association", "parentId": "pkg-thermostat-uuid", "memberEndIds": ["prop-th-display-uuid", "prop-display-owner-uuid"] },
    { "id": "assoc-th-hvac-uuid", "type": "Association", "parentId": "pkg-thermostat-uuid", "memberEndIds": ["prop-th-hvac-uuid", "prop-hvac-owner-uuid"] },
    { "id": "gen-stdhvac-hvac-uuid", "type": "Generalization", "parentId": "blk-standardhvac-uuid", "specificId": "blk-standardhvac-uuid", "generalId": "blk-hvaccontroller-uuid" },
    { "id": "gen-advhvac-hvac-uuid", "type": "Generalization", "parentId": "blk-advancedhvac-uuid", "specificId": "blk-advancedhvac-uuid", "generalId": "blk-hvaccontroller-uuid" },
    { "id": "conn-s-d-uuid", "type": "AssemblyConnector", "name": "sensor_to_display", "parentId": "blk-thermostat-uuid", "kind": "assembly", "end1": { "id": "cend-sd-1-uuid", "partRefId": "prop-th-sensor-uuid", "portRefId": "port-sensor-dataout-uuid" }, "end2": { "id": "cend-sd-2-uuid", "partRefId": "prop-th-display-uuid", "portRefId": "port-display-tempin-uuid" } }
  ]
}

"""

print("⭐⭐⭐处理任务：", "复合COT")

messages = [
    HumanMessage(content=prompt2),
]

response = chat.invoke(messages)

print("😊😊😊处理结果", response.content)
print("😊😊😊使用消耗", response.usage_metadata)