from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

chat = ChatOpenAI(
    model="gpt-4.1-nano-2025-04-14",
    #model="gpt-4o-mini",
    temperature=0.0,
    api_key="sk-TP4VOmsDu2OdsE8H88BaBd09D1B74bD983C7Ca67E6E9AeBf",
    base_url='https://happyapi.org/v1'
)

question1 = """"主要块包括：Frame（承载所有组件，属性包括材料、重量）、Wheel（轮胎、轮辋，属性包括直径、宽度）、DriveSystem（链条、飞轮、变速器，职责为传递动力）、BrakeSystem（手刹、刹车片，职责为减速和停车）、ControlHandle（用户交互界面，提供变速和刹车控制）、Battery（容量10Ah，为电子系统供电）、SensorModule（检测速度、坡度、姿态）、Display（显示速度、档位、电池状态）。接口定义：DriveSystem提供动力输出接口到车轮；BrakeSystem提供制动控制接口；ControlHandle提供用户输入接口；SensorModule提供数据采集接口。内部连接：变速器连接链条，驱动飞轮；电子控制单元连接传感器和显示屏。"""
question2 = """"主要块包括：Frame（承载所有组件，属性包括材料、重量）、Wheel（轮胎、轮辋，属性包括直径、宽度）、DriveSystem（链条、飞轮、变速器，职责为传递动力）、BrakeSystem（手刹、刹车片，职责为减速和停车）、ControlHandle（用户交互界面，提供变速和刹车控制）、Battery（容量10Ah，为电子系统供电）、SensorModule（检测速度、坡度、姿态）、Display（显示速度、档位、电池状态）。接口定义：DriveSystem提供动力输出接口到车轮；BrakeSystem提供制动控制接口；ControlHandle提供用户输入接口；SensorModule提供数据采集接口。内部连接：变速器连接链条，驱动飞轮；电子控制单元连接传感器和显示屏。"""
question3 = """
主要块包括：Frame（承载所有组件，属性包括材料、重量）、Wheel（轮胎、轮辋，属性包括直径、宽度）、DriveSystem（链条、飞轮、变速器，职责为传递动力）、BrakeSystem（手刹、刹车片，职责为减速和停车）、ControlHandle（用户交互界面，提供变速和刹车控制）、Battery（容量10Ah，为电子系统供电）、SensorModule（检测速度、坡度、姿态）、Display（显示速度、档位、电池状态）。接口定义：DriveSystem提供动力输出接口到车轮；BrakeSystem提供制动控制接口；ControlHandle提供用户输入接口；SensorModule提供数据采集接口。内部连接：变速器连接链条，驱动飞轮；电子控制单元连接传感器和显示屏。
"""

prompt1 = """
## 角色
你是一位顶级的系统建模专家和数据结构师，精通 SysML BDD 和 IBD 规范，并且深刻理解 XMI 标准和图数据库（如 Neo4j）的数据建模需求。你的任务是从输入的自然语言工程描述中，全面、精确地提取所有结构和行为元素，并组织成一个统一的、扁平化的思考结果列表，为生成最终的、适用于 XMI 转换和 Neo4j 存储的 JSON 做准备。

## 规则
请严格遵循以下规则和步骤进行思考和提取：
### 最终 JSON 输出结构参考 (目标格式，抽象版) 
```JSON
{
  "model": { // 根模型信息
    "id": "model-uuid",
    "name": "ModelName",
    "diagrams": [ /* 顶层图表 ID 列表 */ ]
  },
  "elements": [ // 所有模型元素的扁平列表
    {
      "id": "...", // 全局唯一 ID (UUID 推荐)
      "type": "Package | Block | ValueType | Unit | Signal | Property | FullPort | ProxyPort | FlowPort | InterfaceBlock | AssemblyConnector | BindingConnector | Association | Generalization | Operation | Reception | Enumeration | EnumerationLiteral | Parameter | ConnectorEnd | Diagram", // 元素类型
      "name": "...", // 元素名称
      "parentId": "...", // 父元素 ID (指向 Package, Block, Model, Signal, Enumeration, ValueType, Operation, Connector 等)
      # --- 特定类型属性 ---
      # Block: isAbstract, properties:[id...], ports:[id...], operations:[id...], receptions:[id...], connectors:[id...], generalizations:[id...], specializations:[id...], ownedDiagrams:[id...]
      # Property: visibility, propertyKind ('value'|'part'|'reference'), aggregation ('composite'|'shared'|'none'), typeId, associationId (for part/ref), multiplicity, defaultValue
      # Port (Full/Proxy/Flow): visibility, typeId (Block/InterfaceBlock/ValueType/DataType/Signal), isProxy(bool), isBehavior(bool), isConjugated(bool), direction(for flow)
      # Connector (Assembly/Binding): kind, typeId(optional assoc ref), end1: {id, partRefId, portRefId/propertyRefId}, end2: {id, partRefId, portRefId/propertyRefId}
      # ConnectorEnd: partRefId, portRefId, propertyRefId # Store refs here now
      # ValueType: baseType, unitId, properties:[id...]
      # Unit: symbol, description
      # Association: memberEndIds: [propId1, propId2]
      # Generalization: specificId, generalId
      # Operation: parameters:[id...]
      # Parameter: direction, typeId
      # Reception: signalId
      # Signal: properties:[id...]
      # Enumeration: literals: [name...] # Store names directly or reference Literal IDs
      # EnumerationLiteral: name
      # Diagram: diagramType ('BDD'|'IBD'), contextId
    }
    # ... 更多元素 ...
  ]
}
```
### 最终JSON输出参考（具体版例子）
```json
{
  "model": {
    "id": "model-fan-uuid",
    "name": "FanSystemModel",
    "diagrams": [
      "diag-fan-bdd",
      "diag-fan-ibd"
    ]
  },
  "elements": [
    {
      "id": "pkg-fan-uuid",
      "type": "Package",
      "name": "FanSystemPackage",
      "parentId": "model-fan-uuid"
    },
    {
      "id": "unit-percent-uuid",
      "type": "Unit",
      "name": "%",
      "parentId": "pkg-fan-uuid",
      "symbol": "%"
    },
    {
      "id": "vt-percentage-uuid",
      "type": "ValueType",
      "name": "Percentage",
      "parentId": "pkg-fan-uuid",
      "baseType": "Real",
      "unitId": "unit-percent-uuid"
    },
    {
      "id": "enum-fanspeed-uuid",
      "type": "Enumeration",
      "name": "FanSpeedLevel",
      "parentId": "pkg-fan-uuid",
      "literals": [
        "lit-fs-off",
        "lit-fs-low",
        "lit-fs-med",
        "lit-fs-high"
      ]
    },
    {
      "id": "enum-ircmdtype-uuid",
      "type": "Enumeration",
      "name": "IRCommandType",
      "parentId": "pkg-fan-uuid",
      "literals": [
        "lit-ircmd-pwt",
        "lit-ircmd-sup",
        "lit-ircmd-sdn"
      ]
    },
    {
      "id": "sig-ircommand-uuid",
      "type": "Signal",
      "name": "IRCommand",
      "parentId": "pkg-fan-uuid",
      "properties": []
    },
    {
      "id": "if-statusdisp-uuid",
      "type": "InterfaceBlock",
      "name": "StatusDisplayInterface",
      "parentId": "pkg-fan-uuid",
      "isAbstract": True
    },
    {
      "id": "blk-acpower-uuid",
      "type": "Block",
      "name": "ACPowerBlock",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False
    },
    {
      "id": "blk-motor-uuid",
      "type": "Block",
      "name": "Motor",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-motor-rpm",
        "prop-motor-fan"
      ],
      "ports": [
        "port-motor-pwrin",
        "port-motor-ctrlin"
      ]
    },
    {
      "id": "blk-irrecv-uuid",
      "type": "Block",
      "name": "IRReceiver",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-irrecv-fan"
      ],
      "ports": [
        "port-irrecv-cmdout"
      ]
    },
    {
      "id": "blk-fan-uuid",
      "type": "Block",
      "name": "Fan",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-fan-motor",
        "prop-fan-recv",
        "prop-fan-speed",
        "prop-fan-remote"
      ],
      "ports": [
        "port-fan-powerin",
        "port-fan-statusdisp"
      ],
      "operations": [
        "op-fan-setspeed"
      ],
      "receptions": [
        "recp-fan-handlesig"
      ],
      "connectors": [
        "conn-fan-recv-motor",
        "conn-fan-pwr-motor",
        "conn-fan-bind-status"
      ],
      "ownedDiagrams": [
        "diag-fan-bdd",
        "diag-fan-ibd"
      ]
    },
    {
      "id": "blk-remote-uuid",
      "type": "Block",
      "name": "RemoteControl",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-remote-battery",
        "prop-remote-fanlink"
      ],
      "operations": [
        "op-remote-sendcmd"
      ]
    },
    {
      "id": "prop-fan-motor",
      "type": "Property",
      "name": "motor",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-motor-uuid",
      "associationId": "assoc-fan-motor",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-recv",
      "type": "Property",
      "name": "receiver",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-irrecv-uuid",
      "associationId": "assoc-fan-recv",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-speed",
      "type": "Property",
      "name": "currentSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "enum-fanspeed-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-remote",
      "type": "Property",
      "name": "_remote",
      "parentId": "blk-fan-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "typeId": "blk-remote-uuid",
      "associationId": "assoc-remote-fan",
      "aggregation": "none"
    },
    {
      "id": "prop-remote-battery",
      "type": "Property",
      "name": "batteryLevel",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "vt-percentage-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-remote-fanlink",
      "type": "Property",
      "name": "pairedFan",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-remote-fan",
      "multiplicity": "[0..1]"
    },
    {
      "id": "prop-motor-rpm",
      "type": "Property",
      "name": "targetRPM",
      "parentId": "blk-motor-uuid",
      "visibility": "private",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "Integer"
    },
    {
      "id": "prop-motor-fan",
      "type": "Property",
      "name": "_fan_motor",
      "parentId": "blk-motor-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-motor",
      "aggregation": "none"
    },
    {
      "id": "prop-irrecv-fan",
      "type": "Property",
      "name": "_fan_recv",
      "parentId": "blk-irrecv-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-recv",
      "aggregation": "none"
    },
    {
      "id": "port-fan-powerin",
      "type": "FullPort",
      "name": "powerIn",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": False
    },
    {
      "id": "port-fan-statusdisp",
      "type": "ProxyPort",
      "name": "statusDisplay",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "if-statusdisp-uuid",
      "isBehavior": True
    },
    {
      "id": "port-motor-pwrin",
      "type": "FullPort",
      "name": "motorPowerIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": False
    },
    {
      "id": "port-motor-ctrlin",
      "type": "ProxyPort",
      "name": "controlIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": True
    },
    {
      "id": "port-irrecv-cmdout",
      "type": "ProxyPort",
      "name": "commandOut",
      "parentId": "blk-irrecv-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": True
    },
    {
      "id": "op-fan-setspeed",
      "type": "Operation",
      "name": "setSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-fss-lvl",
          "name": "level",
          "typeId": "enum-fanspeed-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "op-remote-sendcmd",
      "type": "Operation",
      "name": "sendCommand",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-rsc-cmd",
          "name": "command",
          "typeId": "enum-ircmdtype-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "recp-fan-handlesig",
      "type": "Reception",
      "name": "handleIRCommand",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "signalId": "sig-ircommand-uuid"
    },
    {
      "id": "lit-fs-off",
      "type": "EnumerationLiteral",
      "name": "Off",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-low",
      "type": "EnumerationLiteral",
      "name": "Low",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-med",
      "type": "EnumerationLiteral",
      "name": "Medium",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-high",
      "type": "EnumerationLiteral",
      "name": "High",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-ircmd-pwt",
      "type": "EnumerationLiteral",
      "name": "PowerToggle",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-ircmd-sup",
      "type": "EnumerationLiteral",
      "name": "SpeedUp",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-irccmd-sdn",
      "type": "EnumerationLiteral",
      "name": "SpeedDown",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "assoc-fan-motor",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-motor",
        "prop-motor-fan"
      ]
    },
    {
      "id": "assoc-fan-recv",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-recv",
        "prop-irrecv-fan"
      ]
    },
    {
      "id": "assoc-remote-fan",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-remote-fanlink",
        "prop-fan-remote"
      ]
    },
    {
      "id": "conn-fan-recv-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "id": "cEnd-frcm-1",
        "partRefId": "prop-fan-recv",
        "portRefId": "port-irrecv-cmdout"
      },
      "end2": {
        "id": "cEnd-frcm-2",
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-ctrlin"
      }
    },
    {
      "id": "conn-fan-pwr-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "id": "cEnd-fpwm-1",
        "partRefId": None,
        "portRefId": "port-fan-powerin"
      },
      "end2": {
        "id": "cEnd-fpwm-2",
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-pwrin"
      }
    },
    {
      "id": "conn-fan-bind-status",
      "type": "BindingConnector",
      "parentId": "blk-fan-uuid",
      "kind": "binding",
      "end1": {
        "id": "cEnd-fbs-1",
        "partRefId": None,
        "propertyRefId": "prop-fan-speed"
      },
      "end2": {
        "id": "cEnd-fbs-2",
        "partRefId": None,
        "portRefId": "port-fan-statusdisp"
      }
    }
  ]
}
```

### 提取步骤 (你的思考过程应遵循这些步骤):

1.  **识别顶层结构 (Model & Packages):** 确定根 `Model` 和所有 `Package`，分配 ID，记录名称和 `parentId`。
2.  **识别核心类型定义 (InterfaceBlocks, Abstract Blocks, ValueTypes, Units, Signals, Enumerations, Concrete Blocks used as types):** 识别这些基础元素，分配 ID，记录名称、`parentId` 及类型特定属性。优先处理。
3.  **识别主要功能块 (Concrete Blocks):** 识别核心的功能块，分配 ID，记录名称、`parentId`。
4.  **识别内部成员 (Properties, Ports, Operations, Receptions, Literals, Parameters):**
    *   **属性 (Properties):** 分类 (`value`/`part`/`reference`)，记录详细信息。**暂存关联信息。** 定义为 Block/ValueType/Signal 的子元素 (`parentId`)。
    *   **端口 (Ports):** 分类 (`FullPort`/`ProxyPort`/`FlowPort`)，记录详细信息。**检查类型规则: Proxy->IFBlock; Full->Block; Flow->Block/IFBlock/VT/DT/Signal。** 定义为 Block 的子元素。
    *   **操作 (Operations):** 记录 Operation 信息，`parentId` 指向 Block。
    *   **参数 (Parameters):** **为每个操作参数创建独立的 `Parameter` 元素**，记录 `id`, `name`, `parentId` (指向 Operation ID), `typeId`, `direction`。
    *   **接收 (Receptions):** 记录 Reception 信息，`parentId` 指向 Block，`signalId` 指向 Signal。
    *   **枚举文字 (EnumerationLiterals):** **为每个枚举文字创建独立的 `EnumerationLiteral` 元素**，记录 `id`, `name`, `parentId` (指向 Enumeration ID)。
5.  **识别关系元素 (Associations, Generalizations):**
    *   **Associations:** 为每个 part/reference 属性创建。记录 `id`, `parentId` (Package ID)。`memberEndIds` 列表包含两个有效的 Property ID。
    *   **Generalizations:** 识别 "is-a" 关系。记录 `id`, `parentId` (**子类** Block ID), `specificId`, `generalId`。
6.  **识别 IBD 结构 (Connectors, ConnectorEnds & IBD Diagrams):**
    *   **查找 IBD 描述。**
    *   **对于每个含 IBD 的 Block:**
        *   **连接器 (Connectors):** 分类 (`assembly`/`binding`)，记录 `id`, `name`, `parentId` (**Block ID**), `kind`。**记录 `end1` 和 `end2` 中对应的 `ConnectorEnd` 元素的 ID**。
        *   **连接器端点 (ConnectorEnds):** **为每个连接器的端点创建独立的 `ConnectorEnd` 元素**，记录 `id`, `parentId` (指向 Connector ID)，并记录端点的具体信息： `partRefId` (部件 Property ID 或 null) 和 `portRefId` (端口 ID) 或 `propertyRefId` (属性 ID)。
        *   **IBD 图表:** 创建 `Diagram` 元素 (type="IBD")。
7. JSON里面的true和false，均为True、False。

## 样例

### 输入文本:
"设计一个`风扇系统包` (`FanSystemPackage`)。该包定义了两个主要块：`风扇` (`Fan`) 和 `遥控器` (`RemoteControl`)。

`风扇`块包含以下部件属性（组合关系）：一个`电机`部件 (`motor`, 类型 `Motor`)；一个`接收器单元` (`receiver`, 类型 `IRReceiver`)。`风扇`块还有一个值属性`当前风速等级` (`currentSpeedLevel`, 类型 `FanSpeedLevel` - 枚举: Off, Low, Medium, High)；一个全端口`电源输入` (`powerIn`, 类型 `ACPowerBlock`)；一个代理端口`状态显示接口` (`statusDisplay`, 类型 `StatusDisplayInterface`)；一个操作`手动设置风速` (`setSpeedLevel(level: FanSpeedLevel)`)；一个接收器`处理红外命令` (`handleIRCommand`)，响应`IRCommand`信号。

`遥控器`块包含：一个值属性`电池电量` (`batteryLevel`, 类型 `Percentage`)；一个操作`发送指令` (`sendCommand(command: IRCommandType)`)，其中`IRCommandType`是枚举 (PowerToggle, SpeedUp, SpeedDown)；一个关联属性`配对风扇` (`pairedFan`, 类型 `Fan`)。

需要定义值类型：`Percentage`(Real, 单位 %)。需要定义枚举：`FanSpeedLevel`, `IRCommandType`。需要定义信号：`IRCommand`。需要定义接口块：`StatusDisplayInterface`。需要定义块：`Motor`, `IRReceiver`, `ACPowerBlock` (作为 Full Port 类型)。需要定义单位: `%`。

在 `Fan` 块的内部中，`接收器单元` (`receiver`) 的`指令输出`端口 (`commandOut`) 通过**Assembly Connector**连接到`电机`部件 (`motor`) 的`控制输入`端口 (`controlIn`)。`电源输入`全端口 (`powerIn`) 通过**Assembly Connector**连接到`电机`部件 (`motor`) 的`电源接口`端口 (`motorPowerIn`)。`风扇`块的`当前风速等级`值属性 (`currentSpeedLevel`) 通过**Binding Connector**连接到边界代理端口`状态显示接口` (`statusDisplay`)。
"

### 输出文本:
请你按照如下的6步进行思考推理：

1.  **识别顶层结构:**
    *   Model: id=`model-fan-uuid`, name=`FanSystemModel`
    *   Package: id=`pkg-fan-uuid`, name=`FanSystemPackage`, parentId=`model-fan-uuid`

2.  **识别核心类型定义:**
    *   Unit: id=`unit-percent-uuid`, name=`%`, parentId=`pkg-fan-uuid`, symbol=`%`
    *   ValueType: id=`vt-percentage-uuid`, name=`Percentage`, parentId=`pkg-fan-uuid`, baseType=`Real`, unitId=`unit-percent-uuid`
    *   Enumeration: id=`enum-fanspeed-uuid`, name=`FanSpeedLevel`, parentId=`pkg-fan-uuid`
    *   Enumeration: id=`enum-ircmdtype-uuid`, name=`IRCommandType`, parentId=`pkg-fan-uuid`
    *   Signal: id=`sig-ircommand-uuid`, name=`IRCommand`, parentId=`pkg-fan-uuid`
    *   InterfaceBlock: id=`if-statusdisp-uuid`, name=`StatusDisplayInterface`, parentId=`pkg-fan-uuid`, isAbstract=True
    *   Block: id=`blk-acpower-uuid`, name=`ACPowerBlock`, parentId=`pkg-fan-uuid`, isAbstract=False
    *   Block: id=`blk-motor-uuid`, name=`Motor`, parentId=`pkg-fan-uuid`, isAbstract=False
    *   Block: id=`blk-irrecv-uuid`, name=`IRReceiver`, parentId=`pkg-fan-uuid`, isAbstract=False

3.  **识别主要功能块:**
    *   Block: id=`blk-fan-uuid`, name=`Fan`, parentId=`pkg-fan-uuid`, isAbstract=False
    *   Block: id=`blk-remote-uuid`, name=`RemoteControl`, parentId=`pkg-fan-uuid`, isAbstract=False

4.  **识别内部成员:**
    *   **For Block `Fan` (id: `blk-fan-uuid`):**
        *   Property (Part): `motor`: id=`prop-fan-motor`, parentId=`blk-fan-uuid`, kind=`part`, typeId=`blk-motor-uuid`, assocId=`assoc-fan-motor`.
        *   Property (Part): `receiver`: id=`prop-fan-recv`, parentId=`blk-fan-uuid`, kind=`part`, typeId=`blk-irrecv-uuid`, assocId=`assoc-fan-recv`.
        *   Property (Value): `currentSpeedLevel`: id=`prop-fan-speed`, parentId=`blk-fan-uuid`, kind=`value`, typeId=`enum-fanspeed-uuid`.
        *   Property (Reference): `_remote`: id=`prop-fan-remote`, parentId=`blk-fan-uuid`, kind=`reference`, typeId=`blk-remote-uuid`, assocId=`assoc-remote-fan`.
        *   Port: `powerIn`: id=`port-fan-powerin`, type=`FullPort`, parentId=`blk-fan-uuid`, typeId=`blk-acpower-uuid`.
        *   Port: `statusDisplay`: id=`port-fan-statusdisp`, type=`ProxyPort`, parentId=`blk-fan-uuid`, typeId=`if-statusdisp-uuid`.
        *   Operation: `setSpeedLevel`: id=`op-fan-setspeed`, parentId=`blk-fan-uuid`.
        *   Reception: `handleIRCommand`: id=`recp-fan-handlesig`, parentId=`blk-fan-uuid`, signalId=`sig-ircommand-uuid`.
    *   **For Block `RemoteControl` (id: `blk-remote-uuid`):**
        *   Property (Value): `batteryLevel`: id=`prop-remote-battery`, parentId=`blk-remote-uuid`, kind=`value`, typeId=`vt-percentage-uuid`.
        *   Property (Reference): `pairedFan`: id=`prop-remote-fanlink`, parentId=`blk-remote-uuid`, kind=`reference`, typeId=`blk-fan-uuid`, assocId=`assoc-remote-fan`.
        *   Operation: `sendCommand`: id=`op-remote-sendcmd`, parentId=`blk-remote-uuid`.
    *   **For Block `Motor` (id: `blk-motor-uuid`):**
        *   Property (Value): `targetRPM`: id=`prop-motor-rpm`, parentId=`blk-motor-uuid`, kind=`value`, typeId=`Integer`, visibility=`private`.
        *   Property (Reference): `_fan_motor`: id=`prop-motor-fan`, parentId=`blk-motor-uuid`, kind=`reference`, typeId=`blk-fan-uuid`, assocId=`assoc-fan-motor`, visibility=`private`.
        *   Port: `motorPowerIn`: id=`port-motor-pwrin`, type=`FullPort`, parentId=`blk-motor-uuid`, typeId=`blk-acpower-uuid`.
        *   Port: `controlIn`: id=`port-motor-ctrlin`, type=`ProxyPort`, parentId=`blk-motor-uuid`, typeId=`sig-ircommand-uuid`.
    *   **For Block `IRReceiver` (id: `blk-irrecv-uuid`):**
        *   Property (Reference): `_fan_recv`: id=`prop-irrecv-fan`, parentId=`blk-irrecv-uuid`, kind=`reference`, typeId=`blk-fan-uuid`, assocId=`assoc-fan-recv`, visibility=`private`.
        *   Port: `commandOut`: id=`port-irrecv-cmdout`, type=`ProxyPort`, parentId=`blk-irrecv-uuid`, typeId=`sig-ircommand-uuid`.
    *   **For Operation `setSpeedLevel` (id: `op-fan-setspeed`):**
        *   Parameter: `level`: id=`p-fss-lvl`, name=`level`, parentId=`op-fan-setspeed`, typeId=`enum-fanspeed-uuid`, direction=`in`.
    *   **For Operation `sendCommand` (id: `op-remote-sendcmd`):**
        *   Parameter: `command`: id=`p-rsc-cmd`, name=`command`, parentId=`op-remote-sendcmd`, typeId=`enum-ircmdtype-uuid`, direction=`in`.
    *   **For Enumeration `FanSpeedLevel` (id: `enum-fanspeed-uuid`):**
        *   Literal: id=`lit-fs-off`, name=`Off`, parentId=`enum-fanspeed-uuid`.
        *   Literal: id=`lit-fs-low`, name=`Low`, parentId=`enum-fanspeed-uuid`.
        *   Literal: id=`lit-fs-med`, name=`Medium`, parentId=`enum-fanspeed-uuid`.
        *   Literal: id=`lit-fs-high`, name=`High`, parentId=`enum-fanspeed-uuid`.
    *   **For Enumeration `IRCommandType` (id: `enum-ircmdtype-uuid`):**
        *   Literal: id=`lit-ircmd-pwt`, name=`PowerToggle`, parentId=`enum-ircmdtype-uuid`.
        *   Literal: id=`lit-ircmd-sup`, name=`SpeedUp`, parentId=`enum-ircmdtype-uuid`.
        *   Literal: id=`lit-ircmd-sdn`, name=`SpeedDown`, parentId=`enum-ircmdtype-uuid`.

5.  **识别关系元素:**
    *   Association: id=`assoc-fan-motor`, parentId=`pkg-fan-uuid`, memberEndIds=[`prop-fan-motor`, `prop-motor-fan`].
    *   Association: id=`assoc-fan-recv`, parentId=`pkg-fan-uuid`, memberEndIds=[`prop-fan-recv`, `prop-irrecv-fan`].
    *   Association: id=`assoc-remote-fan`, parentId=`pkg-fan-uuid`, memberEndIds=[`prop-remote-fanlink`, `prop-fan-remote`].

6.  **识别 IBD 结构 (For `Fan` Block):**
    *   Connector: `conn-fan-recv-motor`: id=`conn-fan-recv-motor`, parentId=`blk-fan-uuid`, kind=`assembly`. End1 ID: `cEnd-frcm-1`, End2 ID: `cEnd-frcm-2`.
    *   Connector: `conn-fan-pwr-motor`: id=`conn-fan-pwr-motor`, parentId=`blk-fan-uuid`, kind=`assembly`. End1 ID: `cEnd-fpwm-1`, End2 ID: `cEnd-fpwm-2`.
    *   Connector: `conn-fan-bind-status`: id=`conn-fan-bind-status`, parentId=`blk-fan-uuid`, kind=`binding`. End1 ID: `cEnd-fbs-1`, End2 ID: `cEnd-fbs-2`.
    *   ConnectorEnd: id=`cEnd-frcm-1`, parentId=`conn-fan-recv-motor`, partRefId=`prop-fan-recv`, portRefId=`port-irrecv-cmdout`.
    *   ConnectorEnd: id=`cEnd-frcm-2`, parentId=`conn-fan-recv-motor`, partRefId=`prop-fan-motor`, portRefId=`port-motor-ctrlin`.
    *   ConnectorEnd: id=`cEnd-fpwm-1`, parentId=`conn-fan-pwr-motor`, partRefId=None, portRefId=`port-fan-powerin`.
    *   ConnectorEnd: id=`cEnd-fpwm-2`, parentId=`conn-fan-pwr-motor`, partRefId=`prop-fan-motor`, portRefId=`port-motor-pwrin`.
    *   ConnectorEnd: id=`cEnd-fbs-1`, parentId=`conn-fan-bind-status`, partRefId=None, propertyRefId=`prop-fan-speed`.
    *   ConnectorEnd: id=`cEnd-fbs-2`, parentId=`conn-fan-bind-status`, partRefId=None, portRefId=`port-fan-statusdisp`.
    *   IBD Diagram: id=`diag-fan-ibd`, name=`风扇 IBD`, parentId=`blk-fan-uuid`, diagramType="IBD", contextId=`blk-fan-uuid`.


```

## 具体任务
输入：          
""" + question2  + """输出：请你一步一步进行推理思考，按照上述的思考给我过程。"""

messages = [
    HumanMessage(content=prompt1),
]

print("⭐⭐⭐正在执行任务：", question2)
response = chat.invoke(messages)

print("😊😊😊推理结果：", response.content)
print("😊😊😊使用消耗", response.usage_metadata)

prompt2 = prompt1 + response.content + """


## 角色
你是一位精确的数据转换工程师，负责将详细的、扁平化的 SysML 元素思考过程转化为严格符合规范的统一 JSON 格式，只输出JSON，不能输出其它内容，并且JSON不带有注释，符合python的json格式。


## ！约束规则！
+ 你必须遵循以下的注意事项：
+ 下面提到的JSON是最开始的案例文本提取出的结果，你参考下面的JSON格式对上文“输入文本”所输出的“思考过程”进行整合提取，
+ 特别需要注意绑定连接器(包括end端点)、关联关系等等这些关系的id与元素的对应，需要关联到正确的对象元素上面。
+ 同时以及block之间的关联，block内部的属性等等。
+ 请确保elements中的每一个元素中的parentId，都有一个element的id与之对应，如果找不到对应的id，请在elements中寻找的正确的id与之对应，除非这是一个独立元素。
+ Connector 必须连接端口（Ports）。它的端点应该通过 partRefId 指向一个部件属性（propertyKind: "part"），并通过 portRefId 指向该部件类型上的一个端口。portRefId 为 null，意味着您根本没有连接到任何端口。
+ 如果Connector连接器两端都悬空，因为它没有连接到合法的端口上，则需要检查是否存在错误。
+ ConnectorEnd元素，不需要单独创建，实际就是AssemblyConnector或BindingConnector中的end1或end2；对于两端end，可能会连接不同的对象，例如Block、Value或Port分别对应partRefId、propertyRefId或portRefId，一般一个end连接其中的一个对象即可。
+ 对于type为Property的element，你认真观察对应元素的JSON结构；对于其中的propertyKind属性，它有三种类型："part"、"value"、"reference"，对于不同的类型typeId的值是不同的，例如：
    * 如果propertyKind为"part"，则typeId为block的id；
    * 如果propertyKind为"value"，则typeId填写基本类型例如Real、String、Integer、Boolean等；
    * 如果propertyKind为"reference"，则typeId为block的id。
+ 在你输出结果之前，请你与下方正确的案例JSON进行对比，确保你的结果是正确的：
```JSON
{
  "model": {
    "id": "model-fan-uuid",
    "name": "FanSystemModel",
    "diagrams": [
      "diag-fan-bdd",
      "diag-fan-ibd"
    ]
  },
  "elements": [
    {
      "id": "pkg-fan-uuid",
      "type": "Package",
      "name": "FanSystemPackage",
      "parentId": "model-fan-uuid"
    },
    {
      "id": "unit-percent-uuid",
      "type": "Unit",
      "name": "%",
      "parentId": "pkg-fan-uuid",
      "symbol": "%"
    },
    {
      "id": "vt-percentage-uuid",
      "type": "ValueType",
      "name": "Percentage",
      "parentId": "pkg-fan-uuid",
      "baseType": "Real",
      "unitId": "unit-percent-uuid"
    },
    {
      "id": "enum-fanspeed-uuid",
      "type": "Enumeration",
      "name": "FanSpeedLevel",
      "parentId": "pkg-fan-uuid",
      "literals": [
        "lit-fs-off",
        "lit-fs-low",
        "lit-fs-med",
        "lit-fs-high"
      ]
    },
    {
      "id": "enum-ircmdtype-uuid",
      "type": "Enumeration",
      "name": "IRCommandType",
      "parentId": "pkg-fan-uuid",
      "literals": [
        "lit-ircmd-pwt",
        "lit-ircmd-sup",
        "lit-ircmd-sdn"
      ]
    },
    {
      "id": "sig-ircommand-uuid",
      "type": "Signal",
      "name": "IRCommand",
      "parentId": "pkg-fan-uuid",
      "properties": []
    },
    {
      "id": "if-statusdisp-uuid",
      "type": "InterfaceBlock",
      "name": "StatusDisplayInterface",
      "parentId": "pkg-fan-uuid",
      "isAbstract": True
    },
    {
      "id": "blk-acpower-uuid",
      "type": "Block",
      "name": "ACPowerBlock",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False
    },
    {
      "id": "blk-motor-uuid",
      "type": "Block",
      "name": "Motor",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-motor-rpm",
        "prop-motor-fan"
      ],
      "ports": [
        "port-motor-pwrin",
        "port-motor-ctrlin"
      ]
    },
    {
      "id": "blk-irrecv-uuid",
      "type": "Block",
      "name": "IRReceiver",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-irrecv-fan"
      ],
      "ports": [
        "port-irrecv-cmdout"
      ]
    },
    {
      "id": "blk-fan-uuid",
      "type": "Block",
      "name": "Fan",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-fan-motor",
        "prop-fan-recv",
        "prop-fan-speed",
        "prop-fan-remote"
      ],
      "ports": [
        "port-fan-powerin",
        "port-fan-statusdisp"
      ],
      "operations": [
        "op-fan-setspeed"
      ],
      "receptions": [
        "recp-fan-handlesig"
      ],
      "connectors": [
        "conn-fan-recv-motor",
        "conn-fan-pwr-motor",
        "conn-fan-bind-status"
      ],
      "ownedDiagrams": [
        "diag-fan-bdd",
        "diag-fan-ibd"
      ]
    },
    {
      "id": "blk-remote-uuid",
      "type": "Block",
      "name": "RemoteControl",
      "parentId": "pkg-fan-uuid",
      "isAbstract": False,
      "properties": [
        "prop-remote-battery",
        "prop-remote-fanlink"
      ],
      "operations": [
        "op-remote-sendcmd"
      ]
    },
    {
      "id": "prop-fan-motor",
      "type": "Property",
      "name": "motor",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-motor-uuid",
      "associationId": "assoc-fan-motor",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-recv",
      "type": "Property",
      "name": "receiver",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-irrecv-uuid",
      "associationId": "assoc-fan-recv",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-speed",
      "type": "Property",
      "name": "currentSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "enum-fanspeed-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-remote",
      "type": "Property",
      "name": "_remote",
      "parentId": "blk-fan-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "typeId": "blk-remote-uuid",
      "associationId": "assoc-remote-fan",
      "aggregation": "none"
    },
    {
      "id": "prop-remote-battery",
      "type": "Property",
      "name": "batteryLevel",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "vt-percentage-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-remote-fanlink",
      "type": "Property",
      "name": "pairedFan",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-remote-fan",
      "multiplicity": "[0..1]"
    },
    {
      "id": "prop-motor-rpm",
      "type": "Property",
      "name": "targetRPM",
      "parentId": "blk-motor-uuid",
      "visibility": "private",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "Integer"
    },
    {
      "id": "prop-motor-fan",
      "type": "Property",
      "name": "_fan_motor",
      "parentId": "blk-motor-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-motor",
      "aggregation": "none"
    },
    {
      "id": "prop-irrecv-fan",
      "type": "Property",
      "name": "_fan_recv",
      "parentId": "blk-irrecv-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-recv",
      "aggregation": "none"
    },
    {
      "id": "port-fan-powerin",
      "type": "FullPort",
      "name": "powerIn",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": False
    },
    {
      "id": "port-fan-statusdisp",
      "type": "ProxyPort",
      "name": "statusDisplay",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "if-statusdisp-uuid",
      "isBehavior": True
    },
    {
      "id": "port-motor-pwrin",
      "type": "FullPort",
      "name": "motorPowerIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": False
    },
    {
      "id": "port-motor-ctrlin",
      "type": "ProxyPort",
      "name": "controlIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": True
    },
    {
      "id": "port-irrecv-cmdout",
      "type": "ProxyPort",
      "name": "commandOut",
      "parentId": "blk-irrecv-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": True
    },
    {
      "id": "op-fan-setspeed",
      "type": "Operation",
      "name": "setSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-fss-lvl",
          "name": "level",
          "typeId": "enum-fanspeed-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "op-remote-sendcmd",
      "type": "Operation",
      "name": "sendCommand",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-rsc-cmd",
          "name": "command",
          "typeId": "enum-ircmdtype-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "recp-fan-handlesig",
      "type": "Reception",
      "name": "handleIRCommand",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "signalId": "sig-ircommand-uuid"
    },
    {
      "id": "lit-fs-off",
      "type": "EnumerationLiteral",
      "name": "Off",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-low",
      "type": "EnumerationLiteral",
      "name": "Low",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-med",
      "type": "EnumerationLiteral",
      "name": "Medium",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-high",
      "type": "EnumerationLiteral",
      "name": "High",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-ircmd-pwt",
      "type": "EnumerationLiteral",
      "name": "PowerToggle",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-ircmd-sup",
      "type": "EnumerationLiteral",
      "name": "SpeedUp",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-irccmd-sdn",
      "type": "EnumerationLiteral",
      "name": "SpeedDown",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "assoc-fan-motor",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-motor",
        "prop-motor-fan"
      ]
    },
    {
      "id": "assoc-fan-recv",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-recv",
        "prop-irrecv-fan"
      ]
    },
    {
      "id": "assoc-remote-fan",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-remote-fanlink",
        "prop-fan-remote"
      ]
    },
    {
      "id": "conn-fan-recv-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "id": "cEnd-frcm-1",
        "partRefId": "prop-fan-recv",
        "portRefId": "port-irrecv-cmdout"
      },
      "end2": {
        "id": "cEnd-frcm-2",
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-ctrlin"
      }
    },
    {
      "id": "conn-fan-pwr-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "id": "cEnd-fpwm-1",
        "partRefId": None,
        "portRefId": "port-fan-powerin"
      },
      "end2": {
        "id": "cEnd-fpwm-2",
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-pwrin"
      }
    },
    {
      "id": "conn-fan-bind-status",
      "type": "BindingConnector",
      "parentId": "blk-fan-uuid",
      "kind": "binding",
      "end1": {
        "id": "cEnd-fbs-1",
        "partRefId": None,
        "propertyRefId": "prop-fan-speed"
      },
      "end2": {
        "id": "cEnd-fbs-2",
        "partRefId": None,
        "portRefId": "port-fan-statusdisp"
      }
    }
  ]
}

```
+ 总之，你输出的最终的JSON，必须符合上面JSON的结构，认真查看上述JSON中elements中每一个元素对象的设计结构，如果不符合不允许你输出，请你修改至符合要求为我输出最终的JSON。

"""

print("⭐⭐⭐处理任务：", "复合COT")

messages = [
    HumanMessage(content=prompt2),
]

response = chat.invoke(messages)

print("😊😊😊处理结果", response.content)
print("😊😊😊使用消耗", response.usage_metadata)