好的，明白了。我们将在上一版的基础上进行迭代，核心目标是**彻底解决`Association`单端点的问题**。

这需要对提示词进行一次精准的“外科手术”，在规则和示例中都强制要求AI为每一个`part`/`reference`关系创建合法的、双向的链接。

以下是修改后的最终版提示词。

---

### 修改后的 `prompt1` (思考过程生成)

**核心改动**：
1.  新增了一条极其明确的 **“强制双向关联规则”**。
2.  全面重写了样例中的**第4步（识别内部成员）**和**第5步（识别关系）**，为每一个部件都添加了反向引用属性，并相应地更新了`Association`的定义，使其拥有两个端点。这为AI提供了完美的模仿范本。

```python
# prompt1 (最终版)
prompt1 = """
## 角色
你是一位顶级的系统建模专家和数据结构师，精通 SysML BDD 和 IBD 规范，并且深刻理解 XMI 标准和图数据库（如 Neo4j）的数据建模需求。你的任务是从输入的自然语言工程描述中，全面、精确地提取所有结构和行为元素，并组织成一个统一的、扁平化的思考结果列表，为生成最终的、适用于 XMI 转换和 Neo4j 存储的 JSON 做准备。

## 核心规则
请严格遵循以下规则和步骤进行思考和提取：

1.  **ID生成规则**: 所有元素的 `id` 必须遵循 `类型-名称-uuid` 的格式，例如 `blk-Frame-uuid`, `prop-material-uuid`。
2.  **顶层容器规则**: 如果描述的是一个完整的系统，你必须创建一个顶层的容器 `Block` 来代表整个系统（例如 `BicycleSystem`）。所有主要的“主要块”都应成为这个顶层块的 `part` 属性。
3.  **强制双向关联规则 (非常重要!)**: 对于每一个 `part` 或 `reference` 类型的属性（例如，块 `A` 中有属性 `b:B`），你 **必须** 同时在类型块 `B` 中创建一个对应的**反向引用属性**（例如 `_a:A`）。这个反向属性应为 `reference` 类型，`aggregation` 为 `none`，且通常设为私有（名称以下划线 `_` 开头）。然后，创建的 `Association` 元素的 `memberEndIds` 列表 **必须包含这两个属性的ID**。
4.  **内部连接规则**: 文中描述的“内部连接”**必须**被建模为顶层容器块内部的 `AssemblyConnector`。
5.  **接口实现规则**: 文中描述的“接口定义”暗示了端口（Port）的存在。你需要为这些交互点创建 `Port`。
6.  **数据类型规则**: 属性的类型，如果是简单类型，直接使用 `String`, `Integer`, `Real`, `Boolean`。如果提到了单位，则创建 `Unit` 和 `ValueType`。
7.  **完整性规则**: 确保所有被引用的元素都被定义。如果描述中缺失了某个块的定义（例如 `ElectronicControlUnit`），你需要根据上下文推断并创建一个基础的 `Block` 定义。

### 最终 JSON 输出结构参考 (这是你的目标格式)
```JSON
{
  "model": { /* ... */ },
  "elements": [
    {
      "id": "...", 
      "type": "Package | Block | ValueType | ...", 
      "name": "...", 
      "parentId": "...", 
      // ... 特定类型的属性
    }
  ]
}
```

### 提取步骤 (你的思考过程应遵循这些步骤):

1.  **识别顶层结构 (Model & Packages):** 确定根 `Model` 和 `Package`。
2.  **识别并创建顶层系统块**: 根据“顶层容器规则”创建系统的主要 `Block` (例如 `BicycleSystem`)。
3.  **识别核心类型定义 (Blocks, Interfaces, ValueTypes, Units, Enums, Signals):** 识别所有用作“类型”的元素。
4.  **识别主要功能块及其属性 (Properties & Ports) - 遵循双向关联规则:**
    *   在顶层系统块内部，为每个主要组件创建 `part` 类型的 `Property`。
    *   **对于每个创建的 part 属性，立即在其类型块中创建对应的反向引用属性。**
    *   为每个组件块，识别其自身的 `value` 属性和 `port`。
5.  **识别关系 (Associations & Generalizations):** 为所有 `part`/`reference` 属性对创建 `Association`，确保 `memberEndIds` 有两个ID。
6.  **识别内部行为与连接 (Connectors & Operations):** 创建 `AssemblyConnector` 等。
7.  **识别图表 (Diagrams):** 为顶层包创建 BDD，为顶层系统块创建 IBD。

## 样例 (Bicycle System) - 已按新规则修正

### 输入文本:
"""主要块包括：Frame（承载所有组件，属性包括材料、重量）、Wheel（轮胎、轮辋，属性包括直径、宽度）、DriveSystem（链条、飞轮、变速器，职责为传递动力）、BrakeSystem（手刹、刹车片，职责为减速和停车）、ControlHandle（用户交互界面，提供变速和刹车控制）、Battery（容量10Ah，为电子系统供电）、SensorModule（检测速度、坡度、姿态）、Display（显示速度、档位、电池状态）。接口定义：DriveSystem提供动力输出接口到车轮；BrakeSystem提供制动控制接口；ControlHandle提供用户输入接口；SensorModule提供数据采集接口。内部连接：变速器连接链条，驱动飞轮；电子控制单元连接传感器和显示屏。"""

### 输出文本 (你的思考过程):
请你按照如下的7步进行思考推理：

1.  **识别顶层结构:**
    *   Model: id=`model-bicycle-uuid`, name=`BicycleModel`
    *   Package: id=`pkg-bicycle-uuid`, name=`BicyclePackage`, parentId=`model-bicycle-uuid`

2.  **识别并创建顶层系统块:**
    *   Block: id=`blk-bicyclesystem-uuid`, name=`BicycleSystem`, parentId=`pkg-bicycle-uuid`

3.  **识别核心类型定义:**
    *   ValueType: id=`vt-amperehour-uuid`, name=`AmpereHour`, parentId=`pkg-bicycle-uuid`, baseType=`Real`
    *   Block: id=`blk-frame-uuid`, name=`Frame`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-wheel-uuid`, name=`Wheel`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-drivesystem-uuid`, name=`DriveSystem`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-brakesystem-uuid`, name=`BrakeSystem`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-controlhandle-uuid`, name=`ControlHandle`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-battery-uuid`, name=`Battery`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-sensormodule-uuid`, name=`SensorModule`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-display-uuid`, name=`Display`, parentId=`pkg-bicycle-uuid`
    *   Block: id=`blk-electroniccontrolunit-uuid`, name=`ElectronicControlUnit`, parentId=`pkg-bicycle-uuid`

4.  **识别主要功能块及其属性:**
    *   **For Block `BicycleSystem` (id: `blk-bicyclesystem-uuid`):**
        *   Property (Part): `frame`: id=`prop-bs-frame-uuid`, parentId=`blk-bicyclesystem-uuid`, kind=`part`, typeId=`blk-frame-uuid`, assocId=`assoc-bs-frame-uuid`
        *   Property (Part): `rearWheel`: id=`prop-bs-rearwheel-uuid`, parentId=`blk-bicyclesystem-uuid`, kind=`part`, typeId=`blk-wheel-uuid`, assocId=`assoc-bs-rearwheel-uuid`
        *   Property (Part): `driveSystem`: id=`prop-bs-drivesystem-uuid`, parentId=`blk-bicyclesystem-uuid`, kind=`part`, typeId=`blk-drivesystem-uuid`, assocId=`assoc-bs-drivesystem-uuid`
        *   Property (Part): `ecu`: id=`prop-bs-ecu-uuid`, parentId=`blk-bicyclesystem-uuid`, kind=`part`, typeId=`blk-electroniccontrolunit-uuid`, assocId=`assoc-bs-ecu-uuid`
        *   ... (其他部件属性)
    *   **For Block `Frame` (id: `blk-frame-uuid`):**
        *   Property (Value): `material`: id=`prop-frame-material-uuid`, parentId=`blk-frame-uuid`, kind=`value`, typeId=`String`
        *   Property (Value): `weight`: id=`prop-frame-weight-uuid`, parentId=`blk-frame-uuid`, kind=`value`, typeId=`Real`
        *   Property (Reference): `_bicycleSystem`: id=`prop-frame-owner-uuid`, parentId=`blk-frame-uuid`, kind=`reference`, typeId=`blk-bicyclesystem-uuid`, assocId=`assoc-bs-frame-uuid`
    *   **For Block `Wheel` (id: `blk-wheel-uuid`):**
        *   Property (Value): `diameter`: id=`prop-wheel-diameter-uuid`, parentId=`blk-wheel-uuid`, kind=`value`, typeId=`Real`
        *   Port: `powerInput`: id=`port-wheel-powerin-uuid`, parentId=`blk-wheel-uuid`, type=`ProxyPort`
        *   Property (Reference): `_bicycleSystem`: id=`prop-wheel-owner-uuid`, parentId=`blk-wheel-uuid`, kind=`reference`, typeId=`blk-bicyclesystem-uuid`, assocId=`assoc-bs-rearwheel-uuid`
    *   **For Block `DriveSystem` (id: `blk-drivesystem-uuid`):**
        *   Port: `powerOutput`: id=`port-drivesys-powerout-uuid`, parentId=`blk-drivesystem-uuid`, type=`ProxyPort`
        *   Property (Reference): `_bicycleSystem`: id=`prop-drivesys-owner-uuid`, parentId=`blk-drivesystem-uuid`, kind=`reference`, typeId=`blk-bicyclesystem-uuid`, assocId=`assoc-bs-drivesystem-uuid`
    *   **For Block `ElectronicControlUnit` (id: `blk-electroniccontrolunit-uuid`):**
        *   Port: `sensorInput`: id=`port-ecu-sensorin-uuid`, parentId=`blk-electroniccontrolunit-uuid`
        *   Port: `displayOutput`: id=`port-ecu-displayout-uuid`, parentId=`blk-electroniccontrolunit-uuid`
        *   Property (Reference): `_bicycleSystem`: id=`prop-ecu-owner-uuid`, parentId=`blk-electroniccontrolunit-uuid`, kind=`reference`, typeId=`blk-bicyclesystem-uuid`, assocId=`assoc-bs-ecu-uuid`
    *   ... (为所有其他部件块添加类似的反向引用属性) ...

5.  **识别关系:**
    *   Association: id=`assoc-bs-frame-uuid`, parentId=`pkg-bicycle-uuid`, memberEndIds=[`prop-bs-frame-uuid`, `prop-frame-owner-uuid`]
    *   Association: id=`assoc-bs-rearwheel-uuid`, parentId=`pkg-bicycle-uuid`, memberEndIds=[`prop-bs-rearwheel-uuid`, `prop-wheel-owner-uuid`]
    *   Association: id=`assoc-bs-drivesystem-uuid`, parentId=`pkg-bicycle-uuid`, memberEndIds=[`prop-bs-drivesystem-uuid`, `prop-drivesys-owner-uuid`]
    *   Association: id=`assoc-bs-ecu-uuid`, parentId=`pkg-bicycle-uuid`, memberEndIds=[`prop-bs-ecu-uuid`, `prop-ecu-owner-uuid`]
    *   ... (为所有部件关系创建拥有两个端点的Association) ...

6.  **识别内部行为与连接 (Connectors in BicycleSystem):**
    *   Connector: `conn-drivesys-to-wheel`: id=`conn-drive-wheel-uuid`, parentId=`blk-bicyclesystem-uuid`, kind=`assembly`. End1(part:`prop-bs-drivesystem-uuid`, port:`port-drivesys-powerout-uuid`), End2(part:`prop-bs-rearwheel-uuid`, port:`port-wheel-powerin-uuid`).
    *   ... (其他连接器) ...

7.  **识别图表:**
    *   Diagram: id=`diag-bicycle-bdd-uuid`, name=`Bicycle System BDD`, parentId=`pkg-bicycle-uuid`, diagramType=`BDD`.
    *   Diagram: id=`diag-bicycle-ibd-uuid`, name=`Bicycle System IBD`, parentId=`blk-bicyclesystem-uuid`, diagramType=`IBD`.
```

---

### 修改后的 `prompt2` (JSON 生成)

**核心改动**：
1.  新增一条规则，再次强调`Association`必须有两个端点。
2.  更新了**黄金标准样例 (Golden Standard JSON)**，使其完全符合新的双向关联规则。这个样例现在是100%模型正确的，为AI提供了最精准的模板。

```python
# prompt2 (最终版)
prompt2 = """
## 角色
你是一位精确的数据转换工程师，负责将详细的、扁平化的 SysML 元素思考过程转化为严格符合规范的统一 JSON 格式。你**只输出JSON**，不能输出任何其他内容或注释。

## 核心规则
1.  **严格遵循输入**: 你的唯一信息来源是上方提供的“思考过程”文本。不要添加或猜测任何“思考过程”中没有的元素。
2.  **JSON格式**: 输出必须是单个、完整、无注释的JSON对象，严格符合Python的`json.loads()`规范。
3.  **引用完整性**: 仔细检查所有ID引用 (`parentId`, `typeId`, `associationId`, etc.)，确保它们都指向`elements`列表中存在的另一个元素的`id`。
4.  **关联规则**: 一个 `Association` 元素的 `memberEndIds` 数组 **必须** 包含**两个**属性ID。
5.  **最终检查**: 在输出前，将你的结果与下面提供的黄金标准样例进行结构对比，确保所有字段和嵌套都正确无误。

## 黄金标准样例 (Bicycle System JSON) - 已按新规则修正
```JSON
{
  "model": { "id": "model-bicycle-uuid", "name": "BicycleModel", "diagrams": ["diag-bicycle-bdd-uuid", "diag-bicycle-ibd-uuid"] },
  "elements": [
    { "id": "pkg-bicycle-uuid", "type": "Package", "name": "BicyclePackage", "parentId": "model-bicycle-uuid" },
    { "id": "vt-amperehour-uuid", "type": "ValueType", "name": "AmpereHour", "parentId": "pkg-bicycle-uuid", "baseType": "Real" },
    { "id": "blk-bicyclesystem-uuid", "type": "Block", "name": "BicycleSystem", "parentId": "pkg-bicycle-uuid", "properties": ["prop-bs-frame-uuid", "prop-bs-frontwheel-uuid", "prop-bs-rearwheel-uuid", "prop-bs-drivesystem-uuid", "prop-bs-brakesystem-uuid", "prop-bs-controlhandle-uuid", "prop-bs-battery-uuid", "prop-bs-sensormodule-uuid", "prop-bs-display-uuid", "prop-bs-ecu-uuid"], "connectors": ["conn-drive-wheel-uuid", "conn-sensor-ecu-uuid", "conn-ecu-display-uuid"], "ownedDiagrams": ["diag-bicycle-ibd-uuid"] },
    { "id": "blk-frame-uuid", "type": "Block", "name": "Frame", "parentId": "pkg-bicycle-uuid", "properties": ["prop-frame-material-uuid", "prop-frame-weight-uuid", "prop-frame-owner-uuid"] },
    { "id": "blk-wheel-uuid", "type": "Block", "name": "Wheel", "parentId": "pkg-bicycle-uuid", "properties": ["prop-wheel-diameter-uuid", "prop-wheel-width-uuid", "prop-wheel-owner-uuid"], "ports": ["port-wheel-powerin-uuid"] },
    { "id": "blk-drivesystem-uuid", "type": "Block", "name": "DriveSystem", "parentId": "pkg-bicycle-uuid", "properties": ["prop-drivesys-owner-uuid"], "ports": ["port-drivesys-powerout-uuid"] },
    { "id": "blk-brakesystem-uuid", "type": "Block", "name": "BrakeSystem", "parentId": "pkg-bicycle-uuid", "properties": ["prop-brakesys-owner-uuid"] },
    { "id": "blk-controlhandle-uuid", "type": "Block", "name": "ControlHandle", "parentId": "pkg-bicycle-uuid", "properties": ["prop-controlhandle-owner-uuid"] },
    { "id": "blk-battery-uuid", "type": "Block", "name": "Battery", "parentId": "pkg-bicycle-uuid", "properties": ["prop-battery-capacity-uuid", "prop-battery-owner-uuid"] },
    { "id": "blk-sensormodule-uuid", "type": "Block", "name": "SensorModule", "parentId": "pkg-bicycle-uuid", "properties": ["prop-sensormod-owner-uuid"], "ports": ["port-sensor-dataout-uuid"] },
    { "id": "blk-display-uuid", "type": "Block", "name": "Display", "parentId": "pkg-bicycle-uuid", "properties": ["prop-display-owner-uuid"], "ports": ["port-display-datain-uuid"] },
    { "id": "blk-electroniccontrolunit-uuid", "type": "Block", "name": "ElectronicControlUnit", "parentId": "pkg-bicycle-uuid", "properties": ["prop-ecu-owner-uuid"], "ports": ["port-ecu-sensorin-uuid", "port-ecu-displayout-uuid"] },
    { "id": "prop-bs-frame-uuid", "type": "Property", "name": "frame", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-frame-uuid", "associationId": "assoc-bs-frame-uuid" },
    { "id": "prop-bs-frontwheel-uuid", "type": "Property", "name": "frontWheel", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-wheel-uuid", "associationId": "assoc-bs-frontwheel-uuid" },
    { "id": "prop-bs-rearwheel-uuid", "type": "Property", "name": "rearWheel", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-wheel-uuid", "associationId": "assoc-bs-rearwheel-uuid" },
    { "id": "prop-bs-drivesystem-uuid", "type": "Property", "name": "driveSystem", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-drivesystem-uuid", "associationId": "assoc-bs-drivesystem-uuid" },
    { "id": "prop-bs-brakesystem-uuid", "type": "Property", "name": "brakeSystem", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-brakesystem-uuid", "associationId": "assoc-bs-brakesystem-uuid" },
    { "id": "prop-bs-controlhandle-uuid", "type": "Property", "name": "controlHandle", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-controlhandle-uuid", "associationId": "assoc-bs-controlhandle-uuid" },
    { "id": "prop-bs-battery-uuid", "type": "Property", "name": "battery", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-battery-uuid", "associationId": "assoc-bs-battery-uuid" },
    { "id": "prop-bs-sensormodule-uuid", "type": "Property", "name": "sensorModule", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-sensormodule-uuid", "associationId": "assoc-bs-sensormodule-uuid" },
    { "id": "prop-bs-display-uuid", "type": "Property", "name": "display", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-display-uuid", "associationId": "assoc-bs-display-uuid" },
    { "id": "prop-bs-ecu-uuid", "type": "Property", "name": "ecu", "parentId": "blk-bicyclesystem-uuid", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-electroniccontrolunit-uuid", "associationId": "assoc-bs-ecu-uuid" },
    { "id": "prop-frame-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-frame-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-frame-uuid" },
    { "id": "prop-wheel-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-wheel-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-rearwheel-uuid" },
    { "id": "prop-drivesys-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-drivesystem-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-drivesystem-uuid" },
    { "id": "prop-brakesys-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-brakesystem-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-brakesystem-uuid" },
    { "id": "prop-controlhandle-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-controlhandle-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-controlhandle-uuid" },
    { "id": "prop-battery-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-battery-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-battery-uuid" },
    { "id": "prop-sensormod-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-sensormodule-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-sensormodule-uuid" },
    { "id": "prop-display-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-display-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-display-uuid" },
    { "id": "prop-ecu-owner-uuid", "type": "Property", "name": "_bicycleSystem", "parentId": "blk-electroniccontrolunit-uuid", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-bicyclesystem-uuid", "associationId": "assoc-bs-ecu-uuid" },
    { "id": "prop-frame-material-uuid", "type": "Property", "name": "material", "parentId": "blk-frame-uuid", "propertyKind": "value", "typeId": "String" },
    { "id": "prop-frame-weight-uuid", "type": "Property", "name": "weight", "parentId": "blk-frame-uuid", "propertyKind": "value", "typeId": "Real" },
    { "id": "prop-wheel-diameter-uuid", "type": "Property", "name": "diameter", "parentId": "blk-wheel-uuid", "propertyKind": "value", "typeId": "Real" },
    { "id": "prop-wheel-width-uuid", "type": "Property", "name": "width", "parentId": "blk-wheel-uuid", "propertyKind": "value", "typeId": "Real" },
    { "id": "prop-battery-capacity-uuid", "type": "Property", "name": "capacity", "parentId": "blk-battery-uuid", "propertyKind": "value", "typeId": "vt-amperehour-uuid", "defaultValue": "10" },
    { "id": "port-wheel-powerin-uuid", "type": "ProxyPort", "name": "powerInput", "parentId": "blk-wheel-uuid" },
    { "id": "port-drivesys-powerout-uuid", "type": "ProxyPort", "name": "powerOutput", "parentId": "blk-drivesystem-uuid" },
    { "id": "port-sensor-dataout-uuid", "type": "ProxyPort", "name": "dataOut", "parentId": "blk-sensormodule-uuid" },
    { "id": "port-display-datain-uuid", "type": "ProxyPort", "name": "dataIn", "parentId": "blk-display-uuid" },
    { "id": "port-ecu-sensorin-uuid", "type": "ProxyPort", "name": "sensorInput", "parentId": "blk-electroniccontrolunit-uuid" },
    { "id": "port-ecu-displayout-uuid", "type": "ProxyPort", "name": "displayOutput", "parentId": "blk-electroniccontrolunit-uuid" },
    { "id": "assoc-bs-frame-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-frame-uuid", "prop-frame-owner-uuid"] },
    { "id": "assoc-bs-frontwheel-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-frontwheel-uuid", "prop-wheel-owner-uuid"] },
    { "id": "assoc-bs-rearwheel-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-rearwheel-uuid", "prop-wheel-owner-uuid"] },
    { "id": "assoc-bs-drivesystem-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-drivesystem-uuid", "prop-drivesys-owner-uuid"] },
    { "id": "assoc-bs-brakesystem-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-brakesystem-uuid", "prop-brakesys-owner-uuid"] },
    { "id": "assoc-bs-controlhandle-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-controlhandle-uuid", "prop-controlhandle-owner-uuid"] },
    { "id": "assoc-bs-battery-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-battery-uuid", "prop-battery-owner-uuid"] },
    { "id": "assoc-bs-sensormodule-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-sensormodule-uuid", "prop-sensormod-owner-uuid"] },
    { "id": "assoc-bs-display-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-display-uuid", "prop-display-owner-uuid"] },
    { "id": "assoc-bs-ecu-uuid", "type": "Association", "parentId": "pkg-bicycle-uuid", "memberEndIds": ["prop-bs-ecu-uuid", "prop-ecu-owner-uuid"] },
    { "id": "conn-drive-wheel-uuid", "type": "AssemblyConnector", "name": "conn-drivesys-to-wheel", "parentId": "blk-bicyclesystem-uuid", "kind": "assembly", "end1": { "id": "cend-dw-1-uuid", "partRefId": "prop-bs-drivesystem-uuid", "portRefId": "port-drivesys-powerout-uuid" }, "end2": { "id": "cend-dw-2-uuid", "partRefId": "prop-bs-rearwheel-uuid", "portRefId": "port-wheel-powerin-uuid" } },
    { "id": "conn-sensor-ecu-uuid", "type": "AssemblyConnector", "name": "conn-sensor-to-ecu", "parentId": "blk-bicyclesystem-uuid", "kind": "assembly", "end1": { "id": "cend-se-1-uuid", "partRefId": "prop-bs-sensormodule-uuid", "portRefId": "port-sensor-dataout-uuid" }, "end2": { "id": "cend-se-2-uuid", "partRefId": "prop-bs-ecu-uuid", "portRefId": "port-ecu-sensorin-uuid" } },
    { "id": "conn-ecu-display-uuid", "type": "AssemblyConnector", "name": "conn-ecu-to-display", "parentId": "blk-bicyclesystem-uuid", "kind": "assembly", "end1": { "id": "cend-ed-1-uuid", "partRefId": "prop-bs-ecu-uuid", "portRefId": "port-ecu-displayout-uuid" }, "end2": { "id": "cend-ed-2-uuid", "partRefId": "prop-bs-display-uuid", "portRefId": "port-display-datain-uuid" } },
    { "id": "diag-bicycle-bdd-uuid", "type": "Diagram", "name": "Bicycle System BDD", "parentId": "pkg-bicycle-uuid", "diagramType": "BDD", "contextId": "pkg-bicycle-uuid" },
    { "id": "diag-bicycle-ibd-uuid", "type": "Diagram", "name": "Bicycle System IBD", "parentId": "blk-bicyclesystem-uuid", "diagramType": "IBD", "contextId": "blk-bicyclesystem-uuid" }
  ]
}
```

有了这一套终极版的提示词，AI Agent现在有了一个无比清晰和正确的指令集与模仿目标。它在生成“思考过程”时会被迫创建合法的双向关联，然后在生成JSON时，会根据这个完美的“黄金标准”来构建输出，从而确保最终产出的JSON在结构和内容上都是高质量且符合SysML规范的。