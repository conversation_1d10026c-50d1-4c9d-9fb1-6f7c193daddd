import xml.etree.ElementTree as ET
import uuid
import xml.dom.minidom

# --- Namespace Definitions ---
namespaces = {
    'xmi': 'http://www.omg.org/XMI',
    'uml': 'http://www.omg.org/spec/UML/20090901',
    'sysml': 'http://www.omg.org/spec/SysML/20131001',
    'MD_Customization_for_SysML__additional_stereotypes': 'http://www.magicdraw.com/spec/Customization/190/SysML',
    'diagram': 'http://www.nomagic.com/ns/magicdraw/core/diagram/1.0'
}

# Register namespaces globally
for prefix, uri in namespaces.items():
    reg_prefix = prefix if prefix != 'MD_Customization_for_SysML__additional_stereotypes' else 'MD_Customization_for_SysML__additional_stereotypes'
    ET.register_namespace(reg_prefix, uri)

# --- Helper Functions ---
def create_element(tag, attrs={}, parent=None, text=None):
    """Creates an ElementTree element with namespace handling."""
    qname_tag = tag
    qualified_attrs = {}
    if ':' in tag:
        prefix, local_name = tag.split(':', 1)
        if prefix in namespaces: qname_tag = f"{{{namespaces[prefix]}}}{local_name}"
        else: print(f"W: Unknown ns prefix '{prefix}' in tag '{tag}'")
    for k, v in attrs.items():
        if v is None: continue
        if ':' in k:
            prefix, local_name = k.split(':', 1)
            if prefix in namespaces: qualified_attrs[f"{{{namespaces[prefix]}}}{local_name}"] = str(v)
            else: qualified_attrs[k] = str(v)
        else: qualified_attrs[k] = str(v)
    elem = ET.Element(qname_tag, qualified_attrs)
    if parent is not None: parent.append(elem)
    if text is not None: elem.text = str(text)
    return elem

def add_multiplicity(element_data, parent_xml_element):
    """Adds lowerValue and upperValue sub-elements if multiplicity is defined."""
    multiplicity = element_data.get('multiplicity')
    if multiplicity is None: return
    lower_val_str, upper_val_str = '1', '1'
    if isinstance(multiplicity, str) and multiplicity.startswith('[') and multiplicity.endswith(']'):
        parts = multiplicity[1:-1].split('..')
        if len(parts) == 2: lower_val_str, upper_val_str = parts[0].strip(), parts[1].strip()
        elif len(parts) == 1: lower_val_str = upper_val_str = parts[0].strip()
    elif isinstance(multiplicity, (int, str)): lower_val_str = upper_val_str = str(multiplicity)
    parent_id_str = parent_xml_element.get(f"{{{namespaces['xmi']}}}id", f"elem_{str(uuid.uuid4())[:4]}")
    lower_id = f"{parent_id_str}_lower_{str(uuid.uuid4())[:4]}"; upper_id = f"{parent_id_str}_upper_{str(uuid.uuid4())[:4]}"
    lower_attrs = {'xmi:type': 'uml:LiteralInteger', 'xmi:id': lower_id, 'value': lower_val_str}
    create_element('lowerValue', lower_attrs, parent=parent_xml_element)
    upper_attrs = {'xmi:id': upper_id}
    if upper_val_str == '*': upper_attrs.update({'xmi:type': 'uml:LiteralUnlimitedNatural', 'value': '*'})
    else: upper_attrs.update({'xmi:type': 'uml:LiteralUnlimitedNatural', 'value': upper_val_str if upper_val_str else '1'})
    create_element('upperValue', upper_attrs, parent=parent_xml_element)

def add_diagram_extension(diagram_data, owner_xml_element):
    diagram_id = diagram_data['id']; diagram_name = diagram_data['name']
    diagram_type_sysml = 'SysML Block Definition Diagram'; diagram_type_uml = 'Class Diagram'
    context_id = diagram_data.get('contextId')
    if diagram_data['diagramType'] == 'IBD': diagram_type_sysml, diagram_type_uml = 'SysML Internal Block Diagram', 'Composite Structure Diagram'
    elif diagram_data['diagramType'] == 'BDD': diagram_type_sysml, diagram_type_uml = 'SysML Block Definition Diagram', 'Class Diagram'
    rep_id, contents_id = f"{diagram_id}_rep", f"{diagram_id}_contents"; xmi_id_rep_obj = f"{diagram_id}_xmi_rep"
    owned_diagram_ext_id = f"{diagram_id}_owned_ext"; diag_rep_ext_id = f"{diagram_id}_rep_ext"
    extension = create_element('xmi:Extension', {'extender': 'MagicDraw UML 2021x', 'xmi:id': owned_diagram_ext_id}, parent=owner_xml_element)
    model_extension = create_element('modelExtension', parent=extension)
    owned_diagram_attrs = { 'xmi:type': 'uml:Diagram', 'xmi:id': diagram_id, 'name': diagram_name, 'visibility': diagram_data.get('visibility', 'public')}
    if context_id: owned_diagram_attrs.update({'context': context_id, 'ownerOfDiagram': context_id})
    owned_diagram = create_element('ownedDiagram', owned_diagram_attrs, parent=model_extension)
    inner_extension = create_element('xmi:Extension', {'extender': 'MagicDraw UML 2021x', 'xmi:id': diag_rep_ext_id}, parent=owned_diagram)
    diagram_rep = create_element('diagramRepresentation', parent=inner_extension)
    diagram_obj_attrs = { 'ID': rep_id, 'initialFrameSizeSet': 'true',
        'requiredFeature': 'com.nomagic.magicdraw.plugins.impl.sysml#SysML;UML_Standard_Profile.mdzip;MD_customization_for_SysML.mdzip',
        'type': diagram_type_sysml, 'umlType': diagram_type_uml, 'xmi:id': xmi_id_rep_obj, 'xmi:version': '2.0',
        'xmlns:binary': 'http://www.nomagic.com/ns/cameo/client/binary/1.0', 'xmlns:diagram': namespaces['diagram'],
        'xmlns:xmi': namespaces['xmi'], 'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance'}
    diagram_obj = create_element('diagram:DiagramRepresentationObject', diagram_obj_attrs, parent=diagram_rep)
    create_element('diagram:diagramContents', { 'contentHash': 'placeholder_' + str(uuid.uuid4())[:8],
        'exporterName': 'MagicDraw UML', 'exporterVersion': '2021x', 'xmi:id': contents_id }, parent=diagram_obj)

# --- Primitive Type HREF Mapping ---
primitive_hrefs = {
    'Boolean': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Boolean', 'Real': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real',
    'Integer': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Integer', 'String': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.String',
    'int': 'UML_Standard_Profile.mdzip#eee_1045467100323_917313_65', 'void': 'UML_Standard_Profile.mdzip#eee_1045467100323_249638_60',
    'float': 'UML_Standard_Profile.mdzip#eee_1045467100323_385364_62'}
def get_primitive_href(type_name):
    lookup_name = type_name
    if isinstance(type_name, str):
        if type_name.lower() == 'boolean': lookup_name = 'Boolean'
        elif type_name.lower() == 'integer': lookup_name = 'Integer'
        elif type_name.lower() == 'real': lookup_name = 'Real'
        elif type_name.lower() == 'string': lookup_name = 'String'
    href = primitive_hrefs.get(lookup_name) or primitive_hrefs.get(type_name)
    # if href is None and isinstance(type_name, str) and not type_name.startswith("primitive."): # Avoid warning for non-primitive IDs
    #     print(f"W: Primitive HREF not found for type '{type_name}'")
    return href

# --- Main Generation Function ---
def generate_sysml_xml(json_data):
    """Generates the complete SysML XMI file from the unified JSON data."""
    print("Starting XML Generation...")
    # --- Root Element Setup (Revised) ---
    root_attrs = {
        "xmi:version": "2.5", # Use simple attribute name
        # Add xmlns attributes directly, ET + register_namespace should handle prefixes
       # "xmlns:xmi": namespaces['xmi'],
       # "xmlns:uml": namespaces['uml'],
       # "xmlns:sysml": namespaces['sysml'],
       # "xmlns:MD_Customization_for_SysML__additional_stereotypes": namespaces['MD_Customization_for_SysML__additional_stereotypes'],
    }
    if any(el.get('type') == 'Diagram' for el in json_data.get('elements',[])):
         root_attrs["xmlns:diagram"] = namespaces['diagram']

    root = ET.Element('xmi:XMI', root_attrs) # Use prefixed tag 'xmi:XMI'

    elements_by_id = {elem['id']: elem for elem in json_data['elements']}
    xml_elements_by_id = {}
    nested_connector_ends_to_stereotype = []

    # --- Create uml:Model ---
    model_data = json_data['model']
    print(f"Creating Model: {model_data['name']} ({model_data['id']})")
    model = create_element('uml:Model', {
        'xmi:type': 'uml:Model', 'xmi:id': model_data['id'], 'name': model_data['name']
    }, parent=root)
    xml_elements_by_id[model_data['id']] = model
    profile_app_id = f'{model_data["id"]}_profileapp_{str(uuid.uuid4())[:4]}'
    #profile_app = create_element('profileApplication', {'xmi:type':'uml:ProfileApplication', 'xmi:id': profile_app_id}, parent=model)
    #sysml_profile_href = namespaces['sysml']
    #create_element('appliedProfile', {'href': sysml_profile_href}, parent=profile_app)

    # --- Pass 1: Create Elements EXCEPT Associations ---
    processed_ids = set(); elements_to_process = [e for e in json_data['elements'] if e['type'] != 'Association']
    max_passes = len(elements_to_process) + 5 ; passes = 0
    print("--- Starting Pass 1: Creating non-Association elements ---")
    while elements_to_process and passes < max_passes:
        passes += 1; processed_in_pass = 0; remaining_elements = []
        print(f"Pass 1, Iteration {passes}, Processing {len(elements_to_process)} elements...")
        for elem_data in elements_to_process:
            elem_id = elem_data['id']; parent_id = elem_data['parentId']
            parent_xml = xml_elements_by_id.get(parent_id)
            if parent_xml is None: remaining_elements.append(elem_data); continue

            elem_type = elem_data['type']; xml_elem = None
            element_tag = 'packagedElement'; attrs = {'xmi:id': elem_id}

            try:
                # --- Create Non-Association Elements ---
                if elem_type == "Package":
                    attrs['xmi:type'] = 'uml:Package'; attrs['name'] = elem_data['name']
                    xml_elem = create_element(element_tag, attrs, parent=parent_xml)
                elif elem_type in ["Block", "InterfaceBlock"]:
                    attrs['xmi:type'] = 'uml:Class'; attrs['name'] = elem_data['name']
                    if elem_data.get('isAbstract', False): attrs['isAbstract'] = 'true'
                    xml_elem = create_element(element_tag, attrs, parent=parent_xml)
                elif elem_type == "ValueType":
                    attrs['xmi:type'] = 'uml:DataType'; attrs['name'] = elem_data['name']
                    xml_elem = create_element(element_tag, attrs, parent=parent_xml)
                elif elem_type == "Unit":
                    attrs['xmi:type'] = 'uml:InstanceSpecification'; attrs['name'] = elem_data['name']
                    xml_elem = create_element(element_tag, attrs, parent=parent_xml)
                    classifier = create_element('classifier', parent=xml_elem)
                    classifier.set('href', 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML.Unit')
                elif elem_type == "Signal":
                    attrs['xmi:type'] = 'uml:Signal'; attrs['name'] = elem_data['name']
                    xml_elem = create_element(element_tag, attrs, parent=parent_xml)
                    # Signal properties
                    for prop_id in elem_data.get('properties', []):
                         prop_data = elements_by_id.get(prop_id)
                         if prop_data and prop_data['parentId'] == elem_id:
                              prop_attrs = {'xmi:type':'uml:Property', 'xmi:id':prop_id, 'name':prop_data['name']}
                              prop_type_id = prop_data.get('typeId'); prop_type_elem = None
                              if prop_type_id:
                                   href = get_primitive_href(prop_type_id)
                                   if href: prop_type_elem = create_element('type', {'href': href})
                                   elif prop_type_id in elements_by_id: prop_attrs['type'] = prop_type_id
                                   else: print(f"W: Type ID {prop_type_id} not found for signal property {prop_id}")
                              signal_prop_elem = create_element('ownedAttribute', prop_attrs, parent=xml_elem)
                              if prop_type_elem: signal_prop_elem.append(prop_type_elem)

                elif elem_type == "Property":
                    attrs['xmi:type'] = 'uml:Property'; attrs['name'] = elem_data.get('name')
                    attrs['visibility'] = elem_data.get('visibility', 'public')
                    attrs['aggregation'] = elem_data.get('aggregation', 'none')
                    type_id = elem_data.get('typeId'); assoc_id = elem_data.get('associationId')
                    type_elem = None

                    if type_id:
                        href = get_primitive_href(type_id)
                        if href: type_elem = create_element('type', {'href': href})
                        elif type_id in elements_by_id: attrs['type'] = type_id
                        else: print(f"W: Type ID '{type_id}' not found for Property {elem_id}")

                    # Add association attribute BUT check Association exists in JSON first
                    if elem_data.get('propertyKind') in ['part', 'reference'] and assoc_id:
                        if assoc_id in elements_by_id and elements_by_id[assoc_id]['type'] == 'Association':
                             attrs['association'] = assoc_id
                        else: print(f"W: Association {assoc_id} not found in JSON for Property {elem_id}. Skipping association attribute.")

                    xml_elem = create_element('ownedAttribute', attrs, parent=parent_xml)
                    if type_elem is not None: xml_elem.append(type_elem)
                    add_multiplicity(elem_data, xml_elem)
                    # defaultValue...

                elif elem_type in ["FullPort", "ProxyPort", "FlowPort"]:
                    attrs['xmi:type'] = 'uml:Port'; attrs['name'] = elem_data['name']
                    attrs['visibility'] = elem_data.get('visibility', 'public'); attrs['aggregation'] = 'composite'
                    type_id = elem_data.get('typeId'); xml_elem = create_element('ownedAttribute', attrs, parent=parent_xml)
                    if type_id:
                        href = get_primitive_href(type_id)
                        if href: create_element('type', {'href': href}, parent=xml_elem)
                        elif type_id in elements_by_id: xml_elem.set('type', type_id)
                        else: print(f"W: Type ID '{type_id}' not found for Port {elem_id}")
                    add_multiplicity(elem_data, xml_elem)

                elif elem_type == "Operation":
                    attrs['xmi:type']='uml:Operation'; attrs['name']=elem_data['name']
                    attrs['visibility']=elem_data.get('visibility', 'public')
                    xml_elem = create_element('ownedOperation', attrs, parent=parent_xml)
                    for param_data in elem_data.get('parameters', []):
                        param_attrs = { 'xmi:type': 'uml:Parameter', 'xmi:id': param_data['id'], 'direction': param_data.get('direction', 'in'), 'visibility':'public'}
                        if 'name' in param_data and param_data['name']: param_attrs['name'] = param_data['name']
                        param_elem = create_element('ownedParameter', param_attrs, parent=xml_elem)
                        param_type_id = param_data.get('typeId')
                        if param_type_id:
                            href = get_primitive_href(param_type_id)
                            if href:
                                type_tag = create_element('type', {'href': href}, parent=param_elem)
                                if param_type_id in ['int', 'float', 'void']: # Add MD Extension
                                    param_elem_id_str = param_elem.get(f"{{{namespaces['xmi']}}}id", f"param_{str(uuid.uuid4())[:4]}")
                                    ext_id = f"{param_elem_id_str}_mdext"; ext_attrs = {'extender': 'MagicDraw UML 2021x', 'xmi:id': ext_id}
                                    ext = create_element('xmi:Extension', ext_attrs, parent=type_tag)
                                    ref_path_map = { 'int': 'UML Standard Profile::MagicDraw Profile::datatypes::int', 'float': 'UML Standard Profile::MagicDraw Profile::datatypes::float', 'void': 'UML Standard Profile::MagicDraw Profile::datatypes::void' }
                                    if param_type_id in ref_path_map: create_element('referenceExtension', { 'referentPath': ref_path_map[param_type_id], 'referentType': 'DataType'}, parent=ext)
                            elif param_type_id in elements_by_id: create_element('type', {'xmi:idref': param_type_id}, parent=param_elem)
                            else: print(f"W: Type ID {param_type_id} not found for param {param_data['id']}")

                elif elem_type == "Reception":
                    attrs['xmi:type']='uml:Reception'; attrs['name']=elem_data['name']
                    attrs['visibility']=elem_data.get('visibility', 'public');
                    if 'signalId' in elem_data and elem_data['signalId'] in elements_by_id:
                        attrs['signal']=elem_data['signalId']
                        xml_elem = create_element('ownedReception', attrs, parent=parent_xml)
                    else: print(f"W: Signal {elem_data.get('signalId')} not found for Reception {elem_id}")

                # elif elem_type == "Association": # Skip in Pass 1
                #     pass

                elif elem_type == "Generalization":
                    specific_elem_xml = xml_elements_by_id.get(elem_data['specificId'])
                    if specific_elem_xml:
                        if elem_data['generalId'] in elements_by_id:
                            attrs['xmi:type']='uml:Generalization'; attrs['general']=elem_data['generalId']
                            xml_elem = create_element('generalization', attrs, parent=specific_elem_xml)
                        else: print(f"W: General element {elem_data['generalId']} not found for Gen {elem_id}")
                    else: remaining_elements.append(elem_data); continue

                elif elem_type in ["AssemblyConnector", "BindingConnector"]:
                    attrs['xmi:type']='uml:Connector'; attrs['name']=elem_data.get('name'); attrs['visibility']='public'
                    if 'typeId' in elem_data and elem_data.get('typeId') in elements_by_id: attrs['type'] = elem_data['typeId']
                    xml_elem = create_element('ownedConnector', attrs, parent=parent_xml)
                    valid_ends = True
                    for end_key in ['end1', 'end2']:
                        end_data = elem_data.get(end_key);
                        if not end_data: valid_ends = False; print(f"E: Missing {end_key} data for Connector {elem_id}"); break
                        end_id = end_data.get('id')
                        if not end_id: valid_ends=False; print(f"E: Missing 'id' in {end_key} for Connector {elem_id}"); break
                        end_attrs = {'xmi:type': 'uml:ConnectorEnd', 'xmi:id': end_id}
                        end_elem = create_element('end', end_attrs, parent=xml_elem)
                        xml_elements_by_id[end_id] = end_elem # Store connector end XML

                        part_ref_id = end_data.get('partRefId'); port_ref_id = end_data.get('portRefId'); prop_ref_id = end_data.get('propertyRefId')
                        role_id_to_set = port_ref_id if elem_type == 'AssemblyConnector' else prop_ref_id
                        if role_id_to_set is None: role_id_to_set = port_ref_id if port_ref_id else prop_ref_id

                        if role_id_to_set not in elements_by_id: # Check if role target element exists in JSON
                            print(f"W: Role target element '{role_id_to_set}' not found for conn end {end_key} in {elem_id}")
                            role_id_to_set = None # Prevent setting invalid reference

                        if part_ref_id:
                            if part_ref_id in elements_by_id: # Check part property exists in JSON
                                end_elem.set('partWithPort', part_ref_id)
                                if role_id_to_set:
                                    create_element('role', {'xmi:idref': role_id_to_set}, parent=end_elem)
                                    nested_connector_ends_to_stereotype.append((end_id, part_ref_id))
                                # else: print(f"W: Missing valid role ID for conn end {end_key} via part {part_ref_id} in {elem_id}")
                            else: print(f"W: Part property {part_ref_id} not found for conn end {end_key} in {elem_id}"); valid_ends = False; break
                        else:
                             if role_id_to_set: end_elem.set('role', role_id_to_set)
                             # else: print(f"W: Missing valid role ID for boundary conn end {end_key} in {elem_id}")

                    if not valid_ends:
                        print(f"E: Removing invalid connector {elem_id} due to end errors.")
                        parent_xml.remove(xml_elem); xml_elem = None

                # elif elem_type == "Diagram":
                #     owner_xml = xml_elements_by_id.get(parent_id)
                #     if owner_xml: add_diagram_extension(elem_data, owner_xml)
                #     else: remaining_elements.append(elem_data); continue
                #     xml_elem = None

            except Exception as e:
                 print(f"ERROR processing element {elem_id} (Type: {elem_type}): {e}")
                 import traceback
                 traceback.print_exc() # Print full traceback for debugging
                 xml_elem = None # Mark as failed

            if xml_elem is not None:
                # print(f"Pass {passes}: Created XML for {elem_type} '{elem_data.get('name', elem_id)}'")
                xml_elements_by_id[elem_id] = xml_elem
            processed_ids.add(elem_id); processed_in_pass += 1

        elements_to_process = remaining_elements
        if processed_in_pass == 0 and elements_to_process:
             print(f"Error: Pass 1 stuck. Remaining elements:")
             for rem_elem in elements_to_process: print(f"  - ID: {rem_elem['id']}, Type: {rem_elem['type']}, Needs Parent: {rem_elem['parentId']} (Parent Found: {rem_elem['parentId'] in xml_elements_by_id})")
             break
    if elements_to_process: print(f"Error: {len(elements_to_process)} elements could not be processed after Pass 1.")

    # --- Pass 2: Create BDD Associations and Add MemberEnds ---
    print(f"--- Starting Pass 2: Creating {len([e for e in json_data['elements'] if e['type'] == 'Association'])} BDD Associations ---")
    associations_to_process = [e for e in json_data['elements'] if e['type'] == 'Association']
    for elem_data in associations_to_process:
        elem_id = elem_data['id']; parent_id = elem_data['parentId']
        parent_xml = xml_elements_by_id.get(parent_id)
        if parent_xml is None: print(f"E: Parent {parent_id} not found for Assoc {elem_id}. Skipping."); continue

        attrs = {'xmi:id': elem_id, 'xmi:type': 'uml:Association', 'name': elem_data.get('name')}
        assoc_xml = create_element('packagedElement', attrs, parent=parent_xml)
        xml_elements_by_id[elem_id] = assoc_xml # Store association XML

        member_end_ids = elem_data.get('memberEndIds', [])
        if len(member_end_ids) == 2:
            prop1_xml = xml_elements_by_id.get(member_end_ids[0])
            prop2_xml = xml_elements_by_id.get(member_end_ids[1])
            if prop1_xml is not None and prop2_xml is not None:
                create_element('memberEnd', {'xmi:idref': member_end_ids[0]}, parent=assoc_xml)
                create_element('memberEnd', {'xmi:idref': member_end_ids[1]}, parent=assoc_xml)
                # Update properties with association ID
                prop1_xml.set('association', elem_id)
                prop2_xml.set('association', elem_id)
                # print(f"Pass 2: Created BDD Association {elem_id} and linked Properties.")
            else:
                print(f"W: Could not add member ends for BDD Association {elem_id}. Props XML not found: {[id for id in member_end_ids if id not in xml_elements_by_id]}")
                parent_xml.remove(assoc_xml); del xml_elements_by_id[elem_id]
        else:
             print(f"W: BDD Association {elem_id} does not have exactly two memberEndIds: {member_end_ids}")
             parent_xml.remove(assoc_xml); del xml_elements_by_id[elem_id]

    # --- Pass 3: Add SysML Stereotypes ---
    print("--- Starting Pass 3: Applying Stereotypes ---")
    stereotype_tag_map = { "Block": "sysml:Block", "InterfaceBlock": "sysml:InterfaceBlock", "ValueType": "sysml:ValueType", "FullPort": "sysml:FullPort", "ProxyPort": "sysml:ProxyPort", "FlowPort": "sysml:FlowPort", "BindingConnector": "sysml:BindingConnector", "Unit": "MD_Customization_for_SysML__additional_stereotypes:Unit", "PartProperty": "MD_Customization_for_SysML__additional_stereotypes:PartProperty", "ReferenceProperty": "MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty", "ValueProperty": "MD_Customization_for_SysML__additional_stereotypes:ValueProperty",}
    base_attr_map = { "Block": "base_Class", "InterfaceBlock": "base_Class", "ValueType": "base_DataType", "FullPort": "base_Port", "ProxyPort": "base_Port", "FlowPort": "base_Port", "BindingConnector": "base_Connector", "Unit": "base_InstanceSpecification", "Property": "base_Property"}

    for elem_data in json_data['elements']:
        elem_id = elem_data['id']; elem_type = elem_data['type']
        if elem_id not in xml_elements_by_id and elem_type not in ["Association", "Generalization", "Diagram"]: continue

        stereotype_parent = root
        stereotype_id = f"{elem_id}_application"
        stereotype_attrs = {'xmi:id': stereotype_id}

        stereotype_tag = None; base_attr_name = None
        if elem_type == "Property":
            prop_kind = elem_data.get("propertyKind")
            if prop_kind == 'part': stereotype_tag = stereotype_tag_map.get("PartProperty")
            elif prop_kind == 'reference': stereotype_tag = stereotype_tag_map.get("ReferenceProperty")
            elif prop_kind == 'value': stereotype_tag = stereotype_tag_map.get("ValueProperty")
            if stereotype_tag: base_attr_name = base_attr_map.get("Property")
        elif elem_type in stereotype_tag_map:
            stereotype_tag = stereotype_tag_map[elem_type]
            base_attr_name = base_attr_map.get(elem_type)

        if stereotype_tag and base_attr_name:
            if elem_id in xml_elements_by_id:
                stereotype_attrs[base_attr_name] = elem_id
                if elem_type == "ValueType" and elem_data.get('unitId'): stereotype_attrs['unit'] = elem_data['unitId']
                if elem_type == "ProxyPort" and elem_data.get('isConjugated', False): stereotype_attrs['isConjugated'] = 'true'
                create_element(stereotype_tag, stereotype_attrs, parent=stereotype_parent)
            # else: print(f"W: Base element {elem_id} for stereotype {stereotype_tag} not found.")


    # --- Pass 4: Add NestedConnectorEnd Stereotypes ---
    print(f"--- Starting Pass 4: Applying {len(nested_connector_ends_to_stereotype)} NestedConnectorEnd Stereotypes ---")
    for end_id, part_prop_id in nested_connector_ends_to_stereotype:
        stereotype_id = f"{end_id}_" # Match example ID pattern
        stereotype_attrs = { 'xmi:id': stereotype_id, 'base_ConnectorEnd': end_id, 'propertyPath': part_prop_id }
        connector_end_xml = xml_elements_by_id.get(end_id) # Connector Ends are stored now
        propertyPath_xml = xml_elements_by_id.get(part_prop_id)
        if connector_end_xml is not None and propertyPath_xml is not None:
             create_element('sysml:NestedConnectorEnd', stereotype_attrs, parent=root)
        else: print(f"W: Cannot create NestedConnectorEnd for {end_id}. Base End ({'Found' if connector_end_xml is not None else 'Not Found'}) or Property Path {part_prop_id} ({'Found' if propertyPath_xml is not None else 'Not Found'}).")


    # --- Final XML Output ---
    ET.indent(root, space="    ")
    xml_declaration = "<?xml version='1.0' encoding='UTF-8'?>\n"
    xml_string = ET.tostring(root, encoding='unicode', method='xml', short_empty_elements=False)

    try:
        dom = xml.dom.minidom.parseString(xml_string)
        pretty_xml = dom.toprettyxml(indent="    ")
        final_xml = xml_declaration + '\n'.join([line for line in pretty_xml.splitlines()[1:] if line.strip()])
        return final_xml
    except Exception as e:
        print(f"Warning: xml.dom.minidom pretty-printing failed ({e}). Returning minimally formatted XML.")
        return xml_declaration + xml_string

# --- Python-Compatible JSON Data for Smart Factory (Fixed V3) ---
# Aiming for complete referential integrity based on the complex example

# --- Python-Compatible JSON Data for Smart Thermostat (Simplified V7) ---

# --- Python-Compatible JSON Data for Smart Factory & Thermobox (Fixed V7) ---
# Focus: Add missing definitions (if-hmi-uuid), fix ALL primitive types, ensure all refs valid.
# --- Python-Compatible JSON Data for Smart Thermostat (Simplified V8 - With Port Binding) ---

bdd_ibd_lamp_switch_json =  {
  "model": {
    "id": "model-fan-uuid",
    "name": "FanSystemModel",
    "diagrams": [
      "diag-fan-bdd",
      "diag-fan-ibd"
    ]
  },
  "elements": [
    {
      "id": "pkg-fan-uuid",
      "type": "Package",
      "name": "FanSystemPackage",
      "parentId": "model-fan-uuid"
    },
    {
      "id": "unit-percent-uuid",
      "type": "Unit",
      "name": "%",
      "parentId": "pkg-fan-uuid",
      "symbol": "%"
    },
    {
      "id": "vt-percentage-uuid",
      "type": "ValueType",
      "name": "Percentage",
      "parentId": "pkg-fan-uuid",
      "baseType": "Real",
      "unitId": "unit-percent-uuid"
    },
    {
      "id": "enum-fanspeed-uuid",
      "type": "Enumeration",
      "name": "FanSpeedLevel",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "enum-ircmdtype-uuid",
      "type": "Enumeration",
      "name": "IRCommandType",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "sig-ircommand-uuid",
      "type": "Signal",
      "name": "IRCommand",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "if-statusdisp-uuid",
      "type": "InterfaceBlock",
      "name": "StatusDisplayInterface",
      "parentId": "pkg-fan-uuid",
      "isAbstract": True
    },
    {
      "id": "blk-acpower-uuid",
      "type": "Block",
      "name": "ACPowerBlock",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "blk-motor-uuid",
      "type": "Block",
      "name": "Motor",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-motor-fan"
      ],
      "ports": [
        "port-motor-pwrin",
        "port-motor-ctrlin"
      ]
    },
    {
      "id": "blk-irrecv-uuid",
      "type": "Block",
      "name": "IRReceiver",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-irrecv-fan"
      ],
      "ports": [
        "port-irrecv-cmdout"
      ]
    },
    {
      "id": "blk-fan-uuid",
      "type": "Block",
      "name": "Fan",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-fan-motor",
        "prop-fan-recv",
        "prop-fan-speed",
        "prop-fan-remote"
      ],
      "ports": [
        "port-fan-powerin",
        "port-fan-statusdisp"
      ],
      "operations": [
        "op-fan-setspeed"
      ],
      "receptions": [
        "recp-fan-handlesig"
      ],
      "connectors": [
        "conn-fan-recv-motor",
        "conn-fan-pwr-motor",
        "conn-fan-bind-status"
      ],
      "ownedDiagrams": [
        "diag-fan-bdd",
        "diag-fan-ibd"
      ]
    },
    {
      "id": "blk-remote-uuid",
      "type": "Block",
      "name": "RemoteControl",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-remote-battery",
        "prop-remote-fanlink"
      ],
      "operations": [
        "op-remote-sendcmd"
      ]
    },
    {
      "id": "prop-fan-motor",
      "type": "Property",
      "name": "motor",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-motor-uuid",
      "associationId": "assoc-fan-motor",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-recv",
      "type": "Property",
      "name": "receiver",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-irrecv-uuid",
      "associationId": "assoc-fan-recv",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-speed",
      "type": "Property",
      "name": "currentSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "enum-fanspeed-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-remote",
      "type": "Property",
      "name": "_remote",
      "parentId": "blk-fan-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-remote-uuid",
      "associationId": "assoc-remote-fan"
    },
    {
      "id": "prop-remote-battery",
      "type": "Property",
      "name": "batteryLevel",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "vt-percentage-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-remote-fanlink",
      "type": "Property",
      "name": "pairedFan",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-remote-fan",
      "multiplicity": "[0..1]"
    },
    {
      "id": "prop-motor-fan",
      "type": "Property",
      "name": "_fan_motor",
      "parentId": "blk-motor-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-motor"
    },
    {
      "id": "prop-irrecv-fan",
      "type": "Property",
      "name": "_fan_recv",
      "parentId": "blk-irrecv-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-recv"
    },
    {
      "id": "port-fan-powerin",
      "type": "FullPort",
      "name": "powerIn",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": False
    },
    {
      "id": "port-fan-statusdisp",
      "type": "ProxyPort",
      "name": "statusDisplay",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "if-statusdisp-uuid",
      "isBehavior": True
    },
    {
      "id": "port-motor-pwrin",
      "type": "FullPort",
      "name": "motorPowerIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": False
    },
    {
      "id": "port-motor-ctrlin",
      "type": "ProxyPort",
      "name": "controlIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": True
    },
    {
      "id": "port-irrecv-cmdout",
      "type": "ProxyPort",
      "name": "commandOut",
      "parentId": "blk-irrecv-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": True
    },
    {
      "id": "op-fan-setspeed",
      "type": "Operation",
      "name": "setSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-fss-lvl",
          "name": "level",
          "typeId": "enum-fanspeed-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "op-remote-sendcmd",
      "type": "Operation",
      "name": "sendCommand",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-rsc-cmd",
          "name": "command",
          "typeId": "enum-ircmdtype-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "recp-fan-handlesig",
      "type": "Reception",
      "name": "handleIRCommand",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "signalId": "sig-ircommand-uuid"
    },
    {
      "id": "lit-fs-off",
      "type": "EnumerationLiteral",
      "name": "Off",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-low",
      "type": "EnumerationLiteral",
      "name": "Low",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-med",
      "type": "EnumerationLiteral",
      "name": "Medium",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-high",
      "type": "EnumerationLiteral",
      "name": "High",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "enum-fanspeed-uuid_literals",
      "type": "EnumerationLiteral",
      "name": "Off",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-ircmd-pwt",
      "type": "EnumerationLiteral",
      "name": "PowerToggle",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-ircmd-sup",
      "type": "EnumerationLiteral",
      "name": "SpeedUp",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-ircmd-sdn",
      "type": "EnumerationLiteral",
      "name": "SpeedDown",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "assoc-fan-motor",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-motor",
        "prop-motor-fan"
      ]
    },
    {
      "id": "assoc-fan-recv",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-recv",
        "prop-irrecv-fan"
      ]
    },
    {
      "id": "assoc-remote-fan",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-remote-fanlink",
        "prop-fan-remote"
      ]
    },
    {
      "id": "conn-fan-recv-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "partRefId": "prop-fan-recv",
        "portRefId": "port-irrecv-cmdout"
      },
      "end2": {
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-ctrlin"
      }
    },
    {
      "id": "conn-fan-pwr-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "portRefId": "port-fan-powerin"
      },
      "end2": {
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-pwrin"
      }
    },
    {
      "id": "conn-fan-bind-status",
      "type": "BindingConnector",
      "parentId": "blk-fan-uuid",
      "kind": "binding",
      "end1": {
        "propertyRefId": "prop-fan-speed"
      },
      "end2": {
        "portRefId": "port-fan-statusdisp"
      }
    },
    {
      "id": "diag-fan-bdd",
      "type": "Diagram",
      "name": "Fan BDD",
      "diagramType": "BDD",
      "contextId": "pkg-fan-uuid"
    },
    {
      "id": "diag-fan-ibd",
      "type": "Diagram",
      "name": "Fan IBD",
      "diagramType": "IBD",
      "contextId": "blk-fan-uuid"
    }
  ]
}# Example Usage:
xml_output = generate_sysml_xml(bdd_ibd_lamp_switch_json) # Use the new JSON
print(xml_output)
# with open("thermostat_v8.xmi", "w", encoding="utf-8") as f:
#     f.write(xml_output)