#!/usr/bin/env python3
"""
嵌入式LightRAG API-only测试
仅使用API模型，避免本地模型依赖问题
"""

import asyncio
import os
import shutil
from src.embedded_lightrag import EmbeddedLightRAG, LightRAGConfig

async def test_api_only():
    """测试仅使用API的配置"""
    print("🧪 测试API-only配置...")
    
    try:
        # 1. 清理旧的测试数据
        test_dir = "./test_rag_storage_api_only"
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        # 2. 创建配置（使用默认API-only配置）
        config = LightRAGConfig(
            working_dir=test_dir,
            use_local_embedding=False,  # 强制使用API embedding
            use_local_reranking=False,  # 强制使用API reranking
            use_api_llm=True            # 使用API LLM
        )
        
        print(f"📁 工作目录: {config.working_dir}")
        print(f"🎯 使用API LLM: {config.llm_model_name}")
        print(f"🎯 使用API embedding: {config.embedding_model_name}")
        
        # 3. 检查API密钥
        if not config.api_key:
            print("⚠️  警告: OPENAI_API_KEY环境变量未设置")
            print("请设置: export OPENAI_API_KEY=your-openrouter-key")
            return False
        
        print(f"🔑 使用API密钥: {config.api_key[:10]}...")
        
        # 4. 初始化LightRAG（异步）
        rag = EmbeddedLightRAG(config)
        await rag.initialize()
        
        # 5. 检查初始化状态
        storage_info = rag.get_storage_info()
        print(f"✅ 存储信息: {storage_info}")
        
        # 6. 测试文档插入
        test_doc = """
        用户登录需求：
        1. 用户可以通过邮箱和密码登录系统
        2. 支持记住登录状态
        3. 登录失败时显示错误信息
        4. 支持找回密码功能
        """
        
        print("📄 插入测试文档...")
        doc_id = await rag.insert_document(test_doc)
        print(f"✅ 文档插入成功，ID: {doc_id}")
        
        # 7. 测试查询
        print("🔍 测试查询...")
        await asyncio.sleep(2)  # 等待处理完成
        query = "用户登录需要哪些功能？"
        result = await rag.query(query)
        print(f"🎯 查询结果: {result}")
        
        # 8. 测试批量插入
        print("\n📦 测试批量插入...")
        documents = [
            "需求1：用户注册功能，支持邮箱和手机号注册",
            "需求2：商品管理功能，包括添加、编辑、删除商品",
            "需求3：订单管理功能，支持下单、支付、取消订单",
            "需求4：用户权限管理，区分普通用户和管理员"
        ]
        
        doc_ids = await rag.batch_insert(documents)
        print(f"✅ 批量插入成功，文档IDs: {doc_ids}")
        
        # 9. 测试批量查询
        await asyncio.sleep(2)
        result = await rag.query("系统有哪些核心功能？")
        print(f"🎯 批量查询结果: {result}")
        
        # 10. 清理资源
        await rag.finalize()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 嵌入式LightRAG API-only测试\n")
    
    # 检查环境变量
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  警告: OPENAI_API_KEY环境变量未设置")
        print("请设置: export OPENAI_API_KEY=your-openrouter-key")
        print("然后重新运行测试")
        return False
    
    print(f"🔑 API密钥已设置: {api_key[:10]}...")
    
    # 运行测试
    success = await test_api_only()
    
    if success:
        print("\n🎉 API-only测试通过！")
    else:
        print("\n❌ API-only测试失败")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        exit(1)