#!/usr/bin/env python3
"""
LightRAG安装验证脚本
无需API密钥，仅验证包安装是否成功
"""

import sys
import tempfile
from lightrag import LightRAG

def verify_installation():
    """验证LightRAG安装"""
    try:
        print("🔍 验证LightRAG安装...")
        
        # 测试导入
        import lightrag
        print(f"✅ LightRAG版本: {lightrag.__version__}")
        
        # 测试基本初始化（不连接LLM）
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 临时目录: {temp_dir}")
            
            # 使用最小配置测试初始化
            try:
                rag = LightRAG(
                    working_dir=temp_dir,
                    vector_storage="NanoVectorDBStorage",
                    graph_storage="NetworkXStorage",
                    kv_storage="JsonKVStorage"
                )
                print("✅ LightRAG初始化成功")
                
                # 测试存储组件
                print("✅ 存储组件加载成功:")
                print(f"  - 向量存储: {type(rag.vector_storage).__name__}")
                print(f"  - 图存储: {type(rag.graph_storage).__name__}")
                print(f"  - KV存储: {type(rag.kv_storage).__name__}")
                
                return True
                
            except Exception as e:
                print(f"⚠️ 初始化警告: {e}")
                # 这可能是由于没有LLM配置，但包安装是成功的
                return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    success = verify_installation()
    if success:
        print("\n🎉 LightRAG包安装验证成功！")
        print("下一步：设置OPENAI_API_KEY环境变量后运行完整测试")
    else:
        print("\n❌ LightRAG包安装验证失败")
        sys.exit(1)