# ReAct策略双提示词配置指南

## 1. ReAct策略概述

### 1.1 什么是ReAct策略

ReAct（Reasoning and Acting）是一种将推理和行动相结合的AI智能体策略。它通过"思考-行动-观察"的循环模式，让AI能够：

- **Reasoning（推理）**：分析问题，制定解决方案
- **Acting（行动）**：调用工具执行具体操作
- **Observing（观察）**：分析工具执行结果，决定下一步行动

### 1.2 双提示词架构

在Dify平台中，ReAct策略使用两个独立的提示词：

1. **指令提示词（Instruction Prompt）**：定义智能体的静态特征
2. **查询提示词（Query Prompt）**：定义如何处理动态输入

这种分离设计的优势：
- **职责清晰**：静态角色定义与动态处理逻辑分离
- **易于维护**：可以独立调整角色设定和处理流程
- **复用性强**：指令提示词可以在多个场景中复用
- **灵活性高**：查询提示词可以根据具体任务定制

## 2. 指令提示词设计原则

### 2.1 核心要素

指令提示词应该包含以下核心要素：

```yaml
instruction_prompt: |
  ## 角色定义
  - 明确智能体的身份和专业领域
  - 说明智能体的核心职责
  
  ## 知识库
  - 相关的专业知识和概念
  - 标准、规范和最佳实践
  
  ## 能力清单
  - 具体的技能和能力描述
  - 可以执行的操作类型
  
  ## 工作原则
  - 指导行为的基本原则
  - 质量标准和约束条件
  
  ## 分析框架
  - 结构化的思考方法
  - 评估和决策的标准
```

### 2.2 设计要点

1. **稳定性**：内容应该相对稳定，不频繁变化
2. **完整性**：涵盖智能体需要的所有背景知识
3. **专业性**：体现领域专业知识和经验
4. **原则性**：建立清晰的工作原则和标准

## 3. 查询提示词设计原则

### 3.1 核心要素

查询提示词应该包含以下核心要素：

```yaml
query_prompt: |
  ## 当前任务
  - 明确当前要处理的具体任务
  - 提供必要的上下文信息
  
  ## 分析步骤
  - 详细的处理流程
  - 每一步的具体要求
  
  ## 输出格式
  - 明确的结果格式要求
  - 结构化的输出模板
  
  ## 变量处理
  - 动态变量的使用方法
  - 条件逻辑的处理
```

### 3.2 设计要点

1. **动态性**：能够处理不同的输入和场景
2. **结构化**：提供清晰的处理步骤
3. **可操作**：每一步都有具体的执行指导
4. **标准化**：输出格式统一和规范

## 4. 三个智能体的配置对比

### 4.1 需求分析智能体

**特点**：
- 重点在于理解和提取信息
- 需要强大的语言理解能力
- 输出结构化的分析结果

**指令提示词重点**：
- 需求工程专业知识
- SMART原则等分析框架
- 质量评估标准

**查询提示词重点**：
- 5步分析流程
- JSON格式输出
- 工具调用策略

### 4.2 关系推荐智能体

**特点**：
- 重点在于关系识别和匹配
- 需要深入的语义分析能力
- 输出推荐方案和理由

**指令提示词重点**：
- 5种关系类型的详细说明
- 关系分析的原则和策略
- 避免冲突的规则

**查询提示词重点**：
- 关系分析的5步流程
- 置信度评估方法
- 推荐结果的结构化输出

### 4.3 结构优化智能体

**特点**：
- 重点在于结构分析和优化
- 需要系统性的思维能力
- 输出优化方案和执行计划

**指令提示词重点**：
- 4大优化原则的详细说明
- 多种优化策略的介绍
- 评估维度和标准

**查询提示词重点**：
- 结构分析的5步流程
- 优化方案的设计方法
- 执行计划的制定

## 5. 配置最佳实践

### 5.1 指令提示词最佳实践

1. **使用标准化结构**
   ```yaml
   instruction_prompt: |
     ## 角色定义
     [角色描述]
     
     ## 知识库/专业领域
     [相关知识]
     
     ## 核心能力
     [能力清单]
     
     ## 工作原则
     [指导原则]
     
     ## 分析框架
     [方法论]
   ```

2. **保持内容的权威性**
   - 引用行业标准和最佳实践
   - 使用专业术语和概念
   - 提供清晰的定义和说明

3. **建立清晰的边界**
   - 明确智能体的能力范围
   - 说明不应该做什么
   - 设定质量和安全标准

### 5.2 查询提示词最佳实践

1. **使用结构化流程**
   ```yaml
   query_prompt: |
     ## 当前任务
     [任务描述和上下文]
     
     ## 分析步骤
     ### 第1步：[步骤名称]
     [具体要求]
     
     ### 第2步：[步骤名称]
     [具体要求]
     
     ## 输出格式
     [JSON模板]
   ```

2. **合理使用变量**
   ```yaml
   # 必需变量
   {{query}}              # 用户输入
   {{project_id}}         # 项目上下文
   
   # 条件变量
   {{#variable}}...{{/variable}}    # 条件显示
   {{^variable}}...{{/variable}}    # 条件隐藏
   ```

3. **提供清晰的输出格式**
   - 使用JSON Schema定义结构
   - 提供字段说明和示例
   - 确保格式的一致性

### 5.3 调试和优化技巧

1. **分步测试**
   - 先测试指令提示词的角色设定
   - 再测试查询提示词的处理逻辑
   - 最后测试完整的交互流程

2. **日志分析**
   - 查看智能体的推理过程
   - 分析工具调用的效果
   - 识别改进的机会

3. **迭代优化**
   - 根据实际使用效果调整
   - 收集用户反馈进行改进
   - 定期更新知识库内容

## 6. 常见问题和解决方案

### 6.1 指令提示词问题

**问题1：角色定义不够清晰**
- 解决方案：增加具体的职责描述和能力边界

**问题2：专业知识不够深入**
- 解决方案：补充领域专业知识和最佳实践

**问题3：工作原则过于抽象**
- 解决方案：提供具体的操作指导和评判标准

### 6.2 查询提示词问题

**问题1：处理步骤不够详细**
- 解决方案：细化每个步骤的具体要求

**问题2：输出格式不一致**
- 解决方案：使用标准的JSON Schema模板

**问题3：变量使用不当**
- 解决方案：检查变量名称和条件逻辑

### 6.3 整体配置问题

**问题1：两个提示词逻辑不一致**
- 解决方案：确保查询提示词的步骤符合指令提示词的原则

**问题2：工具调用效果不佳**
- 解决方案：在查询提示词中明确工具使用的时机和方法

**问题3：输出质量不稳定**
- 解决方案：增加更多的约束条件和质量检查

## 7. 总结

ReAct策略的双提示词配置是构建高质量智能体的关键。通过合理分离静态角色定义和动态处理逻辑，可以：

1. **提高智能体的专业性**：通过详细的角色定义和知识库
2. **增强处理的一致性**：通过结构化的处理流程
3. **便于维护和优化**：通过模块化的设计
4. **支持复杂的推理**：通过ReAct的思考-行动循环

正确配置双提示词是实现智能化需求管理的重要基础，需要在实践中不断调整和优化。
