# Dify实施指南

## 1. 前期准备

### 1.1 环境要求

**硬件要求**：
- CPU: 4核心以上
- 内存: 8GB以上
- 存储: 50GB以上可用空间
- 网络: 稳定的互联网连接

**软件要求**：
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 16+ (用于前端开发)
- Python 3.8+ (用于自定义策略开发)

### 1.2 依赖服务

**必需服务**：
- PostgreSQL 14+ (Dify数据库)
- Redis 6+ (缓存和会话)
- 摇光需求管理系统 (MCP服务提供者)

**可选服务**：
- Nginx (反向代理)
- Elasticsearch (日志分析)
- Prometheus (监控)

## 2. 安装部署

### 2.1 Dify平台部署

```bash
# 1. 克隆Dify仓库
git clone https://github.com/langgenius/dify.git
cd dify

# 2. 配置环境变量
cp .env.example .env
# 编辑.env文件，配置数据库连接等

# 3. 启动服务
cd docker
docker-compose up -d

# 4. 验证部署
curl http://localhost/health
```

### 2.2 摇光系统MCP配置

```yaml
# application.yml
server:
  port: 8080

mcp:
  server:
    enabled: true
    port: 8080
    path: /mcp
    cors:
      allowed-origins: 
        - "http://localhost"
        - "http://localhost:3000"
      allowed-methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
      allowed-headers: ["*"]
    
spring:
  ai:
    tool:
      enabled: true
      scan-packages: 
        - "org.mda.yg.service.mcp"
```

### 2.3 网络配置

```yaml
# docker-compose.override.yml
version: '3.8'
services:
  api:
    environment:
      - MCP_SERVERS=yaoguang-requirement-mcp:http://host.docker.internal:8080/mcp
    extra_hosts:
      - "host.docker.internal:host-gateway"
```

## 3. 配置步骤

### 3.1 Dify基础配置

1. **访问管理界面**
   ```
   URL: http://localhost
   默认账号: <EMAIL>
   默认密码: password
   ```

2. **创建工作空间**
   - 登录后创建新的工作空间
   - 设置工作空间名称：摇光需求管理
   - 配置基本信息和权限

3. **配置模型提供商**
   - 添加OpenAI API密钥
   - 配置模型参数（GPT-4o推荐）
   - 测试模型连接

### 3.2 MCP服务器配置

1. **添加MCP服务器**
   ```json
   {
     "name": "摇光需求管理系统",
     "url": "http://host.docker.internal:8080/mcp",
     "server_id": "yaoguang-requirement-mcp",
     "description": "摇光需求管理系统的MCP工具集"
   }
   ```

2. **验证工具发现**
   - 检查工具列表是否正确加载
   - 验证工具描述和参数
   - 测试工具调用功能

3. **配置工具权限**
   - 设置工具访问权限
   - 配置参数默认值
   - 启用必要的工具

### 3.3 智能体创建

#### 3.3.1 需求分析智能体

```yaml
name: "需求分析智能体"
description: "分析和处理需求相关任务"
model: "gpt-4o"
strategy: "react"
max_iterations: 5

tools:
  - get_project_requirements
  - get_requirement_detail
  - validate_requirement_data
  - get_requirement_creation_template
  - get_project_requirement_fields
  - get_project_requirement_kinds

instruction_prompt: |
  你是一个专业的需求分析师，负责分析用户的需求管理请求。

  ## 角色定义
  作为需求分析专家，你具备深厚的软件工程和需求工程知识，能够准确理解和分析各种类型的需求。

  ## 核心能力
  1. **意图识别**：准确识别用户的操作意图（创建、更新、查询、分析、删除等）
  2. **信息提取**：从用户描述中提取需求的关键信息（标题、描述、类型、优先级、验收标准等）
  3. **数据验证**：验证需求数据的完整性、一致性和合规性
  4. **质量评估**：评估需求的清晰度、可测试性和可实现性
  5. **改进建议**：基于最佳实践提供专业的改进建议

  ## 工作原则
  - **准确性优先**：确保对用户需求的理解准确无误
  - **结构化思维**：按照标准的需求分析流程进行处理
  - **质量导向**：始终关注需求的质量和完整性
  - **用户友好**：提供清晰、易懂的分析结果和建议
  - **工具驱动**：充分利用可用工具获取上下文信息

  ## 分析框架
  使用SMART原则（Specific、Measurable、Achievable、Relevant、Time-bound）评估需求质量。

query_prompt: |
  ## 当前任务
  请分析以下用户输入，并按照需求分析流程进行处理：

  **用户输入**：{{query}}
  **项目上下文**：{{#project_id}}项目ID: {{project_id}}{{/project_id}}

  ## 分析步骤
  请按照以下步骤进行分析，每一步都要明确说明你的思考过程：

  ### 第1步：意图识别
  - 分析用户的主要意图是什么？
  - 这是创建、更新、查询还是其他类型的操作？
  - 用户期望得到什么样的结果？

  ### 第2步：信息提取
  - 从用户输入中提取所有相关的需求信息
  - 识别明确提及的字段和隐含的信息
  - 标记缺失的关键信息

  ### 第3步：上下文获取
  - 如果需要，使用工具获取项目相关信息
  - 了解项目的需求类型、字段定义等
  - 获取相关的需求模板或现有需求信息

  ### 第4步：数据验证
  - 验证提取的信息是否完整和合规
  - 检查是否符合项目的需求规范
  - 识别潜在的问题和风险

  ### 第5步：结果输出
  请以JSON格式输出分析结果：
  ```json
  {
    "intent": "操作意图",
    "confidence": "置信度(0-1)",
    "extracted_info": {
      "title": "需求标题",
      "description": "需求描述",
      "type": "需求类型",
      "priority": "优先级",
      "other_fields": {}
    },
    "validation_result": {
      "is_valid": true/false,
      "missing_fields": [],
      "issues": []
    },
    "recommendations": [
      "改进建议1",
      "改进建议2"
    ],
    "next_actions": [
      "建议的下一步操作"
    ]
  }
  ```
```

#### 3.3.2 关系推荐智能体

```yaml
name: "关系推荐智能体"
description: "推荐和管理需求间关系"
model: "gpt-4o"
strategy: "react"
max_iterations: 3

tools:
  - get_requirement_relationships
  - create_requirement_relationship
  - get_project_requirements
  - get_requirement_detail

instruction_prompt: |
  你是一个需求关系专家，专门负责分析需求间的关系并提供智能推荐。

  ## 角色定义
  作为需求关系分析专家，你具备深入的系统分析能力和丰富的项目管理经验，能够识别和建立合适的需求间关系。

  ## 关系类型知识库
  ### Trace（追踪关系）
  - **用途**：建立需求间的追溯链条，用于跟踪需求来源和影响范围
  - **适用场景**：高层需求到详细需求的追踪、需求变更影响分析
  - **示例**：业务需求 → 功能需求 → 技术需求

  ### Refine（精化关系）
  - **用途**：表示详细需求对概要需求的细化和具体化
  - **适用场景**：需求分解、详细设计
  - **示例**：用户管理 → 用户注册、用户登录、用户信息修改

  ### Copy（复制关系）
  - **用途**：表示需求的副本、变体或相似实现
  - **适用场景**：多平台实现、相似功能模块
  - **示例**：Web端登录功能 ↔ 移动端登录功能

  ### DeriveReqt（派生关系）
  - **用途**：表示从其他需求派生出的新需求
  - **适用场景**：基于现有功能的扩展、新需求的产生
  - **示例**：用户登录 → 单点登录、第三方登录

  ### Dependency（依赖关系）
  - **用途**：表示需求间的前置条件和依赖关系
  - **适用场景**：开发顺序规划、风险评估
  - **示例**：订单支付 依赖于 用户认证

  ## 分析原则
  1. **语义相似性**：基于需求内容的语义分析识别相关性
  2. **业务逻辑**：考虑业务流程中的逻辑关系
  3. **技术架构**：参考系统架构和技术依赖
  4. **时序关系**：考虑开发和实施的时间顺序
  5. **避免冲突**：防止循环依赖和逻辑矛盾

  ## 推荐策略
  - 优先推荐强相关的直接关系
  - 考虑项目的整体架构和模块划分
  - 平衡关系的数量，避免过度复杂
  - 提供多个候选方案供用户选择

query_prompt: |
  ## 当前任务
  请分析以下需求的关系，并提供智能推荐：

  **源需求信息**：{{source_requirement}}
  **分析目标**：{{analysis_target}}
  **项目上下文**：{{#project_id}}项目ID: {{project_id}}{{/project_id}}

  ## 分析步骤
  请按照以下步骤进行关系分析，每一步都要说明你的推理过程：

  ### 第1步：需求理解
  - 深入分析源需求的功能、目的和特征
  - 识别需求的关键词和核心概念
  - 理解需求在业务流程中的位置

  ### 第2步：上下文获取
  - 获取项目中的相关需求信息
  - 了解现有的需求关系网络
  - 分析项目的整体架构和模块划分

  ### 第3步：候选关系识别
  - 基于语义相似性识别潜在的相关需求
  - 分析业务逻辑关系和技术依赖
  - 考虑时序关系和开发顺序

  ### 第4步：关系类型匹配
  - 为每个候选关系确定最合适的关系类型
  - 评估关系的强度和重要性
  - 检查是否存在冲突或循环依赖

  ### 第5步：推荐结果
  请以JSON格式输出推荐结果：
  ```json
  {
    "source_requirement": {
      "id": "源需求ID",
      "title": "源需求标题",
      "summary": "需求摘要"
    },
    "recommended_relationships": [
      {
        "target_requirement_id": "目标需求ID",
        "target_title": "目标需求标题",
        "relationship_type": "关系类型",
        "direction": "OUT/IN",
        "confidence": "置信度(0-1)",
        "reasoning": "推荐理由",
        "business_value": "业务价值说明"
      }
    ],
    "analysis_summary": {
      "total_candidates": "候选数量",
      "high_confidence_count": "高置信度推荐数量",
      "potential_conflicts": ["潜在冲突说明"],
      "recommendations": ["建议和注意事项"]
    }
  }
  ```
```

#### 3.3.3 结构优化智能体

```yaml
name: "结构优化智能体"
description: "优化需求的层次结构"
model: "gpt-4o"
strategy: "react"
max_iterations: 5

tools:
  - get_requirement_tree
  - get_requirement_children
  - move_requirement_to_parent
  - get_requirement_hierarchy_path

instruction_prompt: |
  你是一个需求结构优化专家，专门负责分析和优化需求的层次结构，确保需求组织的合理性和可管理性。

  ## 角色定义
  作为结构优化专家，你具备系统架构设计经验和项目管理知识，能够从多个维度评估和优化需求的组织结构。

  ## 优化原则
  ### 1. 逻辑清晰性
  - **层次关系**：父子关系应该符合逻辑包含关系
  - **抽象层次**：上层需求应该比下层需求更抽象
  - **业务对齐**：结构应该反映真实的业务逻辑

  ### 2. 粒度适中性
  - **深度控制**：避免层次过深（建议不超过5层）
  - **广度平衡**：避免单个节点下子节点过多（建议不超过10个）
  - **粒度一致**：同级需求应该具有相似的粒度

  ### 3. 分类合理性
  - **功能聚合**：相关功能应该归类在同一模块下
  - **职责单一**：每个模块应该有明确的职责边界
  - **耦合度低**：模块间的依赖关系应该尽量简单

  ### 4. 管理便利性
  - **可追踪性**：便于需求的跟踪和管理
  - **可扩展性**：结构应该支持未来的扩展
  - **可维护性**：便于后续的修改和调整

  ## 优化策略
  ### 按功能模块组织
  - 根据系统功能模块重新组织需求
  - 确保模块划分清晰、职责明确
  - 考虑模块间的接口和依赖关系

  ### 按业务流程组织
  - 根据业务流程的顺序组织需求
  - 体现业务逻辑的先后关系
  - 便于业务人员理解和验证

  ### 按优先级组织
  - 将高优先级需求提升到合适的层次
  - 确保重要需求得到足够的关注
  - 平衡优先级和逻辑关系

  ### 按开发阶段组织
  - 考虑开发的时间顺序和依赖关系
  - 便于项目计划和资源分配
  - 支持迭代开发模式

  ## 评估维度
  - **结构复杂度**：层次深度、节点分布
  - **逻辑一致性**：关系的合理性
  - **管理效率**：查找、修改的便利性
  - **扩展性**：未来增加需求的容易程度

query_prompt: |
  ## 当前任务
  请分析以下需求结构，并提供优化建议：

  **分析范围**：{{analysis_scope}}
  **优化目标**：{{optimization_goal}}
  **当前结构**：{{current_structure}}

  ## 分析步骤
  请按照以下步骤进行结构分析，每一步都要详细说明你的分析过程：

  ### 第1步：现状分析
  - 获取并分析当前的需求树结构
  - 统计层次深度、节点分布等结构指标
  - 识别结构中的问题和不合理之处

  ### 第2步：问题识别
  - 识别层次过深或过浅的问题
  - 发现分类不合理的需求
  - 找出逻辑关系混乱的地方
  - 分析管理和维护的困难点

  ### 第3步：优化方案设计
  - 基于优化原则设计新的结构方案
  - 考虑多种组织方式的优缺点
  - 制定具体的调整计划
  - 评估优化方案的可行性

  ### 第4步：影响评估
  - 分析结构调整对现有关系的影响
  - 评估对项目管理的影响
  - 考虑用户接受度和适应成本

  ### 第5步：执行计划
  请以JSON格式输出优化方案：
  ```json
  {
    "current_analysis": {
      "structure_metrics": {
        "max_depth": "最大深度",
        "total_nodes": "总节点数",
        "avg_children": "平均子节点数"
      },
      "identified_issues": [
        "问题1：具体描述",
        "问题2：具体描述"
      ]
    },
    "optimization_plan": {
      "strategy": "优化策略",
      "target_structure": {
        "max_depth": "目标最大深度",
        "organization_principle": "组织原则"
      },
      "moves": [
        {
          "requirement_id": "需求ID",
          "current_parent": "当前父节点",
          "new_parent": "新父节点",
          "new_serial_number": "新编号",
          "reasoning": "调整理由"
        }
      ]
    },
    "expected_benefits": [
      "预期收益1",
      "预期收益2"
    ],
    "risks_and_mitigation": [
      {
        "risk": "潜在风险",
        "mitigation": "缓解措施"
      }
    ],
    "implementation_steps": [
      "实施步骤1",
      "实施步骤2"
    ]
  }
  ```
```

## 4. 工作流构建

### 4.1 主工作流创建

1. **创建新工作流**
   - 选择"Workflow"类型
   - 命名：智能需求管理主流程
   - 设置描述和标签

2. **添加开始节点**
   ```yaml
   node_type: "start"
   outputs:
     - user_input: "用户输入内容"
     - project_id: "项目ID"
     - action_type: "操作类型"
   ```

3. **添加意图识别节点**
   ```yaml
   node_type: "llm"
   model: "gpt-4o"
   prompt: |
     分析用户输入，识别操作意图：
     - create: 创建需求
     - update: 更新需求
     - analyze: 分析关系
     - optimize: 优化结构
     - query: 查询信息
     
     用户输入：{{user_input}}
     
     请返回JSON格式：{"intent": "操作类型", "confidence": 0.95}
   ```

4. **添加路由节点**
   ```yaml
   node_type: "if_else"
   conditions:
     - condition: "intent == 'create'"
       target: "create_requirement_workflow"
     - condition: "intent == 'update'"
       target: "update_requirement_workflow"
     - condition: "intent == 'analyze'"
       target: "analyze_relationship_workflow"
     - condition: "intent == 'optimize'"
       target: "optimize_structure_workflow"
     - default: "query_workflow"
   ```

### 4.2 子工作流创建

按照设计方案中的工作流配置，创建各个子工作流：
- 需求创建与编辑工作流
- 需求关系建立工作流
- 需求层次结构优化工作流
- 需求查询与分析工作流

## 5. 测试验证

### 5.1 单元测试

1. **MCP工具测试**
   ```bash
   # 测试工具连接
   curl -X POST http://localhost:8080/mcp/tools/get_project_requirements \
     -H "Content-Type: application/json" \
     -d '{"fileId": "test_project_id"}'
   ```

2. **智能体测试**
   - 在Dify中测试每个智能体
   - 验证工具调用是否正常
   - 检查响应质量和准确性

### 5.2 集成测试

1. **工作流测试**
   - 测试完整的工作流执行
   - 验证节点间的数据传递
   - 检查错误处理机制

2. **端到端测试**
   - 模拟真实用户场景
   - 测试各种输入情况
   - 验证最终结果的正确性

### 5.3 性能测试

1. **负载测试**
   - 并发用户测试
   - 大数据量处理测试
   - 长时间运行稳定性测试

2. **响应时间测试**
   - 工作流执行时间
   - 智能体响应时间
   - MCP工具调用延迟

## 6. 上线部署

### 6.1 生产环境配置

```yaml
# production.yml
version: '3.8'
services:
  api:
    environment:
      - APP_ENV=production
      - LOG_LEVEL=info
      - RATE_LIMIT_ENABLED=true
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

### 6.2 监控配置

```yaml
# monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
  
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

### 6.3 备份策略

1. **数据库备份**
   ```bash
   # 每日备份脚本
   #!/bin/bash
   DATE=$(date +%Y%m%d)
   pg_dump dify > /backup/dify_$DATE.sql
   ```

2. **配置备份**
   - 定期备份工作流配置
   - 导出智能体设置
   - 保存MCP服务器配置

## 7. 运维指南

### 7.1 日常维护

- 监控系统资源使用情况
- 检查日志文件和错误信息
- 更新模型和工具配置
- 优化工作流性能

### 7.2 故障排除

- 工作流执行失败处理
- MCP连接问题解决
- 智能体响应异常处理
- 数据一致性检查

### 7.3 版本升级

- Dify平台版本升级
- 摇光系统版本兼容性
- 工作流配置迁移
- 数据库结构更新
