# Dify工作流架构图

## 1. 整体系统架构

```mermaid
graph TB
    subgraph "用户层"
        UI[Web界面]
        API[API接口]
    end
    
    subgraph "Dify平台层"
        WF[工作流引擎]
        AM[智能体管理器]
        TM[工具管理器]
        
        subgraph "智能体节点"
            A1[需求分析智能体]
            A2[关系推荐智能体]
            A3[结构优化智能体]
        end
        
        subgraph "工作流节点"
            START[开始节点]
            ROUTE[路由节点]
            TOOL[工具节点]
            COND[条件节点]
            END[结束节点]
        end
    end
    
    subgraph "MCP集成层"
        MCP[MCP服务器]
        AUTH[认证管理]
        TOOL_REG[工具注册]
    end
    
    subgraph "摇光系统层"
        REQ_SVC[需求服务]
        REL_SVC[关系服务]
        HIER_SVC[层次服务]
        DB[(MongoDB)]
    end
    
    UI --> WF
    API --> WF
    WF --> AM
    AM --> A1
    AM --> A2
    AM --> A3
    
    A1 --> TM
    A2 --> TM
    A3 --> TM
    TM --> MCP
    
    MCP --> AUTH
    MCP --> TOOL_REG
    MCP --> REQ_SVC
    MCP --> REL_SVC
    MCP --> HIER_SVC
    
    REQ_SVC --> DB
    REL_SVC --> DB
    HIER_SVC --> DB
```

## 2. 主工作流架构

```mermaid
graph TD
    START([开始]) --> INPUT[用户输入解析]
    INPUT --> INTENT[意图识别]
    
    INTENT --> ROUTE{路由决策}
    
    ROUTE -->|创建需求| CREATE_WF[需求创建工作流]
    ROUTE -->|更新需求| UPDATE_WF[需求更新工作流]
    ROUTE -->|分析关系| RELATION_WF[关系分析工作流]
    ROUTE -->|优化结构| OPTIMIZE_WF[结构优化工作流]
    ROUTE -->|查询分析| QUERY_WF[查询分析工作流]
    
    CREATE_WF --> RESULT[结果处理]
    UPDATE_WF --> RESULT
    RELATION_WF --> RESULT
    OPTIMIZE_WF --> RESULT
    QUERY_WF --> RESULT
    
    RESULT --> END([结束])
    
    style START fill:#e1f5fe
    style END fill:#e8f5e8
    style ROUTE fill:#fff3e0
    style RESULT fill:#f3e5f5
```

## 3. 需求创建工作流详细架构

```mermaid
graph TD
    START([开始需求创建]) --> PARSE[解析用户输入]
    PARSE --> ANALYZE[需求分析智能体]
    
    ANALYZE --> EXTRACT[提取需求信息]
    EXTRACT --> VALIDATE[数据验证]
    
    VALIDATE --> VALID{验证通过?}
    VALID -->|是| TEMPLATE[获取创建模板]
    VALID -->|否| ERROR[返回错误信息]
    
    TEMPLATE --> FILL[填充需求数据]
    FILL --> CREATE[创建需求]
    CREATE --> SUCCESS[返回成功结果]
    
    ERROR --> END([结束])
    SUCCESS --> END
    
    subgraph "MCP工具调用"
        T1[get_requirement_creation_template]
        T2[validate_requirement_data]
        T3[create_requirement]
    end
    
    TEMPLATE -.-> T1
    VALIDATE -.-> T2
    CREATE -.-> T3
    
    style START fill:#e1f5fe
    style END fill:#e8f5e8
    style ANALYZE fill:#ffecb3
    style ERROR fill:#ffcdd2
    style SUCCESS fill:#c8e6c9
```

## 4. 关系推荐工作流架构

```mermaid
graph TD
    START([开始关系分析]) --> GET_SOURCE[获取源需求]
    GET_SOURCE --> GET_CONTEXT[获取项目上下文]
    
    GET_CONTEXT --> PARALLEL{并行处理}
    PARALLEL --> ANALYZE[关系推荐智能体]
    PARALLEL --> EXISTING[获取现有关系]
    
    ANALYZE --> RECOMMEND[生成推荐关系]
    EXISTING --> FILTER[过滤重复关系]
    
    RECOMMEND --> MERGE[合并推荐结果]
    FILTER --> MERGE
    
    MERGE --> CONFIRM[用户确认]
    CONFIRM --> APPROVED{用户批准?}
    
    APPROVED -->|是| CREATE[批量创建关系]
    APPROVED -->|否| MODIFY[修改推荐]
    
    MODIFY --> CONFIRM
    CREATE --> SUCCESS[返回成功结果]
    SUCCESS --> END([结束])
    
    subgraph "MCP工具调用"
        T1[get_requirement_detail]
        T2[get_project_requirements]
        T3[get_requirement_relationships]
        T4[create_requirement_relationship]
    end
    
    GET_SOURCE -.-> T1
    GET_CONTEXT -.-> T2
    EXISTING -.-> T3
    CREATE -.-> T4
    
    style START fill:#e1f5fe
    style END fill:#e8f5e8
    style ANALYZE fill:#ffecb3
    style CONFIRM fill:#fff3e0
    style SUCCESS fill:#c8e6c9
```

## 5. 结构优化工作流架构

```mermaid
graph TD
    START([开始结构优化]) --> GET_TREE[获取需求树]
    GET_TREE --> ANALYZE[结构优化智能体]
    
    ANALYZE --> EVALUATE[评估当前结构]
    EVALUATE --> IDENTIFY[识别优化点]
    
    IDENTIFY --> PLAN[生成优化计划]
    PLAN --> PREVIEW[预览优化效果]
    
    PREVIEW --> CONFIRM[用户确认]
    CONFIRM --> APPROVED{用户批准?}
    
    APPROVED -->|是| EXECUTE[执行优化]
    APPROVED -->|否| ADJUST[调整计划]
    
    ADJUST --> PREVIEW
    EXECUTE --> BATCH[批量移动需求]
    BATCH --> VERIFY[验证结果]
    
    VERIFY --> SUCCESS[返回优化结果]
    SUCCESS --> END([结束])
    
    subgraph "MCP工具调用"
        T1[get_requirement_tree]
        T2[get_requirement_children]
        T3[move_requirement_to_parent]
        T4[get_requirement_hierarchy_path]
    end
    
    GET_TREE -.-> T1
    EVALUATE -.-> T2
    EXECUTE -.-> T3
    VERIFY -.-> T4
    
    style START fill:#e1f5fe
    style END fill:#e8f5e8
    style ANALYZE fill:#ffecb3
    style CONFIRM fill:#fff3e0
    style SUCCESS fill:#c8e6c9
```

## 6. 智能体交互架构

```mermaid
graph LR
    subgraph "需求分析智能体"
        A1_INPUT[用户输入]
        A1_PARSE[内容解析]
        A1_EXTRACT[信息提取]
        A1_VALIDATE[数据验证]
        A1_OUTPUT[分析结果]
        
        A1_INPUT --> A1_PARSE
        A1_PARSE --> A1_EXTRACT
        A1_EXTRACT --> A1_VALIDATE
        A1_VALIDATE --> A1_OUTPUT
    end
    
    subgraph "关系推荐智能体"
        A2_INPUT[需求数据]
        A2_CONTEXT[上下文分析]
        A2_MATCH[关系匹配]
        A2_RANK[推荐排序]
        A2_OUTPUT[推荐结果]
        
        A2_INPUT --> A2_CONTEXT
        A2_CONTEXT --> A2_MATCH
        A2_MATCH --> A2_RANK
        A2_RANK --> A2_OUTPUT
    end
    
    subgraph "结构优化智能体"
        A3_INPUT[树结构]
        A3_ANALYZE[结构分析]
        A3_OPTIMIZE[优化算法]
        A3_PLAN[执行计划]
        A3_OUTPUT[优化方案]
        
        A3_INPUT --> A3_ANALYZE
        A3_ANALYZE --> A3_OPTIMIZE
        A3_OPTIMIZE --> A3_PLAN
        A3_PLAN --> A3_OUTPUT
    end
    
    A1_OUTPUT -.-> A2_INPUT
    A1_OUTPUT -.-> A3_INPUT
    A2_OUTPUT -.-> A3_INPUT
    
    style A1_INPUT fill:#e3f2fd
    style A2_INPUT fill:#e8f5e8
    style A3_INPUT fill:#fff3e0
```

## 7. 数据流架构

```mermaid
graph TD
    subgraph "输入层"
        USER_INPUT[用户输入]
        API_INPUT[API调用]
    end
    
    subgraph "处理层"
        WORKFLOW[工作流引擎]
        AGENT[智能体处理]
        TOOL[工具调用]
    end
    
    subgraph "数据层"
        MCP_LAYER[MCP协议层]
        SERVICE_LAYER[服务层]
        DATA_LAYER[数据存储层]
    end
    
    subgraph "输出层"
        RESULT[处理结果]
        NOTIFICATION[通知推送]
        LOG[日志记录]
    end
    
    USER_INPUT --> WORKFLOW
    API_INPUT --> WORKFLOW
    
    WORKFLOW --> AGENT
    AGENT --> TOOL
    TOOL --> MCP_LAYER
    
    MCP_LAYER --> SERVICE_LAYER
    SERVICE_LAYER --> DATA_LAYER
    
    DATA_LAYER --> SERVICE_LAYER
    SERVICE_LAYER --> MCP_LAYER
    MCP_LAYER --> TOOL
    
    TOOL --> RESULT
    TOOL --> NOTIFICATION
    TOOL --> LOG
    
    style USER_INPUT fill:#e1f5fe
    style WORKFLOW fill:#fff3e0
    style AGENT fill:#ffecb3
    style DATA_LAYER fill:#f3e5f5
    style RESULT fill:#e8f5e8
```
