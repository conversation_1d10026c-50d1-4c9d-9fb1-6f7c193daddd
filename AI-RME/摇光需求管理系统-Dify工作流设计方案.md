# 摇光需求管理系统 - Dify工作流设计方案

## 1. 项目概述

### 1.1 背景
摇光需求管理系统已实现完整的MCP工具集，包括需求CRUD操作、关系管理和层次结构管理。本方案旨在基于Dify平台构建智能化需求管理工作流，通过AI智能体提升需求管理的自动化和智能化水平。

### 1.2 目标
- 构建基于Dify的智能需求管理工作流
- 集成现有MCP工具，实现无缝对接
- 提供智能化的需求分析、关系推荐和结构优化
- 简化用户操作，提升工作效率

## 2. Dify平台架构分析

### 2.1 核心组件
- **Workflow（工作流）**：定义业务流程和逻辑
- **Agent Node（智能体节点）**：在工作流中嵌入自主推理能力
- **MCP Integration（MCP集成）**：原生支持MCP服务器和工具
- **Agent Strategy（智能体策略）**：可插拔的推理算法模块

### 2.2 MCP集成机制
- 通过HTTP传输协议连接MCP服务器
- 自动发现和注册MCP工具
- 支持OAuth认证和工具权限管理
- 提供工具参数配置和自定义描述

## 3. 系统架构设计

### 3.1 整体架构

```mermaid
graph TB
    A[用户界面] --> B[Dify工作流引擎]
    B --> C[需求分析智能体]
    B --> D[关系推荐智能体]
    B --> E[结构优化智能体]
    
    C --> F[MCP工具集]
    D --> F
    E --> F
    
    F --> G[摇光需求管理系统]
    G --> H[MongoDB数据库]
    
    subgraph "MCP工具集"
        F1[需求CRUD工具]
        F2[关系管理工具]
        F3[层次结构工具]
        F4[查询分析工具]
    end
```

### 3.2 核心工作流设计

#### 3.2.1 主工作流：智能需求管理流程

```yaml
workflow_name: "智能需求管理主流程"
description: "集成多个智能体的需求管理工作流"
nodes:
  - start_node:
      type: "start"
      outputs: ["user_input"]

  - intent_analysis:
      type: "agent_node"
      agent_strategy: "function_calling"
      description: "分析用户意图和需求类型，主动获取项目上下文"
      inputs: ["user_input"]
      outputs: ["intent", "entities", "confidence", "project_context"]

  - route_decision:
      type: "if_else"
      condition: "intent"
      branches:
        - create_requirement: "intent == 'create'"
        - update_requirement: "intent == 'update'"
        - analyze_relationship: "intent == 'analyze'"
        - optimize_structure: "intent == 'optimize'"
        - query_information: "intent == 'query'"

  - end_node:
      type: "end"
      inputs: ["result"]
```

##### 意图识别智能体详细配置

**智能体基本信息**：
```yaml
name: "意图识别智能体"
description: "识别用户的需求管理操作意图，提取关键实体信息"
model: "gpt-4o"
strategy: "function_calling"
max_iterations: 2
temperature: 0.1
```

**策略选择说明**：
选择"function_calling"策略而非"react"策略的原因：
1. **精确性要求**：意图识别需要准确的分类结果，function_calling提供更精确的结构化输出
2. **效率优先**：意图识别是工作流的入口节点，需要快速响应，function_calling通常比react更高效
3. **工具依赖少**：意图识别主要依赖语言理解，较少需要外部工具调用
4. **输出标准化**：function_calling更容易确保输出格式的一致性

**工具配置**：
```yaml
tools:
  - get_current_project           # 获取当前项目信息，必须首先调用
  - get_project_requirement_kinds # 获取项目需求类型，辅助意图理解
  - get_project_requirement_fields# 获取项目字段信息，识别字段相关操作
  - get_project_requirements      # 获取项目需求列表，用于实体验证
```

**工具调用说明**：
- **get_current_project**：每次分析开始时必须调用，获取当前项目的基本信息
- **get_project_requirement_kinds**：当用户提到特定需求类型时调用，帮助理解上下文
- **get_project_requirement_fields**：当用户提到字段操作时调用，识别字段相关意图
- **get_project_requirements**：当需要验证需求ID或标题时调用，确保实体存在性
- 工具调用策略：先获取项目上下文，再根据用户输入按需调用其他工具

**双提示词配置**：

*指令提示词（Instruction Prompt）*：
```yaml
instruction_prompt: |
  你是一个专业的需求管理意图识别专家，负责准确识别用户在需求管理系统中的操作意图。

  ## 角色定义
  作为意图识别专家，你具备深入的需求管理知识和自然语言处理能力，能够从用户的自然语言输入中准确识别操作意图和提取关键信息。

  ## 意图分类体系
  ### 主要意图类型
  1. **create（创建）**
     - 创建新需求、需求类型、字段等
     - 关键词：创建、新建、添加、建立、制作
     - 示例："创建一个用户登录需求"

  2. **update（更新）**
     - 修改现有需求的内容、状态、属性等
     - 关键词：修改、更新、编辑、调整、变更
     - 示例："修改需求的优先级为高"

  3. **query（查询）**
     - 查找、搜索、获取需求信息
     - 关键词：查询、查找、搜索、获取、显示、列出
     - 示例："查询所有高优先级需求"

  4. **analyze（分析）**
     - 分析需求关系、依赖、影响等
     - 关键词：分析、关系、依赖、影响、追踪
     - 示例："分析需求间的依赖关系"

  5. **optimize（优化）**
     - 优化需求结构、层次、组织等
     - 关键词：优化、重组、调整结构、整理
     - 示例："优化需求的层次结构"

  6. **delete（删除）**
     - 删除需求、关系等
     - 关键词：删除、移除、清除
     - 示例："删除过期的需求"

  ## 实体识别规则
  ### 需求相关实体
  - **需求ID**：REQ开头的标识符或明确的需求引用
  - **需求标题**：引号内容或明确的需求名称
  - **需求类型**：功能性需求、非功能性需求等
  - **优先级**：高、中、低、紧急等
  - **状态**：待开发、开发中、已完成等

  ### 操作相关实体
  - **目标对象**：需求、关系、结构、字段等
  - **操作范围**：项目、模块、表格等
  - **条件限制**：时间范围、状态条件等

  ## 识别原则
  1. **上下文理解**：结合项目背景理解用户意图
  2. **歧义消解**：当意图不明确时，选择最可能的意图
  3. **实体优先**：优先识别明确的实体信息
  4. **置信度评估**：根据关键词匹配度和上下文一致性评估置信度
```

*查询提示词（Query Prompt）*：
```yaml
query_prompt: |
  ## 当前任务
  请分析以下用户输入，识别操作意图并提取关键实体信息：

  **用户输入**：{{user_input}}

  ## 分析步骤

  ### 第1步：获取项目上下文
  - 首先调用get_current_project获取当前项目信息
  - 了解项目的基本信息（项目名称、ID、描述等）
  - 为后续分析提供必要的上下文基础

  ### 第2步：关键词识别
  - 识别用户输入中的动作词汇
  - 匹配意图分类体系中的关键词
  - 注意同义词和近义词的识别

  ### 第3步：意图分类
  - 根据关键词和语境确定主要意图
  - 考虑复合意图的情况（如"查询并分析"）
  - 评估意图识别的置信度

  ### 第4步：实体提取
  - 提取需求相关的实体信息
  - 识别操作的目标对象和范围
  - 提取约束条件和参数

  ### 第5步：上下文验证和补充
  - 根据需要调用其他工具获取详细项目信息
  - 验证提取的实体是否在项目中存在
  - 补充缺失的上下文信息

  ## 输出格式
  请严格按照以下JSON格式输出结果：

  ```json
  {
    "intent": "主要意图类型",
    "confidence": 0.95,
    "project_context": {
      "project_id": "项目ID",
      "project_name": "项目名称",
      "file_id": "文件ID",
      "table_id": "表格ID"
    },
    "entities": {
      "requirement_ids": ["需求ID列表"],
      "requirement_titles": ["需求标题列表"],
      "requirement_types": ["需求类型列表"],
      "priorities": ["优先级列表"],
      "statuses": ["状态列表"],
      "target_objects": ["目标对象列表"],
      "operation_scope": "操作范围",
      "conditions": ["条件限制列表"],
      "parameters": {
        "key": "value"
      }
    },
    "secondary_intents": ["次要意图列表"],
    "ambiguity_flags": ["歧义标记"],
    "context_requirements": ["需要的上下文信息"],
    "reasoning": "意图识别的推理过程"
  }
  ```

  ## 输出要求
  1. **intent字段**：必须是以下值之一：create, update, query, analyze, optimize, delete
  2. **confidence字段**：0-1之间的数值，表示识别置信度
  3. **project_context字段**：必须包含从get_current_project获取的项目信息
  4. **entities字段**：提取的所有相关实体，空值用空数组[]表示
  5. **reasoning字段**：简要说明识别依据和推理过程，包括工具调用结果

  ## 特殊情况处理
  - 如果get_current_project调用失败，在reasoning中说明并继续分析
  - 如果意图不明确，选择最可能的意图并在ambiguity_flags中标记
  - 如果需要更多上下文，在context_requirements中说明
  - 如果是复合操作，在secondary_intents中列出次要意图
```

**输出格式规范**：

*JSON Schema定义*：
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["intent", "confidence", "entities"],
  "properties": {
    "intent": {
      "type": "string",
      "enum": ["create", "update", "query", "analyze", "optimize", "delete"],
      "description": "主要操作意图"
    },
    "confidence": {
      "type": "number",
      "minimum": 0,
      "maximum": 1,
      "description": "识别置信度"
    },
    "project_context": {
      "type": "object",
      "required": ["project_id"],
      "properties": {
        "project_id": {"type": "string", "description": "项目ID"},
        "project_name": {"type": "string", "description": "项目名称"},
        "file_id": {"type": "string", "description": "文件ID"},
        "table_id": {"type": "string", "description": "表格ID"}
      },
      "description": "项目上下文信息"
    },
    "entities": {
      "type": "object",
      "properties": {
        "requirement_ids": {
          "type": "array",
          "items": {"type": "string"},
          "description": "需求ID列表"
        },
        "requirement_titles": {
          "type": "array",
          "items": {"type": "string"},
          "description": "需求标题列表"
        },
        "requirement_types": {
          "type": "array",
          "items": {"type": "string"},
          "description": "需求类型列表"
        },
        "priorities": {
          "type": "array",
          "items": {"type": "string", "enum": ["高", "中", "低", "紧急"]},
          "description": "优先级列表"
        },
        "target_objects": {
          "type": "array",
          "items": {"type": "string"},
          "description": "操作目标对象"
        },
        "operation_scope": {
          "type": "string",
          "description": "操作范围"
        }
      }
    },
    "secondary_intents": {
      "type": "array",
      "items": {"type": "string"},
      "description": "次要意图列表"
    },
    "reasoning": {
      "type": "string",
      "description": "推理过程说明"
    }
  }
}
```

*示例输出*：
```json
{
  "intent": "create",
  "confidence": 0.92,
  "project_context": {
    "project_id": "proj_12345",
    "project_name": "电商系统需求管理",
    "file_id": "file_67890",
    "table_id": "table_abcde"
  },
  "entities": {
    "requirement_ids": [],
    "requirement_titles": ["用户登录功能"],
    "requirement_types": ["功能性需求"],
    "priorities": ["高"],
    "statuses": [],
    "target_objects": ["需求"],
    "operation_scope": "用户管理模块",
    "conditions": [],
    "parameters": {
      "module": "用户管理",
      "feature": "登录"
    }
  },
  "secondary_intents": [],
  "ambiguity_flags": [],
  "context_requirements": [],
  "reasoning": "首先调用get_current_project获取项目信息，然后分析用户输入。用户明确提到'创建'和'用户登录功能'，意图清晰为创建需求，置信度较高"
}
```

**与其他智能体的协作机制**：

*数据流转说明*：
```mermaid
graph LR
    A[用户输入] --> B[意图识别智能体]
    B --> C{路由决策}
    C -->|create| D[需求分析智能体]
    C -->|analyze| E[关系推荐智能体]
    C -->|optimize| F[结构优化智能体]
    C -->|update/query/delete| G[其他处理节点]

    B --> H[意图识别结果]
    H --> I[entities实体信息]
    H --> J[confidence置信度]
    H --> K[context_requirements上下文需求]

    I --> D
    I --> E
    I --> F
```

*协作接口定义*：
1. **向需求分析智能体传递**：
   - 项目上下文信息（project_context）
   - 提取的需求标题、类型、优先级等实体信息
   - 用户的原始输入作为详细描述
   - 识别的操作范围和约束条件

2. **向关系推荐智能体传递**：
   - 项目上下文信息（project_context）
   - 识别的需求ID和标题
   - 分析类型（依赖分析、影响分析等）
   - 相关的实体约束条件

3. **向结构优化智能体传递**：
   - 项目上下文信息（project_context）
   - 优化范围（项目、模块、表格）
   - 优化目标（按功能、按优先级等）
   - 相关的结构约束条件

*路由决策逻辑*：
```yaml
route_decision_rules:
  - condition: "intent == 'create' AND entities.target_objects contains '需求'"
    target: "requirement_creation_workflow"
    data_mapping:
      project_context: "project_context"
      requirement_info: "entities"
      user_description: "user_input"

  - condition: "intent == 'analyze' AND entities.target_objects contains '关系'"
    target: "relationship_analysis_workflow"
    data_mapping:
      project_context: "project_context"
      source_requirements: "entities.requirement_ids"
      analysis_type: "entities.parameters.analysis_type"

  - condition: "intent == 'optimize' AND entities.target_objects contains '结构'"
    target: "structure_optimization_workflow"
    data_mapping:
      project_context: "project_context"
      optimization_scope: "entities.operation_scope"
      optimization_goal: "entities.parameters.goal"

  - condition: "confidence < 0.7"
    target: "clarification_workflow"
    data_mapping:
      project_context: "project_context"
      ambiguity_info: "ambiguity_flags"
      context_needs: "context_requirements"
```

*质量保证机制*：
1. **置信度阈值**：当confidence < 0.7时，触发澄清流程
2. **实体验证**：通过MCP工具验证提取的实体是否存在
3. **上下文补充**：根据context_requirements主动获取缺失信息
4. **错误恢复**：当路由失败时，回退到通用查询处理流程

## 4. 智能体设计

### 4.1 需求分析智能体

**功能定位**：理解用户意图，分析需求内容，提取关键信息

**配置参数**：
```yaml
agent_name: "需求分析智能体"
strategy: "react"
model: "gpt-4o"
max_iterations: 5
tools:
  - get_project_requirements
  - get_requirement_detail
  - validate_requirement_data
  - get_requirement_creation_template

prompt_template: |
  你是一个专业的需求分析师，负责分析用户的需求管理请求。
  
  你的任务：
  1. 理解用户的意图（创建、更新、查询、分析等）
  2. 提取需求的关键信息（标题、描述、类型、优先级等）
  3. 验证需求数据的完整性和合规性
  4. 提供改进建议
  
  当前项目ID：{project_id}
  用户输入：{user_input}
  
  请分析用户需求并提供详细的分析结果。
```

### 4.2 关系推荐智能体

**功能定位**：基于需求内容推荐合适的需求间关系

**配置参数**：
```yaml
agent_name: "关系推荐智能体"
strategy: "function_calling"
model: "gpt-4o"
max_iterations: 3
tools:
  - get_requirement_relationships
  - create_requirement_relationship
  - get_project_requirements

prompt_template: |
  你是一个需求关系专家，负责分析需求间的关系并提供推荐。
  
  关系类型说明：
  - Trace（追踪）：需求间的追溯关系
  - Refine（精化）：需求的细化关系
  - Copy（复制）：需求的复制关系
  - DeriveReqt（派生）：需求的派生关系
  - Depency（依赖）：需求的依赖关系
  
  当前需求：{requirement_data}
  项目上下文：{project_context}
  
  请分析并推荐合适的需求关系。
```

### 4.3 结构优化智能体

**功能定位**：分析并建议最佳的需求层次结构

**配置参数**：
```yaml
agent_name: "结构优化智能体"
strategy: "react"
model: "gpt-4o"
max_iterations: 5
tools:
  - get_requirement_tree
  - get_requirement_children
  - move_requirement_to_parent
  - get_requirement_hierarchy_path

prompt_template: |
  你是一个需求结构优化专家，负责分析和优化需求的层次结构。

  优化原则：
  1. 逻辑清晰：父子关系应该符合逻辑层次
  2. 粒度合适：避免层次过深或过浅
  3. 分类合理：同级需求应该属于同一抽象层次
  4. 易于管理：结构应便于项目管理和追踪

  当前需求树：{requirement_tree}
  优化目标：{optimization_goal}

  请分析当前结构并提供优化建议。
```

## 5. 具体工作流实现

### 5.1 需求创建与编辑工作流

```yaml
workflow_name: "需求创建与编辑流程"
description: "智能化的需求创建和编辑工作流"

nodes:
  - start:
      type: "start"
      outputs: ["user_input", "project_context", "requirement_info"]

  - requirement_analysis:
      type: "agent_node"
      agent: "需求分析智能体"
      inputs: ["user_input", "project_context", "requirement_info"]
      outputs: ["analyzed_requirement", "validation_result"]

  - template_generation:
      type: "tool"
      tool: "get_requirement_creation_template"
      inputs:
        fileId: "{{project_context.file_id}}"
        kindId: "{{analyzed_requirement.kind_id}}"
        tableId: "{{project_context.table_id}}"
      outputs: ["template"]

  - data_validation:
      type: "tool"
      tool: "validate_requirement_data"
      inputs:
        fileId: "{{project_context.file_id}}"
        kindId: "{{analyzed_requirement.kind_id}}"
        requirementData: "{{analyzed_requirement.data}}"
      outputs: ["validation_result"]

  - create_requirement:
      type: "if_else"
      condition: "validation_result.valid == true"
      true_branch:
        - type: "tool"
          tool: "create_requirement"
          inputs:
            fileId: "{{project_context.file_id}}"
            requirementData: "{{analyzed_requirement.data}}"
      false_branch:
        - type: "template"
          template: "需求数据验证失败：{{validation_result.errors}}"

  - end:
      type: "end"
      inputs: ["result"]
```

### 5.2 需求关系建立工作流

```yaml
workflow_name: "需求关系建立流程"
description: "智能推荐和建立需求间关系"

nodes:
  - start:
      type: "start"
      outputs: ["project_context", "source_requirements", "analysis_type"]

  - context_gathering:
      type: "parallel"
      branches:
        - get_source:
            type: "tool"
            tool: "get_requirement_detail"
            inputs:
              requirementId: "{{source_requirements[0]}}"
        - get_project_context:
            type: "tool"
            tool: "get_project_requirements"
            inputs:
              fileId: "{{project_context.file_id}}"

  - relationship_recommendation:
      type: "agent_node"
      agent: "关系推荐智能体"
      inputs: ["source_requirement", "project_requirements", "analysis_type", "project_context"]
      outputs: ["recommended_relationships"]

  - user_confirmation:
      type: "human_feedback"
      prompt: "推荐的关系：{{recommended_relationships}}，是否确认建立？"
      outputs: ["confirmed_relationships"]

  - create_relationships:
      type: "iteration"
      items: "{{confirmed_relationships}}"
      node:
        type: "tool"
        tool: "create_requirement_relationship"
        inputs:
          fileId: "{{project_context.file_id}}"
          sourceId: "{{item.source_id}}"
          targetIds: "{{item.target_ids}}"
          relationshipType: "{{item.type}}"
          direction: "{{item.direction}}"

  - end:
      type: "end"
      inputs: ["created_relationships"]
```

### 5.3 需求层次结构优化工作流

```yaml
workflow_name: "需求层次结构优化流程"
description: "分析和优化需求的层次结构"

nodes:
  - start:
      type: "start"
      outputs: ["table_id", "optimization_goal"]

  - structure_analysis:
      type: "agent_node"
      agent: "结构优化智能体"
      inputs: ["table_id", "optimization_goal"]
      outputs: ["optimization_plan"]

  - execute_optimization:
      type: "iteration"
      items: "{{optimization_plan.moves}}"
      node:
        type: "tool"
        tool: "move_requirement_to_parent"
        inputs:
          requirementId: "{{item.requirement_id}}"
          newParentId: "{{item.new_parent_id}}"
          newSerialNumber: "{{item.new_serial_number}}"

  - validation:
      type: "tool"
      tool: "get_requirement_tree"
      inputs:
        tableId: "{{table_id}}"
        rootParentId: "-1"
      outputs: ["optimized_tree"]

  - end:
      type: "end"
      inputs: ["optimized_tree"]
```

## 6. MCP工具映射配置

### 6.1 MCP服务器配置

```json
{
  "server_name": "摇光需求管理MCP服务器",
  "server_url": "http://localhost:8080/mcp",
  "server_id": "yaoguang-requirement-mcp",
  "description": "摇光需求管理系统的MCP工具集",
  "authorization": {
    "type": "bearer_token",
    "token": "${YAOGUANG_API_TOKEN}"
  }
}
```

### 6.2 工具参数配置

#### 6.2.1 需求CRUD工具配置

```yaml
tools:
  create_requirement:
    description: "创建新的需求"
    parameters:
      fileId:
        type: "auto"
        description: "项目文件ID"
      requirementData:
        type: "auto"
        description: "需求数据对象"

  update_requirement:
    description: "更新现有需求"
    parameters:
      requirementData:
        type: "auto"
        description: "包含需求ID和更新数据的对象"

  get_requirement_detail:
    description: "获取需求详细信息"
    parameters:
      requirementId:
        type: "auto"
        description: "需求ID"

  delete_requirement:
    description: "删除需求"
    parameters:
      requirementId:
        type: "auto"
        description: "需求ID"
```

#### 6.2.2 关系管理工具配置

```yaml
tools:
  create_requirement_relationship:
    description: "创建需求间关系"
    parameters:
      fileId:
        type: "auto"
        description: "项目文件ID"
      sourceId:
        type: "auto"
        description: "源需求ID"
      targetIds:
        type: "auto"
        description: "目标需求ID列表"
      relationshipType:
        type: "auto"
        description: "关系类型"
      direction:
        type: "auto"
        description: "关系方向"

  get_requirement_relationships:
    description: "获取需求关系"
    parameters:
      fileId:
        type: "auto"
        description: "项目文件ID"
      requirementId:
        type: "auto"
        description: "需求ID"
      direction:
        type: "fixed"
        value: "BOTH"
        description: "关系方向，默认双向"
```

#### 6.2.3 层次结构工具配置

```yaml
tools:
  get_requirement_children:
    description: "获取需求子节点"
    parameters:
      tableId:
        type: "auto"
        description: "表格ID"
      parentId:
        type: "auto"
        description: "父需求ID"

  move_requirement_to_parent:
    description: "移动需求到新父节点"
    parameters:
      requirementId:
        type: "auto"
        description: "要移动的需求ID"
      newParentId:
        type: "auto"
        description: "新的父需求ID"
      newSerialNumber:
        type: "auto"
        description: "新的编号（可选）"

  get_requirement_tree:
    description: "获取需求树结构"
    parameters:
      tableId:
        type: "auto"
        description: "表格ID"
      rootParentId:
        type: "fixed"
        value: "-1"
        description: "根父节点ID，默认获取所有根节点"
```

## 7. 用户交互流程

### 7.1 需求创建流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as Dify工作流
    participant A as 需求分析智能体
    participant M as MCP工具
    participant Y as 摇光系统

    U->>D: 输入需求描述
    D->>A: 分析需求内容
    A->>M: 获取需求模板
    M->>Y: 查询需求类型和字段
    Y-->>M: 返回模板数据
    M-->>A: 返回模板
    A->>A: 分析和验证需求
    A-->>D: 返回分析结果
    D->>M: 创建需求
    M->>Y: 执行创建操作
    Y-->>M: 返回创建结果
    M-->>D: 返回结果
    D-->>U: 显示创建成功
```

### 7.2 关系推荐流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as Dify工作流
    participant R as 关系推荐智能体
    participant M as MCP工具
    participant Y as 摇光系统

    U->>D: 选择源需求
    D->>M: 获取需求详情
    M->>Y: 查询需求信息
    Y-->>M: 返回需求数据
    M-->>D: 返回需求详情
    D->>M: 获取项目所有需求
    M->>Y: 查询项目需求
    Y-->>M: 返回需求列表
    M-->>D: 返回项目上下文
    D->>R: 分析推荐关系
    R->>R: 基于内容分析关系
    R-->>D: 返回推荐结果
    D-->>U: 展示推荐关系
    U->>D: 确认建立关系
    D->>M: 创建需求关系
    M->>Y: 执行关系创建
    Y-->>M: 返回创建结果
    M-->>D: 返回结果
    D-->>U: 显示关系建立成功
```

## 8. 部署和配置指南

### 8.1 环境准备

1. **Dify平台部署**
   ```bash
   # 使用Docker部署Dify
   git clone https://github.com/langgenius/dify.git
   cd dify/docker
   docker-compose up -d
   ```

2. **摇光系统MCP服务器配置**
   ```yaml
   # application.yml
   mcp:
     server:
       enabled: true
       port: 8080
       path: /mcp
       cors:
         allowed-origins: "*"
         allowed-methods: ["GET", "POST", "PUT", "DELETE"]
   ```

### 8.2 Dify配置步骤

1. **添加MCP服务器**
   - 登录Dify管理界面
   - 导航到 Tools → MCP
   - 点击 "Add MCP Server (HTTP)"
   - 配置服务器信息：
     - Server URL: `http://localhost:8080/mcp`
     - Name: `摇光需求管理系统`
     - Server Identifier: `yaoguang-requirement-mcp`

2. **创建智能体**
   - 在Dify中创建三个智能体：
     - 需求分析智能体
     - 关系推荐智能体
     - 结构优化智能体
   - 为每个智能体配置相应的MCP工具

3. **构建工作流**
   - 创建主工作流和子工作流
   - 配置节点连接和数据流
   - 设置条件分支和循环逻辑

## 9. 最佳实践建议

### 9.1 工作流设计原则

1. **模块化设计**
   - 将复杂流程拆分为独立的子工作流
   - 每个工作流专注于单一职责
   - 便于维护和复用

2. **错误处理**
   - 在关键节点添加错误处理逻辑
   - 提供友好的错误提示信息
   - 支持流程回滚和重试机制

3. **性能优化**
   - 使用并行节点处理独立任务
   - 合理设置智能体的迭代次数限制
   - 缓存常用数据减少重复查询

### 9.2 智能体配置建议

1. **Prompt工程**
   - 使用清晰、具体的指令
   - 提供充足的上下文信息
   - 包含示例和约束条件

2. **工具选择**
   - 为每个智能体选择最相关的工具
   - 避免工具冗余和功能重叠
   - 定期更新工具配置

3. **参数调优**
   - 根据实际使用情况调整模型参数
   - 平衡响应速度和准确性
   - 监控token使用量和成本

### 9.3 运维监控

1. **日志记录**
   - 记录工作流执行日志
   - 监控智能体推理过程
   - 跟踪MCP工具调用情况

2. **性能监控**
   - 监控工作流执行时间
   - 跟踪API调用频率和响应时间
   - 设置告警阈值

3. **用户反馈**
   - 收集用户使用反馈
   - 分析常见问题和改进点
   - 持续优化工作流设计

## 10. 扩展和定制

### 10.1 自定义智能体策略

开发者可以基于Dify的Agent Strategy框架创建自定义推理策略：

```python
# custom_strategy.py
class RequirementAnalysisStrategy:
    def __init__(self, config):
        self.config = config

    def execute(self, query, tools, context):
        # 自定义推理逻辑
        # 1. 需求内容分析
        # 2. 关键信息提取
        # 3. 验证和建议
        pass
```

### 10.2 工作流模板

提供预定义的工作流模板供快速部署：

- 基础需求管理模板
- 敏捷开发需求模板
- 大型项目需求模板
- 需求评审流程模板

### 10.3 集成扩展

支持与其他系统的集成：

- JIRA集成：同步需求到JIRA
- Confluence集成：生成需求文档
- Git集成：关联代码提交
- 测试工具集成：生成测试用例

## 11. 总结

本设计方案基于Dify平台的强大功能，结合摇光需求管理系统的MCP工具集，构建了一个完整的智能化需求管理工作流。通过三个专门的智能体（需求分析、关系推荐、结构优化），系统能够：

1. **智能化需求处理**：自动分析用户输入，提取关键信息，验证数据完整性
2. **智能关系推荐**：基于需求内容和项目上下文，推荐合适的需求间关系
3. **结构自动优化**：分析需求层次结构，提供优化建议并自动执行调整
4. **无缝工具集成**：通过MCP协议与摇光系统深度集成，实现数据的双向流动

该方案具有良好的可扩展性和可维护性，支持根据实际需求进行定制和扩展，为需求管理工作提供了强大的AI辅助能力。

---

**文档版本**：v1.0
**创建日期**：2025年1月18日
**最后更新**：2025年1月18日
**作者**：摇光需求管理系统开发团队
