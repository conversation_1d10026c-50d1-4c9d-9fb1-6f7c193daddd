# 项目上下文获取策略说明

## 1. 设计变更概述

### 1.1 变更背景
在原始设计中，项目ID等上下文信息是通过工作流的start节点直接传递给智能体的。但这种设计存在以下问题：

1. **依赖外部输入**：需要用户或系统明确提供项目ID
2. **缺乏灵活性**：无法适应动态的项目切换场景
3. **上下文不完整**：仅有项目ID，缺少其他重要的项目信息

### 1.2 新设计方案
改为让智能体主动调用`get_current_project`工具获取项目上下文，具有以下优势：

1. **自主获取**：智能体主动获取当前项目信息
2. **信息完整**：获取项目的完整上下文信息
3. **动态适应**：能够适应项目切换和多项目场景
4. **错误处理**：可以处理项目信息获取失败的情况

## 2. 技术实现方案

### 2.1 意图识别智能体的工具配置

```yaml
tools:
  - get_current_project           # 必须首先调用，获取项目基础信息
  - get_project_requirement_kinds # 按需调用，获取需求类型信息
  - get_project_requirement_fields# 按需调用，获取字段信息
  - get_project_requirements      # 按需调用，用于实体验证
```

**调用顺序**：
1. **第一步**：必须调用`get_current_project`获取基础项目信息
2. **第二步**：根据用户输入的内容，按需调用其他工具
3. **第三步**：将获取的信息整合到输出结果中

### 2.2 输出格式调整

**新增project_context字段**：
```json
{
  "intent": "操作意图",
  "confidence": 0.95,
  "project_context": {
    "project_id": "项目ID",
    "project_name": "项目名称", 
    "file_id": "文件ID",
    "table_id": "表格ID",
    "description": "项目描述",
    "created_time": "创建时间"
  },
  "entities": { ... },
  "reasoning": "推理过程，包括工具调用结果"
}
```

**字段说明**：
- `project_id`: 项目的唯一标识符
- `project_name`: 项目的显示名称
- `file_id`: 项目关联的文件ID
- `table_id`: 项目关联的表格ID
- 其他字段根据`get_current_project`的返回结果动态填充

### 2.3 工作流数据传递调整

**原设计**：
```yaml
start_node:
  outputs: ["user_input", "project_id", "action_type"]

intent_analysis:
  inputs: ["user_input", "project_id"]
```

**新设计**：
```yaml
start_node:
  outputs: ["user_input"]

intent_analysis:
  inputs: ["user_input"]
  outputs: ["intent", "entities", "confidence", "project_context"]
```

## 3. 各智能体的适配方案

### 3.1 需求分析智能体

**接收数据**：
- `user_input`: 用户原始输入
- `project_context`: 从意图识别智能体获取的项目上下文
- `requirement_info`: 从意图识别智能体提取的需求实体信息

**工具调用调整**：
```yaml
# 原来需要传入project_id参数
get_requirement_creation_template:
  inputs:
    fileId: "{{project_id}}"  # 原设计

# 现在从project_context获取
get_requirement_creation_template:
  inputs:
    fileId: "{{project_context.file_id}}"  # 新设计
```

### 3.2 关系推荐智能体

**接收数据**：
- `project_context`: 项目上下文信息
- `source_requirements`: 源需求ID列表
- `analysis_type`: 分析类型

**工具调用调整**：
```yaml
get_project_requirements:
  inputs:
    fileId: "{{project_context.file_id}}"

create_requirement_relationship:
  inputs:
    fileId: "{{project_context.file_id}}"
    sourceId: "{{source_requirement_id}}"
    targetIds: "{{target_ids}}"
```

### 3.3 结构优化智能体

**接收数据**：
- `project_context`: 项目上下文信息
- `optimization_scope`: 优化范围
- `optimization_goal`: 优化目标

**工具调用调整**：
```yaml
get_requirement_tree:
  inputs:
    tableId: "{{project_context.table_id}}"
    rootParentId: "-1"

move_requirement_to_parent:
  inputs:
    requirementId: "{{requirement_id}}"
    newParentId: "{{new_parent_id}}"
```

## 4. 错误处理策略

### 4.1 get_current_project调用失败

**处理策略**：
1. **记录错误**：在reasoning字段中说明调用失败的原因
2. **继续分析**：基于用户输入进行基本的意图识别
3. **标记缺失**：在context_requirements中标记需要项目信息
4. **降级处理**：使用默认值或请求用户提供项目信息

**示例输出**：
```json
{
  "intent": "create",
  "confidence": 0.7,
  "project_context": null,
  "entities": { ... },
  "context_requirements": ["需要项目上下文信息"],
  "reasoning": "get_current_project调用失败，基于用户输入进行基本意图识别"
}
```

### 4.2 项目信息不完整

**处理策略**：
1. **使用可用信息**：使用成功获取的部分信息
2. **补充调用**：尝试调用其他工具补充信息
3. **标记缺失**：明确标记缺失的信息类型

### 4.3 多项目场景

**处理策略**：
1. **当前项目优先**：默认使用当前激活的项目
2. **用户指定**：如果用户明确指定项目，尝试切换上下文
3. **澄清确认**：当项目不明确时，请求用户澄清

## 5. 性能优化考虑

### 5.1 缓存策略

**项目信息缓存**：
- 在会话级别缓存项目基础信息
- 避免重复调用`get_current_project`
- 设置合理的缓存过期时间

**实现方式**：
```yaml
# 在工作流级别设置变量缓存
workflow_variables:
  project_context_cache:
    type: "object"
    ttl: 3600  # 1小时过期
```

### 5.2 调用优化

**按需调用**：
- 只有在真正需要时才调用额外的项目信息工具
- 根据用户输入的复杂度决定调用深度
- 避免不必要的工具链调用

**并行调用**：
- 当需要多个项目信息时，使用并行节点同时调用
- 减少总的响应时间

## 6. 测试验证方案

### 6.1 单元测试

**测试用例**：
1. `get_current_project`成功调用
2. `get_current_project`调用失败
3. 项目信息不完整的情况
4. 多项目切换场景

### 6.2 集成测试

**测试场景**：
1. 完整的需求创建流程
2. 关系分析和推荐流程
3. 结构优化流程
4. 错误恢复和降级处理

### 6.3 性能测试

**测试指标**：
- 项目信息获取的响应时间
- 缓存命中率和效果
- 并发场景下的性能表现

## 7. 迁移指南

### 7.1 现有配置迁移

**步骤**：
1. 更新智能体的工具配置，添加`get_current_project`
2. 修改提示词，增加项目信息获取的步骤
3. 调整输出格式，添加`project_context`字段
4. 更新工作流节点的数据映射关系

### 7.2 向后兼容

**兼容策略**：
- 保留对传统project_id参数的支持
- 提供配置开关，允许选择获取策略
- 逐步迁移，避免影响现有功能

## 8. 总结

这个设计变更将项目上下文的获取从被动接收改为主动获取，具有以下优势：

1. **增强自主性**：智能体能够主动获取所需的上下文信息
2. **提高灵活性**：适应动态的项目环境和多项目场景
3. **完善信息**：获取更完整的项目上下文信息
4. **改善体验**：减少用户需要提供的输入信息

这种设计更符合智能体的自主性原则，能够提供更好的用户体验和更强的系统适应性。

---

**文档版本**：v1.0  
**创建日期**：2025年1月18日  
**更新日期**：2025年1月18日
