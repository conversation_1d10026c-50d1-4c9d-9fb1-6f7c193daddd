# 意图识别智能体配置说明

## 1. 概述

意图识别智能体是摇光需求管理系统Dify工作流的入口节点，负责准确识别用户的操作意图并提取关键实体信息，为后续的智能体协作和工作流路由提供基础数据。

## 2. 设计理念

### 2.1 核心职责
- **意图分类**：将用户的自然语言输入分类为标准的操作意图
- **实体提取**：从用户输入中提取需求管理相关的关键信息
- **置信度评估**：评估意图识别的准确性，支持质量控制
- **路由支持**：为工作流的路由决策提供结构化数据

### 2.2 设计原则
- **准确性优先**：确保意图识别的准确性，避免错误路由
- **标准化输出**：提供统一的JSON格式输出，便于后续处理
- **上下文感知**：结合项目上下文提高识别准确性
- **错误容忍**：对模糊或不完整的输入提供合理的处理

## 3. 技术选型说明

### 3.1 策略选择：Function Calling vs ReAct

**选择Function Calling的原因**：

1. **精确性需求**
   - 意图识别需要准确的分类结果
   - Function Calling提供更精确的结构化输出
   - 减少输出格式的变异性

2. **效率考虑**
   - 作为工作流入口节点，需要快速响应
   - Function Calling通常比ReAct策略更高效
   - 减少不必要的推理循环

3. **工具依赖度**
   - 意图识别主要依赖语言理解能力
   - 较少需要复杂的外部工具调用
   - Function Calling的简单工具调用机制已足够

4. **输出一致性**
   - 需要严格的JSON格式输出
   - Function Calling更容易确保格式一致性
   - 便于后续节点的数据解析

### 3.2 模型配置

```yaml
model: "gpt-4o"
temperature: 0.1
max_iterations: 2
```

**参数说明**：
- **model**: 选择GPT-4o确保理解能力和准确性
- **temperature**: 设置为0.1保证输出的一致性和可预测性
- **max_iterations**: 限制为2次迭代，平衡准确性和效率

## 4. 意图分类体系

### 4.1 主要意图类型

| 意图类型 | 英文标识 | 描述 | 关键词示例 |
|---------|---------|------|-----------|
| 创建 | create | 创建新的需求、关系等 | 创建、新建、添加、建立 |
| 更新 | update | 修改现有内容 | 修改、更新、编辑、调整 |
| 查询 | query | 查找和获取信息 | 查询、查找、搜索、获取 |
| 分析 | analyze | 分析关系、依赖等 | 分析、关系、依赖、影响 |
| 优化 | optimize | 优化结构和组织 | 优化、重组、整理 |
| 删除 | delete | 删除内容 | 删除、移除、清除 |

### 4.2 实体识别规则

**需求相关实体**：
- 需求ID：REQ开头或明确的需求标识
- 需求标题：引号内容或明确的需求名称
- 需求类型：功能性、非功能性等分类
- 优先级：高、中、低、紧急等级别
- 状态：待开发、开发中、已完成等

**操作相关实体**：
- 目标对象：需求、关系、结构、字段等
- 操作范围：项目、模块、表格等
- 条件限制：时间、状态、类型等约束

## 5. 输出格式设计

### 5.1 核心字段说明

```json
{
  "intent": "主要意图类型",
  "confidence": "识别置信度(0-1)",
  "entities": {
    "requirement_ids": ["需求ID列表"],
    "requirement_titles": ["需求标题列表"],
    "target_objects": ["目标对象列表"],
    "operation_scope": "操作范围"
  },
  "reasoning": "推理过程说明"
}
```

**字段设计考虑**：
- **intent**: 使用英文标识符，便于程序处理
- **confidence**: 数值类型，支持阈值判断
- **entities**: 结构化实体信息，支持多值
- **reasoning**: 可解释性支持，便于调试

### 5.2 质量控制机制

1. **置信度阈值**
   - 高置信度(>0.8)：直接路由
   - 中置信度(0.5-0.8)：添加验证步骤
   - 低置信度(<0.5)：触发澄清流程

2. **实体验证**
   - 通过MCP工具验证需求ID是否存在
   - 检查需求类型是否在项目中定义
   - 验证操作范围的有效性

3. **错误处理**
   - 格式错误：重新生成输出
   - 意图不明：标记歧义并请求澄清
   - 实体缺失：补充上下文信息

## 6. 工具集成策略

### 6.1 工具选择

```yaml
tools:
  - get_project_requirement_kinds
  - get_project_requirement_fields
```

**工具使用策略**：
- **按需调用**：仅在需要上下文理解时调用
- **缓存优化**：缓存项目基础信息，减少重复调用
- **错误容忍**：工具调用失败时不影响基本意图识别

### 6.2 调用时机

1. **get_project_requirement_kinds**
   - 用户提到特定需求类型时
   - 需要验证需求类型有效性时
   - 创建需求时需要类型信息时

2. **get_project_requirement_fields**
   - 用户提到字段操作时
   - 需要理解字段相关术语时
   - 验证字段名称有效性时

## 7. 与其他智能体的协作

### 7.1 数据传递接口

**向需求分析智能体**：
```json
{
  "user_input": "原始用户输入",
  "extracted_entities": "提取的实体信息",
  "operation_context": "操作上下文"
}
```

**向关系推荐智能体**：
```json
{
  "source_requirements": "源需求信息",
  "analysis_type": "分析类型",
  "target_scope": "分析范围"
}
```

**向结构优化智能体**：
```json
{
  "optimization_scope": "优化范围",
  "optimization_goal": "优化目标",
  "current_context": "当前上下文"
}
```

### 7.2 路由决策支持

```yaml
routing_logic:
  - high_confidence_direct_routing: "confidence >= 0.8"
  - medium_confidence_with_validation: "0.5 <= confidence < 0.8"
  - low_confidence_clarification: "confidence < 0.5"
  - fallback_to_general_query: "routing_failure"
```

## 8. 性能优化建议

### 8.1 响应时间优化
- 限制工具调用次数和复杂度
- 使用缓存减少重复查询
- 优化提示词长度和复杂度

### 8.2 准确性优化
- 定期更新意图分类体系
- 收集错误案例进行提示词优化
- 建立意图识别的评估指标

### 8.3 可维护性优化
- 模块化提示词设计
- 标准化输出格式
- 完善的日志和监控机制

## 9. 测试和验证

### 9.1 测试用例设计
- 覆盖所有意图类型的典型输入
- 包含歧义和边界情况
- 测试不同复杂度的实体提取

### 9.2 评估指标
- 意图识别准确率
- 实体提取完整性
- 置信度校准质量
- 响应时间性能

### 9.3 持续改进
- 收集用户反馈
- 分析错误模式
- 定期更新和优化配置

---

**文档版本**：v1.0  
**创建日期**：2025年1月18日  
**适用版本**：Dify v1.6.0+
