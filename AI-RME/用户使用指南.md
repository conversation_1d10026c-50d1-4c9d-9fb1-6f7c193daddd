# 摇光需求管理系统 - Dify用户使用指南

## 1. 快速入门

### 1.1 访问系统

1. **登录Dify平台**
   - 访问地址：http://your-dify-domain.com
   - 使用管理员提供的账号密码登录
   - 选择"摇光需求管理"工作空间

2. **界面介绍**
   - **应用列表**：显示所有可用的需求管理应用
   - **工作流**：查看和管理工作流
   - **智能体**：配置和测试智能体
   - **工具**：管理MCP工具连接

### 1.2 基本概念

- **工作流**：自动化的需求处理流程
- **智能体**：具有AI推理能力的处理节点
- **MCP工具**：与摇光系统交互的接口
- **会话**：与AI系统的交互记录

## 2. 主要功能使用

### 2.1 智能需求创建

#### 2.1.1 启动需求创建流程

1. **选择应用**
   - 在应用列表中选择"智能需求管理"
   - 点击"开始对话"按钮

2. **输入需求描述**
   ```
   示例输入：
   "我需要创建一个用户登录功能的需求，包括用户名密码验证、记住登录状态、忘记密码等功能"
   ```

3. **AI分析处理**
   - 系统自动分析需求内容
   - 提取关键信息和字段
   - 验证数据完整性

4. **确认创建**
   - 查看AI生成的需求信息
   - 确认或修改相关字段
   - 点击确认创建

#### 2.1.2 高级创建选项

**指定需求类型**：
```
"创建一个功能性需求：用户登录模块，包含以下子功能..."
```

**设置父子关系**：
```
"在用户管理模块下创建登录验证需求"
```

**指定优先级**：
```
"创建高优先级需求：系统安全认证功能"
```

### 2.2 智能关系推荐

#### 2.2.1 获取关系推荐

1. **选择源需求**
   ```
   "分析需求ID为REQ001的关系，推荐相关需求"
   ```

2. **查看推荐结果**
   - 系统分析需求内容
   - 基于语义相似性推荐关系
   - 显示推荐的关系类型和目标需求

3. **确认建立关系**
   - 查看推荐的关系列表
   - 选择需要建立的关系
   - 确认创建关系

#### 2.2.2 关系类型说明

- **追踪关系（Trace）**：用于需求追溯
- **精化关系（Refine）**：详细需求对概要需求的细化
- **依赖关系（Dependency）**：需求间的前置条件
- **派生关系（Derive）**：从其他需求派生的新需求
- **复制关系（Copy）**：需求的副本或变体

### 2.3 结构智能优化

#### 2.3.1 启动结构优化

1. **选择优化范围**
   ```
   "优化用户管理模块的需求结构"
   ```

2. **设置优化目标**
   ```
   "按功能模块重新组织需求层次，确保逻辑清晰"
   ```

3. **查看优化方案**
   - AI分析当前结构
   - 生成优化建议
   - 显示调整计划

4. **执行优化**
   - 确认优化方案
   - 系统自动执行调整
   - 查看优化结果

#### 2.3.2 优化策略

**按功能分组**：
```
"将所有用户相关需求归类到用户管理模块下"
```

**按优先级排序**：
```
"按需求优先级重新组织结构"
```

**平衡树深度**：
```
"优化需求树结构，避免层次过深"
```

### 2.4 智能查询分析

#### 2.4.1 需求查询

**按条件查询**：
```
"查询所有高优先级的功能性需求"
```

**按关系查询**：
```
"查询与用户登录相关的所有需求"
```

**按状态查询**：
```
"查询当前项目中未完成的需求"
```

#### 2.4.2 数据分析

**需求统计**：
```
"分析当前项目的需求分布情况"
```

**关系分析**：
```
"分析需求间的关系网络"
```

**进度分析**：
```
"分析项目需求完成进度"
```

## 3. 高级功能

### 3.1 批量操作

#### 3.1.1 批量创建需求

```
"批量创建以下需求：
1. 用户注册功能
2. 用户登录功能  
3. 密码重置功能
4. 用户信息管理功能"
```

#### 3.1.2 批量建立关系

```
"为用户管理模块下的所有需求建立与系统安全需求的依赖关系"
```

### 3.2 模板使用

#### 3.2.1 需求模板

**功能性需求模板**：
```
"使用功能性需求模板创建支付模块需求"
```

**非功能性需求模板**：
```
"使用性能需求模板创建系统响应时间要求"
```

#### 3.2.2 自定义模板

用户可以保存常用的需求模式作为模板，便于快速创建类似需求。

### 3.3 协作功能

#### 3.3.1 需求评审

```
"启动用户登录需求的评审流程"
```

#### 3.3.2 变更管理

```
"记录需求REQ001的变更历史"
```

## 4. 最佳实践

### 4.1 需求描述规范

**清晰具体**：
- ✅ "用户可以通过用户名和密码登录系统"
- ❌ "用户登录"

**包含验收标准**：
- ✅ "登录失败3次后锁定账户30分钟"
- ❌ "登录安全控制"

**使用标准术语**：
- ✅ "系统应在2秒内响应用户请求"
- ❌ "系统要快"

### 4.2 关系建立原则

**避免循环依赖**：
- 检查依赖关系的合理性
- 避免A依赖B，B又依赖A的情况

**保持关系一致性**：
- 确保关系类型的正确使用
- 定期检查关系的有效性

### 4.3 结构组织建议

**逻辑分层**：
- 第一层：业务模块（用户管理、订单管理等）
- 第二层：功能分类（注册、登录、权限等）
- 第三层：具体需求

**编号规范**：
- 使用有意义的编号前缀
- 保持编号的连续性和逻辑性

## 5. 常见问题

### 5.1 操作问题

**Q: AI没有正确理解我的需求怎么办？**
A: 尝试使用更具体的描述，包含关键词和上下文信息。

**Q: 推荐的关系不合适怎么办？**
A: 可以手动调整推荐结果，或提供更多上下文信息重新分析。

**Q: 结构优化后不满意怎么办？**
A: 可以撤销优化操作，或手动调整结构。

### 5.2 技术问题

**Q: 系统响应很慢怎么办？**
A: 检查网络连接，或联系管理员检查系统状态。

**Q: 工具调用失败怎么办？**
A: 确认摇光系统是否正常运行，检查MCP连接状态。

### 5.3 权限问题

**Q: 无法创建需求怎么办？**
A: 检查是否有相应的权限，联系管理员分配权限。

**Q: 看不到某些项目怎么办？**
A: 确认是否有项目访问权限。

## 6. 技巧和窍门

### 6.1 提高效率的技巧

1. **使用自然语言**：直接用自然语言描述需求，AI会自动解析
2. **批量操作**：一次性处理多个相关需求
3. **模板复用**：保存常用的需求模式
4. **快捷指令**：使用简短的指令快速执行常见操作

### 6.2 质量提升技巧

1. **详细描述**：提供充分的上下文信息
2. **标准术语**：使用项目统一的术语
3. **及时验证**：创建后及时检查和验证
4. **定期整理**：定期优化需求结构

### 6.3 协作技巧

1. **统一标准**：团队使用统一的需求描述标准
2. **及时沟通**：有问题及时与团队成员沟通
3. **文档记录**：重要决策和变更要有文档记录
4. **定期评审**：定期进行需求评审和优化

## 7. 联系支持

如果在使用过程中遇到问题，可以通过以下方式获取帮助：

- **在线帮助**：点击界面右上角的帮助按钮
- **技术支持**：联系系统管理员
- **用户社区**：参与用户交流群
- **培训资源**：查看在线培训材料

---

**文档版本**：v1.0  
**最后更新**：2025年1月18日  
**适用版本**：Dify v1.6.0+
