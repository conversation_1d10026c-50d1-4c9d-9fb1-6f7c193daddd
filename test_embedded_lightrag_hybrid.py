#!/usr/bin/env python3
"""
嵌入式LightRAG混合配置测试
使用API LLM + 本地embedding和reranking模型
"""

import asyncio
import os
import shutil
from src.embedded_lightrag import EmbeddedLightRAG, LightRAGConfig

async def test_hybrid_configuration():
    """测试混合配置：API LLM + 本地模型"""
    print("🧪 测试混合配置：API LLM + 本地模型...")
    
    try:
        # 1. 清理旧的测试数据
        test_dir = "./test_rag_storage_hybrid"
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        # 2. 创建配置（强制使用混合配置）
        config = LightRAGConfig(
            working_dir=test_dir,
            use_local_embedding=True,
            use_local_reranking=True,
            use_api_llm=True
        )
        
        print(f"📁 工作目录: {config.working_dir}")
        print(f"🎯 LLM: 使用API ({config.llm_model_name})")
        print(f"🎯 Embedding: 使用本地模型 ({config.embedding_model_path})")
        print(f"🎯 Reranking: 使用本地模型 ({config.reranking_model_path})")
        
        # 3. 检查API密钥
        if not config.api_key:
            print("⚠️  警告: API密钥未设置")
            print("请设置环境变量: export OPENAI_API_KEY=your-openrouter-key")
            return False
        
        # 4. 初始化LightRAG（异步）
        rag = EmbeddedLightRAG(config)
        await rag.initialize()
        
        # 5. 检查初始化状态
        storage_info = rag.get_storage_info()
        print(f"✅ 存储信息: {storage_info}")
        
        # 6. 测试文档插入
        test_doc = """
        用户登录需求：
        1. 用户可以通过邮箱和密码登录系统
        2. 支持记住登录状态
        3. 登录失败时显示错误信息
        4. 支持找回密码功能
        """
        
        print("📄 插入测试文档...")
        doc_id = await rag.insert_document(test_doc)
        print(f"✅ 文档插入成功，ID: {doc_id}")
        
        # 7. 测试查询
        print("🔍 测试查询...")
        await asyncio.sleep(2)  # 等待处理完成
        query = "用户登录需要哪些功能？"
        result = await rag.query(query)
        print(f"🎯 查询结果: {result}")
        
        # 8. 清理资源
        await rag.finalize()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_only():
    """测试仅使用API的配置"""
    print("\n🧪 测试仅API配置...")
    
    try:
        # 清理旧的测试数据
        test_dir = "./test_rag_storage_api"
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        # 创建配置（仅使用API）
        config = LightRAGConfig(
            working_dir=test_dir,
            use_local_embedding=False,
            use_local_reranking=False,
            use_api_llm=True
        )
        
        print(f"🎯 使用API LLM: {config.llm_model_name}")
        print(f"🎯 使用API embedding: {config.embedding_model_name}")
        
        # 检查API密钥
        if not config.api_key:
            print("⚠️  警告: API密钥未设置")
            return False
        
        # 初始化LightRAG
        rag = EmbeddedLightRAG(config)
        await rag.initialize()
        
        # 测试文档插入
        test_doc = "测试API配置：用户需要能够注册新账户。"
        doc_id = await rag.insert_document(test_doc)
        print(f"✅ API配置测试成功，文档ID: {doc_id}")
        
        # 清理资源
        await rag.finalize()
        
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_local_only():
    """测试仅使用本地模型的配置"""
    print("\n🧪 测试仅本地配置...")
    
    try:
        # 清理旧的测试数据
        test_dir = "./test_rag_storage_local"
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        # 创建配置（仅使用本地模型）
        config = LightRAGConfig(
            working_dir=test_dir,
            use_local_embedding=True,
            use_local_reranking=True,
            use_api_llm=False  # 这会失败，因为本地LLM未实现
        )
        
        # 初始化LightRAG
        rag = EmbeddedLightRAG(config)
        await rag.initialize()
        
        # 清理资源
        await rag.finalize()
        
        return True
        
    except NotImplementedError as e:
        print(f"✅ 预期行为：{e}")
        return True
    except Exception as e:
        print(f"❌ 本地配置测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 嵌入式LightRAG混合配置测试\n")
    
    # 检查环境变量
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("⚠️  警告: OPENAI_API_KEY环境变量未设置")
        print("请设置: export OPENAI_API_KEY=your-openrouter-key")
        print("然后重新运行测试")
        return False
    
    print(f"🔑 API密钥已设置: {api_key[:10]}...")
    
    # 运行测试
    test1 = await test_hybrid_configuration()
    test2 = await test_api_only()
    test3 = await test_local_only()
    
    if test1 and test2 and test3:
        print("\n🎉 所有测试通过！混合配置成功")
        return True
    else:
        print("\n❌ 部分测试未通过")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        exit(1)