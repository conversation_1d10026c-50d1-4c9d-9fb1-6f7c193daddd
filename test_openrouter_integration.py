#!/usr/bin/env python3
"""
OpenRouter集成测试
验证OpenRouter API与LightRAG的集成
"""

import asyncio
import os
import shutil
from src.embedded_lightrag import EmbeddedLightRAG, LightRAGConfig

async def test_openrouter_integration():
    """测试OpenRouter集成"""
    print("🚀 测试OpenRouter与LightRAG集成...")
    
    try:
        # 1. 清理旧的测试数据
        test_dir = "./test_rag_storage_openrouter"
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        # 2. 创建配置（强制使用OpenRouter）
        config = LightRAGConfig(
            working_dir=test_dir,
            use_local_embedding=False,  # 使用OpenRouter embedding
            use_local_reranking=False,  # 使用OpenRouter reranking
            use_api_llm=True            # 使用OpenRouter LLM
        )
        
        print(f"🔗 OpenRouter配置:")
        print(f"   API Base: {config.api_base}")
        print(f"   LLM Model: {config.llm_model_name}")
        print(f"   Working Dir: {config.working_dir}")
        
        # 3. 检查OpenRouter API密钥
        api_key = config.api_key
        if not api_key:
            print("❌ 错误: OpenRouter API密钥未设置")
            print("   请设置: export OPENAI_API_KEY=your-openrouter-key")
            print("   或设置: export OPENROUTER_API_KEY=your-openrouter-key")
            return False
        
        print(f"🔑 API密钥已设置: {api_key[:10]}...")
        
        # 4. 初始化LightRAG
        rag = EmbeddedLightRAG(config)
        await rag.initialize()
        
        # 5. 检查初始化状态
        storage_info = rag.get_storage_info()
        print(f"✅ 存储信息: {storage_info}")
        
        # 6. 测试API连接
        print("🔍 测试OpenRouter API连接...")
        
        # 7. 测试文档插入
        test_doc = """
        智能需求分析系统需求：
        1. 支持自然语言需求输入
        2. 自动提取需求要素
        3. 生成需求文档和图表
        4. 支持需求变更跟踪
        """
        
        print("📄 插入测试文档...")
        doc_id = await rag.insert_document(test_doc)
        print(f"✅ 文档插入成功，ID: {doc_id}")
        
        # 8. 测试查询
        print("🔍 测试查询...")
        await asyncio.sleep(2)  # 等待处理完成
        query = "智能需求分析系统需要哪些功能？"
        result = await rag.query(query)
        print(f"🎯 查询结果: {result}")
        
        # 9. 测试批量插入
        print("\n📦 测试批量插入...")
        documents = [
            "需求1：支持用户注册和登录功能",
            "需求2：实现需求文档自动生成",
            "需求3：提供需求冲突检测",
            "需求4：支持需求版本管理"
        ]
        
        doc_ids = await rag.batch_insert(documents)
        print(f"✅ 批量插入成功，文档IDs: {doc_ids}")
        
        # 10. 测试批量查询
        await asyncio.sleep(2)
        result = await rag.query("系统有哪些核心需求？")
        print(f"🎯 批量查询结果: {result}")
        
        # 11. 清理资源
        await rag.finalize()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🧪 OpenRouter集成测试\n")
    
    # 检查环境变量
    api_key = os.getenv("OPENAI_API_KEY") or os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ 错误: 未找到OpenRouter API密钥")
        print("   请设置以下环境变量之一：")
        print("   export OPENAI_API_KEY=your-openrouter-key")
        print("   export OPENROUTER_API_KEY=your-openrouter-key")
        return False
    
    print(f"✅ 已检测到API密钥")
    
    # 运行测试
    success = await test_openrouter_integration()
    
    if success:
        print("\n🎉 OpenRouter集成测试通过！")
    else:
        print("\n❌ OpenRouter集成测试失败")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        exit(1)