"""
MBSE工作流定义
该模块使用LangGraph构建MBSE需求处理工作流
"""
from typing import Dict, List, Annotated, TypedDict, Literal, Any, cast
import uuid
import logging
import time
from langgraph.graph import StateGraph, END
from langchain_core.messages import SystemMessage, HumanMessage

from src.graph.workflow_state import WorkflowState, SysMLTask, ProcessStatus, create_initial_state
from src.agents.requirement_expander import expand_requirement
from src.agents.document_processor import process_document
from src.agents.task_classifier import extract_and_assign_tasks
from src.agents.json_enhancer_agent import JSONEnhancerAgent
from src.agents.ingestion_agent import sysml_ingestion_agent
from src.agents.fusion_agent import sysml_fusion_agent
from src.agents.validation_agent import sysml_validation_agent
from src.agents.export_agent import sysml_export_agent

logger = logging.getLogger(__name__)

def check_workflow_path(state: WorkflowState) -> Literal["expand_requirement", "process_document", "extract_and_assign_tasks", "enhance_json", "ingest_sysml_data", "fuse_entities", "validate_model", "export_model", "end"]:
    """
    确定工作流路径的条件函数 - 基于图数据库驱动的架构

    参数:
        state: 当前工作流状态

    返回:
        下一步操作的名称
    """
    # 如果有错误消息，直接结束流程
    if state.error_message:
        logger.error(f"工作流异常终止: {state.error_message}")
        return "end"

    # 根据不同的状态确定下一步操作
    if state.requires_expansion and not state.expanded_content:
        # 需要先扩展简短需求
        logger.info("转向扩展需求阶段")
        return "expand_requirement"

    elif state.input_doc_path and not state.expanded_content:
        # 处理文档
        logger.info("转向处理文档阶段")
        return "process_document"

    elif state.expanded_content and not state.tasks_assigned:
        # 直接从内容中提取任务
        logger.info("转向任务提取与分配阶段")
        return "extract_and_assign_tasks"

    elif state.tasks_assigned and not any(task.type == "JSON Enhancement" for task in state.assigned_tasks):
        # 任务已分配并处理完成，开始JSON增强阶段
        logger.info("任务已分配并处理完成，转向JSON增强阶段")
        return "enhance_json"

    elif any(task.type == "JSON Enhancement" and task.status == ProcessStatus.COMPLETED for task in state.assigned_tasks) and not any(task.type == "Data Ingestion" for task in state.assigned_tasks):
        # JSON增强完成，开始数据摄取阶段
        logger.info("JSON增强完成，转向数据摄取阶段")
        return "ingest_sysml_data"

    elif any(task.type == "Data Ingestion" and task.status == ProcessStatus.COMPLETED for task in state.assigned_tasks) and not any(task.type == "Entity Fusion" for task in state.assigned_tasks):
        # 数据摄取完成，执行实体融合
        logger.info("数据摄取完成，转向实体融合阶段")
        return "fuse_entities"

    elif any(task.type == "Entity Fusion" and task.status == ProcessStatus.COMPLETED for task in state.assigned_tasks) and not any(task.type == "Model Validation" for task in state.assigned_tasks):
        # 实体融合完成，执行模型验证
        logger.info("实体融合完成，转向模型验证阶段")
        return "validate_model"

    elif any(task.type == "Model Validation" and task.status == ProcessStatus.COMPLETED for task in state.assigned_tasks) and not any(task.type == "Model Export" for task in state.assigned_tasks):
        # 模型验证完成，执行模型导出
        logger.info("模型验证完成，转向模型导出阶段")
        return "export_model"

    else:
        # 流程结束（包括已经完成了模型导出的情况）
        logger.info("工作流程完成")
        return "end"


def start_node(state: WorkflowState) -> WorkflowState:
    """
    工作流的起始节点，直接返回状态不做修改

    参数:
        state: 工作流状态

    返回:
        未修改的工作流状态
    """
    logger.info("工作流启动")
    return state


def sysml_json_enhancer_agent(state: WorkflowState) -> WorkflowState:
    """SysML JSON增强Agent节点"""
    agent = JSONEnhancerAgent()
    topupdate = agent.process(state)
    return topupdate


def sysml_ingestion_agent(state: WorkflowState) -> WorkflowState:
    """SysML数据摄取Agent节点"""
    from src.agents.ingestion_agent import IngestionAgent
    agent = IngestionAgent()
    dateupdate = agent.process(state)
    return dateupdate


def sysml_fusion_agent(state: WorkflowState) -> WorkflowState:
    """SysML实体融合Agent节点"""
    from src.agents.fusion_agent import FusionAgent
    agent = FusionAgent()
    fusionupdate = agent.process(state)
    return fusionupdate


def sysml_validation_agent(state: WorkflowState) -> WorkflowState:
    """SysML验证Agent节点"""
    from src.agents.validation_agent import ValidationAgent
    agent = ValidationAgent()
    checkupdate = agent.process(state)
    return checkupdate


def sysml_export_agent(state: WorkflowState) -> WorkflowState:
    """SysML导出Agent节点"""
    from src.agents.export_agent import ExportAgent
    agent = ExportAgent()
    importupdate = agent.process(state)
    return importupdate


def create_mbse_workflow() -> StateGraph:
    """
    创建MBSE工作流图 - 基于图数据库驱动的架构

    返回:
        配置好的StateGraph对象
    """
    # 创建工作流图实例
    workflow = StateGraph(WorkflowState)

    # 添加节点
    workflow.add_node("start", start_node)
    workflow.add_node("expand_requirement", expand_requirement)
    workflow.add_node("process_document", process_document)
    workflow.add_node("extract_and_assign_tasks", extract_and_assign_tasks)
    workflow.add_node("enhance_json", sysml_json_enhancer_agent)
    workflow.add_node("ingest_sysml_data", sysml_ingestion_agent)
    workflow.add_node("fuse_entities", sysml_fusion_agent)
    workflow.add_node("validate_model", sysml_validation_agent)
    workflow.add_node("export_model", sysml_export_agent)

    # 设置条件路由
    workflow.add_conditional_edges(
        "start",  # 开始节点
        check_workflow_path,
        {
            "expand_requirement": "expand_requirement",
            "process_document": "process_document",
            "extract_and_assign_tasks": "extract_and_assign_tasks",
            "enhance_json": "enhance_json",
            "ingest_sysml_data": "ingest_sysml_data",
            "fuse_entities": "fuse_entities",
            "validate_model": "validate_model",
            "export_model": "export_model",
            "end": END
        }
    )

    # 从各节点添加条件路由
    for node in ["expand_requirement", "process_document", "extract_and_assign_tasks", "enhance_json", "ingest_sysml_data", "fuse_entities", "validate_model", "export_model"]:
        workflow.add_conditional_edges(
            node,
            check_workflow_path,
            {
                "expand_requirement": "expand_requirement",
                "process_document": "process_document",
                "extract_and_assign_tasks": "extract_and_assign_tasks",
                "enhance_json": "enhance_json",
                "ingest_sysml_data": "ingest_sysml_data",
                "fuse_entities": "fuse_entities",
                "validate_model": "validate_model",
                "export_model": "export_model",
                "end": END
            }
        )

    # 设置起始节点
    workflow.set_entry_point("start")

    return workflow


def create_mbse_workflow_app():
    """
    创建并配置MBSE工作流应用
    
    返回:
        配置好的LangGraph应用
    """
    # 创建工作流图
    workflow = create_mbse_workflow()
    
    # 编译工作流并配置 - 兼容LangGraph 0.5.x
    app = workflow.compile()

    return app 