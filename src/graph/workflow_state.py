"""
工作流状态定义
此模块定义了MBSE工作流的状态类和相关工具函数
"""
from typing import Dict, List, Optional, TypedDict, Any, Literal
from enum import Enum
from pydantic import BaseModel, Field


class ProcessStatus(str, Enum):
    """处理状态枚举"""
    NOT_STARTED = "not_started"  # 未开始
    IN_PROGRESS = "in_progress"  # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"            # 失败


class SysMLTask(BaseModel):
    """SysML任务定义"""
    id: str = Field(description="任务唯一标识符")
    type: str = Field(description="SysML图表类型")
    content: str = Field(description="任务内容")
    status: ProcessStatus = Field(default=ProcessStatus.NOT_STARTED, description="任务处理状态")
    # 错误信息
    error_message: Optional[str] = Field(default=None, description="错误信息")
    result: Optional[Any] = Field(default=None, description="任务处理结果")

class WorkflowState(BaseModel):
    """工作流状态定义"""
    # 输入
    input_doc_path: Optional[str] = Field(default=None, description="输入文档路径")
    input_short_req: Optional[str] = Field(default=None, description="简短需求描述")
    
    # 流程状态
    requires_expansion: bool = Field(default=False, description="是否需要扩展需求")
    tasks_assigned: bool = Field(default=False, description="任务是否已分配")
    save_stages: bool = Field(default=False, description="是否保存各阶段文档")
    enable_quality_enhancement: bool = Field(default=True, description="是否启用文档质量提升阶段")
    
    # 数据存储
    expanded_content: str = Field(default="", description="扩展后的需求文档内容")
    initial_expanded_content: str = Field(default="", description="初始扩展的需求文档内容")
    assigned_tasks: List[SysMLTask] = Field(default_factory=list, description="分配的SysML任务列表")
    
    # 错误信息
    error_message: Optional[str] = Field(default=None, description="错误信息")


def create_initial_state(doc_path: Optional[str] = None, short_req: Optional[str] = None, save_stages: bool = False, enable_quality_enhancement: bool = True) -> WorkflowState:
    """
    创建初始工作流状态
    
    参数:
        doc_path: 可选的文档路径
        short_req: 可选的简短需求描述
        save_stages: 是否保存各阶段文档
        enable_quality_enhancement: 是否启用文档质量提升阶段
        
    返回:
        初始化的WorkflowState对象
    """
    state = WorkflowState(
        input_doc_path=doc_path,
        input_short_req=short_req,
        requires_expansion=bool(short_req and not doc_path),
        save_stages=save_stages,
        enable_quality_enhancement=enable_quality_enhancement
    )
    return state 