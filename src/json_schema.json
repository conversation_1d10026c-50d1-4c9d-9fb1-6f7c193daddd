{"$schema": "http://json-schema.org/draft-07/schema#", "title": "SysML模型元素统一Schema", "description": "定义了从文本提取并用于生成MagicDraw SysML文件的所有JSON元素的结构。", "$defs": {"Model": {"type": "object", "title": "模型 (Model)", "description": "SysML模型的根元素，是所有包和元素的顶层容器。", "properties": {"id": {"type": "string", "description": "模型的唯一标识符"}, "type": {"type": "string", "const": "Model", "description": "元素类型固定为'Model'"}, "name": {"type": "string", "description": "模型的名称"}}, "required": ["id", "type", "name"]}, "Package": {"type": "object", "title": "包 (Package)", "description": "用于组织模型元素的容器。", "properties": {"id": {"type": "string", "description": "包的唯一标识符"}, "type": {"type": "string", "const": "Package", "description": "元素类型固定为'Package'"}, "name": {"type": "string", "description": "包的名称"}, "parentId": {"type": "string", "description": "父元素（模型或另一个包）的ID"}}, "required": ["id", "type", "name", "parentId"]}, "Requirement": {"type": "object", "title": "需求 (Requirement)", "description": "描述系统必须满足的条件或能力。", "properties": {"id": {"type": "string", "description": "需求的唯一标识符"}, "type": {"type": "string", "const": "Requirement", "description": "元素类型固定为'Requirement'"}, "name": {"type": "string", "description": "需求的简短名称或标题"}, "reqId": {"type": "string", "description": "需求的正式编号，如 REQ-1"}, "text": {"type": "string", "description": "需求的详细文本描述"}, "parentId": {"type": "string", "description": "所属包的ID"}}, "required": ["id", "type", "name", "reqId", "text", "parentId"]}, "TestCase": {"type": "object", "title": "测试用例 (TestCase)", "description": "定义用于验证一个或多个需求的测试过程。", "properties": {"id": {"type": "string", "description": "测试用例的唯一标识符"}, "type": {"type": "string", "const": "TestCase", "description": "元素类型固定为'TestCase'"}, "name": {"type": "string", "description": "测试用例的名称"}, "parentId": {"type": "string", "description": "所属包的ID"}}, "required": ["id", "type", "name", "parentId"]}, "Block": {"type": "object", "title": "块 (Block)", "description": "系统的结构化单元，可以是硬件、软件、数据或人员。", "properties": {"id": {"type": "string", "description": "块的唯一标识符"}, "type": {"type": "string", "const": "Block", "description": "元素类型固定为'Block'"}, "name": {"type": "string", "description": "块的名称"}, "parentId": {"type": "string", "description": "所属包的ID"}, "classifierBehaviorId": {"type": "string", "description": "指向该块的分类器行为（如状态机）的ID"}}, "required": ["id", "type", "name", "parentId"]}, "Property": {"type": "object", "title": "属性 (Property)", "description": "块的组成部分，可以是值属性、部件属性或引用属性。", "properties": {"id": {"type": "string", "description": "属性的唯一标识符"}, "type": {"type": "string", "const": "Property", "description": "元素类型固定为'Property'"}, "name": {"type": "string", "description": "属性的名称"}, "parentId": {"type": "string", "description": "所属块的ID"}, "visibility": {"type": "string", "enum": ["public", "private", "protected"], "description": "可见性"}, "propertyKind": {"type": "string", "enum": ["value", "part", "reference"], "description": "属性种类"}, "aggregation": {"type": "string", "enum": ["composite", "shared", "none"], "description": "聚合类型"}, "typeId": {"type": "string", "description": "属性的类型（另一个块、值类型等）的ID"}, "associationId": {"type": "string", "description": "关联关系的ID（如果适用）"}, "multiplicity": {"type": "string", "description": "多重性，如'[1..1]'或'[0..*]'"}}, "required": ["id", "type", "name", "parentId", "typeId"]}, "InterfaceBlock": {"type": "object", "title": "接口块 (InterfaceBlock)", "description": "定义块之间交互的契约。", "properties": {"id": {"type": "string", "description": "接口块的唯一标识符"}, "type": {"type": "string", "const": "InterfaceBlock", "description": "元素类型固定为'InterfaceBlock'"}, "name": {"type": "string", "description": "接口块的名称"}, "parentId": {"type": "string", "description": "所属包的ID"}, "isAbstract": {"type": "boolean", "description": "是否为抽象接口"}}, "required": ["id", "type", "name", "parentId"]}, "Port": {"type": "object", "title": "端口 (Port)", "description": "块上的交互点，可以是FullPort或ProxyPort。", "properties": {"id": {"type": "string", "description": "端口的唯一标识符"}, "type": {"type": "string", "enum": ["FullPort", "ProxyPort"], "description": "端口的具体类型"}, "name": {"type": "string", "description": "端口的名称"}, "parentId": {"type": "string", "description": "所属块的ID"}, "visibility": {"type": "string", "enum": ["public", "private", "protected"], "description": "可见性"}, "typeId": {"type": "string", "description": "端口的类型（通常是接口块或信号）的ID"}, "isBehavior": {"type": "boolean", "description": "是否为行为端口（通常用于ProxyPort）"}}, "required": ["id", "type", "name", "parentId", "typeId"]}, "Association": {"type": "object", "title": "关联 (Association)", "description": "表示元素之间的关系，如块之间或参与者与用例之间。", "properties": {"id": {"type": "string", "description": "关联的唯一标识符"}, "type": {"type": "string", "const": "Association", "description": "元素类型固定为'Association'"}, "parentId": {"type": "string", "description": "所属包的ID"}, "memberEndIds": {"type": "array", "description": "（结构图中）关联两端的属性ID列表", "items": {"type": "string"}}, "sourceId": {"type": "string", "description": "（用例图中）关联的源元素ID"}, "targetId": {"type": "string", "description": "（用例图中）关联的目标元素ID"}}, "required": ["id", "type", "parentId"], "oneOf": [{"required": ["memberEndIds"]}, {"required": ["sourceId", "targetId"]}]}, "Connector": {"type": "object", "title": "连接器 (Connector)", "description": "表示块的部件属性之间的链接，可以是AssemblyConnector或BindingConnector。", "properties": {"id": {"type": "string", "description": "连接器的唯一标识符"}, "type": {"type": "string", "enum": ["AssemblyConnector", "BindingConnector"], "description": "连接器的具体类型"}, "parentId": {"type": "string", "description": "所属块的ID"}, "end1": {"$ref": "#/$defs/ConnectorEnd"}, "end2": {"$ref": "#/$defs/ConnectorEnd"}}, "required": ["id", "type", "parentId", "end1", "end2"]}, "ConnectorEnd": {"type": "object", "title": "连接器端点", "description": "定义连接器的一端，指向一个属性或端口。", "properties": {"partRefId": {"type": ["string", "null"], "description": "指向部件属性的ID"}, "portRefId": {"type": ["string", "null"], "description": "指向部件端口的ID"}, "propertyRefId": {"type": "string", "description": "（用于BindingConnector）指向属性的ID"}}}, "StateMachine": {"type": "object", "title": "状态机 (StateMachine)", "description": "描述一个块或系统的行为，由状态、转换和事件组成。", "properties": {"id": {"type": "string", "description": "状态机的唯一标识符"}, "type": {"type": "string", "const": "StateMachine", "description": "元素类型固定为'StateMachine'"}, "name": {"type": "string", "description": "状态机的名称"}, "parentId": {"type": "string", "description": "所属块或包的ID"}}, "required": ["id", "type", "name", "parentId"]}, "State": {"type": "object", "title": "状态 (State)", "description": "系统在其生命周期中可能存在的某种状况或模式。", "properties": {"id": {"type": "string", "description": "状态的唯一标识符"}, "type": {"type": "string", "const": "State", "description": "元素类型固定为'State'"}, "name": {"type": "string", "description": "状态的名称"}, "parentId": {"type": "string", "description": "所属区域(Region)的ID"}}, "required": ["id", "type", "name", "parentId"]}, "Transition": {"type": "object", "title": "转换 (Transition)", "description": "表示从一个状态到另一个状态的流转。", "properties": {"id": {"type": "string", "description": "转换的唯一标识符"}, "type": {"type": "string", "const": "Transition", "description": "元素类型固定为'Transition'"}, "sourceId": {"type": "string", "description": "源状态或伪状态的ID"}, "targetId": {"type": "string", "description": "目标状态或伪状态的ID"}, "parentId": {"type": "string", "description": "所属区域(Region)的ID"}, "triggerIds": {"type": "array", "items": {"type": "string"}, "description": "触发此转换的事件ID列表"}, "guard": {"type": ["object", "null"], "description": "转换的监护条件"}, "effect": {"type": ["object", "null"], "description": "转换发生时执行的效应（行为）"}}, "required": ["id", "type", "sourceId", "targetId", "parentId"]}, "Activity": {"type": "object", "title": "活动 (Activity)", "description": "表示一个工作流或过程，由一系列动作和控制流组成。", "properties": {"id": {"type": "string", "description": "活动的唯一标识符"}, "type": {"type": "string", "const": "Activity", "description": "元素类型固定为'Activity'"}, "name": {"type": "string", "description": "活动的名称"}, "parentId": {"type": "string", "description": "所属包的ID"}, "nodes": {"type": "array", "items": {"type": "string"}, "description": "活动包含的节点ID列表"}, "edges": {"type": "array", "items": {"type": "string"}, "description": "活动包含的边ID列表"}, "groups": {"type": "array", "items": {"type": "string"}, "description": "活动包含的泳道ID列表"}}, "required": ["id", "type", "name", "parentId"]}, "ActivityNode": {"type": "object", "title": "活动节点 (ActivityNode)", "description": "活动图中的一个步骤或控制点，如动作、决策点、分支/合并点等。", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["InitialNode", "ActivityFinalNode", "ForkNode", "DecisionNode", "JoinNode", "CallBehaviorAction", "ObjectNode"]}, "name": {"type": "string"}, "parentId": {"type": "string", "description": "所属活动(Activity)的ID"}}, "required": ["id", "type", "name", "parentId"]}, "ActivityEdge": {"type": "object", "title": "活动边 (ActivityEdge)", "description": "连接活动图中两个节点的流，可以是控制流或对象流。", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["ControlFlow", "ObjectFlow"]}, "sourceId": {"type": "string", "description": "边的源节点ID"}, "targetId": {"type": "string", "description": "边的目标节点ID"}, "parentId": {"type": "string", "description": "所属活动(Activity)的ID"}, "guard": {"type": "string", "description": "控制流的监护条件，如'[超速]'"}}, "required": ["id", "type", "sourceId", "targetId", "parentId"]}, "Interaction": {"type": "object", "title": "交互 (Interaction)", "description": "表示一组生命线之间消息交换的序列，即序列图的核心。", "properties": {"id": {"type": "string", "description": "交互的唯一标识符"}, "type": {"type": "string", "const": "Interaction", "description": "元素类型固定为'Interaction'"}, "name": {"type": "string", "description": "交互的名称"}, "parentId": {"type": "string", "description": "所属块或用例的ID"}}, "required": ["id", "type", "name", "parentId"]}, "Lifeline": {"type": "object", "title": "生命线 (Lifeline)", "description": "在交互中代表一个参与者（块的实例）。", "properties": {"id": {"type": "string", "description": "生命线的唯一标识符"}, "type": {"type": "string", "const": "Lifeline", "description": "元素类型固定为'Lifeline'"}, "name": {"type": "string", "description": "生命线的名称"}, "parentId": {"type": "string", "description": "所属交互(Interaction)的ID"}, "representsId": {"type": "string", "description": "该生命线所代表的属性(Property)的ID"}}, "required": ["id", "type", "name", "parentId"]}, "Message": {"type": "object", "title": "消息 (Message)", "description": "生命线之间的通信，可以是同步调用、异步信号或回复。", "properties": {"id": {"type": "string", "description": "消息的唯一标识符"}, "type": {"type": "string", "const": "Message", "description": "元素类型固定为'Message'"}, "name": {"type": "string", "description": "消息的名称"}, "parentId": {"type": "string", "description": "所属交互(Interaction)的ID"}, "sendEventId": {"type": "string", "description": "发送事件的ID"}, "receiveEventId": {"type": "string", "description": "接收事件的ID"}, "messageSort": {"type": "string", "enum": ["synchCall", "asynchCall", "reply", "createMessage", "deleteMessage"], "description": "消息类型"}, "signatureId": {"type": ["string", "null"], "description": "消息所调用的操作(Operation)或信号(Signal)的ID"}}, "required": ["id", "type", "name", "parentId", "sendEventId", "receiveEventId", "messageSort"]}, "Actor": {"type": "object", "title": "参与者 (Actor)", "description": "与系统交互的外部实体，通常是人或另一个系统。", "properties": {"id": {"type": "string", "description": "参与者的唯一标识符"}, "type": {"type": "string", "const": "Actor", "description": "元素类型固定为'Actor'"}, "name": {"type": "string", "description": "参与者的名称"}, "parentId": {"type": "string", "description": "所属包的ID"}}, "required": ["id", "type", "name", "parentId"]}, "UseCase": {"type": "object", "title": "用例 (UseCase)", "description": "描述系统为参与者提供的一组有价值的功能。", "properties": {"id": {"type": "string", "description": "用例的唯一标识符"}, "type": {"type": "string", "const": "UseCase", "description": "元素类型固定为'UseCase'"}, "name": {"type": "string", "description": "用例的名称"}, "parentId": {"type": "string", "description": "所属包的ID"}}, "required": ["id", "type", "name", "parentId"]}, "ConstraintBlock": {"type": "object", "title": "约束块 (ConstraintBlock)", "description": "定义一个或多个参数之间的数学或逻辑约束。", "properties": {"id": {"type": "string", "description": "约束块的唯一标识符"}, "type": {"type": "string", "const": "ConstraintBlock", "description": "元素类型固定为'ConstraintBlock'"}, "name": {"type": "string", "description": "约束块的名称"}, "parentId": {"type": "string", "description": "所属包的ID"}, "specification": {"$ref": "#/$defs/Specification"}}, "required": ["id", "type", "name", "parentId", "specification"]}, "Specification": {"type": "object", "title": "规约", "description": "约束或表达式的文本表示。", "properties": {"expression": {"type": "string", "description": "约束的表达式"}, "language": {"type": "string", "description": "表达式使用的语言"}}, "required": ["expression"]}, "Relationship": {"type": "object", "title": "关系 (Relationship)", "description": "表示不同元素之间的通用关系，如Verify, Satisfy, DeriveReqt, Include, Extend。", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["Verify", "<PERSON><PERSON><PERSON>", "DeriveReqt", "Include", "Extend"]}, "parentId": {"type": "string"}, "sourceId": {"type": "string", "description": "关系源头的元素ID"}, "targetId": {"type": "string", "description": "关系目标的元素ID"}, "blockId": {"type": "string", "description": "（用于Satisfy）满足需求的块的ID"}, "requirementId": {"type": "string", "description": "（用于Satisfy/Verify）被满足或验证的需求的ID"}, "testCaseId": {"type": "string", "description": "（用于Verify）执行验证的测试用例的ID"}, "sourceRequirementId": {"type": "string", "description": "（用于DeriveReqt）派生源的需求ID"}, "derivedRequirementId": {"type": "string", "description": "（用于DeriveReqt）被派生的需求ID"}}, "oneOf": [{"properties": {"type": {"const": "<PERSON><PERSON><PERSON>"}}, "required": ["blockId", "requirementId"]}, {"properties": {"type": {"const": "Verify"}}, "required": ["testCaseId", "requirementId"]}, {"properties": {"type": {"const": "DeriveReqt"}}, "required": ["sourceRequirementId", "derivedRequirementId"]}, {"properties": {"type": {"const": "Include"}}, "required": ["sourceId", "targetId"]}, {"properties": {"type": {"const": "Extend"}}, "required": ["sourceId", "targetId"]}], "required": ["id", "type", "parentId"]}}}