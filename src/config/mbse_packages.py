#!/usr/bin/env python3
"""
MBSE标准包结构配置
定义SysML模型的标准包结构和元素分类规则
"""

from typing import Dict, List, Set, Optional
from dataclasses import dataclass

@dataclass
class PackageInfo:
    """包信息"""
    id: str
    name: str
    description: str
    parent_id: Optional[str] = None

class MBSEPackageConfig:
    """MBSE标准包结构配置"""
    
    # 标准包定义
    STANDARD_PACKAGES = {
        "requirements": PackageInfo(
            id="pkg-reqs-uuid",
            name="Requirements",
            description="需求相关元素：Requirement, TestCase, Verify, Satisfy, DeriveReqt"
        ),
        "usecases": PackageInfo(
            id="pkg-usecases-uuid", 
            name="UseCases",
            description="用例相关元素：Actor, UseCase, Include, Extend"
        ),
        "structure": PackageInfo(
            id="pkg-structure-uuid",
            name="SystemStructure", 
            description="结构相关元素：Block, Property, Port, Connector, Association, InterfaceBlock"
        ),
        "behavior": PackageInfo(
            id="pkg-behavior-uuid",
            name="SystemBehavior",
            description="行为相关元素：Activity, StateMachine, Interaction, 及其子元素"
        ),
        "libraries": PackageInfo(
            id="pkg-libraries-uuid",
            name="Libraries",
            description="库相关元素：ValueType, Enumeration, Signal, Unit, ConstraintBlock"
        ),
        "parametrics": PackageInfo(
            id="pkg-parametrics-uuid",
            name="Parametrics", 
            description="参数相关元素：ConstraintBlock, ConstraintParameter, BindingConnector"
        )
    }
    
    # 元素类型到包的映射
    ELEMENT_TYPE_TO_PACKAGE = {
        # 需求包
        "Requirement": "requirements",
        "TestCase": "requirements", 
        "Verify": "requirements",
        "Satisfy": "requirements",
        "DeriveReqt": "requirements",
        "Refine": "requirements",
        "Copy": "requirements",
        "Trace": "requirements",
        
        # 用例包
        "Actor": "usecases",
        "UseCase": "usecases",
        "Include": "usecases", 
        "Extend": "usecases",
        "Generalization": "usecases",  # 用例泛化关系
        
        # 结构包
        "Block": "structure",
        "InterfaceBlock": "structure",
        "Association": "structure",
        "Connector": "structure",
        "Package": "structure",  # 子包放在结构包下
        "AssociationBlock": "structure",
        "ItemFlow": "structure",
        
        # 行为包  
        "Activity": "behavior",
        "StateMachine": "behavior",
        "Interaction": "behavior",
        "Sequence": "behavior",
        "Region": "behavior",
        "State": "behavior",
        "Transition": "behavior",
        "Pseudostate": "behavior",
        "FinalState": "behavior",
        "CallBehaviorAction": "behavior",
        "SendSignalAction": "behavior",
        "AcceptEventAction": "behavior",
        "OpaqueAction": "behavior",
        "ControlNode": "behavior",
        "ActivityNode": "behavior",
        "ObjectNode": "behavior",
        "ActivityEdge": "behavior",
        "ControlFlow": "behavior",
        "ObjectFlow": "behavior",
        "InitialNode": "behavior",
        "FinalNode": "behavior",
        "DecisionNode": "behavior",
        "MergeNode": "behavior",
        "ForkNode": "behavior",
        "JoinNode": "behavior",
        
        # 库包
        "ValueType": "libraries",
        "Enumeration": "libraries",
        "EnumerationLiteral": "libraries",
        "Signal": "libraries", 
        "Unit": "libraries",
        "Dimension": "libraries",
        "PrimitiveType": "libraries",
        "DataType": "libraries",
        
        # 参数包
        "ConstraintBlock": "parametrics",
        "ConstraintParameter": "parametrics",
        "BindingConnector": "parametrics",
        "ParametricDiagram": "parametrics",
    }
    
    # 需要保持与父元素关系的子元素类型（不应该重新分配到标准包）
    CHILD_ELEMENT_TYPES = {
        # Block的子元素
        "Property", "Port", "Operation", "Reception", "Constraint",
        
        # Activity的子元素  
        "ActivityNode", "ObjectNode", "ControlNode", "ActivityEdge",
        "ControlFlow", "ObjectFlow", "InitialNode", "FinalNode",
        "DecisionNode", "MergeNode", "ForkNode", "JoinNode",
        "CallBehaviorAction", "SendSignalAction", "AcceptEventAction", "OpaqueAction",
        
        # StateMachine的子元素
        "Region", "State", "Transition", "Pseudostate", "FinalState",
        
        # ConstraintBlock的子元素
        "ConstraintParameter",
        
        # Enumeration的子元素
        "EnumerationLiteral",
        
        # Association的子元素
        "Property",  # memberEnd
        
        # Connector的子元素
        "ConnectorEnd",
        
        # Interaction的子元素
        "Lifeline", "Message", "InteractionFragment",
    }
    
    @classmethod
    def get_package_for_element_type(cls, element_type: str) -> Optional[str]:
        """根据元素类型获取应归属的包类型"""
        return cls.ELEMENT_TYPE_TO_PACKAGE.get(element_type)
    
    @classmethod
    def get_package_id_for_element_type(cls, element_type: str) -> str:
        """根据元素类型获取应归属的包ID"""
        package_type = cls.get_package_for_element_type(element_type)
        if package_type and package_type in cls.STANDARD_PACKAGES:
            return cls.STANDARD_PACKAGES[package_type].id
        # 默认返回结构包ID
        return cls.STANDARD_PACKAGES["structure"].id
    
    @classmethod
    def is_child_element(cls, element_type: str) -> bool:
        """判断元素是否是子元素（应保持与父元素的关系）"""
        return element_type in cls.CHILD_ELEMENT_TYPES
    
    @classmethod
    def create_standard_package_elements(cls, model_id: str = "unified-sysml-model") -> List[Dict]:
        """创建标准包元素列表"""
        packages = []
        
        for package_type, package_info in cls.STANDARD_PACKAGES.items():
            package_element = {
                "id": package_info.id,
                "type": "Package", 
                "name": package_info.name,
                "parentId": package_info.parent_id or model_id,
                "description": package_info.description,
                "_auto_generated": True,
                "_package_type": package_type
            }
            packages.append(package_element)
            
        return packages
    
    @classmethod
    def get_all_package_ids(cls) -> Set[str]:
        """获取所有标准包ID"""
        return {pkg.id for pkg in cls.STANDARD_PACKAGES.values()}
    
    @classmethod
    def validate_package_structure(cls, elements: List[Dict]) -> Dict[str, List[str]]:
        """验证包结构的完整性"""
        issues = {
            "missing_packages": [],
            "orphaned_elements": [],
            "incorrect_assignments": []
        }
        
        # 检查是否存在所有标准包
        existing_package_ids = {elem.get("id") for elem in elements if elem.get("type") == "Package"}
        required_package_ids = cls.get_all_package_ids()
        
        missing_packages = required_package_ids - existing_package_ids
        issues["missing_packages"] = list(missing_packages)
        
        # 检查元素分配是否正确
        for element in elements:
            element_type = element.get("type")
            parent_id = element.get("parentId")
            
            if element_type == "Package":
                continue
                
            # 检查顶层元素是否分配到正确的包
            if not cls.is_child_element(element_type):
                expected_package_id = cls.get_package_id_for_element_type(element_type)
                if parent_id != expected_package_id:
                    issues["incorrect_assignments"].append({
                        "element_id": element.get("id"),
                        "element_type": element_type,
                        "current_parent": parent_id,
                        "expected_parent": expected_package_id
                    })
        
        return issues
