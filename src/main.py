# src/main.py
"""
MBSE自动生成器主程序
负责接收命令行参数并启动MBSE工作流
"""
import argparse
import logging
import os
import json
import sys
from pathlib import Path
import datetime

# 确保项目根目录在路径中
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# --- 引入 colorlog ---
import colorlog 

from src.graph.workflow import create_mbse_workflow_app
from src.graph.workflow_state import create_initial_state
from src.core.exceptions import MBSEGeneratorError

# --- 日志配置 ---
# 创建一个自定义的彩色格式器
# %(log_color)s 会根据日志级别自动添加颜色
# %(levelname)s: 日志级别名称 (e.g., INFO, WARNING)
# %(name)s: logger 的名称 (e.g., src.orchestrator, src.llm_agents.base_agent)
# %(message)s: 具体的日志消息
formatter = colorlog.ColoredFormatter(
    '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    log_colors={
        'DEBUG':    'cyan',
        'INFO':     'green',
        'WARNING':  'yellow',
        'ERROR':    'red',
        'CRITICAL': 'bold_red,bg_white',
    },
    secondary_log_colors={
        # 可以为某些字段设置次级颜色
        # 'message': {
        #     'ERROR': 'red',
        #     'CRITICAL': 'bold_red'
        # }
    },
    style='%' # 使用旧式字符串格式化，与 log_colors 兼容
)

# 获取根日志器
root_logger = logging.getLogger()
# 清除可能已存在的处理器，避免重复输出
if root_logger.handlers:
    for handler in root_logger.handlers:
        root_logger.removeHandler(handler)

# 创建一个彩色 StreamHandler (输出到控制台)
console_handler = colorlog.StreamHandler()
console_handler.setFormatter(formatter)

file_handler = logging.FileHandler('mbse.log',encoding="utf-8",mode="a")
file_handler.setFormatter(formatter)
file_handler.setLevel(logging.DEBUG)

# 将彩色处理器添加到根日志器
root_logger.addHandler(console_handler)
root_logger.addHandler(file_handler)
root_logger.setLevel(logging.INFO) # 默认级别为 INFO

# 调整特定模块的日志级别，减少不必要的噪声
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)
logging.getLogger("openai").setLevel(logging.DEBUG)
# --- End 日志配置 ---

logger = logging.getLogger(__name__)


def ensure_output_dir():
    """确保输出目录存在"""
    output_dir = os.path.join(project_root, "data", "output")
    os.makedirs(output_dir, exist_ok=True)
    return output_dir


def save_results_to_file(state, output_path):
    """将结果保存到文件"""
    try:
        # 将所有需求和任务保存为JSON格式
        results = {
            "requirements": [],
            "assigned_tasks": []
        }
        
        # 处理需求
        if "requirements" in state and state["requirements"]:
            for req in state["requirements"]:
                if hasattr(req, "dict"):
                    results["requirements"].append(req.dict())
                else:
                    # 如果是字典类型，直接使用
                    results["requirements"].append(req)
        
        # 处理任务
        if "assigned_tasks" in state and state["assigned_tasks"]:
            for task in state["assigned_tasks"]:
                if hasattr(task, "dict"):
                    results["assigned_tasks"].append(task.dict())
                else:
                    # 如果是字典类型，直接使用
                    results["assigned_tasks"].append(task)
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"结果已保存至: {output_path}")
    except Exception as e:
        logger.error(f"保存结果失败: {str(e)}")


def save_document_to_file(content, stage_name, output_dir=None):
    """
    将文档内容保存到文件
    
    参数:
        content: 文档内容
        stage_name: 阶段名称，用于文件命名
        output_dir: 输出目录，默认为项目的output目录
    
    返回:
        保存的文件路径
    """
    if not output_dir:
        output_dir = ensure_output_dir()
    
    # 生成时间戳，用于文件名
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 构建文件名
    filename = f"{stage_name}_{timestamp}.md"
    file_path = os.path.join(output_dir, filename)
    
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        logger.info(f"{stage_name}已保存至: {file_path}")
        return file_path
    except Exception as e:
        logger.error(f"保存{stage_name}失败: {str(e)}")
        return None


def main():
    """主程序入口点"""
    parser = argparse.ArgumentParser(description="MBSE自动生成器: 需求提取和任务分配")
    parser.add_argument("--doc_path", type=str, help="输入Word文档的路径")
    parser.add_argument("--short_req", type=str, help="要扩展的简短需求短语", default="实现一个简单的自行车。不需要过多的复杂功能，只需要可以包含每一个sysml图形的案例即可，只需要几百字既可以，全文不超过400字")
    parser.add_argument("--log_level", type=str, default="INFO", 
                      choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                      help="设置日志级别")
    parser.add_argument("--save_stages", action="store_true",default=True, help="保存各阶段文档")
    parser.add_argument("--skip_quality_enhancement", action="store_true",default=True, help="跳过文档质量提升阶段，只执行初始扩展")
    
    args = parser.parse_args()
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))
    
    if not args.doc_path and not args.short_req:
        parser.print_help()
        logger.error("\n错误: 请提供 --doc_path 或 --short_req 参数")
        return
    
    if args.doc_path and not os.path.exists(args.doc_path):
        logger.error(f"错误: 文档路径 '{args.doc_path}' 不存在")
        return
    
    try:
        # 确保输出目录存在
        ensure_output_dir()
        
        # 创建工作流应用
        workflow_app = create_mbse_workflow_app()
        img = workflow_app.get_graph().draw_mermaid_png()
        with open("mermaid.png", "wb") as f:
            f.write(img)
        # 创建初始状态
        initial_state = create_initial_state(
            doc_path=args.doc_path,
            short_req=args.short_req,
            save_stages=args.save_stages,  # 传递保存阶段标志
            enable_quality_enhancement=not args.skip_quality_enhancement  # 传递质量提升控制标志
        )
        
        logger.info("开始执行MBSE工作流")
        
        # 执行工作流 - 使用Pydantic模型直接传入
        result = workflow_app.invoke(initial_state)
        
        # 从结果中提取最终状态
        # 在新版LangGraph中，result是一个AddableValuesDict对象
        logger.info("\n--- 工作流完成 ---")
        
        # 检查是否有错误
        if "error_message" in result and result["error_message"]:
            logger.error(f"工作流过程中出现错误: {result['error_message']}")
            return

            
        # 保存任务结果
        if "tasks_assigned" in result and result["tasks_assigned"] and "assigned_tasks" in result and result["assigned_tasks"]:
            logger.info(f"分配的任务摘要:")
            for i, task in enumerate(result["assigned_tasks"]):
                logger.info(f"  {i+1}. 类型: {task.type}, 内容: '{task.content[:50]}...'")
            
            # 保存结果到文件
            output_file = os.path.join(project_root, "data", "output", "mbse_results.json")
            save_results_to_file(result, output_file)
        else:
            logger.info("没有分配任何SysML相关任务")
    
    except MBSEGeneratorError as e:
        logger.critical(f"发生已处理的错误: {e}", exc_info=True)
    except Exception as e:
        logger.critical(f"发生未处理的严重错误: {e}", exc_info=True)


if __name__ == "__main__":
    main()