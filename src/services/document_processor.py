# src/services/document_processor.py
import logging
from docx import Document
from typing import List
from langchain.text_splitter import RecursiveCharacterTextSplitter
import tiktoken
from src.core.exceptions import DocumentProcessingError

logger = logging.getLogger(__name__)

class DocumentProcessor:
    def __init__(self, chunk_size: int = 4000, chunk_overlap: int = 200):
        self.encoding = tiktoken.encoding_for_model("gpt-4")
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=self._count_tokens,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
        logger.info(f"Initialized DocumentProcessor with token chunk_size={chunk_size}, chunk_overlap={chunk_overlap}")

    def _count_tokens(self, text: str) -> int:
        return len(self.encoding.encode(text))

    def read_word_doc(self, doc_path: str) -> str:
        try:
            document = Document(doc_path)
            full_text = []
            for para in document.paragraphs:
                if para.style and para.style.name.startswith('Heading'):
                    level = int(para.style.name.split(' ')[1])
                    full_text.append("\n" + "#" * level + " " + para.text.strip())
                else:
                    full_text.append(para.text.strip())
            return "\n\n".join(full_text)
        except Exception as e:
            logger.error(f"Error reading Word document {doc_path}: {e}", exc_info=True)
            raise DocumentProcessingError(f"Failed to read Word document: {e}") from e

    def chunk_text(self, text: str) -> List[str]:
        if not text:
            logger.warning("Received empty text for chunking.")
            return []
        try:
            chunks = self.text_splitter.split_text(text)
            logger.info(f"Text split into {len(chunks)} chunks.")
            return chunks
        except Exception as e:
            logger.error(f"Error splitting text into chunks: {e}", exc_info=True)
            raise DocumentProcessingError(f"Failed to chunk text: {e}") from e