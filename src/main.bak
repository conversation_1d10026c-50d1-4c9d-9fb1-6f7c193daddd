# src/main.py
import argparse
import logging
import os
from orchestrator import WorkflowOrchestrator
from config.settings import settings
from core.exceptions import MBSEGeneratorError

# --- 引入 colorlog ---
import colorlog 

# --- 日志配置 ---
# 创建一个自定义的彩色格式器
# %(log_color)s 会根据日志级别自动添加颜色
# %(levelname)s: 日志级别名称 (e.g., INFO, WARNING)
# %(name)s: logger 的名称 (e.g., src.orchestrator, src.llm_agents.base_agent)
# %(message)s: 具体的日志消息
formatter = colorlog.ColoredFormatter(
    '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    log_colors={
        'DEBUG':    'cyan',
        'INFO':     'green',
        'WARNING':  'yellow',
        'ERROR':    'red',
        'CRITICAL': 'bold_red,bg_white',
    },
    secondary_log_colors={
        # 可以为某些字段设置次级颜色
        # 'message': {
        #     'ERROR': 'red',
        #     'CRITICAL': 'bold_red'
        # }
    },
    style='%' # 使用旧式字符串格式化，与 log_colors 兼容
)

# 获取根日志器
root_logger = logging.getLogger()
# 清除可能已存在的处理器，避免重复输出
if root_logger.handlers:
    for handler in root_logger.handlers:
        root_logger.removeHandler(handler)

# 创建一个彩色 StreamHandler (输出到控制台)
console_handler = colorlog.StreamHandler()
console_handler.setFormatter(formatter)

# 将彩色处理器添加到根日志器
root_logger.addHandler(console_handler)
root_logger.setLevel(logging.INFO) # 默认级别为 INFO

# 调整特定模块的日志级别，减少不必要的噪声
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)
logging.getLogger("openai").setLevel(logging.DEBUG)
# --- End 日志配置 ---

def main():
    parser = argparse.ArgumentParser(description="MBSE Auto Generator: Requirement Extraction and Task Assignment.")
    parser.add_argument("--doc_path", type=str, help="Path to the input Word document.")
    parser.add_argument("--short_req", type=str, help="A short requirement phrase to expand.", default="实现一个车载空调系统")
    parser.add_argument("--log_level", type=str, default="DEBUG", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                        help="Set the logging level.")

    args = parser.parse_args()
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))
    logger = logging.getLogger(__name__)

    if not args.doc_path and not args.short_req:
        parser.print_help()
        logger.error("\nError: Please provide either --doc_path or --short_req.")
        return

    if args.doc_path and not os.path.exists(args.doc_path):
        logger.error(f"Error: Document path '{args.doc_path}' does not exist.")
        return

    try:
        orchestrator = WorkflowOrchestrator()
        assigned_tasks = []
        if args.doc_path:
            assigned_tasks = orchestrator.process_document(args.doc_path)
        elif args.short_req:
            assigned_tasks = orchestrator.process_short_requirement(args.short_req)
        
        logger.info("\n--- Workflow Completed ---")
        if assigned_tasks:
            logger.info("Assigned Tasks Summary:")
            for i, task in enumerate(assigned_tasks):
                logger.info(f"  {i+1}. Type: {task['type']}, Content: '{task['content'][:100]}...'")
        else:
            logger.info("No specific SysML-related tasks were assigned.")

    except MBSEGeneratorError as e:
        logger.critical(f"A managed error occurred: {e}", exc_info=True)
    except Exception as e:
        logger.critical(f"An unhandled critical error occurred: {e}", exc_info=True)

if __name__ == "__main__":
    main()