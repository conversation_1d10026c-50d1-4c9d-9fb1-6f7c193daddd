# src/orchestrator.py
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# src/orchestrator.py
import logging
from typing import List, Dict
from config.settings import settings
from src.core.exceptions import MBSEGeneratorError,  LLMAgentError
from src.services.document_processor import DocumentProcessor
from src.llm_agents.content_classifier import ContentClassifierAgent
from src.llm_agents.requirement_expander import RequirementExpanderAgent

logger = logging.getLogger(__name__)

class WorkflowOrchestrator:
    def __init__(self):
        self.doc_processor = DocumentProcessor(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap
        )
        # 传递 base_url 给 Agent
        # 注意: settings 里没有 base_url, 你需要手动添加一个或使用默认值
        # 例如: settings.base_url = os.getenv("OPENAI_BASE_URL", None)
        # 或者直接在 settings.py 中定义
        self.req_expander = RequirementExpanderAgent(
            base_url=settings.base_url, # 或者 "https://api.openai.com/v1" 或你的自定义URL
            llm_model=settings.llm_model,
            api_key=settings.openai_api_key,
            temperature=0.3,
            streaming=False # 示例: 开启流式
        )
        self.classifier = ContentClassifierAgent(
            base_url=settings.base_url, # 或者 "https://api.openai.com/v1" 或你的自定义URL
            llm_model=settings.llm_model,
            api_key=settings.openai_api_key,
            temperature=0.0,
            streaming=False # 示例: 关闭流式
        )
        logger.info("WorkflowOrchestrator initialized.")

    def process_document(self, doc_path: str) -> List[Dict[str, str]]:
        try:
            logger.info(f"Starting document processing for: {doc_path}")
            text_content = self.doc_processor.read_word_doc(doc_path)
            chunks = self.doc_processor.chunk_text(text_content)
            assigned_tasks = self._process_chunks(chunks)
            logger.info(f"Document processing for {doc_path} completed. Total assigned tasks: {len(assigned_tasks)}")
            return assigned_tasks
        except MBSEGeneratorError as e:
            logger.error(f"Document processing failed: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.critical(f"An unhandled error occurred during document processing: {e}", exc_info=True)
            raise
        
    def process_short_requirement(self, short_req: str) -> List[Dict[str, str]]:
        try:
            logger.info(f"Starting short requirement processing for: '{short_req}'")
            expanded_text = self.req_expander.process(short_req)
            logger.info(f"Requirement expanded successfully. Now processing chunks...")
            chunks = self.doc_processor.chunk_text(expanded_text)
            assigned_tasks = self._process_chunks(chunks)
            logger.info(f"Short requirement processing for '{short_req}' completed. Total assigned tasks: {len(assigned_tasks)}")
            return assigned_tasks
        except MBSEGeneratorError as e:
            logger.error(f"Short requirement processing failed: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.critical(f"An unhandled error occurred during short requirement processing: {e}", exc_info=True)
            raise

    def _process_chunks(self, chunks: List[str]) -> List[Dict[str, str]]:
        all_assigned_tasks: List[Dict[str, str]] = []
        for i, chunk in enumerate(chunks):
            logger.info(f"Processing chunk {i+1}/{len(chunks)}...")
            try:
                classifications = self.classifier.process(chunk)
                for item in classifications:
                    if item["type"] != "None" and item["type"] != "Error":
                        logger.info(f"  Assigned Task: Type='{item['type']}', Content='{item['content'][:80]}...'")
                        all_assigned_tasks.append(item)
                    else:
                        logger.debug(f"  Skipping content of type: '{item['type']}'")
            except LLMAgentError as e:
                logger.error(f"Error processing chunk {i+1}: {e}", exc_info=True)
                continue
            except Exception as e:
                logger.error(f"An unhandled error occurred while processing chunk {i+1}: {e}", exc_info=True)
                continue
        return all_assigned_tasks