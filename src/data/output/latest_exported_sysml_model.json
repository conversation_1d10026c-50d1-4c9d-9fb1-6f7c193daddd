{"model": {"id": "unified-sysml-model", "name": "Unified SysML Model", "type": "Model"}, "elements": [{"id": "act-save-stopped-uuid", "type": "Activity", "name": "保存停止状态", "parentId": "pkg-behavior-uuid"}, {"id": "act-save-idle-uuid", "type": "Activity", "name": "保存空闲状态信息", "parentId": "pkg-behavior-uuid"}, {"id": "act-detect-braking-complete-uuid", "type": "Activity", "name": "执行刹车完成检测", "parentId": "pkg-behavior-uuid"}, {"id": "act-detect-startup-complete-uuid", "type": "Activity", "name": "执行启动完成检测", "parentId": "pkg-behavior-uuid"}, {"id": "act-clear-fault-uuid", "type": "Activity", "name": "故障清理", "parentId": "pkg-behavior-uuid"}, {"id": "act-activate-stopped-uuid", "type": "Activity", "name": "激活停止硬件命令", "parentId": "pkg-behavior-uuid"}, {"id": "act-activate-braking-uuid", "type": "Activity", "name": "激活刹车硬件命令", "parentId": "pkg-behavior-uuid"}, {"id": "act-activate-starting-uuid", "type": "Activity", "name": "激活启动硬件命令", "parentId": "pkg-behavior-uuid"}, {"id": "act-activate-fault-uuid", "type": "Activity", "name": "激活故障处理命令", "parentId": "pkg-behavior-uuid"}, {"id": "act-activate-idle-uuid", "type": "Activity", "name": "激活空闲状态硬件命令", "parentId": "pkg-behavior-uuid"}, {"id": "act-vehicle-control-uuid", "type": "Activity", "name": "车辆控制流程", "parentId": "pkg-behavior-uuid"}, {"id": "node-end", "type": "ActivityFinalNode", "name": "流程结束", "parentId": "pkg-structure-uuid"}, {"id": "grp-sensors-uuid", "type": "ActivityPartition", "name": "传感器", "parentId": "pkg-structure-uuid"}, {"id": "grp-abnormal-uuid", "type": "ActivityPartition", "name": "异常处理", "parentId": "pkg-structure-uuid"}, {"id": "grp-execution-uuid", "type": "ActivityPartition", "name": "执行系统", "parentId": "pkg-structure-uuid"}, {"id": "grp-control-uuid", "type": "ActivityPartition", "name": "控制系统", "parentId": "pkg-structure-uuid"}, {"id": "grp-user-uuid", "type": "ActivityPartition", "name": "用户操作", "parentId": "pkg-structure-uuid"}, {"id": "actor-002", "type": "Actor", "name": "控制系统（电子控制单元）", "parentId": "pkg-usecases-uuid"}, {"id": "actor-user-uuid", "type": "Actor", "name": "用户", "parentId": "pkg-usecases-uuid"}, {"id": "actor-001", "type": "Actor", "name": "用户（骑行者）", "parentId": "pkg-usecases-uuid"}, {"id": "conn-frame-wheel", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "conn-frame-motor", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "conn-frame-battery", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "conn-controller-sensor", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "conn-brake-controller", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "assoc-controller-sensor", "type": "Association", "name": "ControllerSensorAssociation", "parentId": "pkg-structure-uuid"}, {"id": "assoc-remote-controller", "type": "Association", "name": "RemoteControllerAssociation", "parentId": "pkg-structure-uuid"}, {"id": "assoc-vehicle-wheel", "type": "Association", "name": "Vehicle-Wheel Association", "parentId": "pkg-structure-uuid"}, {"id": "assoc-vehicle-sensor", "type": "Association", "name": "VehicleSensorAssociation", "parentId": "pkg-structure-uuid"}, {"id": "rel-005", "type": "Association", "name": "参与", "parentId": "pkg-structure-uuid", "sourceId": "actor-001", "targetId": "usecase-001"}, {"id": "rel-006", "type": "Association", "name": "参与", "parentId": "pkg-structure-uuid", "sourceId": "actor-001", "targetId": "usecase-004"}, {"id": "rel-007", "type": "Association", "name": "参与", "parentId": "pkg-structure-uuid", "sourceId": "actor-001", "targetId": "usecase-006"}, {"id": "rel-013", "type": "Association", "name": "参与关系", "parentId": "pkg-structure-uuid", "sourceId": "actor-002", "targetId": "usecase-010"}, {"id": "assoc-vehicle-motor", "type": "Association", "name": "", "parentId": "pkg-structure-uuid", "sourceId": "actor-002", "targetId": "usecase-002"}, {"id": "assoc-vehicle-battery", "type": "Association", "name": "", "parentId": "pkg-structure-uuid", "sourceId": "actor-002", "targetId": "usecase-002"}, {"id": "assoc-vehicle-controller", "type": "Association", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "assoc-vehicle-brake", "type": "Association", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "assoc-vehicle-display", "type": "Association", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "conn-1", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-2", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-3", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-4", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-5", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-6", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-7", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-8", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-9", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "blk-abnormal-event-uuid", "type": "Block", "name": "AbnormalEvent", "parentId": "pkg-structure-uuid"}, {"id": "blk-battery-uuid", "type": "Block", "name": "Battery", "parentId": "pkg-structure-uuid"}, {"id": "blk-battery-status-uuid", "type": "Block", "name": "BatteryStatus", "parentId": "pkg-structure-uuid"}, {"id": "blk-brake-force-uuid", "type": "Block", "name": "Brake<PERSON><PERSON><PERSON>", "parentId": "pkg-structure-uuid"}, {"id": "block-ev", "type": "Block", "name": "ElectricVehicleSystem", "parentId": "pkg-structure-uuid"}, {"id": "blk-vehicle-uuid", "type": "Block", "name": "<PERSON>ame", "parentId": "pkg-structure-uuid"}, {"id": "blk-motor-command-uuid", "type": "Block", "name": "MotorControlCommand", "parentId": "pkg-structure-uuid"}, {"id": "blk-pedal-force-uuid", "type": "Block", "name": "PedalForce", "parentId": "pkg-structure-uuid"}, {"id": "blk-speed-info-uuid", "type": "Block", "name": "SpeedInfo", "parentId": "pkg-structure-uuid"}, {"id": "blk-system-status-uuid", "type": "Block", "name": "SystemStatus", "parentId": "pkg-structure-uuid"}, {"id": "blk-wheel-uuid", "type": "Block", "name": "Wheel", "parentId": "pkg-structure-uuid"}, {"id": "blk-sensor-uuid", "type": "Block", "name": "传感器", "parentId": "pkg-structure-uuid"}, {"id": "blk-brake-uuid", "type": "Block", "name": "刹车系统", "parentId": "pkg-structure-uuid"}, {"id": "blk-abnormal-detector-uuid", "type": "Block", "name": "异常检测系统", "parentId": "pkg-structure-uuid"}, {"id": "blk-controller-uuid", "type": "Block", "name": "控制器", "parentId": "pkg-structure-uuid"}, {"id": "blk-display-uuid", "type": "Block", "name": "显示系统", "parentId": "pkg-structure-uuid"}, {"id": "blk-user-uuid", "type": "Block", "name": "用户", "parentId": "pkg-structure-uuid"}, {"id": "blk-motor-uuid", "type": "Block", "name": "电机系统", "parentId": "pkg-structure-uuid"}, {"id": "blk-power-management-uuid", "type": "Block", "name": "电源管理", "parentId": "pkg-structure-uuid"}, {"id": "blk-structure-uuid", "type": "Block", "name": "车体结构", "parentId": "pkg-structure-uuid"}, {"id": "blk-vehicle-controller-uuid", "type": "Block", "name": "车辆控制器", "parentId": "pkg-structure-uuid"}, {"id": "blk-ride-control-uuid", "type": "Block", "name": "骑行控制系统", "parentId": "pkg-structure-uuid"}, {"id": "node-decelerate", "type": "CallBehaviorAction", "name": "减速直至停车", "parentId": "act-vehicle-control-uuid"}, {"id": "node-increase-force", "type": "CallBehaviorAction", "name": "加大踏力", "parentId": "act-vehicle-control-uuid"}, {"id": "node-start-motor", "type": "CallBehaviorAction", "name": "启动电机", "parentId": "act-vehicle-control-uuid"}, {"id": "node-update-speed", "type": "CallBehaviorAction", "name": "实时更新速度显示", "parentId": "act-vehicle-control-uuid"}, {"id": "node-check-battery", "type": "CallBehaviorAction", "name": "检测电池电量", "parentId": "act-vehicle-control-uuid"}, {"id": "node-detect-abnormal", "type": "CallBehaviorAction", "name": "检测系统异常", "parentId": "act-vehicle-control-uuid"}, {"id": "node-check-speed", "type": "CallBehaviorAction", "name": "检测速度", "parentId": "act-vehicle-control-uuid"}, {"id": "node-activate-brake", "type": "CallBehaviorAction", "name": "激活刹车系统", "parentId": "act-vehicle-control-uuid"}, {"id": "node-update-lights", "type": "CallBehaviorAction", "name": "状态指示灯显示", "parentId": "act-vehicle-control-uuid"}, {"id": "node-accelerate", "type": "CallBehaviorAction", "name": "系统响应加速", "parentId": "act-vehicle-control-uuid"}, {"id": "node-cut-power", "type": "CallBehaviorAction", "name": "自动切断电源", "parentId": "act-vehicle-control-uuid"}, {"id": "node-adjust-motor", "type": "CallBehaviorAction", "name": "调节电机输出", "parentId": "act-vehicle-control-uuid"}, {"id": "node-press-brake", "type": "CallBehaviorAction", "name": "踩刹车", "parentId": "act-vehicle-control-uuid"}, {"id": "node-press-pedal", "type": "CallBehaviorAction", "name": "踩踏踏板", "parentId": "act-vehicle-control-uuid"}, {"id": "node-increase-speed", "type": "CallBehaviorAction", "name": "速度逐步提升", "parentId": "act-vehicle-control-uuid"}, {"id": "obj-abnormal-info-uuid", "type": "CentralBufferNode", "name": "异常信息", "parentId": "pkg-structure-uuid", "typeId": "blk-abnormal-event-uuid"}, {"id": "obj-battery-status-uuid", "type": "CentralBufferNode", "name": "电池状态", "parentId": "pkg-structure-uuid", "typeId": "blk-battery-status-uuid"}, {"id": "obj-system-status-uuid", "type": "CentralBufferNode", "name": "系统状态", "parentId": "pkg-structure-uuid", "typeId": "blk-system-status-uuid"}, {"id": "obj-speed-info-uuid", "type": "CentralBufferNode", "name": "速度信息", "parentId": "pkg-structure-uuid", "typeId": "blk-speed-info-uuid"}, {"id": "cls-sensor-uuid", "type": "Class", "name": "传感器", "parentId": "pkg-structure-uuid"}, {"id": "cls-brake-uuid", "type": "Class", "name": "刹车系统", "parentId": "pkg-structure-uuid"}, {"id": "cls-controller-uuid", "type": "Class", "name": "控制器", "parentId": "pkg-structure-uuid"}, {"id": "cls-motor-uuid", "type": "Class", "name": "电机", "parentId": "pkg-structure-uuid"}, {"id": "cf-speed-control-alt-uuid", "type": "CombinedFragment", "name": "速度控制条件", "parentId": "pkg-structure-uuid"}, {"id": "constraint-duration", "type": "ConstraintBlock", "name": "DurationRelation", "parentId": "pkg-parametrics-uuid", "specification": {"expression": "T = E / (P / 1000)", "language": "English"}}, {"id": "constraint-maxP", "type": "ConstraintBlock", "name": "MaxPowerLimit", "parentId": "pkg-parametrics-uuid", "specification": {"expression": "P <= 500", "language": "English"}}, {"id": "constraint-maxV", "type": "ConstraintBlock", "name": "MaxSpeedLimit", "parentId": "pkg-parametrics-uuid", "specification": {"expression": "v <= 30", "language": "English"}}, {"id": "constraint-velocity", "type": "ConstraintBlock", "name": "VelocityRelation", "parentId": "pkg-parametrics-uuid", "specification": {"expression": "v = (P / m) * t", "language": "English"}}, {"id": "param-E", "type": "ConstraintParameter", "name": "E", "parentId": "constraint-duration", "typeId": "Real"}, {"id": "param-P", "type": "ConstraintParameter", "name": "P", "parentId": "constraint-velocity", "typeId": "Real"}, {"id": "param-P2", "type": "ConstraintParameter", "name": "P", "parentId": "constraint-duration", "typeId": "Real"}, {"id": "param-Pmax", "type": "ConstraintParameter", "name": "P", "parentId": "constraint-maxP", "typeId": "Real"}, {"id": "param-T", "type": "ConstraintParameter", "name": "T", "parentId": "constraint-duration", "typeId": "Real"}, {"id": "param-m", "type": "ConstraintParameter", "name": "m", "parentId": "constraint-velocity", "typeId": "Real"}, {"id": "param-t", "type": "ConstraintParameter", "name": "t", "parentId": "constraint-velocity", "typeId": "Real"}, {"id": "param-vel", "type": "ConstraintParameter", "name": "v", "parentId": "constraint-velocity", "typeId": "Real"}, {"id": "param-vmax", "type": "ConstraintParameter", "name": "v", "parentId": "constraint-maxV", "typeId": "Real"}, {"id": "rel-derive-ride-structure-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-derive-ride-environment-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-derive-ride-reliability-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-derive-structure-material-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-derive-ride-battery-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-derive-ride-performance-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-derive-ride-safety-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "enum-ctrl-uuid", "type": "Enumeration", "name": "ControlCommands", "parentId": "pkg-libraries-uuid"}, {"id": "enum-sensor-data-uuid", "type": "Enumeration", "name": "SensorDataTypes", "parentId": "pkg-libraries-uuid"}, {"id": "lit-accelerate", "type": "EnumerationLiteral", "name": "Accelerate", "parentId": "enum-ctrl-uuid"}, {"id": "lit-brake", "type": "EnumerationLiteral", "name": "<PERSON><PERSON><PERSON>", "parentId": "enum-ctrl-uuid"}, {"id": "lit-position", "type": "EnumerationLiteral", "name": "Position", "parentId": "enum-sensor-data-uuid"}, {"id": "lit-speed", "type": "EnumerationLiteral", "name": "Speed", "parentId": "enum-sensor-data-uuid"}, {"id": "lit-start", "type": "EnumerationLiteral", "name": "Start", "parentId": "enum-ctrl-uuid"}, {"id": "lit-temperature", "type": "EnumerationLiteral", "name": "Temperature", "parentId": "enum-sensor-data-uuid"}, {"id": "event-sensor-fault-uuid", "type": "Event", "name": "传感器故障或过热", "parentId": "pkg-structure-uuid"}, {"id": "event-startup-complete-uuid", "type": "Event", "name": "启动完成事件", "parentId": "pkg-structure-uuid"}, {"id": "event-brake-pressed-uuid", "type": "Event", "name": "踩刹车事件", "parentId": "pkg-structure-uuid"}, {"id": "event-speed-zero-uuid", "type": "Event", "name": "速度降至0事件", "parentId": "pkg-structure-uuid"}, {"id": "rel-004", "type": "Extend", "name": "扩展", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-006", "targetId": "usecase-007"}, {"id": "node-fork", "type": "ForkNode", "name": "并行检测与调节", "parentId": "act-vehicle-control-uuid"}, {"id": "port-controller-accelerate", "type": "FullPort", "name": "accelerateCommand", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "enum-ctrl-uuid"}, {"id": "port-controller-brake", "type": "FullPort", "name": "brakeCommand", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "enum-ctrl-uuid"}, {"id": "port-motor-pwr", "type": "FullPort", "name": "powerIn", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "if-controller-uuid"}, {"id": "port-controller-start", "type": "FullPort", "name": "startCommand", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "enum-ctrl-uuid"}, {"id": "rel-001", "type": "Include", "name": "包含", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-001", "targetId": "usecase-002"}, {"id": "rel-002", "type": "Include", "name": "包含", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-001", "targetId": "usecase-003"}, {"id": "rel-003", "type": "Include", "name": "包含", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-002", "targetId": "usecase-009"}, {"id": "node-start", "type": "InitialNode", "name": "用户按下启动按钮", "parentId": "act-vehicle-control-uuid"}, {"id": "interaction-start-uuid", "type": "Interaction", "name": "用户启动骑行", "parentId": "pkg-behavior-uuid"}, {"id": "guard-normal-uuid", "type": "InteractionConstraint", "name": "", "parentId": "pkg-structure-uuid", "specification": {"body": "速度为零或正常范围", "language": "English"}}, {"id": "guard-fault-uuid", "type": "InteractionConstraint", "name": "", "parentId": "pkg-structure-uuid", "specification": {"body": "故障报警", "language": "English"}}, {"id": "guard-brake-uuid", "type": "InteractionConstraint", "name": "", "parentId": "pkg-structure-uuid", "specification": {"body": "刹车信号激活", "language": "English"}}, {"id": "operand-normal-uuid", "type": "InteractionOperand", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "operand-fault-uuid", "type": "InteractionOperand", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "operand-brake-uuid", "type": "InteractionOperand", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "if-controller-uuid", "type": "InterfaceBlock", "name": "ControllerInterface", "parentId": "pkg-structure-uuid"}, {"id": "ll-sensor-uuid", "type": "Lifeline", "name": "传感器", "parentId": "interaction-start-uuid"}, {"id": "ll-brake-uuid", "type": "Lifeline", "name": "刹车系统", "parentId": "interaction-start-uuid"}, {"id": "ll-controller-uuid", "type": "Lifeline", "name": "控制器", "parentId": "interaction-start-uuid"}, {"id": "ll-user-uuid", "type": "Lifeline", "name": "用户", "parentId": "interaction-start-uuid"}, {"id": "ll-motor-uuid", "type": "Lifeline", "name": "电机", "parentId": "interaction-start-uuid"}, {"id": "msg-sensor-feedback-uuid", "type": "Message", "name": "传感器反馈速度", "parentId": "interaction-start-uuid", "messageSort": "signal", "sendEventId": "frag-send-sensor-feedback-uuid", "receiveEventId": "frag-recv-sensor-feedback-uuid"}, {"id": "msg-brake-signal-uuid", "type": "Message", "name": "刹车信号", "parentId": "interaction-start-uuid", "messageSort": "signal", "sendEventId": "frag-send-brake-uuid", "receiveEventId": "frag-recv-brake-uuid"}, {"id": "msg-brake-decelerate-uuid", "type": "Message", "name": "刹车减速", "parentId": "interaction-start-uuid", "messageSort": "signal", "sendEventId": "frag-send-decel-uuid", "receiveEventId": "frag-recv-decel-uuid"}, {"id": "msg-start-uuid", "type": "Message", "name": "启动命令", "parentId": "interaction-start-uuid", "visibility": "public", "propertyKind": "send", "messageSort": "synchCall", "sendEventId": "frag-send-start-uuid", "receiveEventId": "frag-recv-start-uuid"}, {"id": "msg-battery-uuid", "type": "Message", "name": "检测电池状态", "parentId": "interaction-start-uuid", "visibility": "public", "propertyKind": "signal", "messageSort": "signal", "sendEventId": "frag-send-battery-uuid", "receiveEventId": "frag-recv-battery-uuid"}, {"id": "msg-restart-uuid", "type": "Message", "name": "用户关闭或再次骑行", "parentId": "interaction-start-uuid", "visibility": "public", "propertyKind": "part", "messageSort": "signal", "sendEventId": "frag-send-restart-uuid", "receiveEventId": "frag-recv-restart-uuid"}, {"id": "msg-motor-start-uuid", "type": "Message", "name": "电机启动", "parentId": "interaction-start-uuid", "messageSort": "synchCall", "sendEventId": "frag-send-motor-start-uuid", "receiveEventId": "frag-recv-motor-start-uuid"}, {"id": "msg-stop-uuid", "type": "Message", "name": "系统停止", "parentId": "interaction-start-uuid", "messageSort": "signal", "sendEventId": "frag-send-stop-uuid", "receiveEventId": "frag-recv-stop-uuid"}, {"id": "msg-adjust-motor-uuid", "type": "Message", "name": "调节电机输出", "parentId": "interaction-start-uuid", "messageSort": "signal", "sendEventId": "frag-send-adjust-uuid", "receiveEventId": "frag-recv-adjust-uuid"}, {"id": "msg-speed-down-uuid", "type": "Message", "name": "速度下降", "parentId": "interaction-start-uuid", "messageSort": "signal", "sendEventId": "frag-send-speed-down-uuid", "receiveEventId": "frag-recv-speed-down-uuid"}, {"id": "msg-increase-speed-uuid", "type": "Message", "name": "速度逐步上升", "parentId": "interaction-start-uuid", "messageSort": "signal", "sendEventId": "frag-send-increase-uuid", "receiveEventId": "frag-recv-increase-uuid"}, {"id": "frag-send-sensor-feedback-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-sensor-feedback-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-adjust-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-adjust-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-brake-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-brake-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-decel-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-decel-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-speed-down-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-speed-down-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-stop-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-stop-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-restart-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-restart-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-speed-normal-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-fault-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-brake-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-start-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-start-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-battery-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-battery-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-motor-start-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-motor-start-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-send-increase-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "frag-recv-increase-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "pkg-libraries-uuid", "type": "Package", "name": "Libraries", "parentId": "model-unified-uuid"}, {"id": "pkg-parametrics-uuid", "type": "Package", "name": "Parametrics", "parentId": "model-unified-uuid"}, {"id": "pkg-reqs-uuid", "type": "Package", "name": "Requirements", "parentId": "model-unified-uuid"}, {"id": "pkg-behavior-uuid", "type": "Package", "name": "SystemBehavior", "parentId": "model-unified-uuid"}, {"id": "pkg-structure-uuid", "type": "Package", "name": "SystemStructure", "parentId": "model-unified-uuid"}, {"id": "pkg-usecases-uuid", "type": "Package", "name": "UseCases", "parentId": "model-unified-uuid"}, {"id": "constraint2", "type": "Property", "name": "DurationRelation", "parentId": "block-ev", "visibility": "public", "propertyKind": "constraint", "typeId": "constraint-duration"}, {"id": "prop-E", "type": "Property", "name": "E", "parentId": "block-ev", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-P", "type": "Property", "name": "P", "parentId": "block-ev", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "constraint3", "type": "Property", "name": "PowerLimit", "parentId": "block-ev", "visibility": "public", "propertyKind": "constraint", "typeId": "constraint-maxP"}, {"id": "constraint4", "type": "Property", "name": "SpeedLimit", "parentId": "block-ev", "visibility": "public", "propertyKind": "constraint", "typeId": "constraint-maxV"}, {"id": "constraint1", "type": "Property", "name": "VelocityRelation", "parentId": "block-ev", "visibility": "public", "propertyKind": "constraint", "typeId": "constraint-velocity"}, {"id": "prop-sensor-controller", "type": "Property", "name": "_controller", "parentId": "blk-sensor-uuid", "visibility": "private", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-controller-uuid"}, {"id": "prop-controller-sensor", "type": "Property", "name": "_sensor", "parentId": "blk-controller-uuid", "visibility": "private", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-sensor-uuid"}, {"id": "prop-motor-vehicle", "type": "Property", "name": "_vehicle_motor", "parentId": "blk-motor-uuid", "visibility": "private", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-vehicle-uuid"}, {"id": "prop-vehicle-battery", "type": "Property", "name": "battery", "parentId": "blk-vehicle-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-battery-uuid", "multiplicity": "[1..1]"}, {"id": "prop-vehicle-brake", "type": "Property", "name": "brake", "parentId": "blk-vehicle-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-brake-uuid", "multiplicity": "[1..1]"}, {"id": "prop-brake-force", "type": "Property", "name": "brakeForce", "parentId": "blk-brake-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "vt-real-uuid"}, {"id": "prop-battery-capacity", "type": "Property", "name": "capacity", "parentId": "blk-battery-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "vt-real-uuid"}, {"id": "prop-vehicle-controller", "type": "Property", "name": "controller", "parentId": "blk-vehicle-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-controller-uuid", "multiplicity": "[1..1]"}, {"id": "prop-wheel-diameter", "type": "Property", "name": "diameter", "parentId": "blk-wheel-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "vt-real-uuid"}, {"id": "prop-vehicle-display", "type": "Property", "name": "display", "parentId": "blk-vehicle-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-display-uuid", "multiplicity": "[1..1]"}, {"id": "prop-display-content", "type": "Property", "name": "displayContent", "parentId": "blk-display-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "vt-real-uuid"}, {"id": "prop-m", "type": "Property", "name": "m", "parentId": "block-ev", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-vehicle-motor", "type": "Property", "name": "motor", "parentId": "blk-vehicle-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-motor-uuid", "multiplicity": "[1..1]"}, {"id": "prop-user-uuid", "type": "Property", "name": "p_user", "parentId": "interaction-start-uuid", "visibility": "public", "propertyKind": "part", "typeId": "actor-user-uuid"}, {"id": "prop-vehicle-sensor", "type": "Property", "name": "sensor", "parentId": "blk-vehicle-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-sensor-uuid", "multiplicity": "[1..1]"}, {"id": "prop-sensor-data", "type": "Property", "name": "sensorData", "parentId": "blk-sensor-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "sig-sensor-data-uuid"}, {"id": "prop-controller-status", "type": "Property", "name": "status", "parentId": "blk-controller-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "enum-sensor-data-uuid"}, {"id": "prop-t", "type": "Property", "name": "t", "parentId": "block-ev", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-motor-rpm", "type": "Property", "name": "targetRPM", "parentId": "blk-motor-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "vt-real-uuid"}, {"id": "prop-v", "type": "Property", "name": "v", "parentId": "block-ev", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-vehicle-wheel", "type": "Property", "name": "wheel", "parentId": "blk-vehicle-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-wheel-uuid", "multiplicity": "[1..1]"}, {"id": "prop-status-uuid", "type": "Property", "name": "状态标志", "parentId": "cls-controller-uuid", "visibility": "public", "propertyKind": "part", "typeId": "string"}, {"id": "prop-speed-uuid", "type": "Property", "name": "速度", "parentId": "cls-controller-uuid", "visibility": "public", "propertyKind": "part", "typeId": "float"}, {"id": "port-motor-ctrl", "type": "ProxyPort", "name": "controlIn", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "sig-sensor-data-uuid"}, {"id": "port-sensor-out", "type": "ProxyPort", "name": "sensorDataOut", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "sig-sensor-data-uuid"}, {"id": "ps-idle-entry-uuid", "type": "Pseudostate", "name": "进入空闲状态", "parentId": "state-idle-uuid"}, {"id": "ps-main-initial-uuid", "type": "Pseudostate", "name": "", "parentId": "region-main-uuid"}, {"id": "region-main-uuid", "type": "Region", "name": "主操作区域", "parentId": "sm-vehicle-operation-uuid"}, {"id": "req-reliability-uuid", "type": "Requirement", "name": "可靠性要求", "parentId": "pkg-reqs-uuid", "reqId": "4", "text": "连续使用时间≥8小时，故障率≤0.5%。"}, {"id": "req-safety-priority-uuid", "type": "Requirement", "name": "安全优先", "parentId": "pkg-reqs-uuid", "reqId": "8", "text": "安全性优先，次为性能表现。"}, {"id": "req-performance-test-uuid", "type": "Requirement", "name": "性能验证", "parentId": "pkg-reqs-uuid", "reqId": "7", "text": "通过实地骑行测试验证加速、刹车响应时间≤1秒。通过环境测试验证温度范围内性能稳定。"}, {"id": "req-ride-support-uuid", "type": "Requirement", "name": "支持用户骑行", "parentId": "pkg-reqs-uuid", "reqId": "1", "text": "系统应支持用户骑行，包括启动、加速、减速和停止。具备基本的刹车控制，确保安全停车。提供车轮转速显示和状态指示灯。"}, {"id": "req-material-weight-uuid", "type": "Requirement", "name": "材料与重量", "parentId": "pkg-reqs-uuid", "reqId": "5", "text": "使用铝合金材料，最大重量不超过15公斤。"}, {"id": "req-environment-uuid", "type": "Requirement", "name": "环境适应性", "parentId": "pkg-reqs-uuid", "reqId": "3", "text": "系统应在-10°C至50°C环境下正常工作，温度变化影响不超过±2°C。"}, {"id": "req-battery-range-uuid", "type": "Requirement", "name": "续航能力", "parentId": "pkg-reqs-uuid", "reqId": "6", "text": "电源为12V锂电池，续航≥30公里。"}, {"id": "req-structure-weight-uuid", "type": "Requirement", "name": "车体结构承重", "parentId": "pkg-reqs-uuid", "reqId": "2", "text": "车体结构应能承受最大载重100公斤，误差±5公斤。"}, {"id": "rel-satisfy-ridecontrol-support-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-structure-weight-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-power-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-display-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "sig-sensor-data-uuid", "type": "Signal", "name": "SensorDataSignal", "parentId": "pkg-libraries-uuid"}, {"id": "sig-start-button-uuid", "type": "Signal", "name": "启动按钮", "parentId": "pkg-libraries-uuid"}, {"id": "event-start-button-uuid", "type": "SignalEvent", "name": "用户按启动按钮", "parentId": "pkg-structure-uuid"}, {"id": "state-stopped-uuid", "type": "State", "name": "停止", "parentId": "region-main-uuid"}, {"id": "state-braking-uuid", "type": "State", "name": "刹车", "parentId": "region-main-uuid"}, {"id": "state-starting-uuid", "type": "State", "name": "启动中", "parentId": "region-main-uuid"}, {"id": "state-fault-uuid", "type": "State", "name": "故障", "parentId": "region-main-uuid"}, {"id": "state-idle-uuid", "type": "State", "name": "空闲", "parentId": "region-main-uuid"}, {"id": "state-running-uuid", "type": "State", "name": "运行", "parentId": "region-main-uuid"}, {"id": "sm-vehicle-operation-uuid", "type": "StateMachine", "name": "车辆操作状态机", "parentId": "pkg-behavior-uuid"}, {"id": "tc-safety-test-uuid", "type": "TestCase", "name": "安全性测试", "parentId": "pkg-reqs-uuid"}, {"id": "tc-temperature-test-uuid", "type": "TestCase", "name": "温度环境测试", "parentId": "pkg-reqs-uuid"}, {"id": "tc-range-test-uuid", "type": "TestCase", "name": "续航测试", "parentId": "pkg-reqs-uuid"}, {"id": "tc-ride-performance-uuid", "type": "TestCase", "name": "骑行性能测试", "parentId": "pkg-reqs-uuid"}, {"id": "trans-initial-to-idle-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "ps-main-initial-uuid", "targetId": "state-idle-uuid"}, {"id": "trans-idle-to-starting-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-idle-uuid", "targetId": "state-starting-uuid"}, {"id": "trans-starting-to-running-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-starting-uuid", "targetId": "state-running-uuid"}, {"id": "trans-running-to-braking-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-running-uuid", "targetId": "state-braking-uuid"}, {"id": "trans-braking-to-stopped-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-braking-uuid", "targetId": "state-stopped-uuid"}, {"id": "trans-to-fault-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-idle-uuid", "targetId": "state-fault-uuid"}, {"id": "trans-to-fault-starting-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-starting-uuid", "targetId": "state-fault-uuid"}, {"id": "trans-to-fault-running-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-running-uuid", "targetId": "state-fault-uuid"}, {"id": "trans-to-fault-braking-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-braking-uuid", "targetId": "state-fault-uuid"}, {"id": "trans-to-fault-stopped-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-stopped-uuid", "targetId": "state-fault-uuid"}, {"id": "unit-percent-uuid", "type": "Unit", "name": "%", "parentId": "pkg-libraries-uuid"}, {"id": "usecase-007", "type": "UseCase", "name": "减速停车", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-003", "type": "UseCase", "name": "启动电机", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-001", "type": "UseCase", "name": "启动骑行", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-009", "type": "UseCase", "name": "提示充电", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-008", "type": "UseCase", "name": "显示同步更新", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-002", "type": "UseCase", "name": "检测电池", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-005", "type": "UseCase", "name": "调节速度", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-006", "type": "UseCase", "name": "踩刹车", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-004", "type": "UseCase", "name": "踩踏踏板", "parentId": "pkg-usecases-uuid"}, {"id": "usecase-010", "type": "UseCase", "name": "进入故障状态提示用户", "parentId": "pkg-usecases-uuid"}, {"id": "vt-real-uuid", "type": "ValueType", "name": "Real", "parentId": "pkg-libraries-uuid"}, {"id": "rel-verify-ride-support-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-verify-environment-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-verify-range-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-verify-safety-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}]}