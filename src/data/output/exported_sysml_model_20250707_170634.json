{"model": {"id": "unified-sysml-model", "name": "Unified SysML Model", "type": "Model"}, "elements": [{"id": "pkg-requirements-final", "type": "Package", "name": "Requirements", "parentId": "unified-sysml-model"}, {"id": "pkg-usecases-final", "type": "Package", "name": "UseCases", "parentId": "unified-sysml-model"}, {"id": "pkg-systemstructure-final", "type": "Package", "name": "SystemStructure", "parentId": "unified-sysml-model"}, {"id": "pkg-systembehavior-final", "type": "Package", "name": "SystemBehavior", "parentId": "unified-sysml-model"}, {"id": "pkg-libraries-final", "type": "Package", "name": "Libraries", "parentId": "unified-sysml-model"}, {"id": "act-bike-operation-uuid", "type": "Activity", "name": "BikeOperationActivity", "parentId": "pkg-systembehavior-final"}, {"id": "act-user-operation-uuid", "type": "Activity", "name": "UserOperation", "parentId": "pkg-systembehavior-final"}, {"id": "act-start-chain-uuid", "type": "Activity", "name": "启动链条", "parentId": "pkg-systembehavior-final"}, {"id": "act-activate-brake-uuid", "type": "Activity", "name": "激活刹车", "parentId": "pkg-systembehavior-final"}, {"id": "act-adjust-chain-uuid", "type": "Activity", "name": "调整链条", "parentId": "pkg-systembehavior-final"}, {"id": "node-final", "type": "ActivityFinalNode", "name": "流程结束", "parentId": "pkg-structure-uuid"}, {"id": "grp-control-uuid", "type": "ActivityPartition", "name": "控制系统", "parentId": "pkg-structure-uuid"}, {"id": "grp-mech-uuid", "type": "ActivityPartition", "name": "机械系统", "parentId": "pkg-structure-uuid"}, {"id": "grp-user-uuid", "type": "ActivityPartition", "name": "用户", "parentId": "pkg-structure-uuid"}, {"id": "actor-user-uuid", "type": "Actor", "name": "用户", "parentId": "pkg-usecases-final"}, {"id": "actor-001", "type": "Actor", "name": "用户（骑行者）", "parentId": "pkg-usecases-final"}, {"id": "actor-002", "type": "Actor", "name": "系统（自行车机械与电子部分）", "parentId": "pkg-usecases-final"}, {"id": "conn-drivesys-wheel", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "conn-steering-handlebar", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "conn-brake-line", "type": "AssemblyConnector", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "assoc-frame-brake", "type": "Association", "name": "Frame-BrakeAssociation", "parentId": "pkg-systemstructure-final"}, {"id": "assoc-frame-drivesys", "type": "Association", "name": "Frame-DriveSystem Association", "parentId": "pkg-systemstructure-final"}, {"id": "assoc-frame-electrical", "type": "Association", "name": "Frame-Electrical Association", "parentId": "pkg-systemstructure-final"}, {"id": "assoc-frame-gearbox", "type": "Association", "name": "Frame-Gearbox Association", "parentId": "pkg-systemstructure-final"}, {"id": "assoc-frame-steering", "type": "Association", "name": "Frame-Steering Association", "parentId": "pkg-systemstructure-final"}, {"id": "assoc-frame-wheel-front", "type": "Association", "name": "Frame-WheelFront Association", "parentId": "pkg-systemstructure-final"}, {"id": "assoc-frame-wheel-rear", "type": "Association", "name": "Frame-WheelRear Association", "parentId": "pkg-systemstructure-final"}, {"id": "assoc-001", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-001", "targetId": "usecase-001"}, {"id": "assoc-002", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-001", "targetId": "usecase-002"}, {"id": "assoc-003", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-001", "targetId": "usecase-003"}, {"id": "assoc-004", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-001", "targetId": "usecase-004"}, {"id": "assoc-005", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-001", "targetId": "usecase-005"}, {"id": "assoc-006", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-002", "targetId": "usecase-001"}, {"id": "assoc-007", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-002", "targetId": "usecase-002"}, {"id": "assoc-008", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-002", "targetId": "usecase-003"}, {"id": "assoc-009", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-002", "targetId": "usecase-004"}, {"id": "assoc-010", "type": "Association", "name": "", "parentId": "pkg-systemstructure-final", "sourceId": "actor-002", "targetId": "usecase-005"}, {"id": "conn-electrical-power", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "conn-electrical-sensor", "type": "BindingConnector", "name": "", "parentId": "pkg-parametrics-uuid"}, {"id": "blk-bike-sys-uuid", "type": "Block", "name": "BikeControlSystem", "parentId": "pkg-systemstructure-final"}, {"id": "blk-brake-activation-uuid", "type": "Block", "name": "BrakeActivation", "parentId": "pkg-systemstructure-final"}, {"id": "blk-braking-uuid", "type": "Block", "name": "BrakingSystem", "parentId": "pkg-systemstructure-final"}, {"id": "blk-drivesystem-uuid", "type": "Block", "name": "DriveSystem", "parentId": "pkg-systemstructure-final"}, {"id": "blk-electrical-uuid", "type": "Block", "name": "ElectricalSystem", "parentId": "pkg-systemstructure-final"}, {"id": "blk-frame-uuid", "type": "Block", "name": "<PERSON>ame", "parentId": "pkg-systemstructure-final"}, {"id": "blk-gear-position-uuid", "type": "Block", "name": "GearPosition", "parentId": "pkg-systemstructure-final"}, {"id": "blk-gearbox-uuid", "type": "Block", "name": "Gearbox", "parentId": "pkg-systemstructure-final"}, {"id": "blk-handlebar-uuid", "type": "Block", "name": "Handlebar", "parentId": "pkg-systemstructure-final"}, {"id": "blk-handlebar-position-uuid", "type": "Block", "name": "HandlebarPosition", "parentId": "pkg-systemstructure-final"}, {"id": "blk-mech-parts-uuid", "type": "Block", "name": "MechanicalParts", "parentId": "pkg-systemstructure-final"}, {"id": "blk-pedal-force-uuid", "type": "Block", "name": "PedalForce", "parentId": "pkg-systemstructure-final"}, {"id": "blk-steering-uuid", "type": "Block", "name": "Steering", "parentId": "pkg-systemstructure-final"}, {"id": "blk-system-state-uuid", "type": "Block", "name": "SystemState", "parentId": "pkg-systemstructure-final"}, {"id": "blk-user-uuid", "type": "Block", "name": "User", "parentId": "pkg-systemstructure-final"}, {"id": "block-vehicle", "type": "Block", "name": "VehicleSystem", "parentId": "pkg-systemstructure-final"}, {"id": "blk-wheel-uuid", "type": "Block", "name": "Wheel", "parentId": "pkg-systemstructure-final"}, {"id": "blk-wheel-rotation-uuid", "type": "Block", "name": "WheelRotation", "parentId": "pkg-systemstructure-final"}, {"id": "blk-brake-uuid", "type": "Block", "name": "刹车系统", "parentId": "pkg-systemstructure-final"}, {"id": "blk-shift-uuid", "type": "Block", "name": "变速机构", "parentId": "pkg-systemstructure-final"}, {"id": "blk-mech-uuid", "type": "Block", "name": "机械结构", "parentId": "pkg-systemstructure-final"}, {"id": "blk-elec-uuid", "type": "Block", "name": "电气系统", "parentId": "pkg-systemstructure-final"}, {"id": "blk-bike-controller-uuid", "type": "Block", "name": "自行车控制器", "parentId": "pkg-systemstructure-final"}, {"id": "node-pull-brake", "type": "CallBehaviorAction", "name": "拉动刹车手柄", "parentId": "act-bike-operation-uuid"}, {"id": "node-detect-shift", "type": "CallBehaviorAction", "name": "检测变速杆位置", "parentId": "act-bike-operation-uuid"}, {"id": "node-activate-brake", "type": "CallBehaviorAction", "name": "激活刹车块", "parentId": "act-bike-operation-uuid"}, {"id": "node-stop-pedaling", "type": "CallBehaviorAction", "name": "用户停止踩踏", "parentId": "act-bike-operation-uuid"}, {"id": "node-user-adjust", "type": "CallBehaviorAction", "name": "用户通过手柄调整转向和刹车", "parentId": "act-bike-operation-uuid"}, {"id": "node-stationary", "type": "CallBehaviorAction", "name": "系统进入静止状态", "parentId": "act-bike-operation-uuid"}, {"id": "node-adjust-front-wheel", "type": "CallBehaviorAction", "name": "调整前轮方向", "parentId": "act-bike-operation-uuid"}, {"id": "node-shift-gear", "type": "CallBehaviorAction", "name": "调整链条实现档位切换", "parentId": "act-bike-operation-uuid"}, {"id": "node-pedal", "type": "CallBehaviorAction", "name": "踩踏链条", "parentId": "act-bike-operation-uuid"}, {"id": "node-turn-handlebars", "type": "CallBehaviorAction", "name": "转动车把", "parentId": "act-bike-operation-uuid"}, {"id": "node-drive-chain", "type": "CallBehaviorAction", "name": "驱动链条", "parentId": "act-bike-operation-uuid"}, {"id": "node-buffer-brake", "type": "CentralBufferNode", "name": "刹车缓冲区", "parentId": "pkg-structure-uuid", "typeId": "blk-brake-activation-uuid"}, {"id": "node-buffer-gear", "type": "CentralBufferNode", "name": "档位状态缓冲区", "parentId": "pkg-structure-uuid", "typeId": "blk-gear-position-uuid"}, {"id": "node-buffer-shift", "type": "CentralBufferNode", "name": "档位缓冲区", "parentId": "pkg-structure-uuid", "typeId": "blk-gear-position-uuid"}, {"id": "node-buffer-system", "type": "CentralBufferNode", "name": "系统状态缓冲区", "parentId": "pkg-structure-uuid", "typeId": "blk-system-state-uuid"}, {"id": "node-buffer-wheel", "type": "CentralBufferNode", "name": "轮子旋转缓冲区", "parentId": "pkg-structure-uuid", "typeId": "blk-wheel-rotation-uuid"}, {"id": "node-buffer-chain", "type": "CentralBufferNode", "name": "链条缓冲区", "parentId": "pkg-structure-uuid", "typeId": "blk-pedal-force-uuid"}, {"id": "cls-brake-system-uuid", "type": "Class", "name": "刹车系统", "parentId": "pkg-structure-uuid"}, {"id": "cls-pressure-sensor-uuid", "type": "Class", "name": "压力传感器", "parentId": "pkg-structure-uuid"}, {"id": "cls-shift-mechanism-uuid", "type": "Class", "name": "变速机构", "parentId": "pkg-structure-uuid"}, {"id": "cls-display-uuid", "type": "Class", "name": "显示屏", "parentId": "pkg-structure-uuid"}, {"id": "cls-bicycle-uuid", "type": "Class", "name": "自行车", "parentId": "pkg-structure-uuid"}, {"id": "cls-steering-control-uuid", "type": "Class", "name": "转向控制", "parentId": "pkg-structure-uuid"}, {"id": "cls-chain-drive-uuid", "type": "Class", "name": "链条传动系统", "parentId": "pkg-structure-uuid"}, {"id": "cf-brake-release-uuid", "type": "CombinedFragment", "name": "刹车释放", "parentId": "pkg-structure-uuid"}, {"id": "constraint-gear", "type": "ConstraintBlock", "name": "GearLengthRelation", "parentId": "pkg-libraries-final", "specification": {"expression": "GearLength = BaseLength + (GearPosition - 1) * Increment", "language": "English"}}, {"id": "constraint-relationships", "type": "ConstraintBlock", "name": "ParameterRelations", "parentId": "pkg-libraries-final", "specification": {"expression": "V = (π * D) * N / 60; GearLength = BaseLength + (GearPosition - 1) * Increment", "language": "English"}}, {"id": "constraint-performance", "type": "ConstraintBlock", "name": "PerformanceConstraints", "parentId": "pkg-libraries-final", "specification": {"expression": "V_max ≥ 25 km/h, R_min ≥ 2m, T_response ≤ 0.2s, W ≤ 200kg", "language": "English"}}, {"id": "constraint-speed", "type": "ConstraintBlock", "name": "SpeedCalculation", "parentId": "pkg-libraries-final", "specification": {"expression": "V = (π * D) * N / 60", "language": "English"}}, {"id": "param-base-length", "type": "ConstraintParameter", "name": "BaseLength", "parentId": "constraint-gear", "typeId": "Real"}, {"id": "param-D", "type": "ConstraintParameter", "name": "D", "parentId": "constraint-speed", "typeId": "Real"}, {"id": "param-gear-length", "type": "ConstraintParameter", "name": "Gear<PERSON>ength", "parentId": "constraint-gear", "typeId": "Real"}, {"id": "param-gear-position", "type": "ConstraintParameter", "name": "GearPosition", "parentId": "constraint-gear", "typeId": "Real"}, {"id": "param-N", "type": "ConstraintParameter", "name": "N", "parentId": "constraint-speed", "typeId": "Real"}, {"id": "param-R_min", "type": "ConstraintParameter", "name": "R_min", "parentId": "constraint-performance", "typeId": "Real"}, {"id": "param-T_response", "type": "ConstraintParameter", "name": "T_response", "parentId": "constraint-performance", "typeId": "Real"}, {"id": "param-V", "type": "ConstraintParameter", "name": "V", "parentId": "constraint-speed", "typeId": "Real"}, {"id": "param-V_max", "type": "ConstraintParameter", "name": "V_max", "parentId": "constraint-performance", "typeId": "Real"}, {"id": "param-W_max", "type": "ConstraintParameter", "name": "W_max", "parentId": "constraint-performance", "typeId": "Real"}, {"id": "node-decision", "type": "DecisionNode", "name": "档位切换判断", "parentId": "act-bike-operation-uuid"}, {"id": "rel-derive-weight-frame-uuid", "type": "DeriveReqt", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "fragment-destroy-pressure-uuid", "type": "DestructionOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "enum-brake-uuid", "type": "Enumeration", "name": "BrakeState", "parentId": "pkg-libraries-final"}, {"id": "enum-gear-uuid", "type": "Enumeration", "name": "GearPosition", "parentId": "pkg-libraries-final"}, {"id": "lit-fs-high", "type": "EnumerationLiteral", "name": "High", "parentId": "pkg-libraries-uuid"}, {"id": "lit-fs-low", "type": "EnumerationLiteral", "name": "Low", "parentId": "pkg-libraries-uuid"}, {"id": "lit-fs-med", "type": "EnumerationLiteral", "name": "Medium", "parentId": "pkg-libraries-uuid"}, {"id": "lit-fs-off", "type": "EnumerationLiteral", "name": "Off", "parentId": "pkg-libraries-uuid"}, {"id": "lit-ircmd-pwt", "type": "EnumerationLiteral", "name": "PowerToggle", "parentId": "pkg-libraries-uuid"}, {"id": "lit-ircmd-sdn", "type": "EnumerationLiteral", "name": "SpeedDown", "parentId": "pkg-libraries-uuid"}, {"id": "lit-ircmd-sup", "type": "EnumerationLiteral", "name": "SpeedUp", "parentId": "pkg-libraries-uuid"}, {"id": "extend-001", "type": "Extend", "name": "Extend关系", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-003", "targetId": "usecase-011"}, {"id": "extend-002", "type": "Extend", "name": "扩展用例", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-004", "targetId": "usecase-012"}, {"id": "node-fork", "type": "ForkNode", "name": "分发操作", "parentId": "act-bike-operation-uuid"}, {"id": "port-motor-pwrin", "type": "FullPort", "name": "motorPowerIn", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "blk-acpower-uuid"}, {"id": "port-fan-powerin", "type": "FullPort", "name": "powerIn", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "blk-acpower-uuid"}, {"id": "include-002", "type": "Include", "name": "Include", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-002", "targetId": "usecase-007"}, {"id": "include-003", "type": "Include", "name": "Include", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-003", "targetId": "usecase-008"}, {"id": "include-004", "type": "Include", "name": "Include", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-004", "targetId": "usecase-009"}, {"id": "include-005", "type": "Include", "name": "Include关系", "parentId": "pkg-usecases-uuid", "sourceId": "usecase-005", "targetId": "usecase-010"}, {"id": "include-001", "type": "Include", "name": "Include骑行用例", "parentId": "pkg-usecases-uuid", "visibility": "public", "sourceId": "usecase-001", "targetId": "usecase-006"}, {"id": "node-start", "type": "InitialNode", "name": "用户开始踩踏", "parentId": "act-bike-operation-uuid"}, {"id": "interaction-user-ops-uuid", "type": "Interaction", "name": "用户操作流程", "parentId": "pkg-systembehavior-final"}, {"id": "guard-brake-active-uuid", "type": "InteractionConstraint", "name": "", "parentId": "pkg-structure-uuid", "specification": {"body": "刹车激活", "language": "English"}}, {"id": "operand-brake-engaged-uuid", "type": "InteractionOperand", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "operand-brake-released-uuid", "type": "InteractionOperand", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "node-join", "type": "JoinNode", "name": "等待操作完成", "parentId": "act-bike-operation-uuid"}, {"id": "ll-brake-uuid", "type": "Lifeline", "name": "刹车系统", "parentId": "interaction-user-ops-uuid"}, {"id": "ll-pressure-sensor-uuid", "type": "Lifeline", "name": "压力传感器", "parentId": "interaction-user-ops-uuid"}, {"id": "ll-shift-uuid", "type": "Lifeline", "name": "变速机构", "parentId": "interaction-user-ops-uuid"}, {"id": "ll-display-uuid", "type": "Lifeline", "name": "显示屏", "parentId": "interaction-user-ops-uuid"}, {"id": "ll-user-uuid", "type": "Lifeline", "name": "用户", "parentId": "interaction-user-ops-uuid"}, {"id": "ll-bicycle-uuid", "type": "Lifeline", "name": "自行车", "parentId": "interaction-user-ops-uuid"}, {"id": "ll-steering-uuid", "type": "Lifeline", "name": "转向控制", "parentId": "interaction-user-ops-uuid"}, {"id": "ll-chain-drive-uuid", "type": "Lifeline", "name": "链条传动系统", "parentId": "interaction-user-ops-uuid"}, {"id": "node-merge", "type": "MergeNode", "name": "合并路径", "parentId": "act-bike-operation-uuid"}, {"id": "msg-feedback-uuid", "type": "Message", "name": "反馈速度和档位", "parentId": "interaction-user-ops-uuid", "messageSort": "reply", "sendEventId": "send-feedback-event-uuid", "receiveEventId": "recv-feedback-event-uuid"}, {"id": "msg-start-chain-uuid", "type": "Message", "name": "启动链条传动", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-start-chain-event-uuid", "receiveEventId": "recv-start-chain-event-uuid"}, {"id": "msg-pull-brake-uuid", "type": "Message", "name": "拉刹车手柄", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-pull-brake-event-uuid", "receiveEventId": "recv-pull-brake-event-uuid"}, {"id": "msg-control-steering-uuid", "type": "Message", "name": "控制转向", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-control-steering-event-uuid", "receiveEventId": "recv-control-steering-event-uuid"}, {"id": "msg-shift-gear-uuid", "type": "Message", "name": "操作变速杆", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-shift-gear-event-uuid", "receiveEventId": "recv-shift-gear-event-uuid"}, {"id": "msg-release-brake-uuid", "type": "Message", "name": "松开刹车", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-release-brake-event-uuid", "receiveEventId": "recv-release-brake-event-uuid"}, {"id": "msg-detect-angle-uuid", "type": "Message", "name": "检测角度变化", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-detect-angle-event-uuid", "receiveEventId": "recv-detect-angle-event-uuid"}, {"id": "msg-activate-brake-uuid", "type": "Message", "name": "激活刹车块", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-activate-brake-event-uuid", "receiveEventId": "recv-activate-brake-event-uuid"}, {"id": "msg-adjust-chain-uuid", "type": "Message", "name": "调整链条位置", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-adjust-chain-event-uuid", "receiveEventId": "recv-adjust-chain-event-uuid"}, {"id": "msg-press-pedal-uuid", "type": "Message", "name": "踩踏踏板", "parentId": "interaction-user-ops-uuid", "messageSort": "synchCall", "sendEventId": "send-press-pedal-event-uuid", "receiveEventId": "recv-press-pedal-event-uuid"}, {"id": "send-press-pedal-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-press-pedal-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-start-chain-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-start-chain-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-detect-angle-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-detect-angle-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-control-steering-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-control-steering-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-pull-brake-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-pull-brake-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-activate-brake-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-activate-brake-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-shift-gear-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-shift-gear-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-adjust-chain-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-adjust-chain-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-feedback-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-feedback-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "send-release-brake-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "recv-release-brake-event-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "fragment-brake-activate-uuid", "type": "MessageOccurrenceSpecification", "name": "", "parentId": "pkg-structure-uuid"}, {"id": "op-brake-change", "type": "Operation", "name": "changeBrakeState", "parentId": "blk-braking-uuid", "visibility": "public"}, {"id": "op-brake-signal", "type": "Operation", "name": "sendBrakeSignal", "parentId": "sig-brake-uuid", "visibility": "public"}, {"id": "op-remote-sendcmd", "type": "Operation", "name": "sendCommand", "parentId": "pkg-structure-uuid", "visibility": "public"}, {"id": "op-power-signal", "type": "Operation", "name": "sendPowerSignal", "parentId": "sig-power-uuid", "visibility": "public"}, {"id": "op-steering-signal", "type": "Operation", "name": "sendSteeringSignal", "parentId": "sig-steering-uuid", "visibility": "public"}, {"id": "op-gearbox-set", "type": "Operation", "name": "setGearPosition", "parentId": "blk-gearbox-uuid", "visibility": "public"}, {"id": "op-fan-setspeed", "type": "Operation", "name": "setSpeedLevel", "parentId": "pkg-structure-uuid", "visibility": "public"}, {"id": "op-steering", "type": "Operation", "name": "steer", "parentId": "blk-steering-uuid", "visibility": "public"}, {"id": "pkg-libraries-generated", "type": "Package", "name": "Libraries", "parentId": "unified-sysml-model"}, {"id": "pkg-libraries-uuid", "type": "Package", "name": "Libraries", "parentId": "unified-sysml-model"}, {"id": "pkg-parametrics-uuid", "type": "Package", "name": "Parametrics", "parentId": "unified-sysml-model"}, {"id": "pkg-reqs-uuid", "type": "Package", "name": "Requirements", "parentId": "unified-sysml-model"}, {"id": "pkg-requirements-generated", "type": "Package", "name": "Requirements", "parentId": "unified-sysml-model"}, {"id": "pkg-systembehavior-generated", "type": "Package", "name": "SystemBehavior", "parentId": "unified-sysml-model"}, {"id": "pkg-behavior-uuid", "type": "Package", "name": "SystemBehavior", "parentId": "unified-sysml-model"}, {"id": "pkg-structure-uuid", "type": "Package", "name": "SystemStructure", "parentId": "unified-sysml-model"}, {"id": "pkg-systemstructure-generated", "type": "Package", "name": "SystemStructure", "parentId": "unified-sysml-model"}, {"id": "pkg-usecases-generated", "type": "Package", "name": "UseCases", "parentId": "unified-sysml-model"}, {"id": "pkg-usecases-uuid", "type": "Package", "name": "UseCases", "parentId": "unified-sysml-model"}, {"id": "prop-D", "type": "Property", "name": "D", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-F", "type": "Property", "name": "F", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-gear1", "type": "Property", "name": "Gear1_Length", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-gear3", "type": "Property", "name": "Gear3_Length", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-L", "type": "Property", "name": "L", "parentId": "block-vehicle", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-M", "type": "Property", "name": "M", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-N", "type": "Property", "name": "N", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-R_min", "type": "Property", "name": "R_min", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-T", "type": "Property", "name": "T", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-T_response", "type": "Property", "name": "T_response", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-V", "type": "Property", "name": "V", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-V_max", "type": "Property", "name": "V_max", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-W", "type": "Property", "name": "W", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-W_max", "type": "Property", "name": "W_max", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-WheelCircumference", "type": "Property", "name": "WheelCircumference", "parentId": "block-vehicle", "visibility": "public", "propertyKind": "value", "typeId": "Real"}, {"id": "prop-motor-fan", "type": "Property", "name": "_fan_motor", "parentId": "pkg-structure-uuid", "visibility": "private", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-fan-uuid"}, {"id": "prop-irrecv-fan", "type": "Property", "name": "_fan_recv", "parentId": "pkg-structure-uuid", "visibility": "private", "propertyKind": "reference", "aggregation": "none", "typeId": "blk-fan-uuid"}, {"id": "prop-electrical-battery", "type": "Property", "name": "batteryLevel", "parentId": "blk-electrical-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "vt-battery-uuid"}, {"id": "prop-brake-block", "type": "Property", "name": "brakeBlock", "parentId": "blk-braking-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-brake-cable", "type": "Property", "name": "brakeCable", "parentId": "blk-braking-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-handlebar-brake", "type": "Property", "name": "brakeLever", "parentId": "blk-handlebar-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-brake-state", "type": "Property", "name": "brakeState", "parentId": "blk-braking-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "enum-brake-uuid"}, {"id": "prop-frame-brake", "type": "Property", "name": "brakingSystem", "parentId": "blk-frame-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-braking-uuid", "multiplicity": "[1..1]"}, {"id": "prop-drivesys-chain", "type": "Property", "name": "chain", "parentId": "blk-drivesystem-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-handlebar-lever", "type": "Property", "name": "controlLever", "parentId": "blk-handlebar-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-frame-drivesys", "type": "Property", "name": "driveSystem", "parentId": "blk-frame-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-drivesystem-uuid", "multiplicity": "[1..1]"}, {"id": "prop-frame-electrical", "type": "Property", "name": "electricalSystem", "parentId": "blk-frame-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-electrical-uuid", "multiplicity": "[1..1]"}, {"id": "prop-drivesys-flywheel", "type": "Property", "name": "flywheel", "parentId": "blk-drivesystem-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-frame-frontwheel", "type": "Property", "name": "frontWheel", "parentId": "blk-frame-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-wheel-uuid", "multiplicity": "[1..1]"}, {"id": "prop-gearbox-position", "type": "Property", "name": "gearPosition", "parentId": "blk-gearbox-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "enum-gear-uuid"}, {"id": "prop-frame-gearbox", "type": "Property", "name": "gearbox", "parentId": "blk-frame-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-gearbox-uuid", "multiplicity": "[1..1]"}, {"id": "prop-steering-handlebar", "type": "Property", "name": "handlebar", "parentId": "blk-steering-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-electrical-light", "type": "Property", "name": "light", "parentId": "blk-electrical-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-user-uuid", "type": "Property", "name": "p_user", "parentId": "interaction-user-ops-uuid", "visibility": "public", "propertyKind": "part", "typeId": "actor-user-uuid"}, {"id": "prop-drivesys-pedal", "type": "Property", "name": "pedal", "parentId": "blk-drivesystem-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-frame-rearwheel", "type": "Property", "name": "rearWheel", "parentId": "blk-frame-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-wheel-uuid", "multiplicity": "[1..1]"}, {"id": "prop-electrical-sensor", "type": "Property", "name": "sensor", "parentId": "blk-electrical-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-frame-steering", "type": "Property", "name": "steering", "parentId": "blk-frame-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "blk-steering-uuid", "multiplicity": "[1..1]"}, {"id": "prop-steering-angle", "type": "Property", "name": "steeringAngle", "parentId": "blk-steering-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "vt-angle-uuid"}, {"id": "prop-steering-rod", "type": "Property", "name": "steeringRod", "parentId": "blk-steering-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-wheel-hub", "type": "Property", "name": "supportHub", "parentId": "blk-wheel-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "None"}, {"id": "prop-wheel-tire", "type": "Property", "name": "supportTire", "parentId": "blk-wheel-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "None"}, {"id": "prop-motor-rpm", "type": "Property", "name": "targetRPM", "parentId": "pkg-structure-uuid", "visibility": "public", "propertyKind": "value", "aggregation": "none", "typeId": "Integer"}, {"id": "prop-gearbox-tensioner", "type": "Property", "name": "tensioner", "parentId": "blk-gearbox-uuid", "visibility": "public", "propertyKind": "part", "aggregation": "composite", "typeId": "None"}, {"id": "prop-pressure-uuid", "type": "Property", "name": "压力传感器", "parentId": "interaction-user-ops-uuid", "visibility": "public", "propertyKind": "part", "typeId": "cls-pressure-sensor-uuid"}, {"id": "port-irrecv-cmdout", "type": "ProxyPort", "name": "commandOut", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "sig-i<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"id": "port-motor-ctrlin", "type": "ProxyPort", "name": "controlIn", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "sig-i<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}, {"id": "port-fan-statusdisp", "type": "ProxyPort", "name": "statusDisplay", "parentId": "pkg-structure-uuid", "visibility": "public", "typeId": "if-statusdisp-uuid"}, {"id": "ps-main-initial-uuid", "type": "Pseudostate", "name": "", "parentId": "region-main-uuid"}, {"id": "recp-fan-handlesig", "type": "Reception", "name": "handleIRCommand", "parentId": "pkg-structure-uuid", "visibility": "public"}, {"id": "region-main-uuid", "type": "Region", "name": "主操作区域", "parentId": "sm-bike-uuid"}, {"id": "req-priority-uuid", "type": "Requirement", "name": "优先级", "parentId": "pkg-requirements-final", "reqId": "REQ-013", "text": "基础行驶功能优先，次要为变速和灯光"}, {"id": "req-brake-response-uuid", "type": "Requirement", "name": "制动响应时间", "parentId": "pkg-requirements-final", "reqId": "REQ-008", "text": "制动响应时间不超过0.2秒"}, {"id": "req-brake-uuid", "type": "Requirement", "name": "刹车系统", "parentId": "pkg-requirements-final", "reqId": "REQ-003", "text": "具有刹车系统，支持前后轮制动"}, {"id": "req-frontrear-drive-uuid", "type": "Requirement", "name": "前后轮驱动", "parentId": "pkg-requirements-final", "reqId": "REQ-001", "text": "系统应能实现前后轮驱动，支持人力踩踏驱动自行车前行"}, {"id": "req-shift-uuid", "type": "Requirement", "name": "变速功能", "parentId": "pkg-requirements-final", "reqId": "REQ-002", "text": "具备变速功能，支持至少3个档位（低速、中速、高速）"}, {"id": "req-mech-conn-uuid", "type": "Requirement", "name": "机械连接标准", "parentId": "pkg-requirements-final", "reqId": "REQ-009", "text": "所有机械连接必须符合标准尺寸（如螺栓孔径为10mm）"}, {"id": "req-electrical-uuid", "type": "Requirement", "name": "电气安全", "parentId": "pkg-requirements-final", "reqId": "REQ-010", "text": "电气部分（如灯光）必须符合安全规范"}, {"id": "req-weight-uuid", "type": "Requirement", "name": "结构重量", "parentId": "pkg-requirements-final", "reqId": "REQ-005", "text": "结构重量不超过15公斤，确保便携性"}, {"id": "req-roadtest-uuid", "type": "Requirement", "name": "行驶测试", "parentId": "pkg-requirements-final", "reqId": "REQ-012", "text": "通过行驶测试验证变速和刹车性能"}, {"id": "req-frame-uuid", "type": "Requirement", "name": "车架材料", "parentId": "pkg-requirements-final", "reqId": "REQ-007", "text": "车架材料为铝合金，强度满足承载200kg的要求"}, {"id": "req-steering-uuid", "type": "Requirement", "name": "转向控制", "parentId": "pkg-requirements-final", "reqId": "REQ-004", "text": "提供车把转向控制，支持左右转向"}, {"id": "req-wheel-uuid", "type": "Requirement", "name": "轮径和轮胎宽度", "parentId": "pkg-requirements-final", "reqId": "REQ-006", "text": "轮径为700mm，轮胎宽度为25mm，适应城市道路"}, {"id": "req-load-test-uuid", "type": "Requirement", "name": "载重测试", "parentId": "pkg-requirements-final", "reqId": "REQ-011", "text": "通过载重测试验证承载能力"}, {"id": "rel-satisfy-mech-frontrear-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-shift-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-brake-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-steering-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-weight-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-wheel-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-frame-material-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-satisfy-elec-safety-uuid", "type": "<PERSON><PERSON><PERSON>", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "sig-brake-uuid", "type": "Signal", "name": "BrakeSignal", "parentId": "pkg-libraries-final"}, {"id": "sig-power-uuid", "type": "Signal", "name": "PowerSignal", "parentId": "pkg-libraries-final"}, {"id": "sig-steering-uuid", "type": "Signal", "name": "SteeringSignal", "parentId": "pkg-libraries-final"}, {"id": "state-accelerating-uuid", "type": "State", "name": "Accelerating", "parentId": "region-main-uuid"}, {"id": "state-changinggear-uuid", "type": "State", "name": "ChangingGear", "parentId": "region-main-uuid"}, {"id": "state-decelerating-uuid", "type": "State", "name": "Decelerating", "parentId": "region-main-uuid"}, {"id": "state-idle-uuid", "type": "State", "name": "Idle", "parentId": "region-main-uuid"}, {"id": "state-turning-uuid", "type": "State", "name": "Turning", "parentId": "region-main-uuid"}, {"id": "sm-bike-uuid", "type": "StateMachine", "name": "自行车状态机", "parentId": "pkg-systembehavior-final"}, {"id": "tc-brake-resp-uuid", "type": "TestCase", "name": "刹车响应测试", "parentId": "pkg-requirements-final"}, {"id": "tc-shift-test-uuid", "type": "TestCase", "name": "变速测试", "parentId": "pkg-requirements-final"}, {"id": "tc-light-safety-uuid", "type": "TestCase", "name": "灯光安全测试", "parentId": "pkg-requirements-final"}, {"id": "tc-roadtest-uuid", "type": "TestCase", "name": "行驶性能测试", "parentId": "pkg-requirements-final"}, {"id": "tc-loadtest-uuid", "type": "TestCase", "name": "载重测试", "parentId": "pkg-requirements-final"}, {"id": "trans-idle-to-accelerating-uuid", "type": "Transition", "name": "Idle到Accelerating的转换", "parentId": "region-main-uuid", "sourceId": "ps-main-initial-uuid", "targetId": "state-accelerating-uuid"}, {"id": "trans-any-to-changinggear-uuid-4", "type": "Transition", "name": "TurningToChangingGear", "parentId": "region-main-uuid", "sourceId": "state-turning-uuid", "targetId": "state-changinggear-uuid"}, {"id": "trans-accelerating-to-decelerating-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-accelerating-uuid", "targetId": "state-decelerating-uuid"}, {"id": "trans-accelerating-to-turning-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-accelerating-uuid", "targetId": "state-turning-uuid"}, {"id": "trans-any-to-changinggear-uuid", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-idle-uuid", "targetId": "state-changinggear-uuid"}, {"id": "trans-any-to-changinggear-uuid-2", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-accelerating-uuid", "targetId": "state-changinggear-uuid"}, {"id": "trans-any-to-changinggear-uuid-3", "type": "Transition", "name": "", "parentId": "region-main-uuid", "sourceId": "state-decelerating-uuid", "targetId": "state-changinggear-uuid"}, {"id": "unit-ampere-uuid", "type": "Unit", "name": "A", "parentId": "pkg-libraries-uuid"}, {"id": "unit-degree-uuid", "type": "Unit", "name": "Degree", "parentId": "pkg-libraries-uuid"}, {"id": "unit-volt-uuid", "type": "Unit", "name": "V", "parentId": "pkg-libraries-uuid"}, {"id": "usecase-010", "type": "UseCase", "name": "停止踩踏", "parentId": "pkg-usecases-final"}, {"id": "usecase-005", "type": "UseCase", "name": "停车", "parentId": "pkg-usecases-final"}, {"id": "usecase-003", "type": "UseCase", "name": "刹车", "parentId": "pkg-usecases-final"}, {"id": "usecase-004", "type": "UseCase", "name": "变速", "parentId": "pkg-usecases-final"}, {"id": "usecase-012", "type": "UseCase", "name": "变速成功后继续行驶", "parentId": "pkg-usecases-final"}, {"id": "usecase-008", "type": "UseCase", "name": "拉刹车", "parentId": "pkg-usecases-final"}, {"id": "usecase-009", "type": "UseCase", "name": "操作变速杆", "parentId": "pkg-usecases-final"}, {"id": "usecase-006", "type": "UseCase", "name": "踩踏", "parentId": "pkg-usecases-final"}, {"id": "usecase-007", "type": "UseCase", "name": "转动车把", "parentId": "pkg-usecases-final"}, {"id": "usecase-002", "type": "UseCase", "name": "转向", "parentId": "pkg-usecases-final"}, {"id": "usecase-011", "type": "UseCase", "name": "遇障碍物时拉刹车", "parentId": "pkg-usecases-final"}, {"id": "usecase-001", "type": "UseCase", "name": "骑行", "parentId": "pkg-usecases-final"}, {"id": "vt-battery-uuid", "type": "ValueType", "name": "BatteryLevel", "parentId": "pkg-libraries-final"}, {"id": "vt-angle-uuid", "type": "ValueType", "name": "SteeringAngle", "parentId": "pkg-libraries-final"}, {"id": "rel-verify-load-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-verify-roadtest-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-verify-shift-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-verify-brake-resp-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}, {"id": "rel-verify-electrical-uuid", "type": "Verify", "name": "", "parentId": "pkg-reqs-uuid"}]}