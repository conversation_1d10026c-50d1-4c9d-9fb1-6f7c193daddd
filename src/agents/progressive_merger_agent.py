#!/usr/bin/env python3
"""
渐进式实体合并Agent - 实现基于LLM+Neo4j的渐进式JSON合并策略
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from src.agents.neo4j_storage_agent import Neo4jStorageAgent
from config.settings import settings

logger = logging.getLogger(__name__)

class ProgressiveMergerAgent:
    """渐进式实体合并Agent"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )
        self.storage_agent = None
        
    def process(self, state: WorkflowState) -> WorkflowState:
        """
        执行渐进式合并流程
        
        参数:
            state: 工作流状态
            
        返回:
            更新后的工作流状态
        """
        logger.info("🚀 开始渐进式实体合并流程")
        
        try:
            # 初始化Neo4j存储
            self.storage_agent = Neo4jStorageAgent()
            
            # 定义7个SysML相关的任务类型
            sysml_task_types = {
                "Requirement",
                "Use Case",
                "Block Definition and Internal Block",
                "Activity",
                "State Machine",
                "Sequence",
                "Parametric"
            }

            # 获取所有已完成的SysML任务
            completed_tasks = [
                task for task in state.assigned_tasks
                if task.status == ProcessStatus.COMPLETED and
                task.result is not None and
                task.type in sysml_task_types
            ]
            
            if len(completed_tasks) < 2:
                logger.warning("需要至少2个已完成的SysML任务才能进行合并")
                return state

            logger.info(f"找到{len(completed_tasks)}个待合并的SysML任务:")
            for task in completed_tasks:
                elements_count = len(task.result.get('elements', [])) if task.result else 0
                logger.info(f"  - {task.type}: {elements_count} 个元素")
            
            # 执行渐进式合并
            merged_json = self._progressive_merge(completed_tasks)
            
            if merged_json:
                # 创建合并任务结果
                merge_task = SysMLTask(
                    id="PROGRESSIVE-MERGE-TASK",
                    type="Progressive Merge",
                    content="渐进式实体合并完成",
                    status=ProcessStatus.COMPLETED,
                    result=merged_json
                )
                state.assigned_tasks.append(merge_task)
                
                logger.info(f"✅ 渐进式合并完成: {len(merged_json.get('elements', []))} 个元素")
            else:
                logger.error("❌ 渐进式合并失败")
                
        except Exception as e:
            logger.error(f"渐进式合并过程发生异常: {e}", exc_info=True)
        finally:
            if self.storage_agent:
                self.storage_agent.close()
                
        return state
    
    def _progressive_merge(self, tasks: List[SysMLTask]) -> Optional[Dict[str, Any]]:
        """
        执行渐进式合并
        
        参数:
            tasks: 待合并的任务列表
            
        返回:
            合并后的JSON数据
        """
        logger.info("🔄 开始渐进式合并流程")
        
        # 第一步：处理第一个JSON
        current_merged = self._process_single_json(tasks[0])
        if not current_merged:
            logger.error("处理第一个JSON失败")
            return None
            
        logger.info(f"✅ 第一个JSON处理完成: {len(current_merged.get('elements', []))} 个元素")
        
        # 逐个合并其余JSON
        for i, task in enumerate(tasks[1:], 2):
            logger.info(f"🔄 处理第{i}个JSON: {task.type}")
            
            # 处理当前JSON
            processed_json = self._process_single_json(task)
            if not processed_json:
                logger.warning(f"处理第{i}个JSON失败，跳过")
                continue
            
            # 与已合并的JSON进行实体合并
            current_merged = self._merge_two_jsons(current_merged, processed_json, task.type)
            if not current_merged:
                logger.error(f"合并第{i}个JSON失败")
                return None
                
            logger.info(f"✅ 第{i}个JSON合并完成: {len(current_merged.get('elements', []))} 个元素")
        
        # 最后一步：重建包结构
        final_json = self._rebuild_package_structure(current_merged)
        
        logger.info("🎉 渐进式合并流程完成")
        return final_json
    
    def _process_single_json(self, task: SysMLTask) -> Optional[Dict[str, Any]]:
        """
        处理单个JSON：扁平化 + LLM增强修复
        
        参数:
            task: SysML任务
            
        返回:
            处理后的JSON数据
        """
        logger.info(f"🔧 处理单个JSON: {task.type}")
        
        if not task.result:
            return None
            
        # 步骤1: 扁平化JSON结构
        flattened_json = self._flatten_json_structure(task.result)
        
        # 步骤2: LLM增强和修复
        enhanced_json = self._llm_enhance_and_repair(flattened_json, task.content, task.type)
        
        return enhanced_json
    
    def _flatten_json_structure(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        扁平化JSON结构 - 将所有元素的parentId指向model
        
        参数:
            json_data: 原始JSON数据
            
        返回:
            扁平化后的JSON数据
        """
        logger.info("📦 扁平化JSON结构")
        
        flattened = {
            "model": json_data.get("model", []),
            "elements": []
        }
        
        # 获取model ID
        model_id = "default-model"
        if flattened["model"] and isinstance(flattened["model"], list):
            model_id = flattened["model"][0].get("id", "default-model")
        
        # 扁平化所有元素
        elements = json_data.get("elements", [])
        for element in elements:
            flattened_element = element.copy()
            
            # 保存原始parentId作为_original_parent_id
            if "parentId" in flattened_element:
                flattened_element["_original_parent_id"] = flattened_element["parentId"]
            
            # 将parentId指向model（除了Package类型的元素）
            if element.get("type") != "Package":
                flattened_element["parentId"] = model_id
            
            flattened["elements"].append(flattened_element)
        
        logger.info(f"✅ 扁平化完成: {len(flattened['elements'])} 个元素")
        return flattened
    
    def _llm_enhance_and_repair(self, json_data: Dict[str, Any], original_text: str, task_type: str) -> Dict[str, Any]:
        """
        使用LLM增强和修复JSON数据
        
        参数:
            json_data: 扁平化的JSON数据
            original_text: 原始文本描述
            task_type: 任务类型
            
        返回:
            增强修复后的JSON数据
        """
        logger.info(f"🧠 LLM增强和修复JSON: {task_type}")
        
        prompt = self._create_enhancement_prompt(json_data, original_text, task_type)
        
        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            enhanced_json = self._parse_llm_json_response(response.content)
            
            if enhanced_json:
                logger.info(f"✅ LLM增强完成: {len(enhanced_json.get('elements', []))} 个元素")
                return enhanced_json
            else:
                logger.warning("LLM增强失败，返回原始数据")
                return json_data
                
        except Exception as e:
            logger.error(f"LLM增强过程发生异常: {e}")
            return json_data
    
    def _create_enhancement_prompt(self, json_data: Dict[str, Any], original_text: str, task_type: str) -> str:
        """创建LLM增强提示词"""
        prompt = f"""
## 角色
你是一位专业的SysML建模专家，负责分析和增强系统模型数据。

## 任务
分析以下{task_type}的JSON数据，基于原始文本描述进行增强和修复。

## 原始文本描述
```
{original_text[:2000]}...
```

## 当前JSON数据
```json
{json.dumps(json_data, indent=2, ensure_ascii=False)}
```

## 分析要求
1. **完整性检查**: 检查是否有遗漏的重要元素
2. **关系修复**: 检查并修复错误的ID引用关系
3. **属性增强**: 补充缺失的重要属性
4. **一致性验证**: 确保元素间的逻辑一致性

## 输出要求
请输出增强后的完整JSON数据，保持原有结构，只增强和修复，不要删除现有元素。

## 输出格式
```json
{{
  "model": [...],
  "elements": [...]
}}
```
"""
        return prompt
    
    def _parse_llm_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析LLM的JSON响应"""
        try:
            # 提取JSON内容
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].split("```")[0].strip()
            else:
                json_str = response.strip()
            
            return json.loads(json_str)
            
        except Exception as e:
            logger.error(f"解析LLM JSON响应失败: {e}")
            return None
    
    def _merge_two_jsons(self, json1: Dict[str, Any], json2: Dict[str, Any], task_type: str) -> Optional[Dict[str, Any]]:
        """
        使用Neo4j合并两个JSON
        
        参数:
            json1: 第一个JSON（已合并的）
            json2: 第二个JSON（新处理的）
            task_type: 第二个JSON的任务类型
            
        返回:
            合并后的JSON数据
        """
        logger.info(f"🔗 使用Neo4j合并两个JSON")
        
        try:
            # 清空Neo4j数据库
            self.storage_agent.clear_database()
            
            # 加载第一个JSON到Neo4j
            success1 = self.storage_agent.load_sysml_json(json1, "merged_data")
            if not success1:
                logger.error("加载第一个JSON到Neo4j失败")
                return None
            
            # 加载第二个JSON到Neo4j
            success2 = self.storage_agent.load_sysml_json(json2, task_type)
            if not success2:
                logger.error("加载第二个JSON到Neo4j失败")
                return None
            
            # 执行实体去重和合并
            self._perform_entity_deduplication()
            
            # 从Neo4j导出合并后的JSON
            merged_json = self.storage_agent.export_to_json()
            
            logger.info(f"✅ Neo4j合并完成: {len(merged_json.get('elements', []))} 个元素")
            return merged_json
            
        except Exception as e:
            logger.error(f"Neo4j合并过程发生异常: {e}")
            return None
    
    def _perform_entity_deduplication(self):
        """在Neo4j中执行实体去重"""
        logger.info("🔍 执行实体去重")
        
        # 查找潜在重复实体
        duplicate_candidates = self.storage_agent.find_duplicate_candidates()
        
        if not duplicate_candidates:
            logger.info("没有找到重复实体候选")
            return
        
        logger.info(f"找到{len(duplicate_candidates)}对潜在重复实体")
        
        # 使用LLM分析每对候选
        for candidate in duplicate_candidates:
            id1, id2, similarity = candidate['id1'], candidate['id2'], candidate['similarity']
            
            # 获取元素详情
            element1 = self.storage_agent.get_element_details(id1)
            element2 = self.storage_agent.get_element_details(id2)
            
            if not element1 or not element2:
                continue
            
            # LLM决策是否合并
            should_merge, canonical_id = self._llm_decide_merge(element1, element2, id1, id2)
            
            if should_merge and canonical_id:
                discarded_id = id2 if canonical_id == id1 else id1
                success = self.storage_agent.merge_entities(canonical_id, [discarded_id])
                if success:
                    logger.info(f"✅ 成功合并实体: {discarded_id} -> {canonical_id}")
                else:
                    logger.warning(f"❌ 合并实体失败: {discarded_id} -> {canonical_id}")
    
    def _llm_decide_merge(self, element1: Dict, element2: Dict, id1: str, id2: str) -> Tuple[bool, Optional[str]]:
        """使用LLM决策是否合并两个实体"""
        prompt = f"""
## 角色
你是一位专业的SysML建模专家，负责识别重复的模型实体。

## 任务
判断以下两个SysML元素是否表示同一个概念实体。

## 元素1 (ID: {id1})
```json
{json.dumps(element1, indent=2, ensure_ascii=False)}
```

## 元素2 (ID: {id2})
```json
{json.dumps(element2, indent=2, ensure_ascii=False)}
```

## 判断标准
1. 类型是否相同或兼容
2. 名称是否表示同一概念（考虑中英文、同义词）
3. 属性和功能是否一致
4. 在系统中的作用是否相同

## 输出格式
```json
{{
  "should_merge": true/false,
  "canonical_id": "{id1}" 或 "{id2}" 或 null,
  "reasoning": "详细的判断理由"
}}
```
"""
        
        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            result = self._parse_llm_json_response(response.content)
            
            if result:
                should_merge = result.get("should_merge", False)
                canonical_id = result.get("canonical_id")
                return should_merge, canonical_id
            
        except Exception as e:
            logger.error(f"LLM决策合并失败: {e}")
        
        return False, None
    
    def _rebuild_package_structure(self, merged_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        重建标准MBSE包结构
        
        参数:
            merged_json: 合并后的扁平化JSON
            
        返回:
            重建包结构后的JSON
        """
        logger.info("🏗️ 重建标准MBSE包结构")
        
        # 创建标准包结构
        standard_packages = self._create_standard_packages(merged_json)
        
        # 重新分配元素到合适的包
        restructured_json = self._reassign_elements_to_packages(merged_json, standard_packages)
        
        logger.info(f"✅ 包结构重建完成: {len(restructured_json.get('elements', []))} 个元素")
        return restructured_json
    
    def _create_standard_packages(self, merged_json: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建标准MBSE包结构"""
        model_id = "unified-sysml-model"
        if merged_json.get("model") and isinstance(merged_json["model"], list):
            model_id = merged_json["model"][0].get("id", model_id)
        
        packages = [
            {
                "id": "pkg-requirements-uuid",
                "type": "Package",
                "name": "Requirements",
                "parentId": model_id,
                "description": "系统需求包"
            },
            {
                "id": "pkg-usecases-uuid", 
                "type": "Package",
                "name": "UseCases",
                "parentId": model_id,
                "description": "用例包"
            },
            {
                "id": "pkg-structure-uuid",
                "type": "Package", 
                "name": "SystemStructure",
                "parentId": model_id,
                "description": "系统结构包"
            },
            {
                "id": "pkg-behavior-uuid",
                "type": "Package",
                "name": "SystemBehavior", 
                "parentId": model_id,
                "description": "系统行为包"
            },
            {
                "id": "pkg-libraries-uuid",
                "type": "Package",
                "name": "Libraries",
                "parentId": model_id,
                "description": "库包"
            }
        ]
        
        return packages
    
    def _reassign_elements_to_packages(self, merged_json: Dict[str, Any], packages: List[Dict[str, Any]]) -> Dict[str, Any]:
        """重新分配元素到合适的包"""
        
        # 元素类型到包的映射
        type_to_package = {
            # Requirements包
            "Requirement": "pkg-requirements-uuid",
            "TestCase": "pkg-requirements-uuid", 
            "Verify": "pkg-requirements-uuid",
            "Satisfy": "pkg-requirements-uuid",
            "DeriveReqt": "pkg-requirements-uuid",
            
            # UseCases包
            "Actor": "pkg-usecases-uuid",
            "UseCase": "pkg-usecases-uuid",
            "Include": "pkg-usecases-uuid",
            "Extend": "pkg-usecases-uuid",
            
            # SystemStructure包
            "Block": "pkg-structure-uuid",
            "Property": "pkg-structure-uuid",
            "Port": "pkg-structure-uuid", 
            "Connector": "pkg-structure-uuid",
            "Association": "pkg-structure-uuid",
            "InterfaceBlock": "pkg-structure-uuid",
            
            # SystemBehavior包
            "Activity": "pkg-behavior-uuid",
            "StateMachine": "pkg-behavior-uuid",
            "Interaction": "pkg-behavior-uuid",
            "State": "pkg-behavior-uuid",
            "Transition": "pkg-behavior-uuid",
            "Action": "pkg-behavior-uuid",
            "ControlFlow": "pkg-behavior-uuid",
            "Lifeline": "pkg-behavior-uuid",
            "Message": "pkg-behavior-uuid",
            
            # Libraries包
            "ValueType": "pkg-libraries-uuid",
            "Enumeration": "pkg-libraries-uuid", 
            "Signal": "pkg-libraries-uuid",
            "Unit": "pkg-libraries-uuid",
            "ConstraintBlock": "pkg-libraries-uuid"
        }
        
        restructured = {
            "model": merged_json.get("model", []),
            "elements": packages.copy()  # 先添加包元素
        }
        
        # 重新分配其他元素
        for element in merged_json.get("elements", []):
            if element.get("type") == "Package":
                continue  # 跳过包元素，已经添加了
                
            element_copy = element.copy()
            element_type = element.get("type")
            
            # 根据类型分配到合适的包
            if element_type in type_to_package:
                element_copy["parentId"] = type_to_package[element_type]
            else:
                # 未知类型，保持原有parentId或分配到Libraries包
                if "parentId" not in element_copy:
                    element_copy["parentId"] = "pkg-libraries-uuid"
            
            restructured["elements"].append(element_copy)
        
        return restructured
