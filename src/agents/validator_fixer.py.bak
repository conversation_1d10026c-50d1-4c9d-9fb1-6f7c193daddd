"""
SysML验证与修复Agent - 负责验证合并后的SysML JSON模型并修复发现的错误

该模块实现了两个核心组件：
1. SysMLValidator：一个纯Python类，负责对SysML JSON模型进行严格的验证
2. SysMLFixer：一个基于LLM的类，负责根据验证器生成的错误报告修复JSON模型

优化说明:
- 使用merge agent替代align agent，提高合并质量和效率
- 简化了修复循环结构，移除了冗余的内部重试循环
- 增强了错误分析功能，可以识别错误模式和根本原因
- 改进了提示模板，为不同错误类型提供更具体的修复指南
- 添加了智能退出机制，避免无效的修复尝试
- 提供了更详细的日志记录，使修复过程更加透明

使用方法：
    from src.agents.validator_fixer import validate_and_fix_sysml
    
    # 验证并修复合并后的SysML模型
    final_model = validate_and_fix_sysml(merged_model, model_name="gpt-4o")
"""
import logging
import json
import copy
import re
import time
from typing import Dict, List, Any, Set, Optional, Union, Tuple

from langchain.schema import HumanMessage
from langchain_openai import ChatOpenAI
from json_repair import repair_json

from config.settings import settings
from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask

# 导入merge_agent模块
from src.agents.merge_agent import sysml_merge_agent

# 配置日志记录器
logger = logging.getLogger(__name__)


class SysMLValidator:
    """
    一个确定性的、全面的SysML JSON模型验证器。
    它的唯一职责是发现错误，并生成一份清晰的错误报告。
    """
    
    def __init__(self, data: Dict[str, Any]):
        """
        初始化验证器。
        
        参数:
            data: 完整的、合并后的SysML JSON对象。
        """
        logger.info("初始化SysML验证器...")
        
        # 存储错误列表
        self.errors: List[str] = []
        
        # 存储原始数据
        self.data: Dict[str, Any] = data
        
        # 提取elements列表
        self.elements: List[Dict[str, Any]] = data.get("elements", [])
        
        # 存储所有元素ID的集合
        self.all_ids: Set[str] = set()
        
        # 创建一个从ID到元素的快速查找映射
        self.id_map: Dict[str, Dict[str, Any]] = {}
        
        # 初始化ID映射
        for el in self.elements:
            if "id" in el:
                self.id_map[el["id"]] = el
                
        logger.info(f"SysML验证器初始化完成，找到 {len(self.elements)} 个元素")
        
    def validate(self) -> List[str]:
        """
        执行所有验证检查，并返回错误列表。
        如果列表为空，则模型有效。
        
        返回:
            错误信息列表，如果为空则表示验证通过
        """
        logger.info("开始验证SysML模型...")
        
        # 重置错误列表
        self.errors = []
        
        # 执行基本结构检查
        self._check_basic_structure()
        if self.errors:
            logger.error(f"基本结构检查失败，发现 {len(self.errors)} 个错误")
            return self.errors  # 基础结构错误，提前返回
        
        # 执行ID唯一性检查
        self._check_id_uniqueness()
        if self.errors:
            logger.error(f"ID唯一性检查失败，发现 {len(self.errors)} 个错误")
            return self.errors  # ID唯一性是后续检查的基础，提前返回
        
        # 执行引用完整性检查
        self._check_all_references()
        
        # 执行结构规则检查
        self._check_structural_rules()
        
        if not self.errors:
            logger.info("验证通过，未发现错误")
        else:
            logger.error(f"验证失败，发现 {len(self.errors)} 个错误")
            
        return self.errors
    
    def _check_basic_structure(self) -> None:
        """
        检查JSON的基本骨架是否正确。
        """
        logger.info("检查基本结构...")
        
        # 检查顶级结构
        if "model" not in self.data:
            self.errors.append("顶级结构错误: JSON必须包含'model'键")
        
        if "elements" not in self.data:
            self.errors.append("顶级结构错误: JSON必须包含'elements'键")
            return  # 如果没有elements，后续检查无法进行
        
        # 检查elements是否为列表
        if not isinstance(self.elements, list):
            self.errors.append("结构错误: 'elements'的值必须是一个列表")
            return  # 如果elements不是列表，后续检查无法进行
        
        # 检查model是否为字典
        model = self.data.get("model", {})
        if not isinstance(model, dict):
            self.errors.append("结构错误: 'model'的值必须是一个字典")
        else:
            # 检查model是否包含必要的字段
            if "id" not in model:
                self.errors.append("结构错误: 'model'字典必须包含'id'字段")
            if "name" not in model:
                self.errors.append("结构错误: 'model'字典必须包含'name'字段")
    
    def _check_id_uniqueness(self) -> None:
        """
        检查所有元素的ID是否存在且唯一。
        """
        logger.info("检查ID唯一性...")
        
        # 清空并重新填充ID集合
        self.all_ids.clear()
        
        for i, element in enumerate(self.elements):
            # 检查元素是否包含id字段
            if "id" not in element:
                self.errors.append(f"元素定义错误: 第{i+1}个元素缺少'id'键。内容: {str(element)[:100]}...")
                continue
            
            # 检查元素是否包含type字段
            if "type" not in element:
                self.errors.append(f"元素定义错误: ID为'{element['id']}'的元素缺少'type'键")
            
            # 检查ID唯一性
            element_id = element["id"]
            if element_id in self.all_ids:
                self.errors.append(f"ID重复错误: ID'{element_id}'被定义了多次")
            
            # 将ID添加到集合中
            self.all_ids.add(element_id)
    
    def _check_all_references(self) -> None:
        """
        遍历所有元素，检查其所有类型的ID引用是否有效。
        """
        logger.info("检查引用完整性...")
        
        # 常见的引用字段
        ref_keys = [
            "parentId", "typeId", "associationId", "signalId", "contextId", 
            "classifierBehaviorId", "blockId", "requirementId", "testCaseId", 
            "sourceId", "targetId", "coveredId", "representsId"
        ]
        
        # 列表形式的引用字段
        list_ref_keys = ["memberEndIds"]
        
        # 遍历所有元素
        for element in self.elements:
            element_id = element.get("id", "未知ID")
            element_type = element.get("type", "未知类型")
            
            # 检查常见的单值引用字段
            for key in ref_keys:
                if key in element and element[key]:
                    self._validate_reference(element[key], element_id, key)
            
            # 检查列表形式的引用字段
            for key in list_ref_keys:
                if key in element and isinstance(element[key], list):
                    for i, ref_id in enumerate(element[key]):
                        if ref_id:  # 只检查非空引用
                            self._validate_reference(ref_id, element_id, f"{key}[{i}]")
            
            # 检查Connector的端点引用
            if element_type == "Connector" or "Connector" in element_type:
                for end_key in ["end1", "end2"]:
                    if end_key in element and isinstance(element[end_key], dict):
                        for ref_key in ["partRefId", "portRefId", "propertyRefId"]:
                            if ref_key in element[end_key] and element[end_key][ref_key]:
                                self._validate_reference(element[end_key][ref_key], element_id, f"{end_key}.{ref_key}")
    
    def _validate_reference(self, ref_id: str, source_id: str, field_name: str) -> None:
        """
        辅助函数：验证单个ID引用是否存在。
        
        参数:
            ref_id: 引用的ID
            source_id: 包含引用的元素的ID
            field_name: 引用字段的名称
        """
        # 允许引用基本类型
        if ref_id in ["String", "Real", "Integer", "Boolean"]:
            return
        
        # 检查引用是否存在
        if ref_id not in self.all_ids:
            self.errors.append(f"悬空引用错误: 在元素'{source_id}'中，字段'{field_name}'引用了不存在的ID'{ref_id}'")
    
    def _check_structural_rules(self) -> None:
        """
        检查更复杂的结构性规则，例如父子关系是否符合逻辑。
        """
        logger.info("检查结构规则...")
        
        # 获取model ID
        model_id = self.data.get("model", {}).get("id")
        if not model_id:
            self.errors.append("结构错误: 'model'对象必须包含有效的'id'字段")
            return
        
        # 定义元素类型的父子关系规则
        child_parent_rules = {
            "Package": ["Package", "__MODEL__"],  # Package可以是另一个Package的子元素，或直接是model的子元素
            "Property": ["Block", "InterfaceBlock"],
            "FullPort": ["Block", "InterfaceBlock"],
            "ProxyPort": ["Block", "InterfaceBlock"],
            "State": ["Region"],
            "FinalState": ["Region"],
            "Transition": ["Region"],
            "Pseudostate": ["Region"],
            "Region": ["StateMachine", "State"],
            "Action": ["Activity"],
            "ControlNode": ["Activity"],
            "ObjectNode": ["Activity"],
            "ActivityEdge": ["Activity"],
            "ConstraintParameter": ["ConstraintBlock"]
        }
        
        # 标准顶层包名称
        standard_top_packages = ["Requirements", "UseCases", "SystemStructure", "SystemBehavior", "Libraries"]
        
        # 遍历所有元素
        for element_id, element in self.id_map.items():
            element_type = element.get("type")
            parent_id = element.get("parentId")
            
            # 跳过没有parentId的元素
            if not parent_id:
                continue
            
            # 特殊处理：检查顶层Package是否直接引用model
            if element_type == "Package" and element.get("name") in standard_top_packages:
                if parent_id != model_id:
                    # 查找父元素名称以提供更有用的错误信息
                    parent_name = "未知"
                    if parent_id in self.id_map:
                        parent_name = self.id_map[parent_id].get("name", "未知")
                    
                    self.errors.append(
                        f"包结构错误: 顶层包'{element.get('name')}'(ID: {element_id})的父元素应该是model(ID: {model_id})，"
                        f"但现在是'{parent_name}'(ID: {parent_id})"
                    )
                continue  # 已经检查过这个特殊情况，跳过常规检查
            
            # 跳过引用不存在的父元素
            if parent_id not in self.id_map:
                continue  # 这个错误应该已经在_check_all_references中被捕捉到了
            
            # 获取父元素类型
            parent_element = self.id_map[parent_id]
            parent_type = parent_element.get("type")
            
            # 检查父子关系是否符合规则
            if element_type in child_parent_rules:
                valid_parent_types = child_parent_rules[element_type]
                
                # 特殊处理：如果允许的父类型包含__MODEL__，检查父ID是否等于model ID
                if "__MODEL__" in valid_parent_types:
                    valid_parent_types = [t for t in valid_parent_types if t != "__MODEL__"]
                    if parent_id == model_id:
                        continue  # 父ID是model ID，这是允许的
                
                if parent_type not in valid_parent_types:
                    self.errors.append(
                        f"结构逻辑错误: {element_type}'{element_id}'的父元素必须是{' 或 '.join(valid_parent_types)}"
                        f"{' 或 model' if '__MODEL__' in child_parent_rules.get(element_type, []) else ''}，"
                        f"但现在是'{parent_type}'(ID: {parent_id})"
                    )
    
    def __str__(self) -> str:
        """返回验证器的字符串表示。"""
        return f"SysMLValidator(elements={len(self.elements)}, errors={len(self.errors)})"
    
    def __repr__(self) -> str:
        """返回验证器的详细字符串表示。"""
        return self.__str__()


class SysMLFixer:
    """
    一个基于LLM的SysML JSON模型修复器。
    它的职责是根据验证器生成的错误报告修复JSON模型。
    """
    
    def __init__(self, model_name: Optional[str] = None, temperature: float = 0.0):
        """
        初始化修复器。
        
        参数:
            model_name: 使用的LLM模型名称，如果为None则使用settings中的配置
            temperature: LLM生成的随机性，0表示最确定性的输出
        """
        logger.info("初始化SysML修复器...")
        
        # 使用配置中的模型或提供的模型
        self.model_name = model_name or settings.llm_model
        self.temperature = temperature
        
        # 初始化LLM
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            timeout=None,
            max_tokens=None,
            temperature=self.temperature
        )
        
        logger.info(f"SysML修复器初始化完成，使用模型: {self.model_name}")
    
    def fix(self, faulty_json: Dict[str, Any], errors: List[str]) -> Dict[str, Any]:
        """
        修复有错误的SysML JSON模型。
        
        优化说明:
        - 简化为单次修复尝试而非多次重试
        - 提供更详细的错误上下文和修复指南
        - 保留基本的错误处理和安全检查
        - 更详细的日志记录，使修复过程更加透明
        
        参数:
            faulty_json: 有错误的SysML JSON模型
            errors: 验证器生成的错误列表
            
        返回:
            修复后的SysML JSON模型
        """
        logger.info(f"开始修复SysML模型，错误数量: {len(errors)}")
        
        # 如果没有错误，直接返回原始JSON
        if not errors:
            logger.info("没有错误需要修复，返回原始JSON")
            return faulty_json
        
        # 记录所有错误，以便更好地理解问题
        logger.info(f"需要修复的错误: {errors}")
        
        # 创建原始JSON的深度复制，以防止意外修改
        original_json = copy.deepcopy(faulty_json)
        
        # 获取原始元素数量，用于后续安全检查
        original_elements_count = len(original_json.get("elements", []))
        logger.info(f"原始模型包含 {original_elements_count} 个元素")
        
        # 对错误进行分析和分类
        error_types = {}
        for error in errors:
            # 提取错误类型
            if ":" in error:
                error_type = error.split(":")[0].strip()
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        logger.info(f"错误类型分布: {error_types}")
        
        # 创建包含更多上下文的详细提示词
        prompt = self._create_fix_prompt(faulty_json, errors, more_context=True)
        logger.debug(f"生成的修复提示词长度: {len(prompt)} 字符")
        
        try:
            # 调用LLM
            logger.info(f"调用大模型 {self.model_name} 修复错误...")
            response = self.llm.invoke([HumanMessage(content=prompt)])
            
            # 处理响应
            fixed_json = self._process_response(response.content)
            
            # 基本验证：确保修复后的JSON至少包含必要的结构
            if not isinstance(fixed_json, dict):
                raise ValueError(f"修复后的JSON不是字典，而是 {type(fixed_json)}")
            
            if "model" not in fixed_json:
                raise ValueError("修复后的JSON缺少必要的'model'键")
            
            if "elements" not in fixed_json:
                raise ValueError("修复后的JSON缺少必要的'elements'键")
            
            if not isinstance(fixed_json.get("elements"), list):
                raise ValueError("修复后的JSON中'elements'不是列表")
            
            # 比较修复前后的差异
            elements_count = len(fixed_json.get("elements", []))
            logger.info(f"修复前元素数量: {original_elements_count}, 修复后元素数量: {elements_count}")
            
            # 安全检查：如果修复后的元素数量大幅减少，发出警告
            elements_ratio = elements_count / original_elements_count if original_elements_count > 0 else 0
            if elements_count < original_elements_count * 0.5:  # 如果元素减少超过50%
                logger.warning(f"警告：修复后的元素数量大幅减少（{elements_count}，原始数量的 {elements_ratio:.1%}）")
                logger.warning("这可能表明修复过程存在问题")
            
            # 检查是否有实质性变化
            if fixed_json == original_json:
                logger.warning("修复尝试没有产生任何变化，JSON保持不变")
                return original_json
            else:
                # 分析变化的性质
                element_ids_before = {e.get("id") for e in original_json.get("elements", []) if "id" in e}
                element_ids_after = {e.get("id") for e in fixed_json.get("elements", []) if "id" in e}
                
                added_ids = element_ids_after - element_ids_before
                removed_ids = element_ids_before - element_ids_after
                
                logger.info(f"修复过程添加了 {len(added_ids)} 个元素，移除了 {len(removed_ids)} 个元素")
                
                # 最终安全检查：如果移除了大量元素并且没有添加新元素，可能是不正确的修复
                if len(removed_ids) > original_elements_count * 0.7 and len(added_ids) < 5:
                    logger.warning(f"修复过程移除了大量元素({len(removed_ids)})且只添加了少量新元素({len(added_ids)})，这可能是不正确的")
                    logger.warning("由于安全考虑，返回原始模型")
                    return original_json
                
                logger.info("成功修复SysML模型，检测到JSON变化")
                return fixed_json
            
        except Exception as e:
            logger.error(f"修复尝试失败: {str(e)}")
            logger.error("返回原始JSON，尽管它包含错误")
            return original_json
    
    def _create_fix_prompt(self, faulty_json: dict, errors: list, more_context: bool = False) -> str:
        """
        创建用于修复SysML JSON模型的提示词。
        
        参数:
            faulty_json: 有错误的SysML JSON模型
            errors: 验证器生成的错误列表
            more_context: 是否添加更多上下文信息
            
        返回:
            用于修复模型的提示词
        """
        # 为不同类型的错误分类
        error_types = {}
        for error in errors:
            if ":" in error:
                error_type = error.split(":")[0].strip()
                if error_type not in error_types:
                    error_types[error_type] = []
                error_types[error_type].append(error)
        
        # 创建错误描述部分
        error_description = []
        error_description.append(f"在SysML JSON模型中发现了 {len(errors)} 个错误，分为 {len(error_types)} 种类型。")
        
        for error_type, type_errors in error_types.items():
            error_description.append(f"- {error_type}错误 ({len(type_errors)} 个)")
            # 为每种错误类型显示最多3个示例
            for i, error in enumerate(type_errors[:3]):
                error_description.append(f"  - 示例 {i+1}: {error}")
            if len(type_errors) > 3:
                error_description.append(f"  - ...以及 {len(type_errors) - 3} 个其他类似错误")
        
        # 为每种错误类型提供具体修复指南
        repair_guidelines = [
            "## SysML模型修复指南",
            "",
            "### 常见错误类型与修复策略:"
        ]
        
        if "顶级结构错误" in error_types or "结构错误" in error_types:
            repair_guidelines.extend([
                "",
                "#### 1. 顶级结构错误或结构错误",
                "- 确保JSON包含'model'和'elements'两个顶级键",
                "- 'model'必须是一个包含'id'和'name'字段的字典",
                "- 'elements'必须是一个包含多个元素的列表",
                "- 每个元素必须包含'id'和'type'字段"
            ])
        
        if "元素定义错误" in error_types:
            repair_guidelines.extend([
                "",
                "#### 2. 元素定义错误",
                "- 确保每个元素都有唯一的'id'",
                "- 确保每个元素都包含'type'字段",
                "- 修复任何格式不正确的元素"
            ])
        
        if "ID重复错误" in error_types:
            repair_guidelines.extend([
                "",
                "#### 3. ID重复错误",
                "- 确保所有元素ID都是唯一的",
                "- 识别并修复重复ID：为重复ID生成新的唯一ID",
                "- 记得更新所有引用这些ID的地方"
            ])
        
        if "悬空引用错误" in error_types:
            repair_guidelines.extend([
                "",
                "#### 4. 悬空引用错误",
                "- 检查所有引用字段（如parentId, typeId等）",
                "- 确保所有引用的ID都存在于模型中",
                "- 修复方法：创建缺失的元素或更新引用为有效ID"
            ])
        
        if "结构逻辑错误" in error_types or "包结构错误" in error_types:
            repair_guidelines.extend([
                "",
                "#### 5. 结构逻辑错误和包结构错误",
                "- 确保元素的父子关系符合SysML规则（例如，Package只能是Package或model的子元素）",
                "- 标准顶层包名称应为: 'Requirements', 'UseCases', 'SystemStructure', 'SystemBehavior', 'Libraries'",
                "- 确保顶层包的parentId直接引用model的ID",
                "- 检查State、Region、Activity等特殊元素的父子关系是否正确"
            ])
        
        # 添加SysML模型的一般结构规范
        sysml_structure = [
            "",
            "### SysML模型基本结构规范:",
            "",
            "1. **顶级结构**:",
            "   ```json",
            "   {",
            '     "model": { "id": "modelID", "name": "ModelName" },',
            '     "elements": [ ... ]',
            "   }",
            "   ```",
            "",
            "2. **标准包结构**:",
            "   - 顶层包应直接引用model作为父元素",
            "   - 标准顶层包名称: Requirements, UseCases, SystemStructure, SystemBehavior, Libraries"
        ]
        
        # 为大模型提供思考步骤指南
        thinking_steps = [
            "",
            "### 修复步骤:",
            "",
            "1. **分析错误模式**：识别错误之间的关系，找出根本原因",
            "2. **解决基础错误**：首先修复ID唯一性和缺失元素等基础问题",
            "3. **修复结构错误**：接着处理包层次结构和父子关系问题",
            "4. **修复引用错误**：最后处理悬空引用和关系问题",
            "5. **验证修复**：确保修复后的JSON完整有效，没有引入新错误"
        ]
        
        # 组装基本提示词
        prompt = [
            "你是一个专业的SysML JSON模型修复专家。你的任务是修复下面SysML JSON中的错误。",
            "",
            "# 错误报告",
            "\n".join(error_description),
            "",
            "\n".join(repair_guidelines),
            "\n".join(sysml_structure),
            "\n".join(thinking_steps),
            "",
            "# 修复要求",
            "",
            "1. 仅修复上述错误，不要随意添加或删除不必要的元素。",
            "2. 保持原始模型的元素数量尽量不变，除非必须添加或删除元素来修复错误。",
            "3. 为每个修复的错误提供简短的解释，说明修复方法。",
            "4. 在修复完成后，返回完整的、修复后的JSON。",
            "",
            "# 待修复的JSON",
            "```json",
            json.dumps(faulty_json, indent=2),
            "```",
            "",
            "# 详细错误列表",
            "\n".join([f"- {error}" for error in errors]),
            "",
            "请先分析错误，然后进行修复。回复格式应包含：",
            "1. 对错误的分析和修复策略",
            "2. 各个错误的修复方法说明",
            "3. 最后是完整的、修复后的JSON（必须用```json和```包围）"
        ]
        
        # 如果需要更多上下文，添加详细的结构分析
        if more_context:
            # 分析模型结构
            element_counts = {}
            parent_child_map = {}
            
            for element in faulty_json.get("elements", []):
                # 统计元素类型
                element_type = element.get("type", "未知类型")
                element_counts[element_type] = element_counts.get(element_type, 0) + 1
                
                # 创建父子映射关系
                parent_id = element.get("parentId")
                if parent_id:
                    if parent_id not in parent_child_map:
                        parent_child_map[parent_id] = []
                    parent_child_map[parent_id].append({
                        "id": element.get("id"),
                        "type": element_type,
                        "name": element.get("name", "未命名")
                    })
            
            # 创建模型结构统计
            structure_analysis = ["", "# 模型结构分析", ""]
            
            # 添加元素类型统计
            structure_analysis.append("## 元素类型统计")
            for element_type, count in element_counts.items():
                structure_analysis.append(f"- {element_type}: {count}个元素")
            
            # 添加包层次结构分析（如果有足够数据）
            model_id = faulty_json.get("model", {}).get("id")
            if model_id and model_id in parent_child_map:
                structure_analysis.extend(["", "## 顶层包结构"])
                for child in parent_child_map[model_id]:
                    if child["type"] == "Package":
                        structure_analysis.append(f"- {child['name']} (ID: {child['id']}, 类型: {child['type']})")
            
            # 将结构分析添加到提示词中
            prompt.extend(structure_analysis)
        
        return "\n".join(prompt)
    
    def _process_response(self, response_content: str) -> Dict[str, Any]:
        """
        处理LLM的响应，提取并解析JSON。
        
        参数:
            response_content: LLM的响应内容
            
        返回:
            解析后的JSON
            
        异常:
            ValueError: 如果无法从响应中提取有效的JSON
        """
        logger.info("处理LLM响应...")
        
        # 记录响应长度以便调试
        logger.debug(f"收到LLM响应，长度: {len(response_content)} 字符")
        
        # 尝试从响应中提取JSON代码块 - 更精确的模式
        json_matches = re.findall(r'```(?:json)?\s*([\s\S]*?)\s*```', response_content)
        
        if json_matches:
            # 使用第一个找到的JSON代码块
            json_str = json_matches[0].strip()
            logger.info("从响应中提取到JSON代码块")
        else:
            # 如果没有找到代码块，尝试查找大括号包围的内容
            logger.warning("未在响应中找到JSON代码块，尝试查找大括号包围的内容")
            brace_matches = re.findall(r'({[\s\S]*})', response_content)
            if brace_matches:
                # 找到了大括号包围的内容，使用最长的匹配（通常是完整的JSON）
                json_str = max(brace_matches, key=len).strip()
                logger.info("找到大括号包围的内容，可能是JSON")
            else:
                # 如果没有找到大括号包围的内容，尝试直接解析整个响应
                json_str = response_content.strip()
                logger.warning("未找到大括号包围的内容，尝试直接解析整个响应")
        
        # 尝试解析JSON前，先应用一些常见的修复
        # 1. 删除行首和行尾的非JSON文本
        json_str = re.sub(r'^[^{]*', '', json_str)
        json_str = re.sub(r'[^}]*$', '', json_str)
        
        # 2. 修复常见的JSON格式问题
        # 替换单引号为双引号（但避免替换引号内的引号）
        json_str = re.sub(r"(?<!\\\")(')", '"', json_str)
        # 修复没有引号的键
        json_str = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', json_str)
        # 删除尾随逗号
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        # 尝试解析修复后的JSON
        try:
            fixed_json = json.loads(json_str)
            logger.info("成功解析JSON响应")
            
            # 额外验证：确保基本结构符合预期
            if not isinstance(fixed_json, dict):
                raise ValueError(f"解析后的JSON不是字典，而是 {type(fixed_json)}")
            
            # 验证必要的键存在
            if "model" not in fixed_json or "elements" not in fixed_json:
                # 如果解析出的JSON不是预期结构，尝试修复
                logger.warning("解析后的JSON缺少必要的键，尝试修复结构")
                
                # 如果是元素列表，尝试构建正确的结构
                if isinstance(fixed_json, list) and all(isinstance(item, dict) for item in fixed_json):
                    logger.info("将解析的JSON元素列表转换为完整的模型结构")
                    fixed_json = {
                        "model": {"id": "model", "name": "RepairedModel"},
                        "elements": fixed_json
                    }
            
            return fixed_json
            
        except json.JSONDecodeError as e:
            # 记录解析失败的原因和位置
            logger.error(f"JSON解析失败: {str(e)}")
            logger.error(f"错误位置: 行 {e.lineno}, 列 {e.colno}, 字符 '{e.doc[e.pos:e.pos+20]}...'")
            
            # 首先尝试使用json_repair库修复JSON
            try:
                logger.info("尝试使用json_repair库修复JSON...")
                repaired_json_str = repair_json(json_str)
                logger.info("json_repair修复完成，尝试解析...")
                
                fixed_json = json.loads(repaired_json_str)
                
                # 特殊处理: 检查并修复修复后的model字段
                if "model" in fixed_json:
                    if not isinstance(fixed_json["model"], dict):
                        logger.warning(f"修复后的'model'字段仍不是字典，尝试进一步修复")
                        fixed_json["model"] = {"id": "repaired-model-uuid", "name": "Repaired Model"}
                else:
                    logger.warning("修复后的响应中缺少'model'字段，添加默认值")
                    fixed_json["model"] = {"id": "repaired-model-uuid", "name": "Repaired Model"}
                
                # 确保elements字段存在
                if "elements" not in fixed_json:
                    fixed_json["elements"] = []
                
                logger.info(f"使用json_repair成功修复并解析JSON，包含 {len(fixed_json.get('elements', []))} 个元素")
                return fixed_json
                
            except Exception as repair_error:
                logger.error(f"json_repair修复失败: {str(repair_error)}")
                
                # 如果json_repair失败，继续尝试其他方法
                try:
                    # 使用ast.literal_eval（可以处理某些json.loads无法处理的格式）
                    import ast
                    logger.info("尝试使用ast.literal_eval解析...")
                    
                    # 安全起见，只尝试解析看起来像字典的字符串
                    if json_str.strip().startswith('{') and json_str.strip().endswith('}'):
                        try:
                            fixed_json = ast.literal_eval(json_str)
                            logger.info("使用ast.literal_eval成功解析")
                            
                            # 验证结构
                            if isinstance(fixed_json, dict) and "model" in fixed_json and "elements" in fixed_json:
                                return fixed_json
                        except:
                            logger.warning("ast.literal_eval解析失败")
                    
                    # 如果上述方法失败，尝试手动解析常见模式
                    logger.info("尝试使用改进的正则表达式查找和修复JSON结构...")
                    
                    # 使用更复杂的正则表达式，尝试匹配嵌套结构
                    # 注意：这种方法仍然有局限性，但比简单的正则表达式更好
                    model_pattern = r'"model"\s*:\s*({(?:[^{}]|(?:{[^{}]*}))*)}'
                    elements_pattern = r'"elements"\s*:\s*(\[(?:[^\[\]]|(?:\[[^\[\]]*\]))*\])'
                    
                    model_match = re.search(model_pattern, json_str)
                    elements_match = re.search(elements_pattern, json_str)
                    
                    if model_match and elements_match:
                        # 尝试构建有效的JSON结构
                        reconstructed_json = f'{{"model": {model_match.group(1)}, "elements": {elements_match.group(1)}}}'
                        
                        try:
                            fixed_json = json.loads(reconstructed_json)
                            logger.info(f"成功重构并解析JSON，包含 {len(fixed_json.get('elements', []))} 个元素")
                            return fixed_json
                        except json.JSONDecodeError as decode_error:
                            logger.warning(f"重构的JSON解析失败: {str(decode_error)}")
                            
                            # 最后尝试再次使用json_repair修复重构的JSON
                            try:
                                repaired_reconstructed = repair_json(reconstructed_json)
                                fixed_json = json.loads(repaired_reconstructed)
                                logger.info(f"通过组合使用正则表达式和json_repair成功解析JSON，包含 {len(fixed_json.get('elements', []))} 个元素")
                                return fixed_json
                            except:
                                logger.warning("组合修复方法失败")
                    
                except Exception as nested_e:
                    logger.error(f"高级JSON修复尝试失败: {str(nested_e)}")
            
            # 如果所有尝试都失败，抛出更详细的异常
            error_msg = f"无法从LLM响应中提取有效的JSON: {str(e)}"
            logger.error(error_msg)
            # 记录部分JSON内容以便诊断
            logger.error(f"JSON内容摘要: {json_str[:500]}...")
            raise ValueError(error_msg)
    
    def __str__(self) -> str:
        """返回修复器的字符串表示。"""
        return f"SysMLFixer(model={self.model_name})"
    
    def __repr__(self) -> str:
        """返回修复器的详细字符串表示。"""
        return self.__str__()


def validate_and_fix_sysml(
    model: Dict[str, Any], 
    model_name: Optional[str] = None, 
    max_iterations: int = 3
) -> Dict[str, Any]:
    """
    验证并修复SysML JSON模型。
    
    这个函数实现了一个"验证-修复"循环，它首先使用SysMLValidator验证模型，
    如果发现错误，则使用SysMLFixer尝试修复它们。这个过程会重复进行，
    直到没有错误或达到最大迭代次数。
    
    优化说明:
    - 简化为单层循环结构，减少不必要的重试
    - 提供更详细的错误上下文给修复器
    - 增强了错误分析和智能退出机制
    - 对同一类型错误反复出现的情况提供处理
    
    参数:
        model: 要验证和修复的SysML JSON模型
        model_name: 使用的LLM模型名称，如果为None则使用settings中的配置
        max_iterations: 最大验证-修复迭代次数
        
    返回:
        修复后的SysML JSON模型
    """
    logger.info("开始验证和修复SysML模型...")
    logger.info(f"配置: 最大迭代次数={max_iterations}")
    
    # 记录初始模型的基本信息
    model_elements = len(model.get("elements", []))
    model_types = {}
    for element in model.get("elements", []):
        element_type = element.get("type", "未知")
        model_types[element_type] = model_types.get(element_type, 0) + 1
    
    logger.info(f"初始模型包含 {model_elements} 个元素，类型分布: {model_types}")
    
    # 创建验证器和修复器
    validator = SysMLValidator(model)
    fixer = SysMLFixer(model_name=model_name)
    
    # 当前模型版本 - 使用深度复制以防止引用问题
    current_model = copy.deepcopy(model)
    
    # 跟踪每一轮迭代的错误
    iteration_errors = []  # 记录每次迭代的错误列表
    persistent_errors = set()  # 跟踪多次迭代后仍然存在的错误
    
    # 验证-修复循环
    for iteration in range(max_iterations):
        logger.info(f"====== 验证-修复循环: 迭代 {iteration + 1}/{max_iterations} ======")
        
        # 验证当前模型
        logger.info(f"[迭代 {iteration + 1}] 开始验证模型...")
        errors = validator.validate()
        
        # 如果没有错误，返回当前模型
        if not errors:
            logger.info(f"[迭代 {iteration + 1}] 验证通过，没有错误！成功修复模型。")
            return current_model
        
        # 使用错误分析功能
        logger.info(f"[迭代 {iteration + 1}] 分析错误模式...")
        error_analysis = analyze_errors(errors)
        
        # 记录错误分析结果
        logger.info(f"[迭代 {iteration + 1}] 验证失败，发现 {error_analysis['total_errors']} 个错误")
        
        # 记录错误类型分布
        for error_type, info in error_analysis["error_types"].items():
            logger.info(f"- {error_type}: {info['count']}个错误 ({info['percentage']:.1f}%)")
        
        # 记录最常见的错误模式
        if error_analysis["most_common_patterns"]:
            logger.info(f"[迭代 {iteration + 1}] 最常见的错误模式:")
            for i, pattern_info in enumerate(error_analysis["most_common_patterns"][:3]):
                logger.info(f"- 模式 {i+1} (出现 {pattern_info['count']} 次): {pattern_info['pattern'][:100]}...")
        
        # 记录可能的根本原因
        if error_analysis["possible_root_causes"]:
            logger.info(f"[迭代 {iteration + 1}] 可能的根本原因:")
            for cause in error_analysis["possible_root_causes"]:
                logger.info(f"- {cause}")
        
        # 检查错误是否与上一次迭代相同
        if iteration > 0:
            # 计算错误集合的交集（持续存在的错误）
            current_errors_set = set(errors)
            last_errors_set = set(iteration_errors[-1])
            unchanged_errors = current_errors_set.intersection(last_errors_set)
            
            # 如果所有错误都与上一次相同，提前退出
            if current_errors_set == last_errors_set:
                logger.warning(f"[迭代 {iteration + 1}] 警告: 当前错误与上一次完全相同，修复过程没有进展")
                logger.warning(f"[迭代 {iteration + 1}] 提前退出修复循环，返回当前最佳模型")
                return current_model
            
            # 识别持续存在的错误
            if iteration > 1:  # 至少经过两轮迭代
                persistent_errors.update(unchanged_errors)
                if persistent_errors:
                    logger.warning(f"[迭代 {iteration + 1}] 警告: 检测到 {len(persistent_errors)} 个持续存在的错误，可能需要手动干预")
                    # 记录一些持续性错误示例
                    logger.warning(f"持续性错误示例: {list(persistent_errors)[:3]}")
        
        # 保存本次迭代的错误，用于下次比较
        iteration_errors.append(errors)
        
        # 使用深度复制确保当前模型和修复前的模型完全分离
        model_before_fix = copy.deepcopy(current_model)
        
        # 尝试修复错误
        logger.info(f"[迭代 {iteration + 1}] 调用修复器修复模型...")
        fixed_model = fixer.fix(current_model, errors)
        
        # 记录修复前后的元素数量和变化
        elements_before = len(model_before_fix.get("elements", []))
        elements_after = len(fixed_model.get("elements", []))
        logger.info(f"[迭代 {iteration + 1}] 修复前元素数量: {elements_before}, 修复后元素数量: {elements_after}")
        
        # 检查修复前后是否有变化
        if fixed_model == model_before_fix:
            logger.warning(f"[迭代 {iteration + 1}] 警告: 修复前后的模型完全相同，修复过程没有产生任何变化")
            logger.warning(f"[迭代 {iteration + 1}] 修复没有产生任何变化，提前退出修复循环，返回当前模型")
            return current_model
        else:
            # 分析模型变化
            element_ids_before = {e.get("id") for e in model_before_fix.get("elements", []) if "id" in e}
            element_ids_after = {e.get("id") for e in fixed_model.get("elements", []) if "id" in e}
            
            # 计算添加、删除和保留的元素
            added_ids = element_ids_after - element_ids_before
            removed_ids = element_ids_before - element_ids_after
            
            if added_ids:
                logger.info(f"[迭代 {iteration + 1}] 修复过程添加了 {len(added_ids)} 个新元素")
                # 输出一些示例添加的元素
                added_elements = [e for e in fixed_model.get("elements", []) if "id" in e and e["id"] in added_ids]
                for i, elem in enumerate(added_elements[:3]):
                    logger.info(f"  - 添加的元素 {i+1}: ID={elem.get('id')}, 类型={elem.get('type')}, 名称={elem.get('name', '未命名')}")
            
            if removed_ids:
                logger.info(f"[迭代 {iteration + 1}] 修复过程移除了 {len(removed_ids)} 个元素")
                # 输出一些示例移除的元素
                removed_elements = [e for e in model_before_fix.get("elements", []) if "id" in e and e["id"] in removed_ids]
                for i, elem in enumerate(removed_elements[:3]):
                    logger.info(f"  - 移除的元素 {i+1}: ID={elem.get('id')}, 类型={elem.get('type')}, 名称={elem.get('name', '未命名')}")
            
            # 安全检查：移除了过多元素且没有添加足够的新元素可能是修复错误
            if len(removed_ids) > elements_before * 0.5 and len(added_ids) < len(removed_ids) * 0.2:
                logger.warning(f"[迭代 {iteration + 1}] 警告: 修复过程移除了大量元素({len(removed_ids)})但添加很少({len(added_ids)})，可能存在问题")
                logger.warning(f"[迭代 {iteration + 1}] 修复可能不正确，但继续进入下一轮验证以确认")
        
        # 使用deepcopy更新当前模型，确保完全分离
        current_model = copy.deepcopy(fixed_model)
        
        # 重新创建验证器，使用修复后的模型
        validator = SysMLValidator(current_model)
        
        logger.info(f"[迭代 {iteration + 1}] 完成一次验证-修复循环")
    
    # 最后一次验证
    logger.info("执行最终验证...")
    final_errors = validator.validate()
    if not final_errors:
        logger.info(f"最终验证通过，在 {max_iterations} 次迭代后修复了所有错误")
    else:
        logger.warning(f"最终验证失败，在 {max_iterations} 次迭代后仍有 {len(final_errors)} 个错误")
        
        # 使用错误分析功能分析最终错误
        final_error_analysis = analyze_errors(final_errors)
        
        # 记录剩余错误的详细分析
        logger.warning(f"剩余错误分析：")
        for error_type, info in final_error_analysis["error_types"].items():
            logger.warning(f"- {error_type}: {info['count']}个错误 ({info['percentage']:.1f}%)")
        
        # 记录最常见的剩余错误模式
        if final_error_analysis["most_common_patterns"]:
            logger.warning("最常见的剩余错误模式:")
            for i, pattern_info in enumerate(final_error_analysis["most_common_patterns"][:3]):
                logger.warning(f"- 模式 {i+1} (出现 {pattern_info['count']} 次): {pattern_info['pattern'][:100]}...")
        
        # 记录可能需要手动修复的建议
        if final_error_analysis["possible_root_causes"]:
            logger.warning("可能需要手动修复的问题:")
            for cause in final_error_analysis["possible_root_causes"]:
                logger.warning(f"- {cause}")
    
    # 比较最终模型与初始模型的差异
    initial_elements = len(model.get("elements", []))
    final_elements = len(current_model.get("elements", []))
    
    logger.info(f"修复过程总结: 初始元素数量 {initial_elements}, 最终元素数量 {final_elements}")
    
    return current_model


def align_validate_fix_sysml(
    workflow_state: WorkflowState, 
    model_name: Optional[str] = None,
    max_iterations: int = 3
) -> Dict[str, Any]:
    """
    完整的SysML处理流程：合并、验证和修复。
    
    这个函数整合了整个处理流程：
    1. 首先使用SysMLMergeAgent合并模型
    2. 然后使用SysMLValidator验证合并后的模型
    3. 如果发现错误，使用SysMLFixer修复它们
    4. 重复验证-修复循环，直到没有错误或达到最大迭代次数
    
    优化说明:
    - 使用merge agent替代align agent，提高合并质量
    - 增强了日志记录，可以更清晰地追踪整个处理流程
    - 提供了错误分析和诊断信息
    - 简化了循环结构，减少不必要的重试
    
    参数:
        workflow_state: 工作流状态对象，包含所有任务
        model_name: 使用的LLM模型名称，如果为None则使用settings中的配置
        max_iterations: 最大验证-修复迭代次数
        
    返回:
        最终的、经过验证和修复的SysML JSON模型
    """
    logger.info("开始完整的SysML处理流程：合并、验证和修复...")
    
    # 记录处理开始时间
    start_time = time.time()
    
    # 步骤1：使用SysMLMergeAgent合并
    logger.info("步骤1：使用SysMLMergeAgent合并模型...")
    
    # 调用merge agent处理工作流状态
    updated_state = sysml_merge_agent(workflow_state)
    
    # 从更新后的状态中获取合并结果
    merged_tasks = [task for task in updated_state.assigned_tasks if task.type == "Merged SysML Model" and task.status == ProcessStatus.COMPLETED]
    
    if not merged_tasks:
        logger.error("合并失败：未找到合并后的模型任务")
        return {}
    
    # 使用最新的合并任务
    merged_task = merged_tasks[-1]
    merged_model = merged_task.result
    
    if not merged_model or not isinstance(merged_model, dict) or "elements" not in merged_model:
        logger.error("合并失败：合并模型无效或为空")
        return {}
    
    # 记录合并模型的基本信息
    merged_elements_count = len(merged_model.get("elements", []))
    element_types = {}
    for element in merged_model.get("elements", []):
        element_type = element.get("type", "未知")
        element_types[element_type] = element_types.get(element_type, 0) + 1
    
    logger.info(f"合并完成，合并后的模型包含 {merged_elements_count} 个元素")
    logger.info(f"合并模型元素类型分布: {element_types}")
    
    # 步骤2：验证和修复
    logger.info("步骤2：验证和修复合并后的模型...")
    final_model = validate_and_fix_sysml(merged_model, model_name=model_name, max_iterations=max_iterations)
    
    # 记录最终模型的基本信息
    if final_model and "elements" in final_model:
        final_elements_count = len(final_model.get("elements", []))
        final_element_types = {}
        for element in final_model.get("elements", []):
            element_type = element.get("type", "未知")
            final_element_types[element_type] = final_element_types.get(element_type, 0) + 1
        
        logger.info(f"验证和修复完成，最终模型包含 {final_elements_count} 个元素")
        logger.info(f"最终模型元素类型分布: {final_element_types}")
        
        # 对比合并模型和最终模型
        if merged_elements_count != final_elements_count:
            logger.info(f"修复过程中元素数量变化: {merged_elements_count} -> {final_elements_count} ({final_elements_count - merged_elements_count:+d})")
        
        # 计算处理总时间
        elapsed_time = time.time() - start_time
        logger.info(f"完整处理流程总耗时: {elapsed_time:.2f} 秒")
    else:
        logger.error("验证和修复失败：最终模型无效或为空")
    
    return final_model


def sysml_validate_fix_agent(state: WorkflowState) -> WorkflowState:
    """
    SysML验证与修复Agent工作流节点入口函数
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    logger.info("SysML验证与修复Agent节点开始处理")
    
    # 检查是否已经执行过验证与修复
    if any(task.type == "Validated SysML Model" for task in state.assigned_tasks):
        logger.info("已经执行过验证与修复操作，跳过")
        return state
    
    # 查找合并后的模型任务
    merged_tasks = [task for task in state.assigned_tasks if task.type == "Merged SysML Model" and task.status == ProcessStatus.COMPLETED]
    
    if not merged_tasks:
        logger.warning("没有找到已合并的SysML模型，无法进行验证与修复")
        # 创建一个空的验证修复结果，避免再次进入验证修复步骤
        validate_task = SysMLTask(
            id=f"VALIDATE-TASK-EMPTY",
            type="Validated SysML Model",
            content="没有可验证的内容",
            status=ProcessStatus.FAILED,
            error_message="没有找到已合并的SysML模型，无法进行验证与修复"
        )
        state.assigned_tasks.append(validate_task)
        return state
    
    # 使用最新的合并模型
    merged_task = merged_tasks[-1]
    merged_model = merged_task.result
    
    if not merged_model:
        logger.warning("合并模型结果为空，无法进行验证与修复")
        validate_task = SysMLTask(
            id=f"VALIDATE-TASK-EMPTY-RESULT",
            type="Validated SysML Model",
            content="合并模型结果为空",
            status=ProcessStatus.FAILED,
            error_message="合并模型结果为空，无法进行验证与修复"
        )
        state.assigned_tasks.append(validate_task)
        return state
    
    logger.info(f"准备验证和修复合并后的SysML模型 (任务ID: {merged_task.id})")
    
    try:
        # 使用验证和修复函数处理模型
        logger.info("启动验证和修复过程...")
        
        # 获取模型名称（如果在设置中指定）
        model_name = getattr(settings, "llm_model", None)
        
        # 调用验证和修复函数
        fixed_model = validate_and_fix_sysml(merged_model, model_name=model_name)
        
        # 检查结果
        if fixed_model and "elements" in fixed_model:
            # 将验证和修复结果存储在状态中
            elements_count = len(fixed_model.get("elements", []))
            
            # 这里我们创建一个新的"验证修复"任务来存储结果
            validate_task = SysMLTask(
                id=f"VALIDATE-TASK",
                type="Validated SysML Model",
                content=f"验证并修复SysML模型 (包含 {elements_count} 个元素)",
                status=ProcessStatus.COMPLETED,
                result=fixed_model
            )
            
            # 将验证修复任务添加到状态中
            state.assigned_tasks.append(validate_task)
            logger.info(f"SysML模型验证和修复成功，最终模型包含 {elements_count} 个元素")
            
            # 记录元素类型分布，帮助验证修复是否成功
            element_types = {}
            for element in fixed_model.get("elements", []):
                element_type = element.get("type", "未知")
                element_types[element_type] = element_types.get(element_type, 0) + 1
                
            logger.info(f"验证修复后模型的元素类型分布: {element_types}")
        else:
            # 记录验证修复错误，但仍创建一个验证修复任务以避免再次进入验证修复步骤
            error_msg = "验证修复结果无效或为空"
            validate_task = SysMLTask(
                id=f"VALIDATE-TASK-ERROR",
                type="Validated SysML Model",
                content="验证修复失败",
                status=ProcessStatus.FAILED,
                error_message=f"验证修复失败原因: {error_msg}"
            )
            state.assigned_tasks.append(validate_task)
            logger.error(f"SysML模型验证修复失败: {error_msg}")
    
    except Exception as e:
        # 处理异常，但仍创建一个验证修复任务以避免再次进入验证修复步骤
        error_message = f"SysML验证修复Agent处理异常: {str(e)}"
        validate_task = SysMLTask(
            id=f"VALIDATE-TASK-EXCEPTION",
            type="Validated SysML Model",
            content="验证修复异常",
            status=ProcessStatus.FAILED,
            error_message=error_message
        )
        state.assigned_tasks.append(validate_task)
        logger.error(error_message, exc_info=True)
    
    return state


def analyze_errors(errors: List[str]) -> Dict[str, Any]:
    """
    分析错误列表，识别模式和重复出现的错误。
    
    参数:
        errors: 验证器生成的错误列表
        
    返回:
        包含错误分析结果的字典
    """
    analysis_result = {
        "total_errors": len(errors),
        "error_types": {},
        "most_common_patterns": [],
        "id_related_errors": [],
        "reference_errors": [],
        "structure_errors": []
    }
    
    # 按类型分类错误
    for error in errors:
        if ":" in error:
            error_type = error.split(":")[0].strip()
            if error_type not in analysis_result["error_types"]:
                analysis_result["error_types"][error_type] = []
            analysis_result["error_types"][error_type].append(error)
    
    # 查找最常见的错误模式
    error_patterns = {}
    for error in errors:
        # 移除特定的ID，只保留错误模式
        pattern = re.sub(r"'[0-9a-fA-F-]+'", "'ID'", error)
        pattern = re.sub(r'"[0-9a-fA-F-]+"', '"ID"', pattern)
        error_patterns[pattern] = error_patterns.get(pattern, 0) + 1
    
    # 排序找出最常见的模式
    sorted_patterns = sorted(error_patterns.items(), key=lambda x: x[1], reverse=True)
    analysis_result["most_common_patterns"] = [{"pattern": p, "count": c} for p, c in sorted_patterns[:5]]
    
    # 识别ID相关错误
    for error in errors:
        if "ID重复" in error or "缺少'id'" in error or "'id'键" in error:
            analysis_result["id_related_errors"].append(error)
    
    # 识别引用错误
    for error in errors:
        if "引用" in error and "不存在" in error:
            analysis_result["reference_errors"].append(error)
    
    # 识别结构错误
    for error in errors:
        if "结构" in error or "逻辑错误" in error or "父元素" in error:
            analysis_result["structure_errors"].append(error)
    
    # 计算各错误类型的百分比
    for error_type, type_errors in analysis_result["error_types"].items():
        analysis_result["error_types"][error_type] = {
            "errors": type_errors,
            "count": len(type_errors),
            "percentage": len(type_errors) / len(errors) * 100 if errors else 0
        }
    
    # 检测错误之间的关系
    analysis_result["possible_root_causes"] = []
    
    # 检查ID错误是否可能是其他错误的根本原因
    if analysis_result["id_related_errors"]:
        if analysis_result["reference_errors"]:
            analysis_result["possible_root_causes"].append("ID重复或缺失可能导致引用错误")
    
    # 检查结构错误是否涉及特定类型的模式
    package_structure_issues = [e for e in analysis_result.get("structure_errors", []) if "包" in e]
    if package_structure_issues:
        analysis_result["possible_root_causes"].append("模型包结构存在基本问题，可能需要重新组织顶层包")
    
    return analysis_result 