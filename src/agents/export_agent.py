"""
ExportAgent - 负责从Neo4j导出最终的统一SysML JSON模型
基于Gemini方案一的图数据库驱动架构
"""
import logging
import json
import uuid
from typing import Dict, List, Any, Optional

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from src.agents.neo4j_storage_agent import Neo4jStorageAgent

logger = logging.getLogger(__name__)

class ExportAgent:
    """
    模型导出代理，负责从Neo4j导出最终的统一SysML模型
    """
    
    def __init__(self):
        """初始化导出代理"""
        self.storage_agent = None
        
    def process(self, state: WorkflowState) -> WorkflowState:
        """
        处理工作流状态，导出最终的SysML模型
        
        参数:
            state: 当前工作流状态
            
        返回:
            更新后的工作流状态
        """
        logger.info("ExportAgent开始处理")
        
        # 检查是否已经执行过模型导出
        if any(task.type == "Model Export" for task in state.assigned_tasks):
            logger.info("已经执行过模型导出操作，跳过")
            return state
        
        # 检查是否有模型验证任务或其他已完成的任务
        validation_tasks = [task for task in state.assigned_tasks if task.type == "Model Validation"]
        completed_sysml_tasks = [task for task in state.assigned_tasks
                               if task.status == ProcessStatus.COMPLETED and
                               task.result and
                               isinstance(task.result, dict) and
                               'elements' in task.result]

        if not validation_tasks and not completed_sysml_tasks:
            logger.warning("没有找到成功的模型验证任务或SysML任务，无法进行模型导出")
            self._create_export_task(state, "FAILED", "没有找到成功的模型验证任务或SysML任务")
            return state

        # 如果有验证任务但未完成，检查是否有其他已完成的任务
        if validation_tasks and not any(task.status == ProcessStatus.COMPLETED for task in validation_tasks):
            if not completed_sysml_tasks:
                logger.warning("模型验证任务未完成且没有其他可用的SysML任务")
                self._create_export_task(state, "FAILED", "模型验证任务未完成且没有其他可用的SysML任务")
                return state
        
        try:
            # 初始化Neo4j存储代理
            self.storage_agent = Neo4jStorageAgent()
            
            # 从Neo4j导出模型
            exported_model = self.storage_agent.export_to_json()
            
            if not exported_model:
                logger.error("从Neo4j导出模型失败")
                self._create_export_task(state, "FAILED", "从Neo4j导出模型失败")
                return state
            
            # 后处理导出的模型
            final_model = self._post_process_exported_model(exported_model)
            
            # 验证导出的模型
            if not self._validate_exported_model(final_model):
                logger.error("导出的模型验证失败")
                self._create_export_task(state, "FAILED", "导出的模型验证失败")
                return state
            
            # 统计信息
            elements_count = len(final_model.get("elements", []))
            element_types = self._count_element_types(final_model.get("elements", []))

            # 保存导出的模型到文件
            export_file_path = self._save_exported_model(final_model)

            # 创建导出任务结果
            result_msg = f"成功导出包含{elements_count}个元素的统一SysML模型"
            self._create_export_task(state, "COMPLETED", result_msg, {
                "model": final_model,
                "elements_count": elements_count,
                "element_types": element_types,
                "export_file_path": export_file_path
            })

            logger.info(f"模型导出完成: {result_msg}")
            logger.info(f"元素类型分布: {element_types}")
            if export_file_path:
                logger.info(f"📁 导出文件保存至: {export_file_path}")
            
        except Exception as e:
            error_msg = f"模型导出过程发生异常: {str(e)}"
            self._create_export_task(state, "FAILED", error_msg)
            logger.error(error_msg, exc_info=True)
            
        finally:
            # 关闭Neo4j连接
            if self.storage_agent:
                self.storage_agent.close()
                self.storage_agent = None
        
        return state

    def _save_exported_model(self, model: Dict[str, Any]) -> Optional[str]:
        """
        保存导出的模型到文件

        参数:
            model: 导出的模型数据

        返回:
            保存的文件路径，失败时返回None
        """
        try:
            import os
            from datetime import datetime

            # 确保输出目录存在
            output_dir = "data/output"
            os.makedirs(output_dir, exist_ok=True)

            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"exported_sysml_model_{timestamp}.json"
            file_path = os.path.join(output_dir, filename)

            # 保存JSON文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(model, f, indent=2, ensure_ascii=False)

            # 同时保存一个最新版本的副本
            latest_file_path = os.path.join(output_dir, "latest_exported_sysml_model.json")
            with open(latest_file_path, 'w', encoding='utf-8') as f:
                json.dump(model, f, indent=2, ensure_ascii=False)

            logger.info(f"✅ 模型已保存到: {file_path}")
            logger.info(f"✅ 最新版本已保存到: {latest_file_path}")

            return file_path

        except Exception as e:
            logger.error(f"❌ 保存导出模型失败: {e}")
            return None

    def _post_process_exported_model(self, exported_model: Dict[str, Any]) -> Dict[str, Any]:
        """
        后处理导出的模型，确保结构正确
        
        参数:
            exported_model: 从Neo4j导出的原始模型
            
        返回:
            后处理的模型
        """
        logger.info("开始后处理导出的模型")
        
        # 深拷贝以避免修改原始数据
        import copy
        final_model = copy.deepcopy(exported_model)
        
        # 确保模型结构正确
        if "model" not in final_model:
            final_model["model"] = {
                "id": "integrated-model-final",
                "name": "Integrated SysML Model"
            }
        
        if "elements" not in final_model:
            final_model["elements"] = []
        
        # 处理元素列表
        elements = final_model["elements"]
        processed_elements = []
        
        # 不在这里创建标准包结构，因为Neo4j中已经有了
        # 标准包结构应该在合并阶段统一创建，这里只处理现有元素
        
        # 处理现有元素
        for element in elements:
            processed_element = self._process_element(element, [])  # 不需要标准包参数
            if processed_element:
                processed_elements.append(processed_element)
        
        # 去重（基于ID）
        seen_ids = set()
        unique_elements = []
        for element in processed_elements:
            element_id = element.get("id")
            if element_id and element_id not in seen_ids:
                seen_ids.add(element_id)
                unique_elements.append(element)
        
        final_model["elements"] = unique_elements
        
        logger.info(f"后处理完成，最终模型包含{len(unique_elements)}个元素")
        return final_model
    
    def _create_standard_packages(self, model_id: str) -> List[Dict[str, Any]]:
        """
        创建标准包结构
        
        参数:
            model_id: 模型ID
            
        返回:
            标准包列表
        """
        standard_packages = [
            {
                "id": "pkg-requirements-final",
                "type": "Package",
                "name": "Requirements",
                "parentId": model_id
            },
            {
                "id": "pkg-usecases-final",
                "type": "Package", 
                "name": "UseCases",
                "parentId": model_id
            },
            {
                "id": "pkg-systemstructure-final",
                "type": "Package",
                "name": "SystemStructure", 
                "parentId": model_id
            },
            {
                "id": "pkg-systembehavior-final",
                "type": "Package",
                "name": "SystemBehavior",
                "parentId": model_id
            },
            {
                "id": "pkg-libraries-final",
                "type": "Package",
                "name": "Libraries",
                "parentId": model_id
            }
        ]
        
        return standard_packages
    
    def _process_element(self, element: Dict[str, Any], standard_packages: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        处理单个元素，确保其结构正确

        参数:
            element: 原始元素
            standard_packages: 标准包列表（已废弃，保持兼容性）

        返回:
            处理后的元素，如果应该跳过则返回None
        """
        if not element or not isinstance(element, dict):
            return None
        
        # 跳过内部属性
        if element.get("id", "").startswith("_"):
            return None
        
        # 清理元素
        cleaned_element = {}
        
        # 复制基本属性
        for key in ["id", "type", "name"]:
            if key in element:
                cleaned_element[key] = element[key]
        
        # 处理parentId - 保持原有的父子关系
        parent_id = element.get("parentId")
        if parent_id:
            cleaned_element["parentId"] = parent_id
        
        # 复制其他重要属性
        important_attrs = [
            "reqId", "text",  # Requirement
            "visibility", "propertyKind", "aggregation", "typeId", "multiplicity",  # Property
            "sourceId", "targetId",  # Relationship
            "messageSort", "sendEventId", "receiveEventId",  # Message
            "specification"  # ConstraintBlock
        ]
        
        for attr in important_attrs:
            if attr in element:
                cleaned_element[attr] = element[attr]
        
        return cleaned_element
    
    def _reassign_to_standard_package(self, element: Dict[str, Any], standard_packages: List[Dict[str, Any]]) -> Optional[str]:
        """
        将元素重新分配到标准包
        
        参数:
            element: 元素
            standard_packages: 标准包列表
            
        返回:
            新的父包ID，如果不需要重新分配则返回None
        """
        element_type = element.get("type")
        
        # 类型到包的映射
        type_to_package = {
            "Requirement": "pkg-requirements-final",
            "TestCase": "pkg-requirements-final",
            "Actor": "pkg-usecases-final", 
            "UseCase": "pkg-usecases-final",
            "Block": "pkg-systemstructure-final",
            "Property": None,  # Property保持原有父元素
            "Port": None,  # Port保持原有父元素
            "Association": "pkg-systemstructure-final",
            "Connector": None,  # Connector保持原有父元素
            "Activity": "pkg-systembehavior-final",
            "StateMachine": "pkg-systembehavior-final",
            "Interaction": "pkg-systembehavior-final",
            "State": None,  # State保持原有父元素
            "Transition": None,  # Transition保持原有父元素
            "ValueType": "pkg-libraries-final",
            "Enumeration": "pkg-libraries-final",
            "Signal": "pkg-libraries-final",
            "ConstraintBlock": "pkg-libraries-final"
        }
        
        target_package = type_to_package.get(element_type)
        return target_package
    
    def _validate_exported_model(self, model: Dict[str, Any]) -> bool:
        """
        验证导出的模型
        
        参数:
            model: 导出的模型
            
        返回:
            是否有效
        """
        # 基本结构检查
        if not isinstance(model, dict):
            logger.error("导出的模型不是字典")
            return False
        
        if "model" not in model:
            logger.error("导出的模型缺少model字段")
            return False
        
        if "elements" not in model:
            logger.error("导出的模型缺少elements字段")
            return False
        
        model_info = model["model"]
        if not isinstance(model_info, dict) or "id" not in model_info or "name" not in model_info:
            logger.error("模型信息格式不正确")
            return False
        
        elements = model["elements"]
        if not isinstance(elements, list):
            logger.error("elements不是列表")
            return False
        
        # 检查元素
        element_ids = set()
        for i, element in enumerate(elements):
            if not isinstance(element, dict):
                logger.error(f"第{i}个元素不是字典")
                return False
            
            if "id" not in element:
                logger.error(f"第{i}个元素缺少id字段")
                return False
            
            if "type" not in element:
                logger.error(f"第{i}个元素缺少type字段")
                return False
            
            element_id = element["id"]
            if element_id in element_ids:
                logger.error(f"发现重复的元素ID: {element_id}")
                return False
            
            element_ids.add(element_id)
        
        logger.info("导出的模型验证通过")
        return True
    
    def _count_element_types(self, elements: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        统计元素类型分布
        
        参数:
            elements: 元素列表
            
        返回:
            类型计数字典
        """
        type_counts = {}
        for element in elements:
            element_type = element.get("type", "Unknown")
            type_counts[element_type] = type_counts.get(element_type, 0) + 1
        
        return type_counts
    
    def _create_export_task(self, state: WorkflowState, status: str, message: str, result: Dict[str, Any] = None):
        """
        创建模型导出任务记录
        
        参数:
            state: 工作流状态
            status: 任务状态
            message: 状态消息
            result: 任务结果
        """
        task_status = ProcessStatus.COMPLETED if status == "COMPLETED" else ProcessStatus.FAILED
        
        export_task = SysMLTask(
            id=f"EXPORT-TASK-{uuid.uuid4()}",
            type="Model Export",
            content=message,
            status=task_status,
            result=result,
            error_message=message if status == "FAILED" else None
        )
        
        state.assigned_tasks.append(export_task)

def sysml_export_agent(state: WorkflowState) -> WorkflowState:
    """
    SysML模型导出Agent工作流节点入口函数
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    logger.info("SysML模型导出Agent节点开始处理")
    
    agent = ExportAgent()
    return agent.process(state)
