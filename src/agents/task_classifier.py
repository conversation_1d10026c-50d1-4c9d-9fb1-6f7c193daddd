"""
任务提取与分类Agent
直接从文本内容中提取并分类SysML任务
"""
import logging
import uuid
from typing import List,   Literal
from collections import defaultdict

from difflib import SequenceMatcher

from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser
from langchain.output_parsers import OutputFixingParser
from langchain.text_splitter import RecursiveCharacterTextSplitter
from pydantic import BaseModel, Field

from json_repair import repair_json
from src.graph.workflow_state import WorkflowState, SysMLTask, ProcessStatus
from config.settings import settings
import tiktoken
logger = logging.getLogger(__name__)
import json
from src.agents.req_agent import requirement_agent
from src.agents.act_agent import activity_agent
from src.agents.bdd_ibd_agent import bdd_ibd_agent
from src.agents.par_agent import parameter_agent
from src.agents.uc_agent import usecase_agent
from src.agents.stm_agent import state_machine_agent
from src.agents.sd_agent import sequence_agent

# 导入其他可能的agent模块
# 注意：这些模块可能尚未实现，如果未实现，请创建对应的占位文件

# 可以在这里导入更多的agent模块
# from src.agents.state_machine_agent import state_machine_agent
# from src.agents.use_case_agent import use_case_agent
# from src.agents.parameter_agent import parameter_agent
# from src.agents.sequence_agent import sequence_agent
# 注意：merge_agent已废弃，现在使用merge_agent_algorithm

def is_valid_json(json_str):
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError as e:
        logger.error("JSON 不合法：", e)
        return False

class SysMLTaskExtraction(BaseModel):
    """SysML任务提取结果项"""
    type: Literal["Requirement", "State Machine", "Activity", "Use Case", 
                 "Block Definition and Internal Block", "Parameter", "Sequence"] = Field(
        description="SysML图表类型，必须是以下之一: 'Requirement', 'State Machine', 'Activity', 'Use Case', 'Block Definition and Internal Block', 'Parameter', 'Sequence'"
    )
    content: str = Field(description="提取的相关内容")


class SysMLTaskExtractionResult(BaseModel):
    """SysML任务提取结果集合"""
    tasks: List[SysMLTaskExtraction] = Field(description="从文本中提取的SysML任务列表")


# 系统提示模板 - 任务提取与分类
SYSTEM_PROMPT_EXTRACT_AND_CLASSIFY = """
你是一个系统设计助手，专注于MBSE（Model-Based Systems Engineering）。你的任务是分析提供的文本内容，精确识别其中包含的SysML模型相关信息，并将它们分类以便分配给专门的SysML建模Agent。

请将识别出的信息归类为以下类型之一，并提取出对应的具体文本内容：

## 重点关注的四种主要SysML图表类型：

1. **Requirement (需求):** - 最重要
   - 描述系统应实现的功能或非功能约束
   - 包括需求描述、优先级、验证方法
   - 包括需求之间的依赖、派生、验证等关系
   - 例如：系统必须在特定温度范围内工作，响应时间不超过X秒

2. **Block Definition and Internal Block (块定义和内部块):** - 最重要
   - 描述系统中的结构组件（块）、其属性、端口、操作、以及与其他块的关系
   - 描述块的内部结构，包括部件、连接器和端口
   - 包括块的属性、操作、端口、接口、部件、连接器
   - 例如：系统主要组件及其关系、控制器内部组件及其连接

3. **Activity (活动):** - 最重要
   - 描述系统执行的步骤、动作、控制流、并发或选择路径
   - 包括活动步骤、决策点、分支条件、并发活动
   - 例如：用户登录流程、数据处理步骤

4. **State Machine (状态机):** - 最重要
   - 描述系统或组件的行为状态及转换、状态名称、事件等
   - 包括状态名称、转换条件、触发事件
   - 包括状态的进入/退出活动、内部活动
   - 例如：空闲状态、运行状态、错误状态及其转换条件

## 其他SysML图表类型：

5. **Use Case (用例):** 
   - 描述用户与系统之间的交互、系统提供的功能、参与者和场景
   - 包括参与者、用例名称、基本流程和备选流程
   - 包括用例之间的包含、扩展、泛化关系
   - 例如：用户登录系统、管理员配置系统

6. **Parameter (参数):** 
   - 描述系统参数和约束关系
   - 包括数学/物理公式、约束条件
   - 包括参数的单位、范围
   - 例如：温度控制参数、性能指标

7. **Sequence (序列):** 
   - 描述组件之间的交互序列
   - 包括消息、调用、响应、返回值
   - 例如：系统启动过程中组件间的消息交换

重要说明：
1. 每个内容片段必须明确分类为上述7种类型之一，不得使用其他类型名称，不是每一类型都需要提取，而是根据具体的文本内容来决定；同一个内容片段可能对应多个类型，例如片段1内容，可以对应Requirement和Use Case两种类型
2. 每种类型的内容将被分配给专门的Agent处理，因此分类必须准确
3. 对于每种识别出的类型，请提取出该类型对应的**最简洁但完整**的描述文本
4. 如果一个文本片段包含多种类型信息，请分别提取并正确分类
5. 提取的内容必须是原始文本的子集，保持原文的表达方式
6. 请特别注意文本的上下文连贯性，确保提取的内容是完整的语义单元
7. 请特别关注前四种图表类型（需求、块定义和内部块、活动、状态机），这些是SysML中最重要的图表类型，应优先提取

你的输出必须是一个JSON格式的任务列表，每个任务包含两个字段：
- `type`: SysML图表类型，必须是以下之一: "Requirement", "State Machine", "Activity", "Use Case", "Block Definition and Internal Block", "Parameter", "Sequence"
- `content`: 提取的具体内容，应包含足够的细节以便创建对应的SysML图表
"""

# 用户提示模板 - 任务提取与分类
USER_PROMPT_EXTRACT_AND_CLASSIFY = """
请从以下文本中提取适合创建各种SysML图表的内容，并按照图表类型进行分类：

{text}

请确保：
1. 全面分析文本，不遗漏任何有价值的信息
2. 准确分类每个内容片段到对应的SysML图表类型
3. 提取的内容足够详细，同时尽量简洁
4. 输出格式符合要求，每个任务有明确的type和content字段
5. 内容的语义完整性，避免截断重要信息
6. 特别关注需求、块定义和内部块、活动、状态机这四种最重要的SysML图表类型
"""

# 系统提示模板 - 任务整合与优化
SYSTEM_PROMPT_INTEGRATE_AND_OPTIMIZE = """
你是一个MBSE（Model-Based Systems Engineering）专家，负责整合和优化从文档中提取的SysML任务。

你将收到一系列已按类型分类的SysML任务，这些任务是从文档的不同部分提取出来的。你的目标是：

1. **去除重复内容**：识别并合并重复或高度相似的内容
2. **整合相关内容**：将同一类型中相关的内容整合成连贯的描述
3. **确保完整性**：确保整合后的内容保留所有重要信息，不丢失关键细节
4. **提高一致性**：解决可能存在的矛盾或不一致之处
5. **优化结构**：按逻辑顺序组织内容，提高可读性和可理解性
6. **保持专业性**：保持原始文本的专业术语和表达方式


请遵循以下原则：
- 保持原始文本的专业术语和表达方式
- 不要添加原始任务中不存在的信息
- 确保输出的每个任务类型都包含完整、连贯的内容
- 注意保留具体的技术细节、参数和约束条件
- 特别关注需求、块定义和内部块、活动、状态机这四种最重要的SysML图表类型

你的输出应该是一个优化后的任务列表，每个任务包含两个字段：
- `type`: SysML图表类型，与输入任务的类型相同
- `content`: 整合优化后的内容，应该是完整、连贯、无重复的描述
"""

# 用户提示模板 - 任务整合与优化
USER_PROMPT_INTEGRATE_AND_OPTIMIZE = """
请整合和优化以下从文档中提取的SysML任务，确保内容完整、无重复、结构清晰：

{tasks}

请确保：
1. 去除重复内容
2. 整合相关内容
3. 保留所有重要信息和技术细节
4. 解决可能的矛盾或不一致
5. 按逻辑顺序组织内容
6. 重点优化需求、块定义和内部块、活动、状态机这四种最重要的SysML图表类型
"""


def count_tokens(text: str) -> int:
    """
    计算文本的token数量
    
    参数:
        text: 要计算的文本
        
    返回:
        token数量
    """
    encoding = tiktoken.encoding_for_model("gpt-4")
    return len(encoding.encode(text))


def chunk_text(text: str, chunk_size: int = 4000, chunk_overlap: int = 600) -> List[str]:
    """
    将文本分块
    
    参数:
        text: 要分块的文本
        chunk_size: 块大小（token数）
        chunk_overlap: 块重叠大小（token数）
        
    返回:
        文本块列表
    """
    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=count_tokens,
        separators=["\n\n", "\n", "。", ". ", " ", ""]
    )
    
    return splitter.split_text(text)


def calculate_similarity(text1: str, text2: str) -> float:
    """
    计算两段文本的相似度
    
    参数:
        text1: 第一段文本
        text2: 第二段文本
        
    返回:
        相似度得分(0-1)
    """
    return SequenceMatcher(None, text1, text2).ratio()


def merge_similar_contents(contents: List[str], similarity_threshold: float = 0.7) -> List[str]:
    """
    合并相似的内容
    
    参数:
        contents: 内容列表
        similarity_threshold: 相似度阈值
        
    返回:
        合并后的内容列表
    """
    if not contents:
        return []
    
    # 按长度降序排序，优先保留较长的内容
    sorted_contents = sorted(contents, key=len, reverse=True)
    merged_contents = []
    merged_indices = set()
    
    for i, content1 in enumerate(sorted_contents):
        if i in merged_indices:
            continue
            
        merged_content = content1
        merged_indices.add(i)
        
        # 查找与当前内容相似的其他内容
        for j, content2 in enumerate(sorted_contents):
            if j in merged_indices or i == j:
                continue
                
            # 计算相似度
            similarity = calculate_similarity(content1, content2)
            
            # 如果相似度高于阈值，合并内容
            if similarity > similarity_threshold:
                # 检查content2是否包含content1中没有的信息
                if len(content2) > len(content1) * 0.3:  # 只有当content2长度至少为content1的30%时才考虑合并
                    # 简单合并策略：保留较长的内容，并添加较短内容中独特的部分
                    if len(content2) > len(merged_content):
                        merged_content = content2
                    
                merged_indices.add(j)
        
        merged_contents.append(merged_content)
    
    return merged_contents


def merge_tasks_by_type(tasks: List[SysMLTaskExtraction]) -> List[SysMLTaskExtraction]:
    """
    将相同类型的任务合并为一个任务
    
    参数:
        tasks: 任务列表
        
    返回:
        合并后的任务列表
    """
    # 使用defaultdict按类型分组任务
    task_groups = defaultdict(list)
    for task in tasks:
        # 过滤掉内容过短的任务
        # if len(task.content) >= settings.task_extraction_min_content_length:
            task_groups[task.type].append(task.content)
    
    # 合并每种类型的任务内容
    merged_tasks = []
    for task_type, contents in task_groups.items():
        # 使用智能合并策略
        if settings.task_extraction_enhanced:
            # 合并相似内容
            unique_contents = merge_similar_contents(
                contents, 
                similarity_threshold=settings.task_extraction_similarity_threshold
            )
            
            # 构建合并后的内容
            merged_content = "\n\n".join(unique_contents)
        else:
            # 原始简单合并策略
            merged_content = "\n".join(contents)
        
        # 创建合并后的任务
        merged_tasks.append(SysMLTaskExtraction(
            type=task_type,
            content=merged_content.strip()
        ))
    
    return merged_tasks


def optimize_tasks(tasks: List[SysMLTaskExtraction]) -> List[SysMLTaskExtraction]:
    """
    使用LLM优化任务内容，去除重复、整合相关内容
    
    参数:
        tasks: 任务列表
        
    返回:
        优化后的任务列表
    """
    if not tasks:
        return []
    
    try:
        logger.info("开始优化任务内容")
        
        # 将任务转换为格式化文本
        tasks_text = ""
        for i, task in enumerate(tasks, 1):
            tasks_text += f"任务 {i}:\n类型: {task.type}\n内容:\n{task.content}\n\n---\n\n"
        
        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", SYSTEM_PROMPT_INTEGRATE_AND_OPTIMIZE),
            ("human", USER_PROMPT_INTEGRATE_AND_OPTIMIZE.format(tasks=tasks_text))
        ])
        
        # 创建LLM
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )
        
        # 创建解析器
        output_parser = JsonOutputParser(pydantic_object=SysMLTaskExtractionResult)
        json_fix_parser = OutputFixingParser.from_llm(parser=output_parser, llm=llm)
        # 完整提示
        full_prompt = PromptTemplate(
            template=SYSTEM_PROMPT_INTEGRATE_AND_OPTIMIZE + "\n{format_instructions}\n" + USER_PROMPT_INTEGRATE_AND_OPTIMIZE,
            input_variables=["tasks"],
            partial_variables={"format_instructions": output_parser.get_format_instructions()},
        )
        
        # 创建链
        chain = full_prompt | llm | output_parser
        
        # 执行链并获取优化后的任务
        result = chain.invoke({"tasks": tasks_text})
        result = json.loads(repair_json(json.dumps(result)))
        if not is_valid_json(json.dumps(result)):
            logger.error("JSON 不合法")
            result = json_fix_parser.parse(result)
        

        logger.info(f"优化后的任务: {result}")
        if result and 'tasks' in result:
            optimized_tasks = []
            for task_dict in result['tasks']:
                optimized_tasks.append(SysMLTaskExtraction(
                    type=task_dict['type'],
                    content=task_dict['content']
                ))
            
            logger.info(f"任务优化成功，优化后任务数量: {len(optimized_tasks)}")
            return optimized_tasks
        else:
            logger.warning("任务优化未返回有效结果，使用原始任务")
            return tasks
            
    except Exception as e:
        logger.error(f"任务优化失败: {str(e)}", exc_info=True)
        logger.warning("使用原始任务")
        return tasks


def extract_and_assign_tasks(state: WorkflowState) -> WorkflowState:
    """
    从文本内容中直接提取并分配SysML任务
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    if not state.expanded_content:
        state.error_message = "没有可提取任务的内容"
        return state
    
    try:
        logger.info("开始从内容中提取SysML任务")
        
        # 创建LLM
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )

        # 创建输出解析器
        output_parser = JsonOutputParser(pydantic_object=SysMLTaskExtractionResult)
        json_fix_parser = OutputFixingParser.from_llm(parser=output_parser, llm=llm)
        prompt = PromptTemplate(
            template=SYSTEM_PROMPT_EXTRACT_AND_CLASSIFY + "\n{format_instructions}\n" + USER_PROMPT_EXTRACT_AND_CLASSIFY,
            input_variables=["text"],
            partial_variables={"format_instructions": output_parser.get_format_instructions()},
        )
        

        
        # 创建链
        chain = prompt | llm | output_parser
        
        # 分块文本
        chunks = chunk_text(state.expanded_content, settings.chunk_size, settings.chunk_overlap)
        
        all_tasks = []
        
        # 逐块处理
        for i, chunk in enumerate(chunks):
            logger.info(f"处理第 {i+1}/{len(chunks)} 个文本块")
            
            # 执行链并提取任务
            result = chain.invoke({"text": chunk})

            result = json.loads(repair_json(json.dumps(result)))


            if not is_valid_json(json.dumps(result)):
                logger.error(f"块 {i+1} 的JSON格式不合法")
                result = json_fix_parser.parse(result)

            logger.info(f"从块 {i+1} 中提取到 {len(result.get('tasks', []))} 个任务")
            
            # 添加到任务列表
            if result and 'tasks' in result:
                # 从字典中获取任务列表
                task_list = result['tasks']
                for task_dict in task_list:
                    # 创建SysMLTaskExtraction对象
                    task = SysMLTaskExtraction(
                        type=task_dict['type'],
                        content=task_dict['content']
                    )
                    all_tasks.append(task)
        
        logger.info(f"提取到的原始任务数量: {len(all_tasks)}")
        
        # 合并相同类型的任务
        merged_tasks = merge_tasks_by_type(all_tasks)
        logger.info(f"初步合并后的任务数量: {len(merged_tasks)}")
        
        # 如果启用了增强功能，使用LLM进一步优化任务
        if settings.task_extraction_enhanced and len(merged_tasks) > 0:
            logger.info("开始使用LLM优化任务")
            optimized_tasks = optimize_tasks(merged_tasks)
            logger.info(f"LLM优化后的任务数量: {len(optimized_tasks)}")
            final_tasks = optimized_tasks
        else:
            final_tasks = merged_tasks
        
        # 转换为SysMLTask对象
        sysml_tasks = []
        for task in final_tasks:
            task_id = f"TASK-{uuid.uuid4().hex[:8]}"
            sysml_task = SysMLTask(
                id=task_id,
                type=task.type,
                content=task.content,
                status=ProcessStatus.NOT_STARTED
            )
            sysml_tasks.append(sysml_task)
            
            # 记录分配信息
            agent_type = ""
            if task.type == "Requirement":
                agent_type = "需求图Agent"
            elif task.type == "State Machine":
                agent_type = "状态机图Agent"
            elif task.type == "Activity":
                agent_type = "活动图Agent"
            elif task.type == "Use Case":
                agent_type = "用例图Agent"
            elif task.type == "Block Definition and Internal Block":
                agent_type = "IBD/BDD图Agent"
            elif task.type == "Parameter":
                agent_type = "参数图Agent"
            elif task.type == "Sequence":
                agent_type = "序列图Agent"
            
            logger.info(f"创建任务 {task_id}, 类型: {task.type}, 计划分配至 {agent_type}")
        
        # 先更新状态
        state.assigned_tasks = sysml_tasks
        state.tasks_assigned = True
        
        # 然后再执行任务处理 - 根据任务类型分配给不同的agent处理
        for task in sysml_tasks:
            if task.type == "Requirement":
                logger.info(f"分配任务 {task.id} 给需求图Agent处理")
                state = requirement_agent(state, task.id, task.content)
            elif task.type == "Activity":
                logger.info(f"分配任务 {task.id} 给活动图Agent处理")
                state = activity_agent(state, task.id, task.content)
            elif task.type == "Block Definition and Internal Block":
                logger.info(f"分配任务 {task.id} 给IBD/BDD图Agent处理")
                state = bdd_ibd_agent(state, task.id, task.content)
            elif task.type == "State Machine":
                logger.info(f"分配任务 {task.id} 给状态机图Agent处理")
                state = state_machine_agent(state, task.id, task.content)
            elif task.type == "Use Case":
                logger.info(f"分配任务 {task.id} 给用例图Agent处理")
                state = usecase_agent(state, task.id, task.content)
            elif task.type == "Parameter":
                logger.info(f"分配任务 {task.id} 给参数图Agent处理")
                state = parameter_agent(state, task.id, task.content)
            elif task.type == "Sequence":
                logger.info(f"分配任务 {task.id} 给序列图Agent处理")
                state = sequence_agent(state, task.id, task.content)
            else:
                logger.warning(f"暂不支持处理任务类型 {task.type}，任务 {task.id} 将保持未开始状态")
        
        logger.info(f"任务提取与分配成功，总共 {len(sysml_tasks)} 个任务")
        
        return state
        
    except Exception as e:
        logger.error(f"任务提取与分配失败: {str(e)}", exc_info=True)
        state.error_message = f"任务提取与分配失败: {str(e)}"
        return state