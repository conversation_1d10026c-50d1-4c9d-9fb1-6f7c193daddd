#!/usr/bin/env python3
"""
SysML关系创建器 - 基于示例store代码的专门关系处理逻辑
参考temp目录下的7个示例store代码实现
"""

import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class SysMLRelationshipCreator:
    """SysML关系创建器"""
    
    def __init__(self):
        self.elements_by_id = {}
    
    def create_relationships(self, session, elements: List[Dict], source_type: str):
        """
        根据SysML任务类型创建相应的关系
        
        参数:
            session: Neo4j会话
            elements: 元素列表
            source_type: 任务类型
        """
        # 构建元素ID映射
        self.elements_by_id = {elem.get('id'): elem for elem in elements if elem.get('id')}
        
        logger.info(f"开始为{source_type}任务创建关系")
        
        # 根据任务类型调用相应的关系创建方法
        if source_type == "Requirement":
            self._create_requirement_relationships(session)
        elif source_type == "Block Definition and Internal Block":
            self._create_bdd_ibd_relationships(session)
        elif source_type == "Activity":
            self._create_activity_relationships(session)
        elif source_type == "State Machine":
            self._create_state_machine_relationships(session)
        elif source_type == "Use Case":
            self._create_use_case_relationships(session)
        elif source_type == "Parameter":
            self._create_parameter_relationships(session)
        elif source_type == "Sequence":
            self._create_sequence_relationships(session)
        else:
            # 默认只创建CONTAINS关系
            self._create_basic_relationships(session)
    
    def _create_requirement_relationships(self, session):
        """创建需求图关系 - 参考req_store.py"""
        logger.info("创建需求图关系")
        
        # 1. CONTAINS关系
        contains_rels = []
        for element_id, element_data in self.elements_by_id.items():
            parent_id = element_data.get("parentId")
            if parent_id and parent_id != element_id:
                parent_data = self.elements_by_id.get(parent_id)
                parent_type = parent_data.get("type") if parent_data else "Model"
                elem_type = element_data.get("type")
                
                # 排除关系元素
                if parent_type in ["Model", "Package"] and elem_type not in ["DeriveReqt", "Satisfy", "Verify"]:
                    contains_rels.append({
                        "from_id": parent_id,
                        "to_id": element_id
                    })
        
        if contains_rels:
            query = """
            UNWIND $rel_list AS rel_data 
            MATCH (from_node {elementId: rel_data.from_id}) 
            MATCH (to_node {elementId: rel_data.to_id}) 
            MERGE (from_node)-[r:CONTAINS]->(to_node) 
            RETURN count(r)
            """
            session.run(query, {"rel_list": contains_rels})
            logger.info(f"创建了{len(contains_rels)}个CONTAINS关系")
        
        # 2. 特定关系
        specific_rels = []
        for element_id, element_data in self.elements_by_id.items():
            elem_type = element_data.get("type")
            rel_props = {"relationId": element_id, "type": elem_type}
            if "name" in element_data:
                rel_props["name"] = element_data["name"]
            
            if elem_type == "DeriveReqt":
                from_id = element_data.get("derivedRequirementId")
                to_id = element_data.get("sourceRequirementId")
                if from_id and to_id:
                    specific_rels.append({
                        "type": "DERIVES_FROM",
                        "from_id": from_id,
                        "to_id": to_id,
                        "props": rel_props
                    })
            elif elem_type == "Satisfy":
                from_id = element_data.get("blockId")
                to_id = element_data.get("requirementId")
                if from_id and to_id:
                    specific_rels.append({
                        "type": "SATISFIES",
                        "from_id": from_id,
                        "to_id": to_id,
                        "props": rel_props
                    })
            elif elem_type == "Verify":
                from_id = element_data.get("testCaseId")
                to_id = element_data.get("requirementId")
                if from_id and to_id:
                    specific_rels.append({
                        "type": "VERIFIES",
                        "from_id": from_id,
                        "to_id": to_id,
                        "props": rel_props
                    })
        
        # 批量创建特定关系
        for rel_type in ["DERIVES_FROM", "SATISFIES", "VERIFIES"]:
            type_rels = [r for r in specific_rels if r["type"] == rel_type]
            if type_rels:
                query = f"""
                UNWIND $rel_list AS rel_data
                MATCH (from_node {{elementId: rel_data.from_id}})
                MATCH (to_node {{elementId: rel_data.to_id}})
                MERGE (from_node)-[r:`{rel_type}` {{relationId: rel_data.props.relationId}}]->(to_node)
                ON CREATE SET r = rel_data.props
                ON MATCH SET r += rel_data.props
                RETURN count(r)
                """
                session.run(query, {"rel_list": type_rels})
                logger.info(f"创建了{len(type_rels)}个{rel_type}关系")
    
    def _create_bdd_ibd_relationships(self, session):
        """创建BDD/IBD关系 - 参考bdd_and_ibd_store.py"""
        logger.info("创建BDD/IBD关系")
        
        # 1. 父子关系 (HAS_CHILD)
        parent_child_rels = []
        for element_id, element_data in self.elements_by_id.items():
            parent_id = element_data.get('parentId')
            if parent_id:
                parent_child_rels.append({
                    "parent_id": parent_id,
                    "child_id": element_id
                })
        
        if parent_child_rels:
            query = """
            UNWIND $rel_list AS rel_data
            MATCH (p {elementId: rel_data.parent_id})
            MATCH (c {elementId: rel_data.child_id})
            MERGE (p)-[:HAS_CHILD]->(c)
            RETURN count(*)
            """
            session.run(query, {"rel_list": parent_child_rels})
            logger.info(f"创建了{len(parent_child_rels)}个HAS_CHILD关系")
        
        # 2. 类型关系
        type_rels = []
        for element_id, element_data in self.elements_by_id.items():
            elem_type = element_data.get('type')
            
            # Property -> Type
            if elem_type == 'Property' and element_data.get('typeId'):
                type_rels.append({
                    "from_id": element_id,
                    "to_id": element_data['typeId'],
                    "rel_type": "HAS_TYPE"
                })
            
            # Port -> Type
            elif elem_type in ('FullPort', 'ProxyPort') and element_data.get('typeId'):
                type_rels.append({
                    "from_id": element_id,
                    "to_id": element_data['typeId'],
                    "rel_type": "PORT_TYPE"
                })
        
        # 批量创建类型关系
        for rel_type in ["HAS_TYPE", "PORT_TYPE"]:
            type_specific_rels = [r for r in type_rels if r["rel_type"] == rel_type]
            if type_specific_rels:
                query = f"""
                UNWIND $rel_list AS rel_data
                MATCH (from_node {{elementId: rel_data.from_id}})
                MATCH (to_node {{elementId: rel_data.to_id}})
                MERGE (from_node)-[r:`{rel_type}`]->(to_node)
                RETURN count(r)
                """
                session.run(query, {"rel_list": type_specific_rels})
                logger.info(f"创建了{len(type_specific_rels)}个{rel_type}关系")
        
        # 3. Association -> memberEndIds
        assoc_rels = []
        for element_id, element_data in self.elements_by_id.items():
            if element_data.get('type') == 'Association':
                for member_id in element_data.get('memberEndIds', []):
                    assoc_rels.append({
                        "assoc_id": element_id,
                        "member_id": member_id
                    })
        
        if assoc_rels:
            query = """
            UNWIND $rel_list AS rel_data
            MATCH (a:Association {elementId: rel_data.assoc_id})
            MATCH (p:Property {elementId: rel_data.member_id})
            MERGE (a)-[:ASSOC_MEMBER]->(p)
            RETURN count(*)
            """
            session.run(query, {"rel_list": assoc_rels})
            logger.info(f"创建了{len(assoc_rels)}个ASSOC_MEMBER关系")
        
        # 4. Connector端点关系 - 修复版本
        connector_rels = []
        for element_id, element_data in self.elements_by_id.items():
            elem_type = element_data.get('type')
            if elem_type and elem_type.endswith('Connector'):
                # 处理end1和end2
                for end_name in ('end1', 'end2'):
                    end_data = element_data.get(end_name, {})
                    if isinstance(end_data, dict):
                        # 检查所有可能的引用字段
                        for ref_field in ('partRefId', 'propertyRefId', 'portRefId'):
                            ref_id = end_data.get(ref_field)
                            if ref_id and ref_id in self.elements_by_id:
                                connector_rels.append({
                                    "connector_id": element_id,
                                    "ref_id": ref_id,
                                    "role": end_name,
                                    "ref_field": ref_field
                                })

        if connector_rels:
            query = """
            UNWIND $rel_list AS rel_data
            MATCH (c {elementId: rel_data.connector_id})
            MATCH (x {elementId: rel_data.ref_id})
            MERGE (c)-[:CONNECT_END {role: rel_data.role, ref_field: rel_data.ref_field}]->(x)
            RETURN count(*)
            """
            session.run(query, {"rel_list": connector_rels})
            logger.info(f"创建了{len(connector_rels)}个CONNECT_END关系")
    
    def _create_basic_relationships(self, session):
        """创建基本的CONTAINS关系"""
        logger.info("创建基本CONTAINS关系")
        
        contains_rels = []
        for element_id, element_data in self.elements_by_id.items():
            parent_id = element_data.get("parentId")
            if parent_id and parent_id != element_id:
                contains_rels.append({
                    "from_id": parent_id,
                    "to_id": element_id
                })
        
        if contains_rels:
            query = """
            UNWIND $rel_list AS rel_data 
            MATCH (from_node {elementId: rel_data.from_id}) 
            MATCH (to_node {elementId: rel_data.to_id}) 
            MERGE (from_node)-[r:CONTAINS]->(to_node) 
            RETURN count(r)
            """
            session.run(query, {"rel_list": contains_rels})
            logger.info(f"创建了{len(contains_rels)}个CONTAINS关系")

    def _create_activity_relationships(self, session):
        """创建活动图关系 - 参考act_store.py"""
        logger.info("创建活动图关系")

        # 1. CONTAINS关系
        contains_rels = []
        for element_id, element_data in self.elements_by_id.items():
            parent_id = element_data.get("parentId")
            if parent_id and parent_id != element_id:
                contains_rels.append({
                    "parentId": parent_id,
                    "childId": element_id
                })

        if contains_rels:
            query = """
            UNWIND $batch as d
            MATCH (p {elementId: d.parentId})
            MATCH (c {elementId: d.childId})
            MERGE (p)-[r:CONTAINS]->(c)
            RETURN count(r)
            """
            session.run(query, {"batch": contains_rels})
            logger.info(f"创建了{len(contains_rels)}个CONTAINS关系")

        # 2. 控制流和对象流关系
        flow_rels = {"CONTROL_FLOW": [], "OBJECT_FLOW": []}
        for element_id, element_data in self.elements_by_id.items():
            elem_type = element_data.get("type")
            if elem_type == "ControlFlow":
                source_id = element_data.get("sourceId")
                target_id = element_data.get("targetId")
                if source_id and target_id:
                    flow_rels["CONTROL_FLOW"].append({
                        "sourceId": source_id,
                        "targetId": target_id,
                        "props": {
                            "elementId": element_id,
                            "type": elem_type,
                            "name": element_data.get("name", "")
                        }
                    })
            elif elem_type == "ObjectFlow":
                source_id = element_data.get("sourceId")
                target_id = element_data.get("targetId")
                if source_id and target_id:
                    flow_rels["OBJECT_FLOW"].append({
                        "sourceId": source_id,
                        "targetId": target_id,
                        "props": {
                            "elementId": element_id,
                            "type": elem_type,
                            "name": element_data.get("name", "")
                        }
                    })

        # 创建流关系
        for rel_type, rels in flow_rels.items():
            if rels:
                query = f"""
                UNWIND $batch as d
                MATCH (s {{elementId: d.sourceId}})
                MATCH (t {{elementId: d.targetId}})
                MERGE (s)-[r:`{rel_type}` {{elementId: d.props.elementId}}]->(t)
                SET r += d.props
                RETURN count(r)
                """
                session.run(query, {"batch": rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

        # 3. HAS_PIN关系 (Action -> Pin)
        pin_rels = []
        for element_id, element_data in self.elements_by_id.items():
            elem_type = element_data.get("type")
            if elem_type in ["InputPin", "OutputPin"]:
                parent_id = element_data.get("parentId")
                if parent_id:
                    parent_data = self.elements_by_id.get(parent_id)
                    if parent_data and parent_data.get("type") in ["CallBehaviorAction", "Action"]:
                        pin_rels.append({
                            "actionId": parent_id,
                            "pinId": element_id
                        })

        if pin_rels:
            query = """
            UNWIND $batch as d
            MATCH (a {elementId: d.actionId})
            WHERE labels(a)[0] IN ['CallBehaviorAction', 'Action']
            MATCH (p {elementId: d.pinId})
            WHERE labels(p)[0] IN ['InputPin', 'OutputPin']
            MERGE (a)-[r:HAS_PIN]->(p)
            RETURN count(r)
            """
            session.run(query, {"batch": pin_rels})
            logger.info(f"创建了{len(pin_rels)}个HAS_PIN关系")

    def _create_state_machine_relationships(self, session):
        """创建状态机关系 - 参考stm_store.py"""
        logger.info("创建状态机关系")

        # 基本的结构关系
        structure_rels = {"CONTAINS": [], "HAS_REGION": [], "HAS_SUBVERTEX": [], "HAS_TRANSITION": []}

        for element_id, element_data in self.elements_by_id.items():
            parent_id = element_data.get("parentId")
            if not parent_id:
                continue

            parent_data = self.elements_by_id.get(parent_id)
            if not parent_data:
                continue

            parent_type = parent_data.get("type")
            elem_type = element_data.get("type")

            if parent_type in ["Model", "Package"]:
                structure_rels["CONTAINS"].append({"from_id": parent_id, "to_id": element_id})
            elif parent_type == "StateMachine" and elem_type == "Region":
                structure_rels["HAS_REGION"].append({"from_id": parent_id, "to_id": element_id})
            elif parent_type == "State" and elem_type == "Region":
                structure_rels["HAS_REGION"].append({"from_id": parent_id, "to_id": element_id})
            elif parent_type == "Region":
                if elem_type in ["State", "Pseudostate"]:
                    structure_rels["HAS_SUBVERTEX"].append({"from_id": parent_id, "to_id": element_id})
                elif elem_type == "Transition":
                    structure_rels["HAS_TRANSITION"].append({"from_id": parent_id, "to_id": element_id})

        # 批量创建结构关系
        for rel_type, rels in structure_rels.items():
            if rels:
                query = f"""
                UNWIND $rel_list AS rel_data
                MATCH (from_node {{elementId: rel_data.from_id}})
                MATCH (to_node {{elementId: rel_data.to_id}})
                MERGE (from_node)-[r:`{rel_type}`]->(to_node)
                RETURN count(r)
                """
                session.run(query, {"rel_list": rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

        # Transition特定关系
        transition_rels = []
        for element_id, element_data in self.elements_by_id.items():
            if element_data.get("type") == "Transition":
                source_id = element_data.get("sourceId")
                target_id = element_data.get("targetId")
                if source_id and target_id:
                    transition_rels.extend([
                        {"from_id": element_id, "to_id": source_id, "rel_type": "HAS_SOURCE"},
                        {"from_id": element_id, "to_id": target_id, "rel_type": "HAS_TARGET"}
                    ])

        # 批量创建Transition关系
        for rel_type in ["HAS_SOURCE", "HAS_TARGET"]:
            type_rels = [r for r in transition_rels if r["rel_type"] == rel_type]
            if type_rels:
                query = f"""
                UNWIND $rel_list AS rel_data
                MATCH (from_node {{elementId: rel_data.from_id}})
                MATCH (to_node {{elementId: rel_data.to_id}})
                MERGE (from_node)-[r:`{rel_type}`]->(to_node)
                RETURN count(r)
                """
                session.run(query, {"rel_list": type_rels})
                logger.info(f"创建了{len(type_rels)}个{rel_type}关系")

    def _create_use_case_relationships(self, session):
        """创建用例图关系 - 参考uc_store.py"""
        logger.info("创建用例图关系")

        # 基本CONTAINS关系
        self._create_basic_relationships(session)

        # 用例图关系 - 修复版本
        assoc_rels = []
        include_rels = []
        extend_rels = []

        for element_id, element_data in self.elements_by_id.items():
            elem_type = element_data.get("type")

            if elem_type == "Association":
                # 使用sourceId和targetId而不是memberEndIds
                source_id = element_data.get("sourceId")
                target_id = element_data.get("targetId")
                if source_id and target_id and source_id in self.elements_by_id and target_id in self.elements_by_id:
                    assoc_rels.append({
                        "source": source_id,
                        "target": target_id,
                        "assoc_id": element_id
                    })
            elif elem_type == "Include":
                source_id = element_data.get("sourceId")
                target_id = element_data.get("targetId")
                if source_id and target_id and source_id in self.elements_by_id and target_id in self.elements_by_id:
                    include_rels.append({
                        "source": source_id,
                        "target": target_id,
                        "include_id": element_id
                    })
            elif elem_type == "Extend":
                source_id = element_data.get("sourceId")
                target_id = element_data.get("targetId")
                if source_id and target_id and source_id in self.elements_by_id and target_id in self.elements_by_id:
                    extend_rels.append({
                        "source": source_id,
                        "target": target_id,
                        "extend_id": element_id
                    })

        # 创建关系
        if assoc_rels:
            query = """
            UNWIND $rel_list AS rel_data
            MATCH (source {elementId: rel_data.source})
            MATCH (target {elementId: rel_data.target})
            MERGE (source)-[r:ASSOCIATES {relationId: rel_data.assoc_id}]->(target)
            RETURN count(r)
            """
            session.run(query, {"rel_list": assoc_rels})
            logger.info(f"创建了{len(assoc_rels)}个ASSOCIATES关系")

        if include_rels:
            query = """
            UNWIND $rel_list AS rel_data
            MATCH (source {elementId: rel_data.source})
            MATCH (target {elementId: rel_data.target})
            MERGE (source)-[r:INCLUDES {relationId: rel_data.include_id}]->(target)
            RETURN count(r)
            """
            session.run(query, {"rel_list": include_rels})
            logger.info(f"创建了{len(include_rels)}个INCLUDES关系")

        if extend_rels:
            query = """
            UNWIND $rel_list AS rel_data
            MATCH (source {elementId: rel_data.source})
            MATCH (target {elementId: rel_data.target})
            MERGE (source)-[r:EXTENDS {relationId: rel_data.extend_id}]->(target)
            RETURN count(r)
            """
            session.run(query, {"rel_list": extend_rels})
            logger.info(f"创建了{len(extend_rels)}个EXTENDS关系")

    def _create_parameter_relationships(self, session):
        """创建参数图关系 - 参考par_store.py"""
        logger.info("创建参数图关系")

        # 基本CONTAINS关系
        self._create_basic_relationships(session)

        # ConstraintProperty关系
        cp_rels = []
        for element_id, element_data in self.elements_by_id.items():
            if element_data.get("type") == "ConstraintProperty":
                parent_id = element_data.get("parentId")
                type_id = element_data.get("typeId")
                if parent_id and type_id:
                    cp_rels.extend([
                        {"from_id": parent_id, "to_id": element_id, "rel_type": "OWNS"},
                        {"from_id": element_id, "to_id": type_id, "rel_type": "REFERENCES"}
                    ])

        # 批量创建ConstraintProperty关系
        for rel_type in ["OWNS", "REFERENCES"]:
            type_rels = [r for r in cp_rels if r["rel_type"] == rel_type]
            if type_rels:
                query = f"""
                UNWIND $rel_list AS rel_data
                MATCH (from_node {{elementId: rel_data.from_id}})
                MATCH (to_node {{elementId: rel_data.to_id}})
                MERGE (from_node)-[r:`{rel_type}`]->(to_node)
                RETURN count(r)
                """
                session.run(query, {"rel_list": type_rels})
                logger.info(f"创建了{len(type_rels)}个{rel_type}关系")

    def _create_sequence_relationships(self, session):
        """创建序列图关系 - 参考sd_store.py"""
        logger.info("创建序列图关系")

        # 基本CONTAINS关系
        self._create_basic_relationships(session)

        # 序列图特定关系
        seq_rels = []

        for element_id, element_data in self.elements_by_id.items():
            elem_type = element_data.get("type")

            if elem_type == "Lifeline" and element_data.get("representsId"):
                seq_rels.append({
                    "from_id": element_id,
                    "to_id": element_data["representsId"],
                    "rel_type": "REPRESENTS_ELEMENT"
                })
            elif elem_type == "Message":
                if element_data.get("sendEventId"):
                    seq_rels.append({
                        "from_id": element_id,
                        "to_id": element_data["sendEventId"],
                        "rel_type": "HAS_SEND_EVENT"
                    })
                if element_data.get("receiveEventId"):
                    seq_rels.append({
                        "from_id": element_id,
                        "to_id": element_data["receiveEventId"],
                        "rel_type": "HAS_RECEIVE_EVENT"
                    })
            elif elem_type == "MessageOccurrenceSpecification":
                if element_data.get("coveredId"):
                    seq_rels.append({
                        "from_id": element_id,
                        "to_id": element_data["coveredId"],
                        "rel_type": "COVERS_LIFELINE"
                    })

        # 批量创建序列图关系
        rel_types = ["REPRESENTS_ELEMENT", "HAS_SEND_EVENT", "HAS_RECEIVE_EVENT", "COVERS_LIFELINE"]
        for rel_type in rel_types:
            type_rels = [r for r in seq_rels if r["rel_type"] == rel_type]
            if type_rels:
                query = f"""
                UNWIND $rel_list AS rel_data
                MATCH (from_node {{elementId: rel_data.from_id}})
                MATCH (to_node {{elementId: rel_data.to_id}})
                MERGE (from_node)-[r:`{rel_type}`]->(to_node)
                RETURN count(r)
                """
                session.run(query, {"rel_list": type_rels})
                logger.info(f"创建了{len(type_rels)}个{rel_type}关系")
