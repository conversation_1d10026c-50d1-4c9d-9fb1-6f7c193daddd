"""
块定义和内部块图agent - 负责基于输入内容创建SysML块定义和内部块图
"""
import logging
import json
from typing import Dict, Any
import re
from langchain.schema import HumanMessage
from langchain_openai import ChatOpenAI

from src.graph.workflow_state import WorkflowState, ProcessStatus
from config.settings import settings
from json_repair import repair_json

logger = logging.getLogger(__name__)

# 系统提示模板 - 块定义和内部块图建模
prompt1_first = """
## 角色
你是一位顶级的系统建模专家和数据结构师，精通 SysML BDD 和 IBD 规范，并且深刻理解 XMI 标准和图数据库（如 Neo4j）的数据建模需求。你的任务是从输入的自然语言工程描述中，全面、精确地提取所有结构和行为元素，并组织成一个统一的、扁平化的思考结果列表。

## 核心规则 (!!!必须严格遵守!!!)

1.  **ID 管理的黄金法则**:
    *   **唯一性**: 为你识别的 **每一个** 元素（Package, Block, Property, Port, Connector, Diagram等），都 **必须** 立即生成一个独特的、描述性的 ID (例如: `blk-frame-uuid`, `prop-frame-material-uuid`)。
    *   **一致性**: 在后续的所有步骤中，当你需要引用这个元素时（例如在 `parentId`, `typeId`, `associationId`, `portRefId` 中），你 **必须** 使用你之前生成的 **完全相同** 的 ID 字符串。这是最重要的规则，绝不能出错。

2.  **隐含元素推断**:
    *   主动寻找文本中被提及但未在“主要块”列表中明确定义的实体。如果文本描述“电子控制单元连接传感器和显示屏”，但“电子控制单元”未被定义，你 **必须** 为它创建一个新的 `Block` 元素。

3.  **关联属性完整性**:
    *   当你识别出一个 `part` (部件) 或 `reference` (引用) 属性时，你 **必须** 同时在被引用的 Block 上创建对应的反向引用属性，以确保关联的完整性。
    *   **示例**: 如果 `Fan` 有一个部件 `motor: Motor`，那么在 `Motor` Block 上，你必须创建一个对应的私有引用属性 `_fan: Fan`。然后创建一个 `Association` 元素，其 `memberEndIds` 包含这两个属性的 ID。

4.  **输出格式**:
    *   只输出你的思考过程，严格按照下面的7个步骤进行。不要添加任何额外的解释或对话。
    *   使用 `None` 来表示空值。

## 提取步骤 (你的思考过程)

1.  **识别顶层结构 (Model & Packages):**
    *   确定根 `Model` 和所有 `Package`。
    *   **遵守黄金法则**: 为每个元素分配唯一的 ID 并记录。

2.  **识别核心类型定义 (InterfaceBlocks, ValueTypes, Units, Signals, Enumerations, etc.):**
    *   识别所有基础类型定义。这些是其他块将引用的构建块。
    *   **遵守黄金法则**: 为每个元素分配唯一的 ID 并记录其 `parentId` 和其他属性。

3.  **识别主要功能块 (Concrete Blocks):**
    *   识别核心的功能块，包括那些从文本中 **推断** 出来的块 (如 ECU)。
    *   **遵守黄金法则**: 为每个元素分配唯一的 ID 并记录。

4.  **识别内部成员 (Properties, Ports, Operations, etc.):**
    *   遍历每个 Block，识别其所有内部成员。为每个成员分配唯一的 ID 并正确设置其 `parentId`。
    *   **属性 (Properties)**: 明确分类 (`value`/`part`/`reference`)。**遵守关联完整性规则**，为 part/reference 属性创建双向链接并暂存关联信息。
    *   **端口 (Ports)**: 明确分类 (`FullPort`/`ProxyPort`/`FlowPort`)。
    *   **操作/接收/枚举文字等**: 分配 ID 并正确设置其 `parentId`。
    *   **遵守黄金法则**: 为每个成员分配唯一的 ID 并记录。

5.  **识别关系元素 (Associations, Generalizations):**
    *   基于第 4 步中识别的双向属性，创建 `Association` 元素。
    *   `memberEndIds` 列表 **必须** 包含两个真实存在的、在第 4 步中创建的 Property ID。
    *   识别 "is-a" 关系并创建 `Generalization` 元素。
    *   **遵守黄金法则**: 为每个关系分配唯一的 ID。

6.  **识别 IBD 结构 (Connectors & Diagrams):**
    *   分析 "内部连接" 部分的描述。
    *   为每个 `Connector` 分配 ID，并将其 `parentId` 设置为它所属的 Block 的 ID。
    *   对于每个连接器的 `end1` 和 `end2`，精确记录其 `partRefId` (指向一个 `kind: 'part'` 的 Property ID) 和 `portRefId` (指向一个 Port ID)，或者 `propertyRefId` (指向一个 `kind: 'value'` 的 Property ID)。确保这些 ID 都是你在前面步骤中定义过的。**端点本身不需要ID**
    *   *注意*: 在此思考阶段，可以将 `ConnectorEnd` 视为连接器内部的描述，而不是独立的顶级元素。

7.  **最终审查 (!!!关键步骤!!!)**:
    *   在完成所有提取后，**从头到尾扫描你的整个结果列表**。
    *   对于列表中的 **每一个** ID 引用（如 `parentId`, `typeId`, `associationId`, `portRefId`, `signalId` 等），请验证在你的列表中是否存在一个具有该确切 ID 的元素。
    *   **如果发现任何不匹配或悬空的 ID，立即修正它**。此步骤确保了输出给下一步的一致性。


## 样例输入/输出 (参考)

### 输入文本:
"设计一个`风扇系统包` (`FanSystemPackage`)。该包定义了两个主要块：`风扇` (`Fan`) 和 `遥控器` (`RemoteControl`)。

`风扇`块包含以下部件属性（组合关系）：一个`电机`部件 (`motor`, 类型 `Motor`)；一个`接收器单元` (`receiver`, 类型 `IRReceiver`)。`风扇`块还有一个值属性`当前风速等级` (`currentSpeedLevel`, 类型 `FanSpeedLevel` - 枚举: Off, Low, Medium, High)；一个全端口`电源输入` (`powerIn`, 类型 `ACPowerBlock`)；一个代理端口`状态显示接口` (`statusDisplay`, 类型 `StatusDisplayInterface`)；一个操作`手动设置风速` (`setSpeedLevel(level: FanSpeedLevel)`)；一个接收器`处理红外命令` (`handleIRCommand`)，响应`IRCommand`信号。

`遥控器`块包含：一个值属性`电池电量` (`batteryLevel`, 类型 `Percentage`)；一个操作`发送指令` (`sendCommand(command: IRCommandType)`)，其中`IRCommandType`是枚举 (PowerToggle, SpeedUp, SpeedDown)；一个关联属性`配对风扇` (`pairedFan`, 类型 `Fan`)。

需要定义值类型：`Percentage`(Real, 单位 %)。需要定义枚举：`FanSpeedLevel`, `IRCommandType`。需要定义信号：`IRCommand`。需要定义接口块：`StatusDisplayInterface`。需要定义块：`Motor`, `IRReceiver`, `ACPowerBlock` (作为 Full Port 类型)。需要定义单位: `%`。

在 `Fan` 块的内部中，`接收器单元` (`receiver`) 的`指令输出`端口 (`commandOut`) 通过**Assembly Connector**连接到`电机`部件 (`motor`) 的`控制输入`端口 (`controlIn`)。`电源输入`全端口 (`powerIn`) 通过**Assembly Connector**连接到`电机`部件 (`motor`) 的`电源接口`端口 (`motorPowerIn`)。`风扇`块的`当前风速等级`值属性 (`currentSpeedLevel`) 通过**Binding Connector**连接到边界代理端口`状态显示接口` (`statusDisplay`)。
"

### 输出文本:
请你按照如下的7步进行思考推理：

1.  **识别顶层结构:**
    *   Model: id=`model-fan-uuid`, name=`FanSystemModel`
    *   Package: id=`pkg-fan-uuid`, name=`FanSystemPackage`, parentId=`model-fan-uuid`

2.  **识别核心类型定义:**
    *   Unit: id=`unit-percent-uuid`, name=`%`, parentId=`pkg-fan-uuid`, symbol=`%`
    *   ValueType: id=`vt-percentage-uuid`, name=`Percentage`, parentId=`pkg-fan-uuid`, baseType=`Real`, unitId=`unit-percent-uuid`
    *   Enumeration: id=`enum-fanspeed-uuid`, name=`FanSpeedLevel`, parentId=`pkg-fan-uuid`
    *   Enumeration: id=`enum-ircmdtype-uuid`, name=`IRCommandType`, parentId=`pkg-fan-uuid`
    *   Signal: id=`sig-ircommand-uuid`, name=`IRCommand`, parentId=`pkg-fan-uuid`
    *   InterfaceBlock: id=`if-statusdisp-uuid`, name=`StatusDisplayInterface`, parentId=`pkg-fan-uuid`, isAbstract=True
    *   Block: id=`blk-acpower-uuid`, name=`ACPowerBlock`, parentId=`pkg-fan-uuid`, isAbstract=False
    *   Block: id=`blk-motor-uuid`, name=`Motor`, parentId=`pkg-fan-uuid`, isAbstract=False
    *   Block: id=`blk-irrecv-uuid`, name=`IRReceiver`, parentId=`pkg-fan-uuid`, isAbstract=False

3.  **识别主要功能块:**
    *   Block: id=`blk-fan-uuid`, name=`Fan`, parentId=`pkg-fan-uuid`, isAbstract=False
    *   Block: id=`blk-remote-uuid`, name=`RemoteControl`, parentId=`pkg-fan-uuid`, isAbstract=False

4.  **识别内部成员:**
    *   **For Block `Fan` (id: `blk-fan-uuid`):**
        *   Property (Part): `motor`: id=`prop-fan-motor`, parentId=`blk-fan-uuid`, kind=`part`, typeId=`blk-motor-uuid`, assocId=`assoc-fan-motor`.
        *   Property (Part): `receiver`: id=`prop-fan-recv`, parentId=`blk-fan-uuid`, kind=`part`, typeId=`blk-irrecv-uuid`, assocId=`assoc-fan-recv`.
        *   Property (Value): `currentSpeedLevel`: id=`prop-fan-speed`, parentId=`blk-fan-uuid`, kind=`value`, typeId=`enum-fanspeed-uuid`.
        *   Property (Reference): `_remote`: id=`prop-fan-remote`, parentId=`blk-fan-uuid`, kind=`reference`, typeId=`blk-remote-uuid`, assocId=`assoc-remote-fan`.
        *   Port: `powerIn`: id=`port-fan-powerin`, type=`FullPort`, parentId=`blk-fan-uuid`, typeId=`blk-acpower-uuid`.
        *   Port: `statusDisplay`: id=`port-fan-statusdisp`, type=`ProxyPort`, parentId=`blk-fan-uuid`, typeId=`if-statusdisp-uuid`.
        *   Operation: `setSpeedLevel`: id=`op-fan-setspeed`, parentId=`blk-fan-uuid`.
        *   Reception: `handleIRCommand`: id=`recp-fan-handlesig`, parentId=`blk-fan-uuid`, signalId=`sig-ircommand-uuid`.
    *   **For Block `RemoteControl` (id: `blk-remote-uuid`):**
        *   Property (Value): `batteryLevel`: id=`prop-remote-battery`, parentId=`blk-remote-uuid`, kind=`value`, typeId=`vt-percentage-uuid`.
        *   Property (Reference): `pairedFan`: id=`prop-remote-fanlink`, parentId=`blk-remote-uuid`, kind=`reference`, typeId=`blk-fan-uuid`, assocId=`assoc-remote-fan`.
        *   Operation: `sendCommand`: id=`op-remote-sendcmd`, parentId=`blk-remote-uuid`.
    *   **For Block `Motor` (id: `blk-motor-uuid`):**
        *   Property (Value): `targetRPM`: id=`prop-motor-rpm`, parentId=`blk-motor-uuid`, kind=`value`, typeId=`Integer`, visibility=`private`.
        *   Property (Reference): `_fan_motor`: id=`prop-motor-fan`, parentId=`blk-motor-uuid`, kind=`reference`, typeId=`blk-fan-uuid`, assocId=`assoc-fan-motor`, visibility=`private`.
        *   Port: `motorPowerIn`: id=`port-motor-pwrin`, type=`FullPort`, parentId=`blk-motor-uuid`, typeId=`blk-acpower-uuid`.
        *   Port: `controlIn`: id=`port-motor-ctrlin`, type=`ProxyPort`, parentId=`blk-motor-uuid`, typeId=`sig-ircommand-uuid`.
    *   **For Block `IRReceiver` (id: `blk-irrecv-uuid`):**
        *   Property (Reference): `_fan_recv`: id=`prop-irrecv-fan`, parentId=`blk-irrecv-uuid`, kind=`reference`, typeId=`blk-fan-uuid`, assocId=`assoc-fan-recv`, visibility=`private`.
        *   Port: `commandOut`: id=`port-irrecv-cmdout`, type=`ProxyPort`, parentId=`blk-irrecv-uuid`, typeId=`sig-ircommand-uuid`.
    *   **For Operation `setSpeedLevel` (id: `op-fan-setspeed`):**
        *   Parameter: `level`: id=`p-fss-lvl`, name=`level`, parentId=`op-fan-setspeed`, typeId=`enum-fanspeed-uuid`, direction=`in`.
    *   **For Operation `sendCommand` (id: `op-remote-sendcmd`):**
        *   Parameter: `command`: id=`p-rsc-cmd`, name=`command`, parentId=`op-remote-sendcmd`, typeId=`enum-ircmdtype-uuid`, direction=`in`.
    *   **For Enumeration `FanSpeedLevel` (id: `enum-fanspeed-uuid`):**
        *   Literal: id=`lit-fs-off`, name=`Off`, parentId=`enum-fanspeed-uuid`.
        *   Literal: id=`lit-fs-low`, name=`Low`, parentId=`enum-fanspeed-uuid`.
        *   Literal: id=`lit-fs-med`, name=`Medium`, parentId=`enum-fanspeed-uuid`.
        *   Literal: id=`lit-fs-high`, name=`High`, parentId=`enum-fanspeed-uuid`.
    *   **For Enumeration `IRCommandType` (id: `enum-ircmdtype-uuid`):**
        *   Literal: id=`lit-ircmd-pwt`, name=`PowerToggle`, parentId=`enum-ircmdtype-uuid`.
        *   Literal: id=`lit-ircmd-sup`, name=`SpeedUp`, parentId=`enum-ircmdtype-uuid`.
        *   Literal: id=`lit-ircmd-sdn`, name=`SpeedDown`, parentId=`enum-ircmdtype-uuid`.

5.  **识别关系元素:**
    *   Association: id=`assoc-fan-motor`, parentId=`pkg-fan-uuid`, memberEndIds=[`prop-fan-motor`, `prop-motor-fan`].
    *   Association: id=`assoc-fan-recv`, parentId=`pkg-fan-uuid`, memberEndIds=[`prop-fan-recv`, `prop-irrecv-fan`].
    *   Association: id=`assoc-remote-fan`, parentId=`pkg-fan-uuid`, memberEndIds=[`prop-remote-fanlink`, `prop-fan-remote`].

6.  **识别 IBD 结构 (For `Fan` Block):**
    *   Connector: `conn-fan-recv-motor`: id=`conn-fan-recv-motor`, parentId=`blk-fan-uuid`, kind=`assembly`. End1 ID: `cEnd-frcm-1`, End2 ID: `cEnd-frcm-2`.
    *   Connector: `conn-fan-pwr-motor`: id=`conn-fan-pwr-motor`, parentId=`blk-fan-uuid`, kind=`assembly`. End1 ID: `cEnd-fpwm-1`, End2 ID: `cEnd-fpwm-2`.
    *   Connector: `conn-fan-bind-status`: id=`conn-fan-bind-status`, parentId=`blk-fan-uuid`, kind=`binding`. End1 ID: `cEnd-fbs-1`, End2 ID: `cEnd-fbs-2`.
    *   ConnectorEnd: id=`cEnd-frcm-1`, parentId=`conn-fan-recv-motor`, partRefId=`prop-fan-recv`, portRefId=`port-irrecv-cmdout`.
    *   ConnectorEnd: id=`cEnd-frcm-2`, parentId=`conn-fan-recv-motor`, partRefId=`prop-fan-motor`, portRefId=`port-motor-ctrlin`.
    *   ConnectorEnd: id=`cEnd-fpwm-1`, parentId=`conn-fan-pwr-motor`, partRefId=None, portRefId=`port-fan-powerin`.
    *   ConnectorEnd: id=`cEnd-fpwm-2`, parentId=`conn-fan-pwr-motor`, partRefId=`prop-fan-motor`, portRefId=`port-motor-pwrin`.
    *   ConnectorEnd: id=`cEnd-fbs-1`, parentId=`conn-fan-bind-status`, partRefId=None, propertyRefId=`prop-fan-speed`.
    *   ConnectorEnd: id=`cEnd-fbs-2`, parentId=`conn-fan-bind-status`, partRefId=None, portRefId=`port-fan-statusdisp`.


7. 上述思考过程正确，并且均未出现错误引用id的情况(即悬空id)


## 具体任务
输入：

"""
prompt1_last = "输出：请你一步一步进行推理思考。"
# 输出格式提示
prompt2 = """

## 角色
你是一位精确的数据转换工程师。你的任务是接收一份详细、扁平化、且经过验证的 SysML 元素思考列表，并将其 **严格地** 转化为一个统一的、符合规范的 JSON 对象。

## 核心规则 (!!!必须严格遵守!!!)

1.  **精确转换**: 你不能发明、猜测或修改数据。你的唯一工作就是将输入的思考过程 **原样** 转换为 JSON 格式。输入的思考列表被认为是完全正确的。
2.  **只输出 JSON**: 你的最终输出 **必须** 只有一个顶级的 JSON 对象。禁止包含任何注释、解释或任何其他文本。
3.  **结构遵从性**: 严格遵循下方最终目标 JSON 范例的结构。
4.  **布尔值**: 使用 Python 的 `True` 和 `False`，而不是 `true` 和 `false`。
5.  **空值省略**: 如果一个对象中的某个键值是 `None` 或空数组 `[]`，在最终的 JSON 中**省略这个键**，以保持输出的整洁。

## ！关键转换指令！

*   **元素聚合**: 将所有思考条目转换为 `elements` 数组中的 JSON 对象。
*   **ID 映射**: 输入列表中的每一个 `id` 都是神圣不可改动的。在 JSON 中精确地使用它们。
*   **连接器 (Connector) 组装**: `Connector` 元素应包含 `end1` 和 `end2` 对象。`end1` 和 `end2` 的内容直接从思考过程中的连接器描述中获取。**端点对象本身没有 `id`**。
* ConnectorEnd元素，不需要单独创建，实际就是AssemblyConnector或BindingConnector中的end1或end2；对于两端end，可能会连接不同的对象，例如Block、Value或Port分别对应partRefId、propertyRefId或portRefId，一般一个end连接其中的一个对象即可。
* 对于type为Property的element，你认真观察对应元素的JSON结构；对于其中的propertyKind属性，它有三种类型："part"、"value"、"reference"，对于不同的类型typeId的值是不同的，例如：
    1. 如果propertyKind为"part"，则typeId为block的id；
    2. 如果propertyKind为"value"，则typeId填写基本类型例如Real、String、Integer、Boolean等；
    3. 如果propertyKind为"reference"，则typeId为block的id。
*   **操作参数 (Parameter) 嵌套**: 思考过程中的独立 `Parameter` 元素，在最终 JSON 中 **必须** 被嵌套在它们所属的 `Operation` 元素的 `parameters` 数组中。**不要**在 `elements` 列表中创建单独的 `Parameter` 类型的元素。
*   **枚举文字 (EnumerationLiteral)**: 思考过程中的独立 `EnumerationLiteral` 元素，在最终 JSON 中需要出现在两个地方：
    1.  作为顶级元素存在于 `elements` 数组中。
    2.  其 `id` 被包含在所属 `Enumeration` 元素的 `literals` 数组中。
*   **基本类型**: 对于 `propertyKind: 'value'` 的属性，如果其类型是基本数据类型，则 `typeId` 应为字符串，如 `"String"`, `"Real"`, `"Integer"`, `"Boolean"`。


## 最终目标 JSON 格式样例（这是你用于生成的对照示例，只需要专注于结构形式、不要关注内容数据）
```JSON
{
  "model": {
    "id": "model-fan-uuid",
    "name": "FanSystemModel"
  },
  "elements": [
    {
      "id": "pkg-fan-uuid",
      "type": "Package",
      "name": "FanSystemPackage",
      "parentId": "model-fan-uuid"
    },
    {
      "id": "unit-percent-uuid",
      "type": "Unit",
      "name": "%",
      "parentId": "pkg-fan-uuid",
      "symbol": "%"
    },
    {
      "id": "vt-percentage-uuid",
      "type": "ValueType",
      "name": "Percentage",
      "parentId": "pkg-fan-uuid",
      "baseType": "Real",
      "unitId": "unit-percent-uuid"
    },
    {
      "id": "enum-fanspeed-uuid",
      "type": "Enumeration",
      "name": "FanSpeedLevel",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "enum-ircmdtype-uuid",
      "type": "Enumeration",
      "name": "IRCommandType",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "sig-ircommand-uuid",
      "type": "Signal",
      "name": "IRCommand",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "if-statusdisp-uuid",
      "type": "InterfaceBlock",
      "name": "StatusDisplayInterface",
      "parentId": "pkg-fan-uuid",
      "isAbstract": true
    },
    {
      "id": "blk-acpower-uuid",
      "type": "Block",
      "name": "ACPowerBlock",
      "parentId": "pkg-fan-uuid"
    },
    {
      "id": "blk-motor-uuid",
      "type": "Block",
      "name": "Motor",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-motor-fan"
      ],
      "ports": [
        "port-motor-pwrin",
        "port-motor-ctrlin"
      ]
    },
    {
      "id": "blk-irrecv-uuid",
      "type": "Block",
      "name": "IRReceiver",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-irrecv-fan"
      ],
      "ports": [
        "port-irrecv-cmdout"
      ]
    },
    {
      "id": "blk-fan-uuid",
      "type": "Block",
      "name": "Fan",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-fan-motor",
        "prop-fan-recv",
        "prop-fan-speed",
        "prop-fan-remote"
      ],
      "ports": [
        "port-fan-powerin",
        "port-fan-statusdisp"
      ],
      "operations": [
        "op-fan-setspeed"
      ],
      "receptions": [
        "recp-fan-handlesig"
      ],
      "connectors": [
        "conn-fan-recv-motor",
        "conn-fan-pwr-motor",
        "conn-fan-bind-status"
      ],
      "ownedDiagrams": [
        "diag-fan-bdd",
        "diag-fan-ibd"
      ]
    },
    {
      "id": "blk-remote-uuid",
      "type": "Block",
      "name": "RemoteControl",
      "parentId": "pkg-fan-uuid",
      "properties": [
        "prop-remote-battery",
        "prop-remote-fanlink"
      ],
      "operations": [
        "op-remote-sendcmd"
      ]
    },
    {
      "id": "prop-fan-motor",
      "type": "Property",
      "name": "motor",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-motor-uuid",
      "associationId": "assoc-fan-motor",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-recv",
      "type": "Property",
      "name": "receiver",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "part",
      "aggregation": "composite",
      "typeId": "blk-irrecv-uuid",
      "associationId": "assoc-fan-recv",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-speed",
      "type": "Property",
      "name": "currentSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "enum-fanspeed-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-fan-remote",
      "type": "Property",
      "name": "_remote",
      "parentId": "blk-fan-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-remote-uuid",
      "associationId": "assoc-remote-fan"
    },
    {
      "id": "prop-remote-battery",
      "type": "Property",
      "name": "batteryLevel",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "value",
      "aggregation": "none",
      "typeId": "vt-percentage-uuid",
      "multiplicity": "[1..1]"
    },
    {
      "id": "prop-remote-fanlink",
      "type": "Property",
      "name": "pairedFan",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-remote-fan",
      "multiplicity": "[0..1]"
    },
    {
      "id": "prop-motor-fan",
      "type": "Property",
      "name": "_fan_motor",
      "parentId": "blk-motor-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-motor"
    },
    {
      "id": "prop-irrecv-fan",
      "type": "Property",
      "name": "_fan_recv",
      "parentId": "blk-irrecv-uuid",
      "visibility": "private",
      "propertyKind": "reference",
      "aggregation": "none",
      "typeId": "blk-fan-uuid",
      "associationId": "assoc-fan-recv"
    },
    {
      "id": "port-fan-powerin",
      "type": "FullPort",
      "name": "powerIn",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": false
    },
    {
      "id": "port-fan-statusdisp",
      "type": "ProxyPort",
      "name": "statusDisplay",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "typeId": "if-statusdisp-uuid",
      "isBehavior": true
    },
    {
      "id": "port-motor-pwrin",
      "type": "FullPort",
      "name": "motorPowerIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "blk-acpower-uuid",
      "isBehavior": false
    },
    {
      "id": "port-motor-ctrlin",
      "type": "ProxyPort",
      "name": "controlIn",
      "parentId": "blk-motor-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": true
    },
    {
      "id": "port-irrecv-cmdout",
      "type": "ProxyPort",
      "name": "commandOut",
      "parentId": "blk-irrecv-uuid",
      "visibility": "public",
      "typeId": "sig-ircommand-uuid",
      "isBehavior": true
    },
    {
      "id": "op-fan-setspeed",
      "type": "Operation",
      "name": "setSpeedLevel",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-fss-lvl",
          "name": "level",
          "typeId": "enum-fanspeed-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "op-remote-sendcmd",
      "type": "Operation",
      "name": "sendCommand",
      "parentId": "blk-remote-uuid",
      "visibility": "public",
      "parameters": [
        {
          "id": "p-rsc-cmd",
          "name": "command",
          "typeId": "enum-ircmdtype-uuid",
          "direction": "in"
        }
      ]
    },
    {
      "id": "recp-fan-handlesig",
      "type": "Reception",
      "name": "handleIRCommand",
      "parentId": "blk-fan-uuid",
      "visibility": "public",
      "signalId": "sig-ircommand-uuid"
    },
    {
      "id": "lit-fs-off",
      "type": "EnumerationLiteral",
      "name": "Off",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-low",
      "type": "EnumerationLiteral",
      "name": "Low",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-med",
      "type": "EnumerationLiteral",
      "name": "Medium",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-fs-high",
      "type": "EnumerationLiteral",
      "name": "High",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "enum-fanspeed-uuid_literals",
      "type": "EnumerationLiteral",
      "name": "Off",
      "parentId": "enum-fanspeed-uuid"
    },
    {
      "id": "lit-ircmd-pwt",
      "type": "EnumerationLiteral",
      "name": "PowerToggle",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-ircmd-sup",
      "type": "EnumerationLiteral",
      "name": "SpeedUp",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "lit-ircmd-sdn",
      "type": "EnumerationLiteral",
      "name": "SpeedDown",
      "parentId": "enum-ircmdtype-uuid"
    },
    {
      "id": "assoc-fan-motor",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-motor",
        "prop-motor-fan"
      ]
    },
    {
      "id": "assoc-fan-recv",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-fan-recv",
        "prop-irrecv-fan"
      ]
    },
    {
      "id": "assoc-remote-fan",
      "type": "Association",
      "parentId": "pkg-fan-uuid",
      "memberEndIds": [
        "prop-remote-fanlink",
        "prop-fan-remote"
      ]
    },
    {
      "id": "conn-fan-recv-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "partRefId": "prop-fan-recv",
        "portRefId": "port-irrecv-cmdout"
      },
      "end2": {
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-ctrlin"
      }
    },
    {
      "id": "conn-fan-pwr-motor",
      "type": "AssemblyConnector",
      "parentId": "blk-fan-uuid",
      "kind": "assembly",
      "end1": {
        "portRefId": "port-fan-powerin"
      },
      "end2": {
        "partRefId": "prop-fan-motor",
        "portRefId": "port-motor-pwrin"
      }
    },
    {
      "id": "conn-fan-bind-status",
      "type": "BindingConnector",
      "parentId": "blk-fan-uuid",
      "kind": "binding",
      "end1": {
        "propertyRefId": "prop-fan-speed"
      },
      "end2": {
        "portRefId": "port-fan-statusdisp"
      }
    }
  ]
}
```
"""

def is_valid_json(json_str):
    """检查字符串是否是有效的JSON格式"""
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        logger.error("JSON 不合法")
        return False

def process_bdd_ibd_task(state: WorkflowState, task_content: str) -> Dict[str, Any]:
    """
    处理块定义和内部块图任务
    
    参数:
        state: 工作流状态
        task_content: 任务内容
        
    返回:
        处理结果的字典
    """
    logger.info("开始处理块定义和内部块图任务")
    
    try:
        # 创建LLM
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )
        
        new_prompt1 = prompt1_first + task_content + prompt1_last
        # 执行第一步分析
        first_response = llm.invoke(new_prompt1)
        cot_result = first_response.content
        logger.info("完成第一步分析")
        
        
        # 执行第二步，生成JSON输出
        second_response = llm.invoke([HumanMessage(content=new_prompt1 + cot_result + prompt2)])
        json_str = second_response.content
        
        # 修复和验证JSON
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()
            
        json_str = re.sub(r'\\(?!["\\/bfnrtu])', r'\\\\', json_str)
        json_str = repair_json(json_str)
        
        if not is_valid_json(json_str):
            logger.error("生成的JSON格式不正确")
            return {"status": "error", "message": "生成的JSON格式不正确"}
        
        result = json.loads(json_str)
        logger.info("块定义和内部块图任务处理完成")
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"块定义和内部块图任务处理失败: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}

def bdd_ibd_agent(state: WorkflowState, task_id: str, task_content: str) -> WorkflowState:
    """
    块定义和内部块图Agent入口函数
    
    参数:
        state: 当前工作流状态
        task_id: 任务ID
        task_content: 任务内容
        
    返回:
        更新后的工作流状态
    """
    logger.info(f"块定义和内部块图Agent开始处理任务 {task_id}")
    
    # 查找任务
    task_index = -1
    for i, task in enumerate(state.assigned_tasks):
        if task.id == task_id:
            task_index = i
            break
    
    if task_index == -1:
        state.error_message = f"找不到ID为 {task_id} 的任务"
        return state
    
    # 更新任务状态为进行中
    state.assigned_tasks[task_index].status = ProcessStatus.IN_PROGRESS
    
    try:
        # 处理块定义和内部块图任务
        result = process_bdd_ibd_task(state, task_content)
        
        if result["status"] == "success":
            # 更新任务结果和状态
            state.assigned_tasks[task_index].result = result["result"]
            state.assigned_tasks[task_index].status = ProcessStatus.COMPLETED
            logger.info(f"任务 {task_id} 处理完成")
        else:
            # 更新任务状态为失败
            state.assigned_tasks[task_index].status = ProcessStatus.FAILED
            state.assigned_tasks[task_index].error_message = result["message"]
            logger.error(f"任务 {task_id} 处理失败: {result['message']}")
    
    except Exception as e:
        # 更新任务状态为失败
        state.assigned_tasks[task_index].status = ProcessStatus.FAILED
        state.assigned_tasks[task_index].error_message = str(e)
        logger.error(f"任务 {task_id} 处理异常: {str(e)}", exc_info=True)
    
    return state 