"""
JSON增强Agent - 使用大模型修复和完善不完整的SysML JSON数据
基于LLM的智能JSON修复和属性补全系统
"""
import logging
import json
import copy
from typing import Dict, List, Any, Optional
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from config.settings import settings

logger = logging.getLogger(__name__)

class EnhancedSysMLElement(BaseModel):
    """增强后的SysML元素模型"""
    id: str = Field(description="元素唯一标识符")
    type: str = Field(description="元素类型")
    name: Optional[str] = Field(default=None, description="元素名称")
    parentId: Optional[str] = Field(default=None, description="父元素ID")
    
    # 关系属性
    sourceId: Optional[str] = Field(default=None, description="源元素ID")
    targetId: Optional[str] = Field(default=None, description="目标元素ID")
    associationId: Optional[str] = Field(default=None, description="关联ID")
    memberEndIds: Optional[List[str]] = Field(default=None, description="成员端ID列表")
    
    # Property特有属性
    visibility: Optional[str] = Field(default=None, description="可见性")
    propertyKind: Optional[str] = Field(default=None, description="属性类型")
    aggregation: Optional[str] = Field(default=None, description="聚合类型")
    typeId: Optional[str] = Field(default=None, description="类型ID")
    multiplicity: Optional[str] = Field(default=None, description="多重性")
    
    # Transition特有属性
    trigger: Optional[str] = Field(default=None, description="触发条件")
    guard: Optional[str] = Field(default=None, description="守护条件")
    effect: Optional[str] = Field(default=None, description="效果")
    
    # Connector特有属性
    end1: Optional[Dict[str, Any]] = Field(default=None, description="连接端1")
    end2: Optional[Dict[str, Any]] = Field(default=None, description="连接端2")
    
    # 其他属性
    additional_properties: Optional[Dict[str, Any]] = Field(default=None, description="其他属性")

class JSONEnhancerAgent:
    """
    JSON增强代理，使用大模型修复和完善不完整的SysML JSON数据
    """
    
    def __init__(self, max_retries: int = 3, timeout: int = 60):
        """初始化JSON增强代理"""
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            timeout=timeout,
            max_tokens=4000,  # 限制最大token数
            temperature=0.1  # 低温度确保一致性
        )
        self.max_retries = max_retries
        self.timeout = timeout
        
        # 创建JSON解析器
        self.json_parser = JsonOutputParser(pydantic_object=EnhancedSysMLElement)
        
        # 元素类型的关键属性映射
        self.critical_attributes = {
            'Property': ['visibility', 'propertyKind', 'aggregation', 'typeId', 'multiplicity', 'associationId'],
            'Association': ['memberEndIds', 'sourceId', 'targetId'],
            'Transition': ['sourceId', 'targetId', 'trigger', 'guard', 'effect'],
            'ControlFlow': ['sourceId', 'targetId', 'guard'],
            'ObjectFlow': ['sourceId', 'targetId'],
            'BindingConnector': ['end1', 'end2', 'sourceId', 'targetId'],
            'Connector': ['end1', 'end2', 'sourceId', 'targetId'],
            'DeriveReqt': ['derivedRequirementId', 'sourceRequirementId'],
            'Satisfy': ['blockId', 'requirementId'],
            'Verify': ['testCaseId', 'requirementId'],
            'Include': ['baseUseCaseId', 'includedUseCaseId'],
            'Extend': ['baseUseCaseId', 'extendingUseCaseId'],
            'Message': ['sendEvent', 'receiveEvent', 'messageSort'],
            'Lifeline': ['representsId']
        }
    
    def process(self, state: WorkflowState) -> WorkflowState:
        """
        处理工作流状态，执行JSON增强
        
        参数:
            state: 当前工作流状态
            
        返回:
            更新后的工作流状态
        """
        logger.info("JSONEnhancerAgent开始处理")
        
        # 检查是否已经执行过JSON增强
        if any(task.type == "JSON Enhancement" for task in state.assigned_tasks):
            logger.info("已经执行过JSON增强操作，跳过")
            return state.model_dump()
        
        try:
            # 获取已完成的任务
            completed_tasks = [task for task in state.assigned_tasks if task.status == ProcessStatus.COMPLETED]
            if not completed_tasks:
                logger.warning("没有找到已完成的任务，无法进行JSON增强")
                self._create_enhancement_task(state, "FAILED", "没有找到已完成的任务")
                return state.model_dump()
            
            enhanced_tasks = []
            total_enhanced = 0
            
            # 对每个任务进行增强
            for task in completed_tasks:
                if task.result and isinstance(task.result, dict):
                    enhanced_result = self.enhance_task_json(task, state)
                    if enhanced_result:
                        # 创建增强后的任务副本
                        enhanced_task = copy.deepcopy(task)
                        enhanced_task.result = enhanced_result
                        enhanced_tasks.append(enhanced_task)
                        
                        # 统计增强的元素数量
                        original_count = len(task.result.get('elements', []))
                        enhanced_count = len(enhanced_result.get('elements', []))
                        total_enhanced += enhanced_count - original_count
                        
                        logger.info(f"任务 '{task.type}' 增强完成: {original_count} -> {enhanced_count} 个元素")
            
            # 更新状态中的任务
            if enhanced_tasks:
                # 替换原有任务
                for i, task in enumerate(state.assigned_tasks):
                    if task.status == ProcessStatus.COMPLETED:
                        for enhanced_task in enhanced_tasks:
                            if enhanced_task.id == task.id:
                                state.assigned_tasks[i] = enhanced_task
                                break
                
                # 创建增强任务记录
                self._create_enhancement_task(
                    state, 
                    "COMPLETED", 
                    f"成功增强了 {len(enhanced_tasks)} 个任务，新增 {total_enhanced} 个元素属性",
                    {
                        "enhanced_tasks": len(enhanced_tasks),
                        "total_enhanced_attributes": total_enhanced
                    }
                )
                
                logger.info(f"JSON增强完成: 处理了 {len(enhanced_tasks)} 个任务，新增 {total_enhanced} 个元素属性")
            else:
                self._create_enhancement_task(state, "COMPLETED", "没有需要增强的JSON数据")
                
        except Exception as e:
            error_msg = f"JSON增强过程发生异常: {str(e)}"
            self._create_enhancement_task(state, "FAILED", error_msg)
            logger.error(error_msg, exc_info=True)
        
        return state.model_dump()
    
    def enhance_task_json(self, task: SysMLTask, state: WorkflowState) -> Optional[Dict[str, Any]]:
        """
        增强单个任务的JSON数据
        
        参数:
            task: 要增强的任务
            state: 工作流状态（用于获取上下文信息）
            
        返回:
            增强后的JSON数据
        """
        try:
            elements = task.result.get('elements', [])
            if not elements:
                return task.result
            
            enhanced_elements = []
            
            # 分批处理元素，避免单次请求过大
            batch_size = 5
            for i in range(0, len(elements), batch_size):
                batch = elements[i:i + batch_size]
                enhanced_batch = self._enhance_element_batch(batch, task, state)
                enhanced_elements.extend(enhanced_batch)
            
            # 创建增强后的结果
            enhanced_result = copy.deepcopy(task.result)
            enhanced_result['elements'] = enhanced_elements
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"增强任务 '{task.type}' 的JSON数据失败: {e}")
            return None
    
    def _enhance_element_batch(self, elements: List[Dict[str, Any]], task: SysMLTask, state: WorkflowState) -> List[Dict[str, Any]]:
        """
        增强一批元素
        
        参数:
            elements: 要增强的元素列表
            task: 原始任务
            state: 工作流状态
            
        返回:
            增强后的元素列表
        """
        try:
            # 识别需要增强的元素
            incomplete_elements = []
            complete_elements = []
            
            for element in elements:
                if self._needs_enhancement(element):
                    incomplete_elements.append(element)
                else:
                    complete_elements.append(element)
            
            if not incomplete_elements:
                return elements
            
            # 构建增强提示
            enhancement_prompt = self._build_enhancement_prompt(incomplete_elements, task, state)
            
            # 调用LLM进行增强（带重试机制）
            enhanced_elements = self._call_llm_with_retry(enhancement_prompt, incomplete_elements)
            
            # 合并结果
            result = complete_elements + enhanced_elements
            
            logger.info(f"批次增强完成: {len(incomplete_elements)} 个元素被增强")
            return result
            
        except Exception as e:
            logger.error(f"增强元素批次失败: {e}")
            return elements  # 返回原始元素
    
    def _needs_enhancement(self, element: Dict[str, Any]) -> bool:
        """
        判断元素是否需要增强
        
        参数:
            element: 要检查的元素
            
        返回:
            是否需要增强
        """
        element_type = element.get('type')
        if element_type not in self.critical_attributes:
            return False
        
        # 检查关键属性是否缺失 - 修复：只有完全不存在的属性才视为缺失，null值是有效的
        critical_attrs = self.critical_attributes[element_type]
        missing_attrs = [attr for attr in critical_attrs if attr not in element]
        
        return len(missing_attrs) > 0

    def _call_llm_with_retry(self, prompt: str, incomplete_elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        带重试机制的LLM调用

        参数:
            prompt: 增强提示
            incomplete_elements: 不完整的元素列表

        返回:
            增强后的元素列表
        """
        import time

        for attempt in range(self.max_retries):
            try:
                logger.info(f"LLM增强尝试 {attempt + 1}/{self.max_retries}")

                response = self.llm.invoke([
                    SystemMessage(content=self._get_system_prompt()),
                    HumanMessage(content=prompt)
                ])

                # 解析响应
                enhanced_elements = self._parse_enhancement_response(response.content, incomplete_elements)

                logger.info(f"LLM增强成功，处理了 {len(enhanced_elements)} 个元素")
                return enhanced_elements

            except KeyboardInterrupt:
                logger.warning("用户中断了LLM增强过程")
                return incomplete_elements  # 返回原始元素

            except Exception as e:
                logger.warning(f"LLM增强尝试 {attempt + 1} 失败: {e}")

                if attempt < self.max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 指数退避
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"LLM增强最终失败，返回原始元素")
                    return incomplete_elements

        return incomplete_elements

    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        return """你是一个专业的SysML模型分析专家。你的任务是分析不完整的SysML JSON元素，并根据上下文信息补全缺失的关键属性。

⚠️ 重要约束：
1. 只能补全现有元素的缺失属性，绝对不能创建新元素
2. 必须保持元素数量不变
3. 必须保持每个元素的id、type、name等核心属性不变
4. 只补全明确缺失且可以从上下文推断的属性

补全规则：
1. 只补全明确缺失且可以从上下文推断的属性
2. 保持与SysML标准的一致性
3. 确保补全的属性在逻辑上合理
4. 不要修改已存在的属性值
5. 如果无法确定属性值，请保持原样

重点关注以下关系属性：
- Property元素的associationId、visibility、propertyKind等
- Association元素的memberEndIds（但不要创建新的Property元素）
- Transition元素的trigger、guard、effect
- ControlFlow/ObjectFlow元素的sourceId、targetId
- Connector元素的end1、end2连接信息

请以JSON格式返回增强后的元素列表，元素数量必须与输入完全一致。"""
    
    def _build_enhancement_prompt(self, elements: List[Dict[str, Any]], task: SysMLTask, state: WorkflowState) -> str:
        """
        构建增强提示
        
        参数:
            elements: 需要增强的元素
            task: 原始任务
            state: 工作流状态
            
        返回:
            增强提示文本
        """
        # 获取任务的原始文本内容
        task_content = task.content if task.content else "无原始文本内容"
        
        # 获取相关的其他元素作为上下文
        context_elements = []
        for other_task in state.assigned_tasks:
            if (other_task.status == ProcessStatus.COMPLETED and 
                other_task.id != task.id and 
                other_task.result and 
                isinstance(other_task.result, dict)):
                context_elements.extend(other_task.result.get('elements', []))
        
        prompt = f"""
请分析以下不完整的SysML元素，并根据提供的上下文信息补全缺失的关键属性：

⚠️ 严格要求：
- 输入有 {len(elements)} 个元素，输出必须也是 {len(elements)} 个元素
- 不能创建新元素，不能删除元素，不能修改元素的id和type
- 只能为现有元素补全缺失的属性

## 原始任务描述
任务类型: {task.type}
任务内容: {task_content}

## 需要增强的元素（共{len(elements)}个）
{json.dumps(elements, indent=2, ensure_ascii=False)}

## 上下文信息（相关元素）
{json.dumps(context_elements[:10], indent=2, ensure_ascii=False) if context_elements else "无相关上下文元素"}

## 要求
请为每个元素补全缺失的关键属性，特别关注：
1. 关系属性（sourceId、targetId、associationId等）
2. 类型特定属性（visibility、propertyKind、trigger等）
3. 连接信息（end1、end2、memberEndIds等）

⚠️ 再次提醒：必须返回 {len(elements)} 个元素，不多不少！

请返回完整的增强后元素列表（JSON格式）：
"""
        return prompt
    
    def _parse_enhancement_response(self, response: str, original_elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        解析LLM的增强响应
        
        参数:
            response: LLM响应文本
            original_elements: 原始元素列表
            
        返回:
            增强后的元素列表
        """
        try:
            # 尝试解析JSON响应
            if response.strip().startswith('['):
                enhanced_elements = json.loads(response)
            else:
                # 提取JSON部分
                start = response.find('[')
                end = response.rfind(']') + 1
                if start != -1 and end != 0:
                    json_part = response[start:end]
                    enhanced_elements = json.loads(json_part)
                else:
                    raise ValueError("无法找到有效的JSON数据")
            
            # 验证增强结果
            if len(enhanced_elements) != len(original_elements):
                logger.warning(f"❌ LLM违反约束：增强后的元素数量与原始数量不匹配: {len(enhanced_elements)} != {len(original_elements)}")
                logger.warning("🔒 数据完整性保护：使用原始元素以保证数据完整性")
                logger.debug(f"LLM尝试创建额外元素，这违反了只增强现有元素的约束")
                return original_elements
            
            # 确保每个元素都有必要的基本属性
            for i, enhanced in enumerate(enhanced_elements):
                original = original_elements[i]
                if enhanced.get('id') != original.get('id'):
                    logger.warning(f"元素ID不匹配: {enhanced.get('id')} != {original.get('id')}")
                    enhanced_elements[i] = original
            
            return enhanced_elements
            
        except Exception as e:
            logger.error(f"解析增强响应失败: {e}")
            logger.debug(f"响应内容: {response}")
            return original_elements
    
    def _create_enhancement_task(self, state: WorkflowState, status: str, message: str, result: Optional[Dict] = None):
        """创建增强任务记录"""
        enhancement_task = SysMLTask(
            id=f"JSON-ENHANCEMENT-{len(state.assigned_tasks)}",
            type="JSON Enhancement",
            content=message,
            status=ProcessStatus.COMPLETED if status == "COMPLETED" else ProcessStatus.FAILED,
            result=result,
            error_message=message if status == "FAILED" else None
        )
        state.assigned_tasks.append(enhancement_task)
