"""
SysML合并Agent - 负责将多个SysML图的JSON输出合并为一个统一的模型
"""
import logging
import json
import copy
from typing import Dict, List, Any, Set, Tuple, Optional

from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain.schema import HumanMessage
from langchain_openai import ChatOpenAI
from json_repair import repair_json

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from config.settings import settings
# 导入算法合并方法
from src.agents.merge_agent_algorithm import algorithmic_merge_sysml_tasks

logger = logging.getLogger(__name__)
# ---- Pydantic 模型定义 ----
from pydantic import BaseModel, Field, AnyUrl, Extra
from typing import List, Dict, Any, Optional

class ModelInfo(BaseModel):
    id: str
    name: str

class Element(BaseModel):
    id: str
    type: str
    name: str
    parentId: Optional[str] = None
    
    # 使用 Extra.allow 来允许模型中存在未在上面明确定义的其他字段
    # 这对于SysML这种字段多变的元素至关重要
    class Config:
        extra = Extra.allow

class MergedSysMLModel(BaseModel):
    model: ModelInfo  # 改为单个对象，因为最终模型只有一个
    elements: List[Element]


from langchain_core.utils.function_calling import convert_to_openai_function


# 系统提示模板 - SysML JSON合并
SYSTEM_PROMPT_MERGE = """
## 角色
你是一位专业的 SysML 集成建模专家。你的任务是将多个不同类型的 SysML 图表的 JSON 表示合并为一个统一的、一致的 SysML 模型。

## 输入数据
我将提供以下内容：
1. 目标JSON结构模板
2. 多个来源SysML图的JSON数据（可能包括需求图、活动图、块定义图、状态机图、序列图、参数图、用例图等）


## 规则
你需要合并多个JSON模型，每个JSON包含了不同类型的SysML图表元素（如需求图、活动图、状态机图、块定义图等）。请遵循以下规则进行合并:

### 1. 元素唯一性判断
- 如果两个元素ID完全相同，则视为同一元素
- 如果两个元素类型相同且名称完全相同，很可能是同一元素
- 如果两个元素类型相同且名称相似度高，需进行语义分析判断是否为同一元素
- 在合并同一元素时，保留更完整的属性信息

### 2. 包结构组织
- 使用以下标准包结构：
  - SystemModelPackage (根包)
    - Requirements (需求相关元素)
    - UseCases (用例相关元素)
    - SystemStructure (结构相关元素)
    - SystemBehavior (行为相关元素)
    - Libraries (通用类型、约束等)
- 根据元素类型自动分配到合适的包中：
  - 需求图元素 → Requirements包
  - 用例图元素 → UseCases包
  - 块定义图/内部模块图元素 → SystemStructure包
  - 活动图/状态机图/序列图元素 → SystemBehavior包
  - 值类型/约束块/信号等 → Libraries包

### 3. 关系处理
- 保留所有关系类型
- 更新关系引用以反映合并后的元素ID
- 处理跨图关系，确保引用完整性

### 4. 冲突解决
- 元素属性冲突时，优先保留更详细的信息
- 结构冲突时，保持系统完整性
- 记录并报告所有解决的冲突

## 目标JSON结构
```JSON
{
  "model": {
    "id": "model-master-system-uuid",
    "name": "MasterSystemModel"
  },
  "elements": [
    // =================================================================
    // 1. 核心包结构 (Logical Organization)
    // =================================================================
    { "id": "pkg-root-uuid", "type": "Package", "name": "SystemModelPackage", "parentId": "model-master-system-uuid" },
    { "id": "pkg-reqs-uuid", "type": "Package", "name": "Requirements", "parentId": "pkg-root-uuid" },
    { "id": "pkg-usecases-uuid", "type": "Package", "name": "UseCases", "parentId": "pkg-root-uuid" },
    { "id": "pkg-structure-uuid", "type": "Package", "name": "SystemStructure", "parentId": "pkg-root-uuid" },
    { "id": "pkg-behavior-uuid", "type": "Package", "name": "SystemBehavior", "parentId": "pkg-root-uuid" },
    { "id": "pkg-libraries-uuid", "type": "Package", "name": "Libraries", "parentId": "pkg-root-uuid" },

    // =================================================================
    // 2. 用例图元素 (From Use Case Diagram)
    // =================================================================
    { "id": "actor-user-uuid", "type": "Actor", "name": "User", "parentId": "pkg-usecases-uuid" },
    { "id": "uc-operate-fan-uuid", "type": "UseCase", "name": "Operate Fan", "parentId": "pkg-usecases-uuid" },
    { "id": "assoc-user-operates-fan-uuid", "type": "Association", "sourceId": "actor-user-uuid", "targetId": "uc-operate-fan-uuid", "parentId": "pkg-usecases-uuid" },

    // =================================================================
    // 3. 需求图元素 (From Requirements Diagram)
    // =================================================================
    { "id": "req-fan-power-uuid", "type": "Requirement", "name": "Fan Power Requirement", "reqId": "R-1.0", "text": "The fan system shall be powered by a standard AC outlet.", "parentId": "pkg-reqs-uuid" },
    { "id": "req-fan-speed-uuid", "type": "Requirement", "name": "Fan Speed Control", "reqId": "R-2.0", "text": "The user shall be able to control the fan speed.", "parentId": "pkg-reqs-uuid" },
    { "id": "tc-verify-power-uuid", "type": "TestCase", "name": "VerifyACPower", "parentId": "pkg-reqs-uuid" },

    // =================================================================
    // 4. 结构元素 (From BDD/IBD, Parametrics) - Placed in Structure & Libraries
    // =================================================================
    // --- Primitives in a Library Package ---
    { "id": "vt-percentage-uuid", "type": "ValueType", "name": "Percentage", "parentId": "pkg-libraries-uuid", "baseType": "Real" },
    { "id": "enum-fanspeed-uuid", "type": "Enumeration", "name": "FanSpeedLevel", "parentId": "pkg-libraries-uuid", "literals": ["Off", "Low", "Medium", "High"] },
    { "id": "sig-ircommand-uuid", "type": "Signal", "name": "IRCommand", "parentId": "pkg-libraries-uuid" },
    // --- Blocks ---
    { "id": "blk-fan-uuid", "type": "Block", "name": "Fan", "parentId": "pkg-structure-uuid", "classifierBehaviorId": "sm-fan-control-uuid" /* Link to StateMachine */, "properties": ["prop-fan-motor", "prop-fan-speed"], "ports": ["port-fan-powerin"], "connectors": ["conn-fan-pwr-motor"], "ownedDiagrams": ["diag-fan-ibd"] },
    { "id": "blk-motor-uuid", "type": "Block", "name": "Motor", "parentId": "pkg-structure-uuid" },
    // --- Properties (Parts, Values, Constraints) ---
    { "id": "prop-fan-motor", "type": "Property", "name": "motor", "parentId": "blk-fan-uuid", "propertyKind": "part", "typeId": "blk-motor-uuid" },
    { "id": "prop-fan-speed", "type": "Property", "name": "currentSpeed", "propertyKind": "value", "parentId": "blk-fan-uuid", "typeId": "enum-fanspeed-uuid" },
    // --- Ports ---
    { "id": "port-fan-powerin", "type": "ProxyPort", "name": "powerIn", "parentId": "blk-fan-uuid", "typeId": "some-power-interface-block-id" },
    // --- Connectors (IBD) ---
    { "id": "conn-fan-pwr-motor", "type": "AssemblyConnector", "parentId": "blk-fan-uuid", "end1": {"partRefId": null, "portRefId": "port-fan-powerin"}, "end2": {"partRefId": "prop-fan-motor", "portRefId": "port-motor-powerin"} },
    // --- Parametric Elements ---
    { "id": "cblk-force-eq-uuid", "type": "ConstraintBlock", "name": "ForceEquation", "parentId": "pkg-libraries-uuid", "specification": {"expression": "F = m * a", "language": "Math"} },
    { "id": "param-f", "type": "ConstraintParameter", "name": "F", "parentId": "cblk-force-eq-uuid", "typeId": "Real" },
    { "id": "param-m", "type": "ConstraintParameter", "name": "m", "parentId": "cblk-force-eq-uuid", "typeId": "Real" },
    { "id": "param-a", "type": "ConstraintParameter", "name": "a", "parentId": "cblk-force-eq-uuid", "typeId": "Real" },
    { "id": "cprop-force-instance", "type": "Property", "name": "forceCalc", "propertyKind": "constraint", "parentId": "blk-motor-uuid", "typeId": "cblk-force-eq-uuid" },

    // =================================================================
    // 5. 行为元素 (From Activity, State Machine, Sequence Diagrams)
    // =================================================================
    // --- State Machine for Fan Block ---
    { "id": "sm-fan-control-uuid", "type": "StateMachine", "name": "FanControlSM", "parentId": "blk-fan-uuid" },
    { "id": "region-fan-main-uuid", "type": "Region", "parentId": "sm-fan-control-uuid" },
    { "id": "state-off-uuid", "type": "State", "name": "Off", "parentId": "region-fan-main-uuid" },
    { "id": "state-on-uuid", "type": "State", "name": "On", "parentId": "region-fan-main-uuid" },
    { "id": "trans-turn-on-uuid", "type": "Transition", "sourceId": "state-off-uuid", "targetId": "state-on-uuid", "parentId": "region-fan-main-uuid" },
    // --- Activity for a specific process ---
    { "id": "act-adjust-speed-uuid", "type": "Activity", "name": "AdjustFanSpeed", "parentId": "pkg-behavior-uuid", "nodes": ["node-initial-speed-uuid", "node-action-set-rpm-uuid"], "edges": ["edge-control-flow-1"], "groups": ["part-controller-uuid"] },
    { "id": "part-controller-uuid", "type": "ActivityPartition", "name": "Controller", "representsId": "some-controller-block-id", "parentId": "act-adjust-speed-uuid", "nodeIds": ["node-action-set-rpm-uuid"] },
    { "id": "node-initial-speed-uuid", "type": "InitialNode", "parentId": "act-adjust-speed-uuid" },
    { "id": "node-action-set-rpm-uuid", "type": "OpaqueAction", "name": "Set RPM", "parentId": "act-adjust-speed-uuid" },
    { "id": "edge-control-flow-1", "type": "ControlFlow", "sourceId": "node-initial-speed-uuid", "targetId": "node-action-set-rpm-uuid", "parentId": "act-adjust-speed-uuid" },
    // --- Sequence Diagram (Interaction) ---
    { "id": "inter-remote-press-uuid", "type": "Interaction", "name": "RemotePressInteraction", "parentId": "uc-operate-fan-uuid" /* Can be owned by a UseCase */, "lifelineIds": ["ll-user-uuid", "ll-fan-uuid"], "messageIds": ["msg-press-button-uuid"] },
    { "id": "ll-user-uuid", "type": "Lifeline", "name": "u:User", "parentId": "inter-remote-press-uuid", "representsId": "actor-user-uuid" /* Link back to Actor */ },
    { "id": "ll-fan-uuid", "type": "Lifeline", "name": "f:Fan", "parentId": "inter-remote-press-uuid", "representsId": "blk-fan-uuid" /* Link back to Block */ },
    { "id": "msg-press-button-uuid", "type": "Message", "name": "pressPowerButton()", "parentId": "inter-remote-press-uuid", "sendEventId": "send-1", "receiveEventId": "recv-1", "messageSort": "synchCall" },
    { "id": "send-1", "type": "MessageOccurrenceSpecification", "parentId": "inter-remote-press-uuid", "coveredId": "ll-user-uuid", "messageId": "msg-press-button-uuid" },
    { "id": "recv-1", "type": "MessageOccurrenceSpecification", "parentId": "inter-remote-press-uuid", "coveredId": "ll-fan-uuid", "messageId": "msg-press-button-uuid" },

    // =================================================================
    // 6. 关系元素 (Cross-cutting relationships) - Placed in logical packages
    // =================================================================
    // --- Satisfy (Requirement -> Block) ---
    { "id": "rel-satisfy-power-uuid", "type": "Satisfy", "blockId": "blk-fan-uuid", "requirementId": "req-fan-power-uuid", "parentId": "pkg-reqs-uuid" },
    // --- Verify (TestCase -> Requirement) ---
    { "id": "rel-verify-power-uuid", "type": "Verify", "testCaseId": "tc-verify-power-uuid", "requirementId": "req-fan-power-uuid", "parentId": "pkg-reqs-uuid" }
  ]
}
```

"""

# 用户提示模板 - SysML JSON合并
USER_PROMPT_MERGE = """
请将以下SysML图表的JSON表示合并为一个统一的、一致的SysML模型，统一的JSON结构需要符合上述目标结构。

{json_strings}

请特别注意：
1. 务必合并所有图表的所有元素，除非有明确的重复（相同ID或完全相同的名称和类型）
2. 保留每个元素的所有属性和字段，确保数据不丢失
3. 调整元素的parentId以符合逻辑包结构，但保持子元素指向其正确的父元素
4. 确保所有引用（如sourceId、targetId、typeId等）保持一致性
5. 输出一个结构清晰、元素完整的JSON，包含所有非重复元素

生成的JSON必须：
- 包含一个有效的model对象
- 包含一个elements数组，其中包含所有合并后的元素（包括逻辑包结构和原始任务中的所有元素）
- 不包含任何注释
- 格式正确，可以被JSON解析器解析
"""


SYSTEM_PROMPT_MERGE_V2 = """
## 角色与目标
你是一个顶级的SysML系统架构师和数据处理引擎。你的任务是将多个独立的SysML JSON片段融合成一个统一的、逻辑结构清晰的SysML主模型。你必须严格按照算法步骤操作，而不是模仿任何示例。

## 核心算法步骤

### 步骤 1: 初始化最终模型
1.1. 创建一个最终的JSON对象。
1.2. 在其中添加一个`model`对象，`id`为`model-master-system-uuid`，`name`可以从输入中选择一个最合适的，比如 "Integrated Vehicle AirConditioner System"。
1.3. 在其中添加一个空的`elements`数组。

### 步骤 2: 创建逻辑包结构
2.1. 在最终的`elements`数组中，创建以下6个核心逻辑包。确保它们的`parentId`都指向步骤1.1中创建的`model`对象的`id`。
   - `{ "id": "pkg-reqs-uuid", "type": "Package", "name": "Requirements", ... }`
   - `{ "id": "pkg-usecases-uuid", "type": "Package", "name": "UseCases", ... }`
   - `{ "id": "pkg-structure-uuid", "type": "Package", "name": "SystemStructure", ... }`
   - `{ "id": "pkg-behavior-uuid", "type": "Package", "name": "SystemBehavior", ... }`
   - `{ "id": "pkg-libraries-uuid", "type": "Package", "name": "Libraries", ... }`
   - `{ "id": "pkg-analysis-uuid", "type": "Package", "name": "Analysis", ... }` (可选，用于测试用例等)

### 步骤 3: 元素处理与合并 (核心循环)
遍历所有输入JSON中的每一个`element`：

3.1. **识别与去重**:
   - 维护一个`id_map`（将`id`映射到已处理的元素）和一个`name_type_map`（将`name`和`type`的组合映射到已处理的元素）。
   - **按ID检查**: 如果当前元素的`id`已在`id_map`中，则认为是同一个元素。将当前元素的属性合并到`id_map`中已有的元素上（取更详细的值）。
   - **按名称+类型检查**: 如果ID是新的，但其`name`和`type`的组合已在`name_type_map`中，则极有可能是同一个元素。
     - **操作**: 将此元素的ID记录在一个"别名映射表"中（例如 `{"old_id": "new_canonical_id"}`）。然后将属性合并到主元素上。**不要将这个有别名的元素添加到最终列表中。**
   - **新元素**: 如果ID和名称+类型都是新的，则这是一个新元素。

3.2. **逻辑重组 (设置`parentId`)**:
   - 对于确定要添加到最终列表中的元素（无论是新元素还是合并后的主元素），根据其`type`，将其`parentId`更新为步骤2中创建的逻辑包的ID。
     - `Requirement`, `TestCase`: `pkg-reqs-uuid`
     - `Actor`, `UseCase`: `pkg-usecases-uuid`
     - `Block`, `Property`, `Port`, `Connector`, `Association`, `InterfaceBlock`: `pkg-structure-uuid`
     - `Activity`, `StateMachine`, `Interaction`, `State`, `Transition`, `Region`, `Action`, `Node`, `Edge`: `pkg-behavior-uuid`
     - `ValueType`, `Enumeration`, `Signal`, `Unit`, `ConstraintBlock`: `pkg-libraries-uuid`
   - **重要例外**: 对于容器内的元素（如`State`在`Region`中，`Node`在`Activity`中），它们的`parentId`必须保持指向其容器的ID。你移动的是顶层容器（如`Activity`, `StateMachine`）到逻辑包中。

3.3. **添加到最终列表**: 将处理好的新元素或合并后的主元素添加到最终的`elements`数组中。

### 步骤 4: 引用修复 (Final Pass)
遍历最终`elements`数组中的所有元素一遍：
- 检查所有引用字段（如 `sourceId`, `targetId`, `typeId`, `representsId`, `classifierBehaviorId`, `blockId`, `requirementId` 等）。
- 如果引用的ID在步骤3.1的"别名映射表"中，将其更新为正确的"主ID"。

### 步骤 5: 语义链接 (智能增强)
在引用修复的同时，执行智能链接：
- **生命线(Lifeline)**: `name`为`"i:Type"`的生命线，其`representsId`应指向名为`"Type"`的`Block`或`Actor`。
- **活动分区(ActivityPartition)**: `name`为`"Role"`的分区，其`representsId`应指向名为`"Role"`的`Block`或`Actor`。
- **行为绑定**: `Block`的`classifierBehaviorId`应指向逻辑上描述其行为的`StateMachine`或`Activity`。

### 最终输出要求
- **不要包含任何注释**。
- **不要遗漏任何信息**，除非它是明确的重复项。
- 最终的JSON必须是完整且结构正确的。
- 所有原始的`Package`和`Model`元素（除了最终的根`model`对象）都应该被忽略，因为你正在从头创建一个全新的逻辑结构。
"""


USER_PROMPT_MERGE_V2 = """
请严格遵循你在系统提示中被赋予的角色和详细的算法步骤，将以下多个SysML图表的JSON表示合并为一个统一的、逻辑结构清晰的SysML主模型。

以下是待合并的JSON数据：
{json_strings}

请执行算法并生成最终的、不含注释的完整JSON对象。
"""

SYSTEM_PROMPT_MERGE_ENHANCED = """
## 角色与任务
你是一位SysML系统架构专家，负责将多个SysML图表的JSON表示合并为一个完整的、统一的SysML模型。这是一个极其重要的任务，**必须确保所有原始元素都被正确保留**，不能丢失任何信息。

## 核心要求：完整性与准确性

### 关键指令（必须严格遵守）：
1. **元素完整性**：你必须包含所有输入JSON中的所有元素，除非确定是完全重复的。宁可包含可能的重复，也不要丢失任何独特元素。
2. **逻辑组织**：使用标准的包结构组织元素，但不能仅创建包结构而忽略具体元素。
3. **引用完整性**：保持所有元素间的引用关系完整有效。

## 合并过程
1. **创建基础模型**：
   ```json
   {
     "model": {
       "id": "model-master-system-uuid",
       "name": "Integrated Vehicle AirConditioner System"
     },
     "elements": []
   }
   ```

2. **添加核心包结构**：在elements数组中添加标准包结构（Requirements、UseCases、SystemStructure等）。

3. **元素合并（最关键步骤）**：
   - 收集所有输入JSON的所有元素（**不要遗漏任何元素**）
   - 识别重复元素（相同ID或相同类型+名称）
   - 合并重复元素，保留更完整的信息
   - 对于新元素，根据类型确定适当的父包（parentId）
   - **将所有不重复的元素添加到最终的elements数组中**

4. **引用处理**：更新所有元素间引用以确保一致性

## 元素分类指南
- 需求相关：分配到"Requirements"包
- 用例相关：分配到"UseCases"包
- 结构相关（块、属性、端口）：分配到"SystemStructure"包
- 行为相关（活动、状态、交互）：分配到"SystemBehavior"包
- 类型、枚举等：分配到"Libraries"包

## 重要规则
1. 子元素应保持其原始parentId，只有顶级元素才分配到标准包。
2. 合并结果必须是一个有效的JSON。
3. **最终elements数组应包含所有原始输入中的非重复元素**。

请确保生成一个结构完善、元素完整的SysML模型。宁可包含所有元素，也不要丢失任何信息。
"""

def is_valid_json(json_str):
    """检查字符串是否是有效的JSON格式"""
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        logger.error("JSON 不合法")
        return False

def merge_sysml_tasks(state: WorkflowState) -> Dict[str, Any]:
    """
    合并所有SysML任务的输出为一个统一的JSON
    
    参数:
        state: 工作流状态
        
    返回:
        合并后的JSON结果的字典
    """
    logger.info("开始合并SysML任务输出")
    
    # 只处理成功完成的任务
    completed_tasks = [task for task in state.assigned_tasks if task.status == ProcessStatus.COMPLETED]
    if not completed_tasks:
        logger.error("没有找到已完成的SysML任务")
        return {"status": "error", "message": "没有找到已完成的SysML任务"}
    
    # 收集所有已完成任务的JSON输出
    json_strings = []
    task_types = []
    for task in completed_tasks:
        if task.result is not None:
            task_types.append(task.type)
            json_strings.append(f"## {task.type} 图JSON:\n```json\n{json.dumps(task.result, ensure_ascii=False, indent=2)}\n```\n")
    
    if not json_strings:
        logger.error("没有找到有效的任务输出结果")
        return {"status": "error", "message": "没有找到有效的任务输出结果"}
    
    logger.info(f"找到 {len(json_strings)} 个可合并的JSON结果: {', '.join(task_types)}")
    
    # 如果只有一个任务结果，直接返回它
    if len(json_strings) == 1:
        logger.info("只有一个任务结果，无需合并")
        #return {"status": "success", "result": completed_tasks[0].result}
        pass
    
    # 创建LLM
    try:
        # 注释掉函数调用相关代码
        """
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            timeout=None,
            max_tokens=None,
            temperature=0.0
        )

        # 将我们的Pydantic模型转换为OpenAI函数/工具的描述
        openai_tools = [convert_to_openai_function(MergedSysMLModel)]

        # 创建一个绑定了工具的LLM
        # .bind_tools() 是较新的用法，.bind(functions=...)是稍早的用法，效果类似
        llm_with_tool = llm.bind_tools(
        tools=openai_tools,
        tool_choice={"type": "function", "function": {"name": MergedSysMLModel.__name__}}
        )


        # 创建合并提示
        merge_prompt = USER_PROMPT_MERGE_V2.format(
            json_strings="\n\n".join(json_strings)
        )
        
        # 执行合并
        logger.info("开始执行SysML JSON合并")
        logger.info(f"合并的JSON数量: {len(json_strings)}")
        response = llm_with_tool.invoke([HumanMessage(content=SYSTEM_PROMPT_MERGE_V2 + merge_prompt)])
        # 响应中会包含工具调用的参数
        if response.tool_calls:
         tool_call_args = response.tool_calls[0]['args']
         # tool_call_args 已经是一个字典，可以直接用Pydantic验证
         validated_model = MergedSysMLModel.parse_obj(tool_call_args)
         result = validated_model.dict(by_alias=True)
        
         logger.info(f"Structured output received and validated. Final model contains {len(result.get('elements', []))} elements.")
         return {"status": "success", "result": result}
        else:
         logger.error("LLM did not call the tool as expected.")
         return {"status": "error", "message": "LLM failed to produce structured output."}
        """

        # 恢复原来的文本生成方法，使用增强版提示词
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            timeout=None,
            max_tokens=None,
            temperature=0.0
        )
        
        # 创建合并提示，使用增强版提示词
        merge_prompt = USER_PROMPT_MERGE.format(
            json_strings="\n\n".join(json_strings)
        )
        
        # 执行合并
        logger.info("开始执行SysML JSON合并（使用增强版提示词）")
        logger.info(f"合并的JSON数量: {len(json_strings)}")
        response = llm.invoke([HumanMessage(content=SYSTEM_PROMPT_MERGE_ENHANCED + merge_prompt)])
        merged_json_str = response.content
        
        logger.info("获取到合并响应，处理中...")
        # 提取JSON内容
        if "```json" in merged_json_str:
            merged_json_str = merged_json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in merged_json_str:
            merged_json_str = merged_json_str.split("```")[1].split("```")[0].strip()
            
        # 修复和验证JSON
        merged_json_str = repair_json(merged_json_str)
        
        if not is_valid_json(merged_json_str):
            logger.error("合并后的JSON格式不正确")
            return {"status": "error", "message": "合并后的JSON格式不正确"}
        
        result = json.loads(merged_json_str)
        logger.info("SysML JSON解析完成，验证结果中...")
        
        # 增强的验证逻辑
        if not result or not isinstance(result, dict):
            logger.error("合并结果不是有效的字典")
            return {"status": "error", "message": "合并结果不是有效的字典"}
            
        if "model" not in result or "elements" not in result:
            logger.error("合并结果缺少必要的'model'或'elements'字段")
            return {"status": "error", "message": "合并结果缺少必要的'model'或'elements'字段"}
        
        # 统计元素数量和类型
        elements = result.get("elements", [])
        elements_count = len(elements)
        
        # 计算元素类型分布
        element_types = {}
        for element in elements:
            element_type = element.get("type", "未知")
            element_types[element_type] = element_types.get(element_type, 0) + 1
        
        # 记录元素分布信息
        logger.info(f"合并成功，模型包含 {elements_count} 个元素")
        logger.info(f"元素类型分布: {element_types}")
        
        # 确保合并后的元素数量合理（必须多于简单的包结构）
        if elements_count <= 10:  # 基本包结构通常不超过10个元素
            logger.warning(f"合并结果元素数量过少 ({elements_count})，可能未包含所有原始元素")
            
            # 统计输入任务中的大致元素数量
            input_element_count = 0
            for task in completed_tasks:
                if task.result and "elements" in task.result:
                    input_element_count += len(task.result.get("elements", []))
            
            logger.warning(f"输入任务中估计有约 {input_element_count} 个元素，但输出仅有 {elements_count} 个元素")
            
            # 如果输出元素数量明显少于输入元素，可能是合并有问题
            if elements_count < input_element_count * 0.5:  # 如果少于一半，则可能有问题
                logger.error("合并结果元素数量远少于输入元素数量，合并可能不完整")
                return {"status": "error", "message": "合并不完整，结果元素数量明显少于输入元素总数"}
        
        return {"status": "success", "result": result}
    
    except Exception as e:
        logger.error(f"合并SysML任务失败: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}

def sysml_merge_agent(state: WorkflowState) -> WorkflowState:
    """
    SysML合并Agent入口函数
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    logger.info("SysML合并Agent开始处理")
    
    # 检查是否已经执行过合并
    if any(task.type == "Merged SysML Model" for task in state.assigned_tasks):
        logger.info("已经执行过合并操作，跳过")
        return state
    
    # 检查是否有已完成的任务
    completed_tasks = [task for task in state.assigned_tasks if task.status == ProcessStatus.COMPLETED]
    if not completed_tasks:
        logger.warning("没有成功完成的任务，无法进行合并")
        # 创建一个空的合并结果，避免再次进入合并步骤
        merge_task = SysMLTask(
            id=f"MERGE-TASK-EMPTY",
            type="Merged SysML Model",
            content="没有可合并的内容",
            status=ProcessStatus.FAILED,
            error_message="没有成功完成的任务，无法进行合并"
        )
        state.assigned_tasks.append(merge_task)
        return state
    
    # 记录待合并任务的信息
    task_types = [task.type for task in completed_tasks]
    logger.info(f"准备合并以下类型的任务: {', '.join(task_types)}")

    try:
        # 使用纯算法逻辑进行合并
        logger.info("启动纯算法逻辑合并过程...")
        merge_result = algorithmic_merge_sysml_tasks(state)
        
        if merge_result["status"] == "success":
            # 将合并结果存储在状态中
            result_data = merge_result["result"]
            elements_count = len(result_data.get("elements", []))
            
            # 这里我们创建一个新的"合并"任务来存储结果
            merge_task = SysMLTask(
                id=f"MERGE-TASK-ALGORITHM",
                type="Merged SysML Model",
                content=f"使用纯算法逻辑合并所有SysML图表 (包含 {elements_count} 个元素)",
                status=ProcessStatus.COMPLETED,
                result=result_data
            )
            
            # 将合并任务添加到状态中
            state.assigned_tasks.append(merge_task)
            logger.info(f"SysML模型合并成功，最终模型包含 {elements_count} 个元素")
            
            # 记录元素类型分布，帮助验证合并是否成功
            element_types = {}
            for element in result_data.get("elements", []):
                element_type = element.get("type", "未知")
                element_types[element_type] = element_types.get(element_type, 0) + 1
                
            logger.info(f"合并后模型的元素类型分布: {element_types}")
        else:
            # 如果算法合并失败，尝试使用原始LLM合并方法
            logger.warning(f"算法合并失败: {merge_result['message']}，尝试使用LLM合并...")
            merge_result = merge_sysml_tasks(state)
            
            if merge_result["status"] == "success":
                # 将合并结果存储在状态中
                result_data = merge_result["result"]
                elements_count = len(result_data.get("elements", []))
                
                # 这里我们创建一个新的"合并"任务来存储结果
                merge_task = SysMLTask(
                    id=f"MERGE-TASK-LLM",
                    type="Merged SysML Model",
                    content=f"使用LLM合并所有SysML图表 (包含 {elements_count} 个元素)",
                    status=ProcessStatus.COMPLETED,
                    result=result_data
                )
                
                # 将合并任务添加到状态中
                state.assigned_tasks.append(merge_task)
                logger.info(f"SysML模型合并成功，最终模型包含 {elements_count} 个元素")
                
                # 记录元素类型分布，帮助验证合并是否成功
                element_types = {}
                for element in result_data.get("elements", []):
                    element_type = element.get("type", "未知")
                    element_types[element_type] = element_types.get(element_type, 0) + 1
                    
                logger.info(f"合并后模型的元素类型分布: {element_types}")
            else:
                # 记录合并错误，但仍创建一个合并任务以避免再次进入合并步骤
                error_msg = merge_result["message"]
                merge_task = SysMLTask(
                    id=f"MERGE-TASK-ERROR",
                    type="Merged SysML Model",
                    content="合并失败",
                    status=ProcessStatus.FAILED,
                    error_message=f"合并失败原因: {error_msg}"
                )
                state.assigned_tasks.append(merge_task)
                logger.error(f"SysML模型合并失败: {error_msg}")
    
    except Exception as e:
        # 处理异常，但仍创建一个合并任务以避免再次进入合并步骤
        error_message = f"SysML合并Agent处理异常: {str(e)}"
        merge_task = SysMLTask(
            id=f"MERGE-TASK-EXCEPTION",
            type="Merged SysML Model",
            content="合并异常",
            status=ProcessStatus.FAILED,
            error_message=error_message
        )
        state.assigned_tasks.append(merge_task)
        logger.error(error_message, exc_info=True)
    
    return state 