#!/usr/bin/env python3
"""
数据完整性修复Agent - 使用LLM智能修复SysML数据中的缺失元素和ID错误
"""

import json
import logging
from typing import Dict, List, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from config.settings import settings

logger = logging.getLogger(__name__)

class DataIntegrityFixerAgent:
    """数据完整性修复Agent"""
    
    def __init__(self, timeout: int = 60):
        """
        初始化数据完整性修复Agent

        参数:
            timeout: 超时时间
        """
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.1,  # 低温度确保一致性
            timeout=timeout
        )
    
    def fix_data_integrity(self, json_data: Dict[str, Any], task_type: str, task_content: str) -> Dict[str, Any]:
        """
        修复JSON数据的完整性问题
        
        参数:
            json_data: 原始JSON数据
            task_type: 任务类型
            task_content: 任务内容描述
            
        返回:
            修复后的JSON数据
        """
        logger.info(f"🔧 开始修复 {task_type} 任务的数据完整性")
        
        try:
            # 1. 分析数据完整性问题
            issues = self._analyze_integrity_issues(json_data, task_type)
            
            if not issues:
                logger.info("✅ 数据完整性良好，无需修复")
                return json_data
            
            logger.info(f"发现 {len(issues)} 个完整性问题:")
            for issue in issues:
                logger.info(f"  - {issue['type']}: {issue['description']}")
            
            # 2. 使用LLM修复问题
            fixed_data = self._llm_fix_issues(json_data, issues, task_type, task_content)
            
            # 3. 验证修复结果
            remaining_issues = self._analyze_integrity_issues(fixed_data, task_type)
            
            if remaining_issues:
                logger.warning(f"修复后仍有 {len(remaining_issues)} 个问题未解决")
                for issue in remaining_issues:
                    logger.warning(f"  - {issue['type']}: {issue['description']}")
            else:
                logger.info("✅ 所有完整性问题已修复")
            
            return fixed_data
            
        except Exception as e:
            logger.error(f"数据完整性修复失败: {e}")
            return json_data  # 返回原始数据作为fallback
    
    def _analyze_integrity_issues(self, json_data: Dict[str, Any], task_type: str) -> List[Dict[str, Any]]:
        """
        分析数据完整性问题
        
        参数:
            json_data: JSON数据
            task_type: 任务类型
            
        返回:
            问题列表
        """
        issues = []
        elements = json_data.get("elements", [])
        
        if not elements:
            return issues
        
        # 创建ID到元素的映射
        id_to_element = {elem.get('id'): elem for elem in elements if elem.get('id')}
        
        # 1. 检查引用完整性
        for element in elements:
            # 检查parentId引用
            parent_id = element.get('parentId')
            if parent_id and parent_id not in id_to_element:
                issues.append({
                    'type': 'missing_parent',
                    'description': f"元素 {element.get('id')} 的父元素 {parent_id} 不存在",
                    'element_id': element.get('id'),
                    'missing_id': parent_id,
                    'element': element
                })
            
            # 检查其他ID引用
            for field in ['sourceId', 'targetId', 'typeId', 'associationId']:
                ref_id = element.get(field)
                if ref_id and ref_id not in id_to_element:
                    issues.append({
                        'type': 'missing_reference',
                        'description': f"元素 {element.get('id')} 的 {field} 引用 {ref_id} 不存在",
                        'element_id': element.get('id'),
                        'field': field,
                        'missing_id': ref_id,
                        'element': element
                    })
        
        # 2. 特殊检查：Activity任务的节点完整性
        if task_type == "Activity":
            activity_elements = [elem for elem in elements if elem.get('type') == 'Activity' and 'nodes' in elem]
            for activity in activity_elements:
                referenced_nodes = set(activity.get('nodes', []))
                defined_nodes = set(id_to_element.keys())
                missing_nodes = referenced_nodes - defined_nodes
                
                for missing_node in missing_nodes:
                    issues.append({
                        'type': 'missing_activity_node',
                        'description': f"Activity {activity.get('id')} 引用的节点 {missing_node} 未定义",
                        'activity_id': activity.get('id'),
                        'missing_node_id': missing_node,
                        'activity': activity
                    })
        
        return issues
    
    def _llm_fix_issues(self, json_data: Dict[str, Any], issues: List[Dict[str, Any]], 
                       task_type: str, task_content: str) -> Dict[str, Any]:
        """
        使用LLM修复完整性问题
        
        参数:
            json_data: 原始JSON数据
            issues: 问题列表
            task_type: 任务类型
            task_content: 任务内容
            
        返回:
            修复后的JSON数据
        """
        # 构建修复提示
        prompt = self._build_fix_prompt(json_data, issues, task_type, task_content)
        
        # 调用LLM
        response = self.llm.invoke([
            SystemMessage(content=self._get_fix_system_prompt()),
            HumanMessage(content=prompt)
        ])
        
        # 解析响应
        try:
            # 提取JSON部分 - 改进的解析逻辑
            response_text = response.content
            logger.debug(f"LLM响应长度: {len(response_text)}")

            # 尝试多种JSON提取方法
            fixed_data = None

            # 方法1: 寻找完整的JSON对象
            json_start = response_text.find('```json')
            if json_start != -1:
                json_start = response_text.find('{', json_start)
                json_end = response_text.rfind('}', json_start) + 1
                if json_start != -1 and json_end > json_start:
                    json_part = response_text[json_start:json_end]
                    try:
                        fixed_data = json.loads(json_part)
                    except:
                        pass

            # 方法2: 直接寻找JSON对象
            if not fixed_data:
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_part = response_text[json_start:json_end]
                    try:
                        fixed_data = json.loads(json_part)
                    except:
                        pass

            # 方法3: 尝试修复常见的JSON格式问题
            if not fixed_data:
                # 移除可能的markdown标记
                cleaned_text = response_text.replace('```json', '').replace('```', '')
                json_start = cleaned_text.find('{')
                json_end = cleaned_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_part = cleaned_text[json_start:json_end]
                    try:
                        fixed_data = json.loads(json_part)
                    except:
                        pass

            if fixed_data:
                # 验证修复后的数据结构
                if self._validate_fixed_data(fixed_data, json_data):
                    logger.info("✅ LLM修复成功，数据结构验证通过")
                    return fixed_data
                else:
                    logger.warning("LLM修复的数据结构验证失败，使用原始数据")
                    return json_data
            else:
                logger.warning("无法从LLM响应中提取有效JSON，使用原始数据")
                logger.debug(f"响应内容前500字符: {response_text[:500]}")
                return json_data

        except Exception as e:
            logger.error(f"解析LLM修复响应失败: {e}")
            logger.debug(f"响应内容: {response_text[:1000] if 'response_text' in locals() else 'N/A'}")
            return json_data
    
    def _get_fix_system_prompt(self) -> str:
        """获取修复系统提示"""
        return """你是一个专业的SysML数据完整性修复专家。你的任务是分析和修复SysML JSON数据中的完整性问题。

修复原则：
1. 保持原有数据的完整性，只添加缺失的元素或修正错误的引用
2. 新创建的元素应该符合SysML标准和业务逻辑
3. 确保所有ID引用都有对应的元素定义
4. 保持元素间的逻辑关系正确
5. 新元素的命名应该有意义且符合上下文

修复策略：
- 对于缺失的父元素：根据上下文创建合理的父元素
- 对于缺失的引用元素：分析业务逻辑，创建必要的元素
- 对于Activity节点：根据节点ID和业务流程创建合适的节点类型
- 对于ID错误：修正为正确的引用ID

重要：请只返回修复后的JSON数据，不要添加任何解释文字。使用以下格式：

```json
{
  "model": [...],
  "elements": [...]
}
```"""
    
    def _build_fix_prompt(self, json_data: Dict[str, Any], issues: List[Dict[str, Any]], 
                         task_type: str, task_content: str) -> str:
        """构建修复提示"""
        
        prompt = f"""
请修复以下SysML JSON数据中的完整性问题：

## 任务信息
任务类型: {task_type}
任务描述: {task_content}

## 发现的问题 ({len(issues)} 个)
"""
        
        for i, issue in enumerate(issues, 1):
            prompt += f"{i}. {issue['type']}: {issue['description']}\n"
        
        prompt += f"""

## 原始JSON数据
{json.dumps(json_data, indent=2, ensure_ascii=False)}

## 修复要求
1. 为每个缺失的元素创建合理的定义
2. 确保新创建的元素符合SysML标准
3. 保持业务逻辑的一致性
4. 所有ID引用必须有对应的元素

请返回修复后的完整JSON数据：
"""
        
        return prompt
    
    def _validate_fixed_data(self, fixed_data: Dict[str, Any], original_data: Dict[str, Any]) -> bool:
        """
        验证修复后的数据
        
        参数:
            fixed_data: 修复后的数据
            original_data: 原始数据
            
        返回:
            是否有效
        """
        try:
            # 基本结构检查
            if not isinstance(fixed_data, dict):
                return False
            
            if "elements" not in fixed_data:
                return False
            
            fixed_elements = fixed_data["elements"]
            if not isinstance(fixed_elements, list):
                return False
            
            # 检查是否包含原始元素
            original_elements = original_data.get("elements", [])
            original_ids = {elem.get('id') for elem in original_elements if elem.get('id')}
            fixed_ids = {elem.get('id') for elem in fixed_elements if elem.get('id')}
            
            # 修复后应该包含所有原始元素
            if not original_ids.issubset(fixed_ids):
                logger.warning("修复后的数据缺少原始元素")
                return False
            
            # 检查新增元素的合理性
            new_elements_count = len(fixed_elements) - len(original_elements)
            if new_elements_count > 20:  # 防止LLM创建过多元素
                logger.warning(f"修复后新增了过多元素: {new_elements_count}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证修复数据失败: {e}")
            return False
