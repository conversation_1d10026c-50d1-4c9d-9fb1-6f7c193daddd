"""
FusionAgent - 负责实体去重和融合
基于Gemini方案一的图数据库驱动架构，使用LLM进行语义匹配
"""
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from src.agents.neo4j_storage_agent import Neo4jStorageAgent
from config.settings import settings

logger = logging.getLogger(__name__)

class FusionAgent:
    """
    实体融合代理，负责去重和合并重复的SysML元素
    """
    
    def __init__(self):
        """初始化融合代理"""
        self.storage_agent = None
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            timeout=None,
            max_tokens=None,
            temperature=0.0
        )
        
    def process(self, state: WorkflowState) -> WorkflowState:
        """
        处理工作流状态，执行实体融合
        
        参数:
            state: 当前工作流状态
            
        返回:
            更新后的工作流状态
        """
        logger.info("FusionAgent开始处理")
        
        # 检查是否已经执行过实体融合
        if any(task.type == "Entity Fusion" for task in state.assigned_tasks):
            logger.info("已经执行过实体融合操作，跳过")
            return state
        
        # 检查是否有已完成的SysML任务（包括Requirement、Block等）
        completed_tasks = [task for task in state.assigned_tasks
                          if task.status == ProcessStatus.COMPLETED and
                          task.result and
                          isinstance(task.result, dict) and
                          'elements' in task.result]

        if not completed_tasks:
            logger.warning("没有找到成功的SysML任务，无法进行实体融合")
            self._create_fusion_task(state, "FAILED", "没有找到成功的SysML任务")
            return state
        
        try:
            # 初始化Neo4j存储代理
            self.storage_agent = Neo4jStorageAgent()
            
            # 查找重复候选
            duplicate_candidates = self.storage_agent.find_duplicate_candidates()
            
            if not duplicate_candidates:
                logger.info("没有找到重复的元素候选")
                self._create_fusion_task(state, "COMPLETED", "没有发现重复元素，无需融合", {
                    "processed_pairs": 0,
                    "merged_elements": 0
                })
                return state
            
            logger.info(f"找到{len(duplicate_candidates)}对可能重复的元素")
            
            # 处理每对候选 - 增强版
            merged_count = 0
            processed_pairs = 0

            for id1, id2, similarity_score in duplicate_candidates:
                try:
                    processed_pairs += 1
                    logger.debug(f"🔍 分析候选对 {processed_pairs}/{len(duplicate_candidates)}: {id1} <-> {id2} (相似度: {similarity_score})")

                    # 获取元素上下文信息
                    element1_context = self._get_element_context(id1)
                    element2_context = self._get_element_context(id2)

                    if not element1_context or not element2_context:
                        logger.warning(f"⚠️ 无法获取元素上下文: {id1} 或 {id2}")
                        continue

                    # 使用增强的LLM决策
                    action_plan = self._get_enhanced_merge_decision(element1_context, element2_context, similarity_score)

                    if action_plan and action_plan.get("action") == "merge":
                        canonical_id = action_plan.get("canonicalId")
                        discarded_ids = action_plan.get("discardedIds", [action_plan.get("discardedId")])
                        property_mappings = action_plan.get("propertyMappings", [])

                        if canonical_id and discarded_ids:
                            # 执行增强的合并操作
                            success = self._execute_enhanced_merge(canonical_id, discarded_ids, property_mappings)
                            if success:
                                merged_count += 1
                                logger.info(f"✅ 成功合并元素: {discarded_ids} -> {canonical_id}")
                            else:
                                logger.warning(f"❌ 合并元素失败: {discarded_ids} -> {canonical_id}")
                        else:
                            logger.warning(f"⚠️ LLM决策缺少必要信息: canonical_id={canonical_id}, discarded_ids={discarded_ids}")
                    elif action_plan and action_plan.get("action") == "keep_separate":
                        logger.debug(f"🔄 LLM决定保持元素分离: {id1}, {id2} - 原因: {action_plan.get('reason', 'N/A')}")
                    else:
                        logger.warning(f"⚠️ LLM未能生成有效的合并决策: {id1}, {id2}")

                except Exception as e:
                    logger.error(f"❌ 处理重复候选({id1}, {id2})时发生异常: {e}")
            
            # 创建融合任务结果
            result_msg = f"处理了{processed_pairs}对候选，成功合并{merged_count}个重复元素"
            self._create_fusion_task(state, "COMPLETED", result_msg, {
                "processed_pairs": processed_pairs,
                "merged_elements": merged_count,
                "total_candidates": len(duplicate_candidates)
            })
            
            logger.info(f"实体融合完成: {result_msg}")
            
        except Exception as e:
            error_msg = f"实体融合过程发生异常: {str(e)}"
            self._create_fusion_task(state, "FAILED", error_msg)
            logger.error(error_msg, exc_info=True)
            
        finally:
            # 关闭Neo4j连接
            if self.storage_agent:
                self.storage_agent.close()
                self.storage_agent = None
        
        return state
    
    def _analyze_duplicate_pair(self, id1: str, id2: str, similarity_score: float) -> Tuple[bool, Optional[str]]:
        """
        分析一对可能重复的元素，决定是否合并
        
        参数:
            id1: 第一个元素ID
            id2: 第二个元素ID
            similarity_score: 相似度分数
            
        返回:
            (是否应该合并, 保留的主元素ID)
        """
        # 获取元素详细信息
        element1 = self.storage_agent.get_element_details(id1)
        element2 = self.storage_agent.get_element_details(id2)
        
        if not element1 or not element2:
            logger.warning(f"无法获取元素详情: {id1}, {id2}")
            return False, None
        
        # 如果相似度很高，直接合并
        if similarity_score >= 0.95:
            # 选择更完整的元素作为主元素
            canonical_id = self._choose_canonical_element(element1, element2, id1, id2)
            return True, canonical_id
        
        # 对于中等相似度，使用LLM进行语义分析
        if similarity_score >= 0.7:
            should_merge, canonical_id = self._llm_analyze_duplicates(element1, element2, id1, id2)
            return should_merge, canonical_id
        
        # 相似度太低，不合并
        return False, None
    
    def _choose_canonical_element(self, element1: Dict, element2: Dict, id1: str, id2: str) -> str:
        """
        选择更完整的元素作为主元素
        
        参数:
            element1: 第一个元素数据
            element2: 第二个元素数据
            id1: 第一个元素ID
            id2: 第二个元素ID
            
        返回:
            主元素ID
        """
        # 计算元素的完整性分数
        score1 = self._calculate_completeness_score(element1)
        score2 = self._calculate_completeness_score(element2)
        
        if score1 >= score2:
            return id1
        else:
            return id2
    
    def _calculate_completeness_score(self, element: Dict) -> int:
        """
        计算元素的完整性分数
        
        参数:
            element: 元素数据
            
        返回:
            完整性分数
        """
        score = 0
        
        # 基本属性
        if element.get('name'):
            score += 1
        if element.get('type'):
            score += 1
        
        # 特定类型属性
        element_type = element.get('type')
        if element_type == 'Requirement':
            if element.get('reqId'):
                score += 2
            if element.get('text'):
                score += 2
        elif element_type == 'Property':
            if element.get('propertyKind'):
                score += 1
            if element.get('typeId'):
                score += 1
            if element.get('multiplicity'):
                score += 1
        
        # 源数量（来自更多源的元素可能更重要）
        sources = element.get('sources', [])
        if isinstance(sources, list):
            score += len(sources)
        
        return score
    
    def _llm_analyze_duplicates(self, element1: Dict, element2: Dict, id1: str, id2: str) -> Tuple[bool, Optional[str]]:
        """
        使用LLM分析两个元素是否为重复
        
        参数:
            element1: 第一个元素数据
            element2: 第二个元素数据
            id1: 第一个元素ID
            id2: 第二个元素ID
            
        返回:
            (是否应该合并, 保留的主元素ID)
        """
        prompt = self._create_duplicate_analysis_prompt(element1, element2, id1, id2)
        
        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            result = self._parse_llm_response(response.content, id1, id2)
            return result
            
        except Exception as e:
            logger.error(f"LLM分析重复元素失败: {e}")
            # 如果LLM失败，使用启发式规则
            return self._heuristic_duplicate_analysis(element1, element2, id1, id2)
    
    def _create_duplicate_analysis_prompt(self, element1: Dict, element2: Dict, id1: str, id2: str) -> str:
        """创建重复分析提示词"""
        prompt = f"""
## 角色
你是一位专业的SysML建模专家，负责识别和处理重复的模型元素。

## 任务
分析以下两个SysML元素是否表示同一个概念实体，如果是，请决定保留哪一个。

## 元素1 (ID: {id1})
```json
{json.dumps(element1, indent=2, ensure_ascii=False)}
```

## 元素2 (ID: {id2})
```json
{json.dumps(element2, indent=2, ensure_ascii=False)}
```

## 分析要求
1. 比较元素的类型、名称、属性和语义含义
2. 考虑元素的完整性和信息丰富度
3. 判断是否为同一概念的不同表示

## 输出格式
请严格按照以下JSON格式回复：
```json
{{
  "should_merge": true/false,
  "canonical_id": "{id1}" 或 "{id2}" 或 null,
  "reasoning": "详细的分析理由"
}}
```

如果should_merge为true，canonical_id应该是保留的元素ID。
如果should_merge为false，canonical_id应该为null。
"""
        return prompt
    
    def _parse_llm_response(self, response: str, id1: str, id2: str) -> Tuple[bool, Optional[str]]:
        """解析LLM响应"""
        try:
            # 提取JSON内容
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].split("```")[0].strip()
            else:
                json_str = response.strip()
            
            result = json.loads(json_str)
            should_merge = result.get("should_merge", False)
            canonical_id = result.get("canonical_id")
            
            # 验证canonical_id
            if should_merge and canonical_id not in [id1, id2]:
                logger.warning(f"LLM返回的canonical_id无效: {canonical_id}")
                return False, None
            
            return should_merge, canonical_id
            
        except Exception as e:
            logger.error(f"解析LLM响应失败: {e}")
            return False, None
    
    def _heuristic_duplicate_analysis(self, element1: Dict, element2: Dict, id1: str, id2: str) -> Tuple[bool, Optional[str]]:
        """启发式重复分析（LLM失败时的备选方案）"""
        # 简单的启发式规则
        type1 = element1.get('type')
        type2 = element2.get('type')
        name1 = element1.get('name', '').lower()
        name2 = element2.get('name', '').lower()
        
        # 类型必须相同
        if type1 != type2:
            return False, None
        
        # 名称必须非常相似
        if name1 == name2:
            canonical_id = self._choose_canonical_element(element1, element2, id1, id2)
            return True, canonical_id
        
        return False, None
    
    def _create_fusion_task(self, state: WorkflowState, status: str, message: str, result: Dict[str, Any] = None):
        """
        创建实体融合任务记录
        
        参数:
            state: 工作流状态
            status: 任务状态
            message: 状态消息
            result: 任务结果
        """
        task_status = ProcessStatus.COMPLETED if status == "COMPLETED" else ProcessStatus.FAILED
        
        fusion_task = SysMLTask(
            id=f"FUSION-TASK-{uuid.uuid4()}",
            type="Entity Fusion",
            content=message,
            status=task_status,
            result=result,
            error_message=message if status == "FAILED" else None
        )
        
        state.assigned_tasks.append(fusion_task)

    def _get_element_context(self, element_id: str) -> Optional[Dict[str, Any]]:
        """
        获取元素的上下文信息，包括元素本身和相关关系

        参数:
            element_id: 元素ID

        返回:
            包含元素详情和上下文的字典
        """
        if not self.storage_agent:
            return None

        try:
            with self.storage_agent._driver.session() as session:
                # 获取元素详情和相关关系
                query = """
                MATCH (n {elementId: $elementId})
                OPTIONAL MATCH (n)-[r1]->(related1)
                OPTIONAL MATCH (n)<-[r2]-(related2)
                OPTIONAL MATCH (parent {elementId: n.parentId})
                RETURN n as element,
                       collect(DISTINCT {type: type(r1), target: related1.elementId, targetType: related1.type}) as outgoing_rels,
                       collect(DISTINCT {type: type(r2), source: related2.elementId, sourceType: related2.type}) as incoming_rels,
                       parent.name as parentName,
                       parent.type as parentType
                """

                result = session.run(query, {'elementId': element_id})
                record = result.single()

                if record:
                    element = dict(record['element'])
                    return {
                        'element': element,
                        'outgoing_relationships': record['outgoing_rels'],
                        'incoming_relationships': record['incoming_rels'],
                        'parent_name': record['parentName'],
                        'parent_type': record['parentType'],
                        'context_summary': self._generate_context_summary(element, record)
                    }
                return None

        except Exception as e:
            logger.error(f"获取元素{element_id}上下文失败: {e}")
            return None

    def _generate_context_summary(self, element: Dict, record) -> str:
        """生成元素上下文摘要"""
        summary_parts = []

        # 基本信息
        element_type = element.get('type', 'Unknown')
        element_name = element.get('name', 'Unnamed')
        summary_parts.append(f"{element_type} '{element_name}'")

        # 父元素信息
        if record['parentName']:
            summary_parts.append(f"位于{record['parentType']} '{record['parentName']}'中")

        # 关系信息
        outgoing_count = len([r for r in record['outgoing_rels'] if r['type']])
        incoming_count = len([r for r in record['incoming_rels'] if r['type']])
        if outgoing_count > 0 or incoming_count > 0:
            summary_parts.append(f"有{outgoing_count}个出关系和{incoming_count}个入关系")

        return "，".join(summary_parts)

    def _get_enhanced_merge_decision(self, element1_context: Dict, element2_context: Dict, similarity_score: float) -> Optional[Dict[str, Any]]:
        """
        使用增强的LLM进行合并决策，基于Gemini方案一的设计

        参数:
            element1_context: 第一个元素的上下文
            element2_context: 第二个元素的上下文
            similarity_score: 相似度分数

        返回:
            合并决策的action_plan
        """
        try:
            prompt = self._build_enhanced_merge_prompt(element1_context, element2_context, similarity_score)

            response = self.llm.invoke([HumanMessage(content=prompt)])
            response_text = response.content.strip()

            # 解析JSON响应
            if response_text.startswith('```json'):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith('```'):
                response_text = response_text[3:-3].strip()

            action_plan = json.loads(response_text)

            # 验证action_plan的完整性
            if self._validate_action_plan(action_plan):
                return action_plan
            else:
                logger.warning("LLM生成的action_plan格式不正确")
                return None

        except json.JSONDecodeError as e:
            logger.error(f"解析LLM响应JSON失败: {e}")
            logger.debug(f"原始响应: {response_text}")
            return None
        except Exception as e:
            logger.error(f"LLM合并决策失败: {e}")
            return None

    def _build_enhanced_merge_prompt(self, element1_context: Dict, element2_context: Dict, similarity_score: float) -> str:
        """
        构建增强的合并决策Prompt，基于Gemini方案一的设计
        """
        element1 = element1_context['element']
        element2 = element2_context['element']

        prompt = f"""## Role
You are an expert MBSE modeler specializing in SysML entity resolution and graph fusion.

## Context
I am merging SysML elements from different sources in a Neo4j graph database. I have found two elements that might be duplicates and need your expert analysis.

**Element 1:**
- ID: {element1.get('elementId', 'N/A')}
- Type: {element1.get('type', 'N/A')}
- Name: {element1.get('name', 'N/A')}
- Context: {element1_context.get('context_summary', 'N/A')}
- Parent: {element1_context.get('parent_name', 'N/A')} ({element1_context.get('parent_type', 'N/A')})
- Relationships: {len(element1_context.get('outgoing_relationships', []))} outgoing, {len(element1_context.get('incoming_relationships', []))} incoming

**Element 2:**
- ID: {element2.get('elementId', 'N/A')}
- Type: {element2.get('type', 'N/A')}
- Name: {element2.get('name', 'N/A')}
- Context: {element2_context.get('context_summary', 'N/A')}
- Parent: {element2_context.get('parent_name', 'N/A')} ({element2_context.get('parent_type', 'N/A')})
- Relationships: {len(element2_context.get('outgoing_relationships', []))} outgoing, {len(element2_context.get('incoming_relationships', []))} incoming

**Similarity Score:** {similarity_score}

## Task
1. **Analyze:** Determine if these two elements represent the same conceptual entity in the system model. Consider:
   - Semantic similarity of names (including different languages)
   - Type compatibility
   - Context and parent relationships
   - Structural role in the system

2. **Reason:** Explain your reasoning step-by-step (Chain of Thought).

3. **Decide:** Choose one of the following actions:
   - "merge": If they represent the same entity
   - "keep_separate": If they are different entities

4. **Action Plan:** If merging, specify which element to keep as canonical and how to handle property conflicts.

## Output Format
Provide your response as a single JSON object:

{{
  "reasoning": "Your step-by-step analysis here...",
  "action": "merge" or "keep_separate",
  "canonicalId": "ID to keep (if merging)",
  "discardedIds": ["IDs to discard (if merging)"],
  "propertyMappings": [
    {{
      "source_property": "property_name",
      "target_property": "target_property_name",
      "merge_strategy": "overwrite|append|prefer_canonical"
    }}
  ],
  "reason": "Brief reason for the decision",
  "confidence": 0.0-1.0
}}"""

        return prompt

    def _validate_action_plan(self, action_plan: Dict[str, Any]) -> bool:
        """验证action_plan的格式和完整性"""
        required_fields = ["reasoning", "action", "reason", "confidence"]

        # 检查必需字段
        for field in required_fields:
            if field not in action_plan:
                logger.warning(f"action_plan缺少必需字段: {field}")
                return False

        # 检查action字段的值
        if action_plan["action"] not in ["merge", "keep_separate"]:
            logger.warning(f"action字段值无效: {action_plan['action']}")
            return False

        # 如果是合并操作，检查必需的合并字段
        if action_plan["action"] == "merge":
            merge_fields = ["canonicalId", "discardedIds"]
            for field in merge_fields:
                if field not in action_plan:
                    logger.warning(f"合并操作缺少必需字段: {field}")
                    return False

        return True

    def _execute_enhanced_merge(self, canonical_id: str, discarded_ids: List[str], property_mappings: List[Dict]) -> bool:
        """
        执行增强的合并操作，支持属性映射

        参数:
            canonical_id: 保留的主元素ID
            discarded_ids: 要合并的重复元素ID列表
            property_mappings: 属性映射规则

        返回:
            是否成功合并
        """
        if not self.storage_agent:
            return False

        try:
            with self.storage_agent._driver.session() as session:
                for discarded_id in discarded_ids:
                    # 应用属性映射规则
                    for mapping in property_mappings:
                        source_prop = mapping.get("source_property")
                        target_prop = mapping.get("target_property")
                        strategy = mapping.get("merge_strategy", "prefer_canonical")

                        if source_prop and target_prop:
                            self._apply_property_mapping(session, canonical_id, discarded_id, source_prop, target_prop, strategy)

                    # 执行标准合并操作
                    success = self.storage_agent.merge_elements(canonical_id, [discarded_id])
                    if not success:
                        return False

            return True

        except Exception as e:
            logger.error(f"执行增强合并失败: {e}")
            return False

    def _apply_property_mapping(self, session, canonical_id: str, discarded_id: str, source_prop: str, target_prop: str, strategy: str):
        """应用属性映射规则"""
        try:
            if strategy == "append":
                # 将discarded元素的属性追加到canonical元素
                query = """
                MATCH (canonical {elementId: $canonicalId})
                MATCH (discarded {elementId: $discardedId})
                WHERE discarded[$sourceProp] IS NOT NULL
                SET canonical[$targetProp] = CASE
                    WHEN canonical[$targetProp] IS NULL THEN discarded[$sourceProp]
                    ELSE canonical[$targetProp] + ', ' + discarded[$sourceProp]
                END
                """
                session.run(query, {
                    'canonicalId': canonical_id,
                    'discardedId': discarded_id,
                    'sourceProp': source_prop,
                    'targetProp': target_prop
                })
            elif strategy == "overwrite":
                # 用discarded元素的属性覆盖canonical元素
                query = """
                MATCH (canonical {elementId: $canonicalId})
                MATCH (discarded {elementId: $discardedId})
                WHERE discarded[$sourceProp] IS NOT NULL
                SET canonical[$targetProp] = discarded[$sourceProp]
                """
                session.run(query, {
                    'canonicalId': canonical_id,
                    'discardedId': discarded_id,
                    'sourceProp': source_prop,
                    'targetProp': target_prop
                })
            # "prefer_canonical" 策略不需要操作，保持canonical元素的属性

        except Exception as e:
            logger.error(f"应用属性映射失败: {e}")

def sysml_fusion_agent(state: WorkflowState) -> WorkflowState:
    """
    SysML实体融合Agent工作流节点入口函数

    参数:
        state: 当前工作流状态

    返回:
        更新后的工作流状态
    """
    logger.info("SysML实体融合Agent节点开始处理")

    agent = FusionAgent()
    return agent.process(state)
