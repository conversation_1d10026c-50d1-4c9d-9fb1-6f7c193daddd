"""
IngestionAgent - 负责将多个SysML JSON文件加载到Neo4j图数据库
基于Gemini方案一的图数据库驱动架构
"""
import logging
import json
import uuid
from typing import Dict, List, Any, Optional
from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from src.agents.neo4j_storage_agent import Neo4jStorageAgent

logger = logging.getLogger(__name__)

class IngestionAgent:
    """
    数据摄取代理，负责将SysML任务结果加载到Neo4j
    """
    
    def __init__(self):
        """初始化摄取代理"""
        self.storage_agent = None
        
    def process(self, state: WorkflowState) -> WorkflowState:
        """
        处理工作流状态，将完成的SysML任务加载到Neo4j
        
        参数:
            state: 当前工作流状态
            
        返回:
            更新后的工作流状态
        """
        logger.info("IngestionAgent开始处理")
        
        # 检查是否已经执行过数据摄取
        if any(task.type == "Data Ingestion" for task in state.assigned_tasks):
            logger.info("已经执行过数据摄取操作，跳过")
            return state
        
        # 获取所有已完成的SysML任务
        completed_tasks = [
            task for task in state.assigned_tasks 
            if task.status == ProcessStatus.COMPLETED and 
            task.result is not None and
            task.type not in ["Data Ingestion", "Entity Fusion", "Model Validation", "Model Export"]
        ]
        
        if not completed_tasks:
            logger.warning("没有找到已完成的SysML任务，无法进行数据摄取")
            self._create_ingestion_task(state, "FAILED", "没有找到已完成的SysML任务")
            return state
        
        logger.info(f"找到{len(completed_tasks)}个已完成的SysML任务")
        
        try:
            # 初始化Neo4j存储代理
            self.storage_agent = Neo4jStorageAgent()

            # 清空数据库以确保干净的开始
            self.storage_agent.clear_database()

            # 第一步：创建基础图结构
            self._create_base_graph_structure()

            # 第二步：使用智能合并器创建统一的JSON数据（包含标准包结构）
            merged_data = self._intelligent_merge_tasks(completed_tasks)

            if merged_data:
                # 第三步：将合并后的统一数据摄取到Neo4j
                success = self._ingest_merged_data_to_graph(merged_data)
                loaded_count = 1 if success else 0
                failed_tasks = [] if success else ["Merged Data"]
            else:
                # 回退到逐个加载任务的方式
                logger.warning("智能合并失败，回退到逐个摄取模式")
                loaded_count = 0
                failed_tasks = []

                for task in completed_tasks:
                    try:
                        if self._ingest_task_to_graph(task):
                            loaded_count += 1
                            logger.info(f"成功摄取任务到图: {task.type}")
                        else:
                            failed_tasks.append(task.type)
                            logger.warning(f"摄取任务失败: {task.type}")
                    except Exception as e:
                        failed_tasks.append(task.type)
                        logger.error(f"摄取任务{task.type}时发生异常: {e}")

            # 创建摄取任务结果
            if loaded_count > 0:
                success_msg = f"成功摄取{loaded_count}个SysML任务到图数据库"
                if failed_tasks:
                    success_msg += f"，失败任务: {', '.join(failed_tasks)}"

                self._create_ingestion_task(state, "COMPLETED", success_msg, {
                    "ingested_count": loaded_count,
                    "failed_tasks": failed_tasks,
                    "total_tasks": len(completed_tasks),
                    "next_step": "ready_for_fusion"
                })
                logger.info(f"数据摄取完成: {success_msg}")

                # 统计摄取结果（不进行修复）
                stats = self._get_ingestion_stats()
                logger.info(f"摄取统计: {stats}")
            else:
                error_msg = f"所有任务摄取失败: {', '.join(failed_tasks)}"
                self._create_ingestion_task(state, "FAILED", error_msg)
                logger.error(f"数据摄取失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"数据摄取过程发生异常: {str(e)}"
            self._create_ingestion_task(state, "FAILED", error_msg)
            logger.error(error_msg, exc_info=True)
            
        finally:
            # 关闭Neo4j连接
            if self.storage_agent:
                self.storage_agent.close()
                self.storage_agent = None
        
        return state

    def _create_base_graph_structure(self):
        """创建基础图结构 - 方案一的第一步"""
        logger.info("🏗️ 创建基础图结构")

        try:
            with self.storage_agent._driver.session() as session:
                # 创建根模型节点
                session.run("""
                MERGE (m:Model {elementId: 'unified-sysml-model'})
                ON CREATE SET m.name = 'Unified SysML Model', m.type = 'Model', m._created_by = 'IngestionAgent'
                RETURN m
                """)

                # 创建MBSE规范的基础包结构
                packages = [
                    {"id": "pkg-reqs-uuid", "name": "Requirements", "description": "用于 Requirement, TestCase, Verify, Satisfy, DeriveReqt"},
                    {"id": "pkg-usecases-uuid", "name": "UseCases", "description": "用于 Actor, UseCase, 和它们之间的关系"},
                    {"id": "pkg-structure-uuid", "name": "SystemStructure", "description": "用于 Block, Property, Port, Connector, Association, InterfaceBlock"},
                    {"id": "pkg-behavior-uuid", "name": "SystemBehavior", "description": "用于 Activity, StateMachine, Interaction, 和它们的子元素"},
                    {"id": "pkg-libraries-uuid", "name": "Libraries", "description": "用于 ValueType, Enumeration, Signal, Unit, ConstraintBlock"}
                ]

                for pkg in packages:
                    session.run("""
                    MERGE (p:Package {elementId: $pkgId})
                    ON CREATE SET p.name = $pkgName, p.type = 'Package', p.parentId = 'unified-sysml-model',
                                  p.description = $pkgDesc, p._created_by = 'IngestionAgent'
                    WITH p
                    MATCH (m:Model {elementId: 'unified-sysml-model'})
                    MERGE (m)-[:CONTAINS]->(p)
                    RETURN p
                    """, {"pkgId": pkg["id"], "pkgName": pkg["name"], "pkgDesc": pkg["description"]})

                logger.info("✅ 基础图结构创建完成")

        except Exception as e:
            logger.error(f"创建基础图结构失败: {e}")
            raise

    def _ingest_task_to_graph(self, task: SysMLTask) -> bool:
        """将单个任务摄取到图数据库 - 方案一的核心"""
        logger.info(f"📥 摄取任务到图: {task.type}")

        try:
            if not task.result or 'elements' not in task.result:
                logger.warning(f"任务{task.type}没有有效的elements数据")
                return False

            elements = task.result['elements']

            # 为每个元素生成全局唯一UUID，保留原始ID作为别名
            processed_elements = []
            for element in elements:
                processed_element = element.copy()

                # 生成全局唯一ID（方案一的核心）
                import uuid
                original_id = element.get('id', str(uuid.uuid4()))
                global_id = f"{task.type.lower().replace(' ', '-')}-{original_id}"

                # 保存原始信息（方案一要求）
                processed_element['elementId'] = global_id  # Neo4j中的主键
                processed_element['_original_id'] = original_id  # 原始ID作为别名
                processed_element['_source_task'] = task.type  # 来源任务
                processed_element['_source_content'] = task.content[:100] + "..." if len(task.content) > 100 else task.content

                # 保持原始引用不变（重要！）
                # parentId, sourceId, targetId等保持原样，等待FusionAgent处理

                processed_elements.append(processed_element)

            # 批量创建节点
            with self.storage_agent._driver.session() as session:
                # 按类型分组创建节点
                elements_by_type = {}
                for element in processed_elements:
                    elem_type = element.get('type', 'Unknown')
                    if elem_type not in elements_by_type:
                        elements_by_type[elem_type] = []
                    elements_by_type[elem_type].append(element)

                # 批量创建节点
                for elem_type, type_elements in elements_by_type.items():
                    self._create_nodes_batch(session, type_elements, elem_type, task.type)

                # 创建关系 - 使用新的智能关系创建逻辑
                logger.info(f"开始为任务{task.type}创建关系...")
                self.storage_agent._create_relationships(session, task.result, task.type)

            logger.info(f"✅ 成功摄取任务{task.type}: {len(processed_elements)}个元素")
            return True

        except Exception as e:
            logger.error(f"摄取任务{task.type}失败: {e}")
            return False

    def _create_raw_relationships(self, session, elements: List[Dict], source_task: str):
        """创建原始关系（不做智能合并）"""
        logger.info(f"创建{source_task}任务的原始关系")

        # 创建基本的parentId关系
        parent_child_rels = []
        for element in elements:
            global_id = element.get('_global_id')
            parent_id = element.get('parentId')

            if parent_id and global_id:
                # 尝试在当前任务中找到父节点
                parent_global_id = f"{source_task.lower()}-{parent_id}"
                parent_child_rels.append({
                    "parent_global_id": parent_global_id,
                    "child_global_id": global_id,
                    "original_parent_id": parent_id,
                    "source_task": source_task
                })

        if parent_child_rels:
            query = """
            UNWIND $rel_list AS rel_data
            MATCH (p {_global_id: rel_data.parent_global_id})
            MATCH (c {_global_id: rel_data.child_global_id})
            MERGE (p)-[r:RAW_CONTAINS {source_task: rel_data.source_task, original_parent_id: rel_data.original_parent_id}]->(c)
            RETURN count(r)
            """
            result = session.run(query, {"rel_list": parent_child_rels})
            count = result.single()[0]
            logger.info(f"创建了{count}个RAW_CONTAINS关系")

    def _get_ingestion_stats(self) -> Dict[str, Any]:
        """获取摄取统计信息"""
        try:
            with self.storage_agent._driver.session() as session:
                # 统计各任务的节点数
                task_stats_query = """
                MATCH (n)
                WHERE n._source_task IS NOT NULL
                RETURN n._source_task as task, count(n) as node_count
                ORDER BY node_count DESC
                """
                result = session.run(task_stats_query)
                task_stats = [{"task": record["task"], "nodes": record["node_count"]} for record in result]

                # 统计总数
                total_query = """
                MATCH (n) WHERE n._source_task IS NOT NULL
                RETURN count(n) as total_nodes
                """
                total_result = session.run(total_query)
                total_nodes = total_result.single()["total_nodes"]

                # 统计关系数
                rel_query = """
                MATCH ()-[r]->()
                RETURN count(r) as total_relations
                """
                rel_result = session.run(rel_query)
                total_relations = rel_result.single()["total_relations"]

                return {
                    "total_nodes": total_nodes,
                    "total_relations": total_relations,
                    "task_breakdown": task_stats
                }

        except Exception as e:
            logger.error(f"获取摄取统计失败: {e}")
            return {"error": str(e)}

    def _create_nodes_batch(self, session, elements: List[Dict], elem_type: str, source_task: str):
        """批量创建节点 - 完整业务版本"""
        if not elements:
            return

        logger.info(f"创建 {len(elements)} 个 {elem_type} 节点 (来源: {source_task})")

        # 准备节点数据
        node_data = []
        for element in elements:
            node_props = {
                'elementId': element.get('elementId'),
                'type': elem_type,
                'source': source_task,
                '_original_id': element.get('_original_id'),
                '_source_task': element.get('_source_task'),
                '_source_content': element.get('_source_content')
            }

            # 添加通用属性
            for attr in ['name', 'parentId', 'id']:
                if attr in element and element[attr] is not None:
                    node_props[attr] = element[attr]

            # 添加特定类型属性
            if elem_type == 'Requirement':
                for attr in ['reqId', 'text', 'priority', 'status']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type == 'Block':
                for attr in ['isAbstract', 'visibility', 'stereotype']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type == 'Property':
                for attr in ['visibility', 'propertyKind', 'aggregation', 'typeId', 'multiplicity']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type in ['FullPort', 'ProxyPort']:
                for attr in ['visibility', 'typeId', 'direction', 'isConjugated']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type == 'Association':
                for attr in ['memberEndIds', 'navigableOwnedEndIds']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type in ['InputPin', 'OutputPin']:
                for attr in ['typeId', 'multiplicity']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type in ['ControlFlow', 'ObjectFlow']:
                for attr in ['sourceId', 'targetId']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type == 'Transition':
                for attr in ['sourceId', 'targetId', 'triggerIds', 'effectId']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type in ['Include', 'Extend']:
                for attr in ['sourceId', 'targetId']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]
            elif elem_type == 'Message':
                for attr in ['sendEventId', 'receiveEventId', 'signatureId']:
                    if attr in element and element[attr] is not None:
                        node_props[attr] = element[attr]

            # 存储完整的原始数据
            import json
            node_props['originalData'] = json.dumps(element, ensure_ascii=False)
            node_data.append(node_props)

        # 使用简洁的标签策略
        element_label = elem_type.replace(' ', '_').replace('-', '_')
        element_label = ''.join(c if c.isalnum() or c == '_' else '_' for c in element_label)

        # 获取辅助标签
        category_labels = self._get_category_labels(elem_type)
        all_labels = [element_label] + category_labels
        labels_str = ':'.join(all_labels)

        # 批量创建节点
        query = f"""
        UNWIND $nodes AS node
        MERGE (n:`{labels_str}` {{elementId: node.elementId}})
        ON CREATE SET n = node, n.sources = [node.source]
        ON MATCH SET n += node, n.sources = CASE
            WHEN n.sources IS NULL THEN [node.source]
            WHEN NOT node.source IN n.sources THEN n.sources + node.source
            ELSE n.sources
        END
        RETURN count(n) as created_count
        """

        try:
            result = session.run(query, {"nodes": node_data})
            count = result.single()["created_count"]
            logger.info(f"成功创建 {count} 个 {elem_type} 节点，使用标签: {labels_str}")
        except Exception as e:
            logger.error(f"创建 {elem_type} 节点失败: {e}")
            raise

    def _get_category_labels(self, element_type: str) -> List[str]:
        """获取元素类型的分类标签"""
        category_labels = []

        # 端口相关
        if element_type in ['FullPort', 'ProxyPort']:
            category_labels.append('Port')

        # 连接器相关
        elif element_type in ['ItemFlowConnector', 'BindingConnector']:
            category_labels.append('Connector')

        # 节点相关
        elif element_type in ['InitialNode', 'ActivityFinalNode', 'DecisionNode', 'MergeNode', 'ForkNode', 'JoinNode']:
            category_labels.append('ActivityNode')

        # 引脚相关
        elif element_type in ['InputPin', 'OutputPin']:
            category_labels.append('Pin')

        # 流相关
        elif element_type in ['ControlFlow', 'ObjectFlow']:
            category_labels.append('Flow')

        # 状态机相关
        elif element_type in ['State', 'Pseudostate', 'Transition']:
            category_labels.append('StateMachineElement')

        # 用例相关
        elif element_type in ['Include', 'Extend']:
            category_labels.append('UseCaseRelationship')

        # 需求相关
        elif element_type in ['Satisfy', 'Verify', 'DeriveReqt']:
            category_labels.append('RequirementRelationship')

        return category_labels

    def _intelligent_merge_tasks(self, tasks: List[SysMLTask]) -> Optional[Dict[str, Any]]:
        """使用智能JSON合并器合并任务数据，创建标准MBSE包结构"""
        try:
            from .intelligent_json_merger import IntelligentJSONMerger

            logger.info("🔄 开始智能合并多个SysML任务，创建标准MBSE包结构")
            merger = IntelligentJSONMerger()
            merged_data = merger.merge_sysml_tasks(tasks)

            logger.info(f"✅ 智能合并完成: {len(merged_data.get('elements', []))} 个元素")

            # 统计包结构
            packages = [elem for elem in merged_data.get('elements', []) if elem.get('type') == 'Package']
            logger.info(f"📦 创建了 {len(packages)} 个标准包")

            return merged_data

        except Exception as e:
            logger.error(f"❌ 智能合并失败: {e}")
            return None

    def _ingest_merged_data_to_graph(self, merged_data: Dict[str, Any]) -> bool:
        """将合并后的统一数据摄取到Neo4j图数据库"""
        try:
            logger.info("📥 开始摄取合并后的统一数据到Neo4j")

            # 摄取所有元素
            elements = merged_data.get("elements", [])
            if not elements:
                logger.warning("合并数据中没有元素")
                return False

            # 使用存储代理的load_sysml_json方法摄取数据
            success = self.storage_agent.load_sysml_json(
                json_data=merged_data,
                source_name="Merged Data"
            )

            if success:
                logger.info(f"✅ 成功摄取合并数据: {len(elements)} 个元素")
            else:
                logger.error("❌ 摄取合并数据失败")

            return success

        except Exception as e:
            logger.error(f"❌ 摄取合并数据失败: {e}")
            return False

    def _intelligent_merge_tasks_old(self, tasks: List[SysMLTask]) -> Optional[Dict[str, Any]]:
        """使用智能JSON合并器合并任务数据（旧版本，保留备用）"""
        try:
            from .intelligent_json_merger import IntelligentJSONMerger

            merger = IntelligentJSONMerger()
            merged_data = merger.merge_sysml_tasks(tasks)

            logger.info(f"智能合并完成: {len(merged_data.get('elements', []))} 个元素")
            return merged_data

        except Exception as e:
            logger.error(f"智能合并失败: {e}")
            return None

    def _load_merged_data_to_neo4j(self, merged_data: Dict[str, Any]) -> bool:
        """将合并后的数据加载到Neo4j"""
        try:
            # 创建统一模型结构
            self._create_unified_model_structure()

            # 加载合并后的元素
            elements = merged_data.get('elements', [])

            # 按类型分组批量创建节点
            elements_by_type = {}
            for element in elements:
                elem_type = element.get('type')
                if elem_type not in elements_by_type:
                    elements_by_type[elem_type] = []
                elements_by_type[elem_type].append(element)

            # 批量创建节点
            with self.storage_agent._driver.session() as session:
                for elem_type, type_elements in elements_by_type.items():
                    self._create_nodes_batch(session, type_elements, elem_type, "Merged")

                # 创建关系
                from .sysml_relationship_creator import SysMLRelationshipCreator
                relationship_creator = SysMLRelationshipCreator()
                relationship_creator.create_relationships(session, elements, "Merged")

            return True

        except Exception as e:
            logger.error(f"加载合并数据到Neo4j失败: {e}")
            return False

    def _create_unified_model_structure(self):
        """创建统一的模型结构"""
        logger.info("创建统一的模型结构")

        try:
            with self.storage_agent._driver.session() as session:
                # 创建唯一的根模型
                model_query = """
                CREATE (m:Model {
                    elementId: 'unified-sysml-model',
                    type: 'Model',
                    name: 'Unified SysML Model',
                    source: 'unified_structure'
                })
                """
                session.run(model_query)
                logger.info("创建了统一根模型")

                # 创建标准包结构
                standard_packages = [
                    ('pkg-requirements', 'Requirements', '需求包'),
                    ('pkg-usecases', 'UseCases', '用例包'),
                    ('pkg-structure', 'Structure', '结构包'),
                    ('pkg-behavior', 'Behavior', '行为包'),
                    ('pkg-parametrics', 'Parametrics', '参数包'),
                    ('pkg-libraries', 'Libraries', '库包')
                ]

                for pkg_id, pkg_name, description in standard_packages:
                    package_query = """
                    CREATE (p:Package {
                        elementId: $packageId,
                        type: 'Package',
                        name: $packageName,
                        parentId: 'unified-sysml-model',
                        source: 'unified_structure',
                        description: $description
                    })
                    """
                    session.run(package_query, {
                        'packageId': pkg_id,
                        'packageName': pkg_name,
                        'description': description
                    })

                    # 创建包含关系
                    contains_query = """
                    MATCH (m {elementId: 'unified-sysml-model'})
                    MATCH (p {elementId: $packageId})
                    MERGE (m)-[:CONTAINS]->(p)
                    """
                    session.run(contains_query, {'packageId': pkg_id})

                logger.info(f"创建了{len(standard_packages)}个标准包")

        except Exception as e:
            logger.error(f"创建统一模型结构失败: {e}")
            raise

    def _load_task_to_unified_structure(self, task: SysMLTask) -> bool:
        """将任务加载到统一结构中"""
        if not task.result:
            logger.warning(f"任务{task.type}没有结果数据")
            return False

        # 验证JSON结构
        if not self._validate_json_structure(task.result):
            logger.warning(f"任务{task.type}的JSON结构无效")
            return False

        # 预处理数据，重新组织到统一结构
        processed_data = self._reorganize_to_unified_structure(task.result, task.type)

        # 加载到Neo4j
        return self._load_elements_to_neo4j(processed_data, task.type)

    def _reorganize_to_unified_structure(self, json_data: Dict[str, Any], source_type: str) -> Dict[str, Any]:
        """重新组织数据到统一结构"""
        import copy

        # 深拷贝数据
        processed_data = copy.deepcopy(json_data)

        # 移除原有的model信息，因为我们使用统一的model
        if "model" in processed_data:
            del processed_data["model"]

        # 重新组织元素到标准包结构
        elements = processed_data.get("elements", [])
        reorganized_elements = []

        for element in elements:
            # 为每个元素添加源信息
            element["_source"] = source_type

            # 确保ID存在且唯一
            if "id" not in element or not element["id"]:
                element["id"] = f"{source_type.lower()}-{uuid.uuid4()}"

            # 重新分配父元素到标准包
            new_parent_id = self._assign_to_standard_package(element, source_type)
            if new_parent_id:
                element["parentId"] = new_parent_id

            reorganized_elements.append(element)

        processed_data["elements"] = reorganized_elements
        return processed_data

    def _assign_to_standard_package(self, element: Dict[str, Any], source_type: str) -> str:
        """将元素分配到标准包"""
        element_type = element.get("type")

        # 元素类型到包的映射
        type_to_package = {
            # 需求相关
            "Requirement": "pkg-requirements",
            "TestCase": "pkg-requirements",

            # 用例相关
            "Actor": "pkg-usecases",
            "UseCase": "pkg-usecases",
            "Include": "pkg-usecases",
            "Extend": "pkg-usecases",

            # 结构相关
            "Block": "pkg-structure",
            "Property": None,  # Property保持原有父元素
            "Port": None,
            "FullPort": None,
            "ProxyPort": None,
            "Association": "pkg-structure",
            "AssemblyConnector": None,
            "Connector": None,
            "InterfaceBlock": "pkg-structure",

            # 行为相关
            "Activity": "pkg-behavior",
            "StateMachine": "pkg-behavior",
            "State": None,  # State保持在StateMachine内
            "Transition": None,
            "Interaction": "pkg-behavior",
            "Lifeline": None,
            "Message": None,
            "CallBehaviorAction": None,
            "ControlFlow": None,
            "ObjectFlow": None,

            # 参数相关
            "ConstraintBlock": "pkg-parametrics",
            "ConstraintParameter": None,
            "BindingConnector": None,

            # 库相关
            "ValueType": "pkg-libraries",
            "Enumeration": "pkg-libraries",
            "EnumerationLiteral": None,
            "Signal": "pkg-libraries",
            "Unit": "pkg-libraries",

            # 其他
            "Package": "pkg-structure",  # 子包放在结构包下
        }

        target_package = type_to_package.get(element_type)

        # 如果没有指定目标包，保持原有父元素关系
        if target_package is None:
            return element.get("parentId", "unified-sysml-model")

        return target_package

    def _load_elements_to_neo4j(self, processed_data: Dict[str, Any], source_type: str) -> bool:
        """将重新组织的元素加载到Neo4j"""
        try:
            with self.storage_agent._driver.session() as session:
                elements = processed_data.get("elements", [])

                # 按类型分组处理元素
                elements_by_type = {}
                for element in elements:
                    element_type = element.get('type', 'Unknown')
                    if element_type not in elements_by_type:
                        elements_by_type[element_type] = []
                    elements_by_type[element_type].append(element)

                # 批量创建各类型节点
                for element_type, type_elements in elements_by_type.items():
                    self._create_elements_batch(session, element_type, type_elements, source_type)

                # 创建关系 - 使用专门的SysML关系创建逻辑
                from .sysml_relationship_creator import SysMLRelationshipCreator
                relationship_creator = SysMLRelationshipCreator()
                relationship_creator.create_relationships(session, elements, source_type)

                return True

        except Exception as e:
            logger.error(f"加载元素到Neo4j失败: {e}")
            return False

    def _create_elements_batch(self, session, element_type: str, elements: List[Dict], source_type: str):
        """批量创建元素"""
        if not elements:
            return

        node_data = []
        for element in elements:
            node_props = {
                'elementId': element.get('id'),
                'type': element_type,
                'source': source_type
            }

            # 添加通用属性
            for attr in ['name', 'parentId']:
                if attr in element:
                    node_props[attr] = element[attr]

            # 添加特定类型属性
            if element_type == 'Requirement':
                for attr in ['reqId', 'text']:
                    if attr in element:
                        node_props[attr] = element[attr]
            elif element_type == 'Property':
                for attr in ['visibility', 'propertyKind', 'aggregation', 'typeId', 'multiplicity']:
                    if attr in element:
                        node_props[attr] = element[attr]

            # 存储完整的原始数据
            node_props['originalData'] = json.dumps(element)
            node_data.append(node_props)

        # 批量创建节点 - 使用简洁的标签策略（参考temp目录最佳实践）
        element_label = element_type.replace(' ', '_').replace('-', '_')
        element_label = ''.join(c if c.isalnum() or c == '_' else '_' for c in element_label)

        # 获取辅助标签（只在特定情况下添加）
        category_labels = self._get_category_labels(element_type)
        all_labels = [element_label] + category_labels
        labels_str = ':'.join(all_labels)

        query = f"""
        UNWIND $nodes AS node
        MERGE (n:`{labels_str}` {{elementId: node.elementId}})
        ON CREATE SET n = node, n.sources = [node.source]
        ON MATCH SET n += node, n.sources = CASE
            WHEN n.sources IS NULL THEN [node.source]
            WHEN NOT node.source IN n.sources THEN n.sources + node.source
            ELSE n.sources
        END
        """

        session.run(query, {'nodes': node_data})
        logger.info(f"创建了{len(node_data)}个{element_type}节点，使用标签:{labels_str}")

    def _create_relationships_batch(self, session, elements: List[Dict], source_type: str):
        """批量创建关系"""
        # 创建包含关系（CONTAINS）
        contains_rels = []
        for element in elements:
            element_id = element.get('id')
            parent_id = element.get('parentId')

            if element_id and parent_id and element_id != parent_id:
                contains_rels.append({
                    'parentId': parent_id,
                    'childId': element_id,
                    'source': source_type
                })

        if contains_rels:
            contains_query = """
            UNWIND $rels AS rel
            MATCH (parent {elementId: rel.parentId})
            MATCH (child {elementId: rel.childId})
            MERGE (parent)-[r:CONTAINS]->(child)
            ON CREATE SET r.source = rel.source, r.sources = [rel.source]
            ON MATCH SET r.sources = CASE
                WHEN r.sources IS NULL THEN [rel.source]
                WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                ELSE r.sources
            END
            """
            session.run(contains_query, {'rels': contains_rels})
            logger.info(f"创建了{len(contains_rels)}个CONTAINS关系")

        # 创建特定类型关系
        self._create_specific_relationships_batch(session, elements, source_type)

    def _create_specific_relationships_batch(self, session, elements: List[Dict], source_type: str):
        """批量创建特定类型的关系"""
        relationship_types = {
            'DeriveReqt': ('DERIVES_FROM', 'derivedRequirementId', 'sourceRequirementId'),
            'Satisfy': ('SATISFIES', 'blockId', 'requirementId'),
            'Verify': ('VERIFIES', 'testCaseId', 'requirementId'),
            'Association': ('ASSOCIATES', 'sourceId', 'targetId'),
            'Transition': ('TRANSITIONS_TO', 'sourceId', 'targetId')
        }

        for element in elements:
            element_type = element.get('type')
            if element_type in relationship_types:
                rel_type, from_field, to_field = relationship_types[element_type]
                from_id = element.get(from_field)
                to_id = element.get(to_field)

                if from_id and to_id:
                    rel_query = f"""
                    MATCH (from {{elementId: $fromId}})
                    MATCH (to {{elementId: $toId}})
                    MERGE (from)-[r:{rel_type}]->(to)
                    ON CREATE SET r.relationId = $relationId, r.source = $source, r.sources = [$source]
                    ON MATCH SET r.sources = CASE
                        WHEN r.sources IS NULL THEN [$source]
                        WHEN NOT $source IN r.sources THEN r.sources + $source
                        ELSE r.sources
                    END
                    """
                    session.run(rel_query, {
                        'fromId': from_id,
                        'toId': to_id,
                        'relationId': element.get('id'),
                        'source': source_type
                    })

    def _load_task_to_neo4j(self, task: SysMLTask) -> bool:
        """
        将单个任务结果加载到Neo4j

        参数:
            task: SysML任务

        返回:
            是否成功加载
        """
        if not task.result:
            logger.warning(f"任务{task.type}没有结果数据")
            return False

        # 验证JSON结构
        if not self._validate_json_structure(task.result):
            logger.warning(f"任务{task.type}的JSON结构无效")
            return False

        # 设置当前任务内容供智能修复使用
        self._current_task_content = task.content

        # 为每个元素生成全局唯一ID（如果需要）
        processed_data = self._preprocess_json_data(task.result, task.type)
        
        # 加载到Neo4j
        return self.storage_agent.load_sysml_json(processed_data, task.type)
    
    def _validate_json_structure(self, json_data: Dict[str, Any]) -> bool:
        """
        验证JSON数据结构

        参数:
            json_data: JSON数据

        返回:
            是否有效
        """
        # 基本结构检查
        if not isinstance(json_data, dict):
            return False

        # 特殊处理：JSON Enhancement任务的结果结构不同
        if "enhanced_tasks" in json_data and "total_enhanced_attributes" in json_data:
            # 这是JSON Enhancement任务的统计结果，不需要elements字段
            return True

        # 检查必要字段
        if "elements" not in json_data:
            return False

        elements = json_data["elements"]
        if not isinstance(elements, list):
            return False

        # 检查元素结构
        for element in elements:
            if not isinstance(element, dict):
                return False
            if "id" not in element or "type" not in element:
                return False

        return True
    
    def _preprocess_json_data(self, json_data: Dict[str, Any], source_type: str) -> Dict[str, Any]:
        """
        预处理JSON数据，确保ID唯一性等
        
        参数:
            json_data: 原始JSON数据
            source_type: 数据源类型
            
        返回:
            处理后的JSON数据
        """
        # 深拷贝数据以避免修改原始数据
        import copy
        processed_data = copy.deepcopy(json_data)
        
        # 为每个元素添加源信息
        elements = processed_data.get("elements", [])
        for element in elements:
            # 添加源标记
            element["_source"] = source_type

            # 确保ID存在
            if "id" not in element or not element["id"]:
                element["id"] = f"{source_type.lower()}-{uuid.uuid4()}"

        # 智能修复数据完整性问题
        processed_data = self._fix_data_integrity(processed_data, source_type)
        
        # 处理model信息
        model_info = processed_data.get("model")
        if model_info:
            if isinstance(model_info, list) and model_info:
                model_info = model_info[0]
                processed_data["model"] = model_info
            
            # 确保model有ID
            if "id" not in model_info or not model_info["id"]:
                model_info["id"] = f"model-{source_type.lower()}-{uuid.uuid4()}"
        
        return processed_data
    
    def _get_category_labels(self, element_type: str) -> List[str]:
        """
        根据元素类型获取功能分类标签（参考temp目录下的最佳实践）

        参数:
            element_type: 元素类型

        返回:
            分类标签列表（只在特定情况下添加辅助标签）
        """
        category_labels = []

        # 参考 temp/bdd_and_ibd/bdd_and_ibd_store.py 的设计
        # 只为特定类型添加辅助标签，不是所有元素都添加通用标签

        # Port类型的辅助标签
        if element_type in {'FullPort', 'ProxyPort'}:
            category_labels.append('Port')

        # Connector类型的辅助标签
        elif element_type.endswith('Connector'):
            category_labels.append('Connector')

        # 其他特殊情况可以根据需要添加
        # 但不应该给每个元素都添加通用标签

        return category_labels

    def _fix_data_integrity(self, json_data: Dict[str, Any], source_type: str) -> Dict[str, Any]:
        """
        智能修复数据完整性问题

        参数:
            json_data: 原始JSON数据
            source_type: 数据源类型

        返回:
            修复后的JSON数据
        """
        try:
            # 导入数据完整性修复Agent
            from .data_integrity_fixer_agent import DataIntegrityFixerAgent

            # 获取任务内容（用于LLM理解上下文）
            task_content = getattr(self, '_current_task_content', f'{source_type} 任务的SysML数据')

            # 创建修复Agent并执行修复
            fixer = DataIntegrityFixerAgent()
            fixed_data = fixer.fix_data_integrity(json_data, source_type, task_content)

            return fixed_data

        except Exception as e:
            logger.error(f"智能数据完整性修复失败: {e}")
            logger.info("回退到原始数据")
            return json_data

    def _fix_missing_activity_nodes_old(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        修复Activity任务中缺失的节点定义

        参数:
            json_data: Activity任务的JSON数据

        返回:
            修复后的JSON数据
        """
        import copy
        fixed_data = copy.deepcopy(json_data)
        elements = fixed_data.get("elements", [])

        # 找到主Activity
        main_activity = None
        for elem in elements:
            if elem.get('type') == 'Activity' and 'nodes' in elem:
                main_activity = elem
                break

        if not main_activity:
            return fixed_data

        # 获取引用的节点和已定义的节点
        referenced_nodes = set(main_activity['nodes'])
        defined_nodes = set()

        for elem in elements:
            if elem.get('id', '').startswith('node-'):
                defined_nodes.add(elem['id'])

        # 找到缺失的节点
        missing_nodes = referenced_nodes - defined_nodes

        if missing_nodes:
            logger.info(f"发现Activity中缺失的节点: {missing_nodes}")

            # 为缺失的节点创建默认定义
            for node_id in missing_nodes:
                # 根据节点ID推断节点类型和名称
                node_type, node_name = self._infer_node_type_and_name(node_id)

                missing_node = {
                    "id": node_id,
                    "type": node_type,
                    "name": node_name,
                    "parentId": main_activity['id'],
                    "_source": "Activity",
                    "_auto_generated": True  # 标记为自动生成
                }

                elements.append(missing_node)
                logger.info(f"自动创建缺失节点: {node_type} - {node_name} (ID: {node_id})")

        return fixed_data

    def _infer_node_type_and_name(self, node_id: str) -> tuple:
        """
        根据节点ID推断节点类型和名称

        参数:
            node_id: 节点ID

        返回:
            (节点类型, 节点名称)
        """
        # 基于ID模式推断类型
        if 'start' in node_id:
            return 'InitialNode', '开始'
        elif 'end' in node_id:
            return 'ActivityFinalNode', '结束'
        elif 'decision' in node_id:
            return 'DecisionNode', '决策'
        elif 'fork' in node_id:
            return 'ForkNode', '分叉'
        elif 'join' in node_id:
            return 'JoinNode', '合并'
        elif 'merge' in node_id:
            return 'MergeNode', '汇合'
        elif any(keyword in node_id for keyword in ['bike-start', 'pedal', 'drive-chain', 'monitor-speed']):
            # 这些是具体的业务动作节点
            name_mapping = {
                'node-bike-start': '启动自行车',
                'node-pedal': '踩踏板',
                'node-drive-chain': '传动链运转',
                'node-monitor-speed': '监测速度'
            }
            return 'CallBehaviorAction', name_mapping.get(node_id, node_id.replace('node-', '').replace('-', ' '))
        else:
            # 默认为CallBehaviorAction
            return 'CallBehaviorAction', node_id.replace('node-', '').replace('-', ' ')

    def _check_orphaned_nodes(self):
        """检查和报告孤立节点"""
        try:
            with self.storage_agent._driver.session() as session:
                # 查找没有父节点的非Model节点
                query = """
                MATCH (n)
                WHERE n.elementId IS NOT NULL
                AND n.type IS NOT NULL
                AND n.type <> 'Model'
                AND NOT EXISTS {
                    MATCH (p)-[:CONTAINS]->(n)
                }
                RETURN n.elementId as id, n.type as type, n.name as name, n.parentId as parentId
                ORDER BY n.type, n.name
                """
                result = session.run(query)
                orphaned_nodes = []

                for record in result:
                    orphaned_nodes.append({
                        'id': record['id'],
                        'type': record['type'],
                        'name': record['name'],
                        'parentId': record['parentId']
                    })

                if orphaned_nodes:
                    logger.warning(f"发现 {len(orphaned_nodes)} 个孤立节点:")
                    for node in orphaned_nodes:
                        logger.warning(f"  - {node['type']}: {node['name']} (parentId: {node['parentId']})")
                else:
                    logger.info("✅ 没有发现孤立节点")

                return orphaned_nodes

        except Exception as e:
            logger.error(f"检查孤立节点失败: {e}")
            return []

    def _fix_orphaned_nodes_with_llm(self, orphaned_nodes: List[Dict]):
        """
        使用LLM修复孤立节点

        参数:
            orphaned_nodes: 孤立节点列表
        """
        try:
            from langchain_openai import ChatOpenAI
            from langchain_core.messages import SystemMessage, HumanMessage
            from config.settings import settings

            # 创建LLM
            llm = ChatOpenAI(
                model=settings.llm_model,
                api_key=settings.openai_api_key,
                base_url=settings.base_url,
                temperature=0.1
            )

            # 构建修复提示
            prompt = self._build_orphan_fix_prompt(orphaned_nodes)

            # 调用LLM
            response = llm.invoke([
                SystemMessage(content=self._get_orphan_fix_system_prompt()),
                HumanMessage(content=prompt)
            ])

            # 解析并执行修复建议
            self._execute_orphan_fixes(response.content, orphaned_nodes)

        except Exception as e:
            logger.error(f"LLM修复孤立节点失败: {e}")

    def _get_orphan_fix_system_prompt(self) -> str:
        """获取孤立节点修复的系统提示"""
        return """你是一个专业的SysML数据修复专家。你的任务是分析孤立节点（没有父子关系的节点）并提供修复建议。

修复策略：
1. 分析孤立节点的parentId，判断是否应该创建缺失的父节点
2. 检查是否存在ID映射错误，建议正确的父节点ID
3. 对于确实应该孤立的节点（如顶级包），建议连接到根模型

请以JSON格式返回修复建议：
{
  "fixes": [
    {
      "type": "create_missing_parent",
      "orphan_id": "节点ID",
      "missing_parent_id": "缺失的父节点ID",
      "suggested_parent_type": "建议的父节点类型",
      "suggested_parent_name": "建议的父节点名称"
    },
    {
      "type": "connect_to_root",
      "orphan_id": "节点ID",
      "reason": "连接到根的原因"
    }
  ]
}"""

    def _build_orphan_fix_prompt(self, orphaned_nodes: List[Dict]) -> str:
        """构建孤立节点修复提示"""

        # 获取现有节点信息
        existing_nodes = []
        try:
            with self.storage_agent._driver.session() as session:
                query = """
                MATCH (n)
                WHERE n.elementId IS NOT NULL
                RETURN n.elementId as id, n.type as type, n.name as name
                LIMIT 50
                """
                result = session.run(query)
                existing_nodes = [
                    {'id': record['id'], 'type': record['type'], 'name': record['name']}
                    for record in result
                ]
        except:
            pass

        prompt = f"""
请分析以下孤立节点并提供修复建议：

## 孤立节点列表 ({len(orphaned_nodes)} 个)
"""

        for i, node in enumerate(orphaned_nodes[:20], 1):  # 限制数量避免提示过长
            prompt += f"{i}. {node['type']}: {node['name']} (ID: {node['id']}, parentId: {node['parentId']})\n"

        if len(orphaned_nodes) > 20:
            prompt += f"... 还有 {len(orphaned_nodes) - 20} 个节点\n"

        prompt += f"""

## 现有节点参考 (前50个)
"""
        for node in existing_nodes:
            prompt += f"- {node['type']}: {node['name']} (ID: {node['id']})\n"

        prompt += """

## 分析要求
1. 检查孤立节点的parentId是否指向应该存在但缺失的节点
2. 判断是否存在ID映射错误
3. 识别应该连接到根模型的顶级元素
4. 提供具体的修复建议

请返回JSON格式的修复建议。
"""

        return prompt

    def _execute_orphan_fixes(self, llm_response: str, orphaned_nodes: List[Dict]):
        """执行LLM提供的修复建议"""
        try:
            import json

            # 提取JSON部分
            start = llm_response.find('{')
            end = llm_response.rfind('}') + 1
            if start == -1 or end == 0:
                logger.warning("无法从LLM响应中提取JSON修复建议")
                return

            json_part = llm_response[start:end]
            fixes = json.loads(json_part)

            if 'fixes' not in fixes:
                logger.warning("LLM响应中没有找到fixes字段")
                return

            logger.info(f"LLM提供了 {len(fixes['fixes'])} 个修复建议")

            # 执行修复
            with self.storage_agent._driver.session() as session:
                for fix in fixes['fixes']:
                    self._apply_single_fix(session, fix)

            # 重新检查孤立节点
            remaining_orphans = self._check_orphaned_nodes()
            logger.info(f"修复后剩余孤立节点: {len(remaining_orphans)} 个")

        except Exception as e:
            logger.error(f"执行孤立节点修复失败: {e}")

    def _apply_single_fix(self, session, fix: Dict):
        """应用单个修复建议"""
        try:
            fix_type = fix.get('type')

            if fix_type == 'create_missing_parent':
                # 创建缺失的父节点
                parent_id = fix.get('missing_parent_id')
                parent_type = fix.get('suggested_parent_type', 'Package')
                parent_name = fix.get('suggested_parent_name', 'Generated Parent')
                orphan_id = fix.get('orphan_id')

                # 创建父节点
                create_query = f"""
                MERGE (p:`{parent_type}` {{elementId: $parentId}})
                ON CREATE SET p.type = $parentType, p.name = $parentName, p._auto_generated = true
                RETURN p
                """
                session.run(create_query, {
                    'parentId': parent_id,
                    'parentType': parent_type,
                    'parentName': parent_name
                })

                # 创建CONTAINS关系
                connect_query = """
                MATCH (p {elementId: $parentId})
                MATCH (c {elementId: $childId})
                MERGE (p)-[:CONTAINS]->(c)
                RETURN count(*)
                """
                session.run(connect_query, {
                    'parentId': parent_id,
                    'childId': orphan_id
                })

                logger.info(f"创建了缺失父节点 {parent_type}: {parent_name} 并连接到 {orphan_id}")

            elif fix_type == 'connect_to_root':
                # 连接到根模型
                orphan_id = fix.get('orphan_id')

                connect_query = """
                MATCH (root:Model {elementId: 'unified-sysml-model'})
                MATCH (orphan {elementId: $orphanId})
                MERGE (root)-[:CONTAINS]->(orphan)
                RETURN count(*)
                """
                session.run(connect_query, {'orphanId': orphan_id})

                logger.info(f"将孤立节点 {orphan_id} 连接到根模型")

        except Exception as e:
            logger.error(f"应用修复建议失败: {fix}, 错误: {e}")

    def _create_ingestion_task(self, state: WorkflowState, status: str, message: str, result: Dict[str, Any] = None):
        """
        创建数据摄取任务记录
        
        参数:
            state: 工作流状态
            status: 任务状态
            message: 状态消息
            result: 任务结果
        """
        task_status = ProcessStatus.COMPLETED if status == "COMPLETED" else ProcessStatus.FAILED
        
        ingestion_task = SysMLTask(
            id=f"INGESTION-TASK-{uuid.uuid4()}",
            type="Data Ingestion",
            content=message,
            status=task_status,
            result=result,
            error_message=message if status == "FAILED" else None
        )
        
        state.assigned_tasks.append(ingestion_task)

def sysml_ingestion_agent(state: WorkflowState) -> WorkflowState:
    """
    SysML数据摄取Agent工作流节点入口函数
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    logger.info("SysML数据摄取Agent节点开始处理")
    
    agent = IngestionAgent()
    return agent.process(state)
