"""
SysML智能对齐Agent - 负责将多个SysML图的JSON输出智能合并为一个统一的模型

该模块实现了一个基于大型语言模型的智能对齐Agent，用于解决SysML模型合并中的复杂问题。
与传统的算法合并方法不同，该Agent能够：

1. 识别语义上相同但名称和ID不同的元素
2. 智能合并来自不同任务的元素属性和关系
3. 修复跨模型的引用关系
4. 建立统一的包结构
5. 创建跨领域的新关系

使用方法：
    from src.agents.aligner_agent import sysml_aligner_agent
    
    # 作为工作流节点使用
    updated_state = sysml_aligner_agent(workflow_state)
"""
import logging
import json
import copy
import re
import uuid
from typing import Dict, List, Any, Set, Tuple, Optional, Union

from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain.schema import HumanMessage
from langchain_openai import ChatOpenAI
from json_repair import repair_json

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from config.settings import settings

# 配置日志记录器
logger = logging.getLogger(__name__)

# 系统提示模板 - SysML智能对齐与合并
ALIGNER_PROMPT = """
## 角色
你是一位顶级的系统建模专家、SysML 架构师和知识图谱融合工程师。你的任务是将多个独立的、描述同一系统不同侧面的 SysML JSON 片段，智能地融合成一个单一的、逻辑一致的、去重的、全局连通的最终系统模型。

## 背景
你将收到多个 JSON 输入，每个输入都来自一个专注于特定任务（如需求、结构、行为等）的 AI Agent。因此，这些输入之间可能存在以下问题：
- **实体重复**: 同一个概念（如 "Frame"）可能在多个输入中以不同的名称（如 "Frame", "结构框架"）和不同的 ID 出现。
- **信息分散**: 关于同一个实体的信息（如属性、端口）可能分散在多个输入中。
- **引用不一致**: 跨输入的引用是不存在的或不正确的。
- **包结构混乱**: 每个输入都有自己的包结构，需要统一。

## 核心任务
将所有输入片段合并成一个统一的、高质量的 SysML 模型。请严格遵循以下规则和步骤。

---

### 规则与指令

#### 1. **建立统一的包结构(最关键的规则)**
- 在最终输出的根模型下，创建以下标准的顶层包结构。这是所有元素的最终归宿。
  - `pkg-reqs-uuid`: "Requirements" (用于 Requirement, TestCase, Verify, Satisfy, DeriveReqt)
  - `pkg-usecases-uuid`: "UseCases" (用于 Actor, UseCase, 和它们之间的关系)
  - `pkg-structure-uuid`: "SystemStructure" (用于 Block, Property, Port, Connector, Association, InterfaceBlock)
  - `pkg-behavior-uuid`: "SystemBehavior" (用于 Activity, StateMachine, Interaction, 和它们的子元素)
  - `pkg-libraries-uuid`: "Libraries" (用于 ValueType, Enumeration, Signal, Unit, ConstraintBlock)


#### 2. **实体对齐与去重 (最关键的规则)**
- **识别等价实体**: 仔细阅读所有输入，识别指向现实世界中同一概念的元素。判断依据（优先级从高到低）：
    a. **名称和类型**: 如果两个元素的 `type` 相同，且 `name` 在语义上相同（例如，"Frame" 和 "结构框架"； "用户" 和 "用户（骑行者）"; "传感器" 和 "SensorModule"），则将它们视为**同一个实体**。
    b. **上下文和关系**: 通过元素的属性和连接关系来辅助判断。
- **合并实体**:
    a. **选择规范 ID**: 当你合并多个实体时，从它们的 ID 中选择一个作为最终的**规范 ID (Canonical ID)**。
    b. **聚合信息**: 将所有被合并实体的信息（如 `text`, `properties`, `ports`, `operations`, `entry`, `exit`, `effect` 等）全部聚合到最终的那个单一实体中。确保不丢失任何信息。如果是列表（如 `properties`），则合并列表内容。

#### 3. **ID 引用重写**
- 在整个合并过程中，你必须维护一个内部的"别名映射表"（旧 ID -> 规范 ID）。
- 在生成最终 JSON 时，**所有**的 ID 引用字段（如 `parentId`, `typeId`, `sourceId`, `targetId`, `associationId`, `partRefId`, `portRefId`, `requirementId`, `blockId`, `coveredId`, `representsId` 等）都**必须**被更新为它们对应的规范 ID。绝不允许出现指向旧的、已被合并的 ID 的悬空引用。

#### 4. **父子关系重构**
- **分类归包**: 将所有顶层元素（如 `Block`, `Requirement`, `Activity` 等）的 `parentId` 修改为步骤 1 中定义的相应标准包的 ID。
- **保持内部层级 (!!!非常重要!!!)**: **必须**保持元素内部原有的父子层级关系。例如：
    - 一个 `Property` 或 `Port` 的 `parentId` **必须**是它所属的那个（合并后的）`Block` 的规范 ID。
    - 一个 `State` 或 `Transition` 的 `parentId` **必须**是它所属的 `Region` 的规范 ID。
    - 一个 `Action` 或 `ControlNode` 的 `parentId` **必须**是它所属的 `Activity` 的规范 ID。
    - 一个 `ConstraintParameter` 的 `parentId` **必须**是它所属的 `ConstraintBlock` 的规范 ID。
    - **不要**将这些子元素的 `parentId` 错误地修改为顶层标准包的 ID。

#### 5. **创建和修复关系**
- **跨领域关联**: 在实体对齐后，主动寻找并创建新的关系来连接不同领域的模型。例如：
    - 如果一个 `Block`（来自结构模型）在语义上满足了一个 `Requirement`（来自需求模型），请创建一个 `Satisfy` 关系，并正确设置其 `blockId` 和 `requirementId`。
    - 如果一个 `Actor`（来自用例模型）实际上代表了一个 `Block`（来自结构模型），请思考它们之间的关系。
- **修复关联**: 确保所有 `Association` 的 `memberEndIds` 都指向真实存在的、合并后的 `Property` 元素。

#### 6. **输出格式**
- 最终输出必须是一个**单一的 JSON 对象**。
- JSON 对象结构应为 `{"model": {...}, "elements": [...]}`。
- `model` 对象应有一个合适的、统一的名称和 ID。
- `elements` 数组应包含所有合并和处理后的元素。

---

## 具体任务

以下是来自多个独立 Agent 的 SysML JSON 输出。请根据上述所有规则，将它们智能地融合成一个统一的、最终的系统模型。特别注意，对于不同的独立JSON，都有不同的package结构，你需要根据实际情况，将元素归类到正确的标准包中，忽略原始的package结构。

{json_strings}
"""

# 定义标准包类型和名称映射
STANDARD_PACKAGES = {
    "requirements": {"id": "pkg-reqs-uuid", "name": "Requirements"},
    "usecases": {"id": "pkg-usecases-uuid", "name": "UseCases"},
    "structure": {"id": "pkg-structure-uuid", "name": "SystemStructure"},
    "behavior": {"id": "pkg-behavior-uuid", "name": "SystemBehavior"},
    "libraries": {"id": "pkg-libraries-uuid", "name": "Libraries"}
}

# 定义元素类型到标准包的映射
ELEMENT_TYPE_TO_PACKAGE = {
    # 需求包
    "Requirement": "requirements",
    "TestCase": "requirements",
    "Verify": "requirements",
    "Satisfy": "requirements",
    "DeriveReqt": "requirements",
    
    # 用例包
    "Actor": "usecases",
    "UseCase": "usecases",
    "Include": "usecases",
    "Extend": "usecases",
    
    # 结构包
    "Block": "structure",
    "InterfaceBlock": "structure",
    "Association": "structure",
    "Connector": "structure",
    
    # 行为包
    "Activity": "behavior",
    "StateMachine": "behavior",
    "Interaction": "behavior",
    "Sequence": "behavior",
    
    # 库包
    "ValueType": "libraries",
    "Enumeration": "libraries",
    "Signal": "libraries",
    "Unit": "libraries",
    "ConstraintBlock": "libraries"
}


class SysMLAlignerAgent:
    """
    SysML智能对齐Agent - 负责将多个SysML图的JSON输出智能合并为一个统一的模型
    
    该代理利用大型语言模型的语义理解能力，能够更智能地识别和合并相同概念的元素，
    即使它们的ID和名称不完全相同。
    """
    
    def __init__(self, model_name=None, temperature=0.0):
        """
        初始化SysML智能对齐Agent
        
        参数:
            model_name: 使用的LLM模型名称，如果为None则使用settings中的配置
            temperature: LLM生成的随机性，0表示最确定性的输出
        """
        # 使用配置中的模型或提供的模型
        self.model_name = model_name or settings.llm_model
        self.temperature = temperature
        
        # 初始化LLM
        self.llm = ChatOpenAI(
            model=self.model_name,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            timeout=None,
            max_tokens=None,
            temperature=self.temperature
        )
        
        logger.info(f"初始化SysML智能对齐Agent，使用模型: {self.model_name}")
    
    def _format_inputs(self, tasks: List[SysMLTask]) -> str:
        """
        将多个任务的JSON输出格式化为提示词中的输入部分
        
        参数:
            tasks: SysML任务列表
            
        返回:
            格式化后的JSON字符串，用于插入到提示词中
        """
        logger.info(f"格式化 {len(tasks)} 个任务的JSON输出")
        
        formatted_string = ""
        task_count = 0
        
        for task in tasks:
            if task.status != ProcessStatus.COMPLETED or task.result is None:
                continue
                
            task_count += 1
            task_type = task.type
            task_result = task.result
            
            # 清理结果，只保留核心的model和elements
            cleaned_result = {}
            
            # 处理model字段
            if "model" in task_result:
                cleaned_result["model"] = task_result["model"]
            
            # 处理elements字段
            if "elements" in task_result:
                cleaned_result["elements"] = task_result["elements"]
            
            # 特殊处理：某些任务可能将关系存储在单独的字段中
            for special_field in ["relations", "signals", "events", "activities"]:
                if special_field in task_result and isinstance(task_result[special_field], list):
                    if "elements" not in cleaned_result:
                        cleaned_result["elements"] = []
                    cleaned_result["elements"].extend(task_result[special_field])
            
            # 如果没有elements，跳过此任务
            if "elements" not in cleaned_result or not cleaned_result["elements"]:
                logger.warning(f"任务 '{task_type}' 没有elements，跳过")
                continue
            
            # 转换为JSON字符串，使用缩进以提高可读性
            json_string = json.dumps(cleaned_result, ensure_ascii=False, indent=2)
            
            # 添加到格式化字符串中
            formatted_string += f"### 输入 {task_count}: {task_type} 模型\n"
            formatted_string += f"```json\n{json_string}\n```\n\n"
        
        logger.info(f"成功格式化 {task_count} 个任务的JSON输出")
        return formatted_string
        
    def execute_merge(self, tasks: List[SysMLTask], max_retries: int = 2) -> Dict[str, Any]:
        """
        执行SysML模型合并操作
        
        参数:
            tasks: 要合并的SysML任务列表
            max_retries: 最大重试次数（处理LLM可能返回的无效JSON）
            
        返回:
            合并后的SysML模型，格式为{"model": {...}, "elements": [...]}
        """
        logger.info(f"开始执行SysML模型合并，任务数量: {len(tasks)}")
        
        # 过滤已完成的任务
        completed_tasks = [task for task in tasks if task.status == ProcessStatus.COMPLETED and task.result is not None]
        
        if not completed_tasks:
            logger.warning("没有找到已完成的任务，无法执行合并")
            return {"model": {"id": "empty-model-uuid", "name": "Empty Model"}, "elements": []}
        
        # 记录任务类型，帮助调试
        task_types = [task.type for task in completed_tasks]
        logger.info(f"准备合并以下类型的任务: {', '.join(task_types)}")
        
        # 格式化输入
        json_strings = self._format_inputs(completed_tasks)
        
        if not json_strings.strip():
            logger.warning("没有有效的JSON输入，无法执行合并")
            return {"model": {"id": "empty-model-uuid", "name": "Empty Model"}, "elements": []}
        
        # 创建提示词
        prompt = ALIGNER_PROMPT.format(json_strings=json_strings)
        
        # 执行合并，允许重试
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"执行合并尝试 {attempt + 1}/{max_retries + 1}")
                
                # 调用LLM
                response = self.llm.invoke([HumanMessage(content=prompt)])
                
                # 处理响应
                merged_model = self._process_response(response.content)
                
                # 验证结果
                try:
                    self._validate_result(merged_model)
                except ValueError as validate_error:
                    logger.error(f"验证失败: {str(validate_error)}")
                    # 尝试修复常见的验证错误
                    if "model" in str(validate_error):
                        logger.info("尝试修复model字段问题")
                        if "model" not in merged_model or not isinstance(merged_model["model"], dict):
                            merged_model["model"] = {"id": "fixed-model-uuid", "name": "Fixed Model"}
                        # 重新验证
                        self._validate_result(merged_model)
                    else:
                        # 其他验证错误，重新抛出
                        raise
                
                logger.info("成功完成SysML模型合并")
                return merged_model
                
            except Exception as e:
                logger.error(f"合并尝试 {attempt + 1} 失败: {str(e)}")
                if attempt == max_retries:
                    logger.error("已达到最大重试次数，合并失败")
                    # 创建一个基本的合并模型而不是抛出异常
                    logger.info("返回基本合并模型")
                    return {"model": {"id": "error-model-uuid", "name": "Error Model"}, "elements": []}
        
        # 这里不应该到达，但为了代码完整性添加
        return {"model": {"id": "error-model-uuid", "name": "Error Model"}, "elements": []}
        
    def _process_response(self, response_content: str) -> Dict[str, Any]:
        """
        处理LLM的响应，提取并解析JSON
        
        参数:
            response_content: LLM的响应内容
            
        返回:
            解析后的合并模型JSON
            
        异常:
            ValueError: 如果无法从响应中提取有效的JSON
        """
        logger.info("处理LLM响应...")
        
        # 记录响应的前200个字符，帮助调试
        logger.debug(f"LLM响应前200个字符: {response_content[:200]}...")
        
        # 尝试从响应中提取JSON代码块
        json_matches = re.findall(r'```(?:json)?\s*([\s\S]*?)\s*```', response_content)
        
        if json_matches:
            # 使用第一个找到的JSON代码块
            json_str = json_matches[0].strip()
            logger.info("从响应中提取到JSON代码块")
        else:
            # 如果没有找到代码块，尝试直接解析整个响应
            json_str = response_content.strip()
            logger.warning("未在响应中找到JSON代码块，尝试直接解析整个响应")
        
        # 尝试解析JSON
        try:
            merged_model = json.loads(json_str)
            logger.info("成功解析JSON响应")
            
            # 特殊处理: 检查并修复model字段
            if "model" in merged_model:
                if not isinstance(merged_model["model"], dict):
                    logger.warning(f"'model'字段不是字典，而是{type(merged_model['model'])}，值为: {merged_model['model']}，尝试修复")
                    # 创建一个默认的model对象
                    merged_model["model"] = {"id": "fixed-model-uuid", "name": "Fixed Model"}
            else:
                logger.warning("响应中缺少'model'字段，添加默认值")
                merged_model["model"] = {"id": "default-model-uuid", "name": "Default Model"}
            
            # 确保elements字段存在
            if "elements" not in merged_model:
                logger.warning("响应中缺少'elements'字段，添加空列表")
                merged_model["elements"] = []
                
            return merged_model
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            
            # 尝试修复JSON
            try:
                logger.info("尝试修复JSON...")
                repaired_json = repair_json(json_str)
                merged_model = json.loads(repaired_json)
                
                # 特殊处理: 检查并修复修复后的model字段
                if "model" in merged_model:
                    if not isinstance(merged_model["model"], dict):
                        logger.warning(f"修复后的'model'字段仍不是字典，尝试进一步修复")
                        merged_model["model"] = {"id": "repaired-model-uuid", "name": "Repaired Model"}
                else:
                    logger.warning("修复后的响应中缺少'model'字段，添加默认值")
                    merged_model["model"] = {"id": "repaired-model-uuid", "name": "Repaired Model"}
                
                # 确保elements字段存在
                if "elements" not in merged_model:
                    merged_model["elements"] = []
                
                logger.info("成功修复并解析JSON")
                return merged_model
            except Exception as repair_error:
                logger.error(f"JSON修复失败: {str(repair_error)}")
                
                # 最后尝试使用正则表达式查找可能的JSON对象
                logger.info("尝试使用正则表达式查找JSON对象...")
                potential_json_matches = re.findall(r'({[\s\S]*})', json_str)
                
                for potential_json in potential_json_matches:
                    try:
                        merged_model = json.loads(potential_json)
                        
                        # 特殊处理: 检查并修复找到的JSON对象
                        if "model" in merged_model:
                            if not isinstance(merged_model["model"], dict):
                                merged_model["model"] = {"id": "regex-model-uuid", "name": "Regex Found Model"}
                        else:
                            merged_model["model"] = {"id": "regex-model-uuid", "name": "Regex Found Model"}
                        
                        # 确保elements字段存在
                        if "elements" not in merged_model:
                            merged_model["elements"] = []
                        
                        logger.info("使用正则表达式成功找到并解析JSON对象")
                        return merged_model
                    except:
                        continue
                
                # 如果所有尝试都失败，创建一个最小的有效模型
                logger.error("所有JSON解析尝试均失败，返回默认空模型")
                return {"model": {"id": "fallback-model-uuid", "name": "Fallback Model"}, "elements": []}
                
    def _validate_result(self, merged_model: Dict[str, Any]) -> None:
        """
        验证合并结果的有效性
        
        参数:
            merged_model: 合并后的SysML模型
            
        异常:
            ValueError: 如果合并结果无效
        """
        logger.info("验证合并结果...")
        
        # 检查基本结构
        if not isinstance(merged_model, dict):
            error_msg = f"合并结果必须是字典，当前类型: {type(merged_model)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 检查必要的顶层字段
        required_fields = ["model", "elements"]
        for field in required_fields:
            if field not in merged_model:
                error_msg = f"合并结果缺少必要字段: '{field}'"
                logger.error(error_msg)
                raise ValueError(error_msg)
        
        # 检查model字段
        model = merged_model["model"]
        if not isinstance(model, dict):
            error_msg = f"'model'字段必须是字典，当前类型: {type(model)}，值: {model}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 检查model必要字段
        model_required_fields = ["id", "name"]
        for field in model_required_fields:
            if field not in model:
                error_msg = f"'model'字段缺少必要的子字段: '{field}'，当前model: {model}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        
        # 检查elements字段
        elements = merged_model["elements"]
        if not isinstance(elements, list):
            error_msg = f"'elements'字段必须是列表，当前类型: {type(elements)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 检查elements中的每个元素
        element_ids = set()
        for i, element in enumerate(elements):
            if not isinstance(element, dict):
                error_msg = f"elements[{i}]必须是字典，当前类型: {type(element)}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 检查元素必要字段
            element_required_fields = ["id", "type"]
            for field in element_required_fields:
                if field not in element:
                    error_msg = f"elements[{i}]缺少必要字段: '{field}'"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            
            # 检查ID唯一性
            element_id = element["id"]
            if element_id in element_ids:
                error_msg = f"发现重复的元素ID: '{element_id}'"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            element_ids.add(element_id)
        
        # 检查引用完整性
        self._check_reference_integrity(merged_model, element_ids)
        
        logger.info(f"合并结果验证通过，包含 {len(elements)} 个元素")
    
    def _check_reference_integrity(self, merged_model: Dict[str, Any], element_ids: Set[str]) -> None:
        """
        检查合并模型中的引用完整性
        
        参数:
            merged_model: 合并后的SysML模型
            element_ids: 所有元素ID的集合
            
        异常:
            ValueError: 如果发现悬空引用
        """
        logger.info("检查引用完整性...")
        
        # 常见的引用字段
        reference_fields = [
            "parentId", "typeId", "sourceId", "targetId", "associationId", 
            "partRefId", "portRefId", "requirementId", "blockId", "coveredId", 
            "representsId", "memberEndIds"
        ]
        
        # 遍历所有元素
        for i, element in enumerate(merged_model["elements"]):
            # 检查常见引用字段
            for field in reference_fields:
                if field in element:
                    ref_value = element[field]
                    
                    # 处理单一引用
                    if isinstance(ref_value, str) and ref_value:
                        if ref_value not in element_ids:
                            # 记录警告但不抛出异常，因为某些引用可能是有意为空或指向外部
                            logger.warning(f"元素 {element['id']} 的 '{field}' 引用了不存在的ID: '{ref_value}'")
                    
                    # 处理列表引用（如memberEndIds）
                    elif isinstance(ref_value, list):
                        for j, ref_id in enumerate(ref_value):
                            if isinstance(ref_id, str) and ref_id and ref_id not in element_ids:
                                logger.warning(f"元素 {element['id']} 的 '{field}[{j}]' 引用了不存在的ID: '{ref_id}'")
        
        logger.info("引用完整性检查完成")


def is_valid_json(json_str: str) -> bool:
    """
    检查字符串是否为有效的JSON
    
    参数:
        json_str: 要检查的JSON字符串
        
    返回:
        布尔值，表示是否为有效的JSON
    """
    try:
        json.loads(json_str)
        return True
    except:
        return False


def create_standard_packages() -> List[Dict[str, Any]]:
    """
    创建标准包结构
    
    返回:
        包含标准包元素的列表
    """
    packages = []
    
    for pkg_type, pkg_info in STANDARD_PACKAGES.items():
        # 创建包元素
        package = {
            "id": pkg_info["id"],
            "type": "Package",
            "name": pkg_info["name"]
        }
        packages.append(package)
    
    return packages


def get_package_for_element_type(element_type: str) -> str:
    """
    根据元素类型获取应归属的标准包ID
    
    参数:
        element_type: 元素类型
        
    返回:
        标准包ID，如果没有匹配的包则返回结构包ID
    """
    package_type = ELEMENT_TYPE_TO_PACKAGE.get(element_type, "structure")
    return STANDARD_PACKAGES[package_type]["id"]


def generate_unique_id(prefix: str = "auto") -> str:
    """
    生成唯一ID
    
    参数:
        prefix: ID前缀
        
    返回:
        唯一ID字符串
    """
    return f"{prefix}-{str(uuid.uuid4())}"


def sysml_aligner_agent(workflow_state: WorkflowState, model_name: Optional[str] = None) -> Dict[str, Any]:
    """
    SysML智能对齐Agent的函数接口，用于合并多个SysML任务的输出
    
    参数:
        workflow_state: 工作流状态对象，包含所有任务
        model_name: 可选，使用的LLM模型名称
        
    返回:
        合并后的SysML模型，格式为{"model": {...}, "elements": [...]}
    """
    logger.info("启动SysML智能对齐Agent...")
    
    # 获取所有已完成的SysML任务
    completed_tasks = [task for task in workflow_state.assigned_tasks if task.status == ProcessStatus.COMPLETED]
    
    if not completed_tasks:
        logger.warning("没有找到已完成的SysML任务，无法执行合并")
        return {"model": {"id": "empty-model-uuid", "name": "Empty Model"}, "elements": []}
    
    logger.info(f"找到 {len(completed_tasks)} 个已完成的SysML任务")
    
    # 记录任务类型，帮助调试
    task_types = [task.type for task in completed_tasks]
    logger.info(f"准备合并以下类型的任务: {', '.join(task_types)}")
    
    # 创建并执行对齐Agent
    aligner = SysMLAlignerAgent(model_name=model_name)
    
    try:
        # 执行合并
        merged_model = aligner.execute_merge(completed_tasks)
        logger.info("SysML智能对齐Agent成功完成合并")
        return merged_model
        
    except Exception as e:
        logger.error(f"SysML智能对齐Agent执行失败: {str(e)}")
        # 尝试从已完成的任务中提取模型和元素
        try:
            logger.info("尝试从已完成任务中创建基本模型...")
            model = {"id": "fallback-model-uuid", "name": "Fallback Model"}
            elements = []
            
            # 收集所有元素
            for task in completed_tasks:
                if task.result and isinstance(task.result, dict) and "elements" in task.result:
                    task_elements = task.result.get("elements", [])
                    if isinstance(task_elements, list):
                        logger.info(f"从任务 '{task.type}' 中收集 {len(task_elements)} 个元素")
                        elements.extend(task_elements)
            
            # 去重元素（基于ID）
            unique_elements = []
            seen_ids = set()
            for element in elements:
                if isinstance(element, dict) and "id" in element:
                    element_id = element["id"]
                    if element_id not in seen_ids:
                        seen_ids.add(element_id)
                        unique_elements.append(element)
            
            logger.info(f"从任务中收集到 {len(unique_elements)} 个唯一元素")
            return {"model": model, "elements": unique_elements}
        except Exception as fallback_error:
            logger.error(f"创建基本模型失败: {str(fallback_error)}")
            # 返回一个最小的有效模型
            return {"model": {"id": "error-model-uuid", "name": "Error Model"}, "elements": []}


def sysml_align_agent(state: WorkflowState) -> WorkflowState:
    """
    SysML对齐Agent工作流节点入口函数
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    logger.info("SysML对齐Agent节点开始处理")
    
    # 检查是否已经执行过对齐
    if any(task.type == "Aligned SysML Model" for task in state.assigned_tasks):
        logger.info("已经执行过对齐操作，跳过")
        return state
    
    # 检查是否有已完成的任务
    completed_tasks = [task for task in state.assigned_tasks if task.status == ProcessStatus.COMPLETED]
    if not completed_tasks:
        logger.warning("没有成功完成的任务，无法进行对齐")
        # 创建一个空的对齐结果，避免再次进入对齐步骤
        align_task = SysMLTask(
            id=f"ALIGN-TASK-EMPTY",
            type="Aligned SysML Model",
            content="没有可对齐的内容",
            status=ProcessStatus.FAILED,
            error_message="没有成功完成的任务，无法进行对齐"
        )
        state.assigned_tasks.append(align_task)
        return state
    
    # 记录待对齐任务的信息
    task_types = [task.type for task in completed_tasks]
    logger.info(f"准备对齐以下类型的任务: {', '.join(task_types)}")

    try:
        # 使用智能对齐Agent进行对齐
        logger.info("启动智能对齐过程...")
        
        # 调用对齐函数
        result_data = sysml_aligner_agent(state)
        
        # 检查结果
        if result_data and "elements" in result_data:
            # 将对齐结果存储在状态中
            elements_count = len(result_data.get("elements", []))
            
            # 这里我们创建一个新的"对齐"任务来存储结果
            align_task = SysMLTask(
                id=f"ALIGN-TASK",
                type="Aligned SysML Model",
                content=f"使用智能对齐Agent对齐所有SysML图表 (包含 {elements_count} 个元素)",
                status=ProcessStatus.COMPLETED,
                result=result_data
            )
            
            # 将对齐任务添加到状态中
            state.assigned_tasks.append(align_task)
            logger.info(f"SysML模型对齐成功，最终模型包含 {elements_count} 个元素")
            
            # 记录元素类型分布，帮助验证对齐是否成功
            element_types = {}
            for element in result_data.get("elements", []):
                element_type = element.get("type", "未知")
                element_types[element_type] = element_types.get(element_type, 0) + 1
                
            logger.info(f"对齐后模型的元素类型分布: {element_types}")
        else:
            # 记录对齐错误，但仍创建一个对齐任务以避免再次进入对齐步骤
            error_msg = "对齐结果无效或为空"
            align_task = SysMLTask(
                id=f"ALIGN-TASK-ERROR",
                type="Aligned SysML Model",
                content="对齐失败",
                status=ProcessStatus.FAILED,
                error_message=f"对齐失败原因: {error_msg}"
            )
            state.assigned_tasks.append(align_task)
            logger.error(f"SysML模型对齐失败: {error_msg}")
    
    except Exception as e:
        # 处理异常，但仍创建一个对齐任务以避免再次进入对齐步骤
        error_message = f"SysML对齐Agent处理异常: {str(e)}"
        align_task = SysMLTask(
            id=f"ALIGN-TASK-EXCEPTION",
            type="Aligned SysML Model",
            content="对齐异常",
            status=ProcessStatus.FAILED,
            error_message=error_message
        )
        state.assigned_tasks.append(align_task)
        logger.error(error_message, exc_info=True)
    
    return state 