#!/usr/bin/env python3
"""
数据验证和修复Agent - 在数据摄取完成后进行全面的数据验证和智能修复
"""

import logging
from typing import Dict, List, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from config.settings import settings
from src.agents.neo4j_storage_agent import Neo4jStorageAgent
from src.graph.workflow_state import WorkflowState

logger = logging.getLogger(__name__)

class DataValidationAndRepairAgent:
    """数据验证和修复Agent"""
    
    def __init__(self):
        """初始化数据验证和修复Agent"""
        self.storage_agent = Neo4jStorageAgent()
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.1
        )
    
    def validate_and_repair(self, state: WorkflowState) -> Dict[str, Any]:
        """
        验证数据完整性并进行智能修复
        
        参数:
            state: 工作流状态（包含完整的JSON数据）
            
        返回:
            验证和修复结果
        """
        logger.info("🔍 开始数据验证和修复")
        
        try:
            # 1. 数据验证
            validation_results = self._validate_data_integrity()
            
            # 2. 如果有问题，进行智能修复
            if validation_results['orphaned_nodes'] > 0:
                logger.info(f"发现 {validation_results['orphaned_nodes']} 个孤立节点，开始智能修复")
                repair_results = self._intelligent_repair(state, validation_results)
                
                # 3. 重新验证
                final_validation = self._validate_data_integrity()
                
                return {
                    'initial_validation': validation_results,
                    'repair_results': repair_results,
                    'final_validation': final_validation,
                    'improvement': {
                        'orphaned_nodes_fixed': validation_results['orphaned_nodes'] - final_validation['orphaned_nodes'],
                        'relationships_added': final_validation['total_relationships'] - validation_results['total_relationships']
                    }
                }
            else:
                logger.info("✅ 数据完整性良好，无需修复")
                return {
                    'initial_validation': validation_results,
                    'repair_needed': False
                }
                
        except Exception as e:
            logger.error(f"数据验证和修复失败: {e}")
            return {'error': str(e)}
        finally:
            self.storage_agent.close()
    
    def _validate_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        logger.info("📊 执行数据完整性验证")
        
        validation_results = {}
        
        try:
            with self.storage_agent._driver.session() as session:
                # 1. 统计节点数量
                node_count_query = "MATCH (n) RETURN count(n) as total"
                result = session.run(node_count_query)
                validation_results['total_nodes'] = result.single()['total']
                
                # 2. 统计关系数量
                rel_count_query = "MATCH ()-[r]->() RETURN count(r) as total"
                result = session.run(rel_count_query)
                validation_results['total_relationships'] = result.single()['total']
                
                # 3. 检查孤立节点
                orphan_query = """
                MATCH (n)
                WHERE n.elementId IS NOT NULL 
                AND n.type IS NOT NULL 
                AND n.type <> 'Model'
                AND NOT EXISTS {
                    MATCH (p)-[:CONTAINS]->(n)
                }
                RETURN n.elementId as id, n.type as type, n.name as name, n.parentId as parentId
                ORDER BY n.type, n.name
                """
                result = session.run(orphan_query)
                orphaned_nodes = [
                    {
                        'id': record['id'],
                        'type': record['type'],
                        'name': record['name'],
                        'parentId': record['parentId']
                    }
                    for record in result
                ]
                validation_results['orphaned_nodes'] = len(orphaned_nodes)
                validation_results['orphaned_nodes_list'] = orphaned_nodes
                
                # 4. 统计各类型节点
                type_stats_query = """
                MATCH (n)
                WHERE n.type IS NOT NULL
                RETURN n.type as type, count(n) as count
                ORDER BY count DESC
                LIMIT 10
                """
                result = session.run(type_stats_query)
                validation_results['top_node_types'] = [
                    {'type': record['type'], 'count': record['count']} 
                    for record in result
                ]
                
                # 5. 统计各类型关系
                rel_stats_query = """
                MATCH ()-[r]->()
                RETURN type(r) as type, count(r) as count
                ORDER BY count DESC
                LIMIT 10
                """
                result = session.run(rel_stats_query)
                validation_results['top_relationship_types'] = [
                    {'type': record['type'], 'count': record['count']} 
                    for record in result
                ]
                
                # 6. 检查缺失的关键关系
                missing_relations = self._check_missing_relationships(session)
                validation_results['missing_relationships'] = missing_relations
                
            logger.info(f"验证完成: {validation_results['total_nodes']} 节点, {validation_results['total_relationships']} 关系, {validation_results['orphaned_nodes']} 孤立节点")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return {'error': str(e)}
    
    def _check_missing_relationships(self, session) -> List[Dict]:
        """检查缺失的关键关系"""
        missing_relations = []
        
        try:
            # 检查Association是否缺少memberEnd关系
            assoc_query = """
            MATCH (a:Association)
            WHERE NOT EXISTS {
                MATCH (a)-[:ASSOC_MEMBER]->()
            }
            RETURN a.elementId as id, a.name as name
            LIMIT 10
            """
            result = session.run(assoc_query)
            for record in result:
                missing_relations.append({
                    'type': 'missing_association_members',
                    'element_id': record['id'],
                    'element_name': record['name']
                })
            
            # 检查Connector是否缺少端点关系
            connector_query = """
            MATCH (c)
            WHERE c.type ENDS WITH 'Connector'
            AND NOT EXISTS {
                MATCH (c)-[:CONNECT_END]->()
            }
            RETURN c.elementId as id, c.name as name, c.type as type
            LIMIT 10
            """
            result = session.run(connector_query)
            for record in result:
                missing_relations.append({
                    'type': 'missing_connector_ends',
                    'element_id': record['id'],
                    'element_name': record['name'],
                    'element_type': record['type']
                })
            
        except Exception as e:
            logger.error(f"检查缺失关系失败: {e}")
        
        return missing_relations
    
    def _intelligent_repair(self, state: WorkflowState, validation_results: Dict) -> Dict[str, Any]:
        """
        使用LLM进行智能修复
        
        参数:
            state: 工作流状态（包含完整JSON数据）
            validation_results: 验证结果
            
        返回:
            修复结果
        """
        logger.info("🤖 开始LLM智能修复")
        
        try:
            # 构建修复提示
            prompt = self._build_comprehensive_repair_prompt(state, validation_results)
            
            # 调用LLM
            response = self.llm.invoke([
                SystemMessage(content=self._get_comprehensive_repair_system_prompt()),
                HumanMessage(content=prompt)
            ])
            
            # 解析并执行修复建议
            repair_results = self._execute_comprehensive_repairs(response.content, state)
            
            return repair_results
            
        except Exception as e:
            logger.error(f"智能修复失败: {e}")
            return {'error': str(e)}
    
    def _get_comprehensive_repair_system_prompt(self) -> str:
        """获取综合修复的系统提示"""
        return """你是一个专业的SysML数据完整性修复专家。你现在有完整的上下文信息：
1. 完整的JSON数据（所有assigned_tasks的结果）
2. Neo4j图数据库的当前状态
3. 孤立节点和缺失关系的详细信息

你的任务是分析这些信息，提供全面的修复建议。

修复策略：
1. **包结构修复**: 创建缺失的包节点，建立正确的包层次结构
2. **ID映射修复**: 修正错误的parentId引用
3. **关系补全**: 创建缺失的关键关系（如Association的memberEnd，Connector的端点等）
4. **数据一致性**: 确保JSON数据和Neo4j数据的一致性

请以JSON格式返回修复建议：
{
  "package_fixes": [
    {
      "action": "create_package",
      "package_id": "缺失的包ID",
      "package_name": "包名称",
      "parent_id": "父包ID",
      "description": "包描述"
    }
  ],
  "id_mapping_fixes": [
    {
      "action": "fix_parent_reference",
      "element_id": "元素ID",
      "current_parent_id": "当前错误的父ID",
      "correct_parent_id": "正确的父ID",
      "reason": "修复原因"
    }
  ],
  "relationship_fixes": [
    {
      "action": "create_relationship",
      "relationship_type": "关系类型",
      "from_id": "源节点ID",
      "to_id": "目标节点ID",
      "properties": {"key": "value"},
      "reason": "创建原因"
    }
  ],
  "connect_to_root": [
    {
      "element_id": "元素ID",
      "reason": "连接到根的原因"
    }
  ]
}"""
    
    def _build_comprehensive_repair_prompt(self, state: WorkflowState, validation_results: Dict) -> str:
        """构建综合修复提示"""
        
        # 获取Neo4j中的现有节点信息
        existing_nodes = []
        try:
            with self.storage_agent._driver.session() as session:
                query = """
                MATCH (n)
                WHERE n.elementId IS NOT NULL
                RETURN n.elementId as id, n.type as type, n.name as name, n.parentId as parentId
                ORDER BY n.type, n.name
                LIMIT 100
                """
                result = session.run(query)
                existing_nodes = [
                    {
                        'id': record['id'],
                        'type': record['type'],
                        'name': record['name'],
                        'parentId': record['parentId']
                    }
                    for record in result
                ]
        except:
            pass
        
        # 收集所有JSON数据中的元素
        all_json_elements = []
        for task in state.assigned_tasks:
            if task.result and 'elements' in task.result:
                for element in task.result['elements']:
                    all_json_elements.append({
                        'id': element.get('id'),
                        'type': element.get('type'),
                        'name': element.get('name'),
                        'parentId': element.get('parentId'),
                        'task_type': task.type
                    })
        
        prompt = f"""
请分析以下SysML数据并提供全面的修复建议：

## 数据验证结果
- 总节点数: {validation_results.get('total_nodes', 0)}
- 总关系数: {validation_results.get('total_relationships', 0)}
- 孤立节点数: {validation_results.get('orphaned_nodes', 0)}
- 缺失关系数: {len(validation_results.get('missing_relationships', []))}

## 孤立节点列表 (前20个)
"""
        
        orphaned_nodes = validation_results.get('orphaned_nodes_list', [])
        for i, node in enumerate(orphaned_nodes[:20], 1):
            prompt += f"{i}. {node['type']}: {node['name']} (ID: {node['id']}, parentId: {node['parentId']})\n"
        
        if len(orphaned_nodes) > 20:
            prompt += f"... 还有 {len(orphaned_nodes) - 20} 个孤立节点\n"
        
        prompt += f"""

## Neo4j中的现有节点 (前50个)
"""
        for node in existing_nodes[:50]:
            prompt += f"- {node['type']}: {node['name']} (ID: {node['id']}, parentId: {node['parentId']})\n"
        
        prompt += f"""

## JSON数据中的所有元素 (前50个)
"""
        for element in all_json_elements[:50]:
            prompt += f"- {element['type']}: {element['name']} (ID: {element['id']}, parentId: {element['parentId']}, 来源: {element['task_type']})\n"
        
        prompt += f"""

## 缺失的关系
"""
        for missing in validation_results.get('missing_relationships', []):
            prompt += f"- {missing['type']}: {missing['element_id']} ({missing.get('element_name', 'N/A')})\n"
        
        prompt += """

## 分析要求
1. 对比JSON数据和Neo4j数据，找出不一致之处
2. 分析孤立节点的parentId，判断是否需要创建缺失的包或父节点
3. 识别ID映射错误，提供正确的映射关系
4. 补全缺失的关键关系
5. 建立正确的包层次结构

请提供详细的修复建议。
"""
        
        return prompt

    def _execute_comprehensive_repairs(self, llm_response: str, state: WorkflowState) -> Dict[str, Any]:
        """执行LLM提供的综合修复建议"""
        try:
            import json

            # 提取JSON部分
            start = llm_response.find('{')
            end = llm_response.rfind('}') + 1
            if start == -1 or end == 0:
                logger.warning("无法从LLM响应中提取JSON修复建议")
                return {'error': 'Invalid JSON response'}

            json_part = llm_response[start:end]
            fixes = json.loads(json_part)

            repair_results = {
                'packages_created': 0,
                'id_mappings_fixed': 0,
                'relationships_created': 0,
                'elements_connected_to_root': 0,
                'errors': []
            }

            logger.info(f"LLM提供了综合修复建议:")
            logger.info(f"  包修复: {len(fixes.get('package_fixes', []))}")
            logger.info(f"  ID映射修复: {len(fixes.get('id_mapping_fixes', []))}")
            logger.info(f"  关系修复: {len(fixes.get('relationship_fixes', []))}")
            logger.info(f"  连接到根: {len(fixes.get('connect_to_root', []))}")

            # 执行修复
            with self.storage_agent._driver.session() as session:
                # 1. 创建缺失的包
                for fix in fixes.get('package_fixes', []):
                    try:
                        self._create_missing_package(session, fix)
                        repair_results['packages_created'] += 1
                    except Exception as e:
                        repair_results['errors'].append(f"创建包失败: {fix.get('package_id')} - {e}")

                # 2. 修复ID映射
                for fix in fixes.get('id_mapping_fixes', []):
                    try:
                        self._fix_id_mapping(session, fix)
                        repair_results['id_mappings_fixed'] += 1
                    except Exception as e:
                        repair_results['errors'].append(f"修复ID映射失败: {fix.get('element_id')} - {e}")

                # 3. 创建缺失的关系
                for fix in fixes.get('relationship_fixes', []):
                    try:
                        self._create_missing_relationship(session, fix)
                        repair_results['relationships_created'] += 1
                    except Exception as e:
                        repair_results['errors'].append(f"创建关系失败: {fix.get('relationship_type')} - {e}")

                # 4. 连接到根模型
                for fix in fixes.get('connect_to_root', []):
                    try:
                        self._connect_to_root(session, fix)
                        repair_results['elements_connected_to_root'] += 1
                    except Exception as e:
                        repair_results['errors'].append(f"连接到根失败: {fix.get('element_id')} - {e}")

            logger.info(f"修复完成: 创建{repair_results['packages_created']}个包, "
                       f"修复{repair_results['id_mappings_fixed']}个ID映射, "
                       f"创建{repair_results['relationships_created']}个关系, "
                       f"连接{repair_results['elements_connected_to_root']}个元素到根")

            if repair_results['errors']:
                logger.warning(f"修复过程中出现 {len(repair_results['errors'])} 个错误")
                for error in repair_results['errors']:
                    logger.warning(f"  - {error}")

            return repair_results

        except Exception as e:
            logger.error(f"执行综合修复失败: {e}")
            return {'error': str(e)}

    def _create_missing_package(self, session, fix: Dict):
        """创建缺失的包"""
        package_id = fix.get('package_id')
        package_name = fix.get('package_name', 'Generated Package')
        parent_id = fix.get('parent_id', 'unified-sysml-model')

        # 创建包节点
        create_query = """
        MERGE (p:Package {elementId: $packageId})
        ON CREATE SET p.type = 'Package', p.name = $packageName, p._auto_generated = true
        RETURN p
        """
        session.run(create_query, {
            'packageId': package_id,
            'packageName': package_name
        })

        # 创建CONTAINS关系
        connect_query = """
        MATCH (parent {elementId: $parentId})
        MATCH (pkg {elementId: $packageId})
        MERGE (parent)-[:CONTAINS]->(pkg)
        RETURN count(*)
        """
        session.run(connect_query, {
            'parentId': parent_id,
            'packageId': package_id
        })

        logger.info(f"创建了缺失包: {package_name} (ID: {package_id})")

    def _fix_id_mapping(self, session, fix: Dict):
        """修复ID映射"""
        element_id = fix.get('element_id')
        current_parent_id = fix.get('current_parent_id')
        correct_parent_id = fix.get('correct_parent_id')

        # 删除错误的关系
        if current_parent_id:
            delete_query = """
            MATCH (wrong_parent {elementId: $currentParentId})-[r:CONTAINS]->(element {elementId: $elementId})
            DELETE r
            RETURN count(r)
            """
            session.run(delete_query, {
                'currentParentId': current_parent_id,
                'elementId': element_id
            })

        # 创建正确的关系
        create_query = """
        MATCH (correct_parent {elementId: $correctParentId})
        MATCH (element {elementId: $elementId})
        MERGE (correct_parent)-[:CONTAINS]->(element)
        SET element.parentId = $correctParentId
        RETURN count(*)
        """
        session.run(create_query, {
            'correctParentId': correct_parent_id,
            'elementId': element_id
        })

        logger.info(f"修复了ID映射: {element_id} -> {correct_parent_id}")

    def _create_missing_relationship(self, session, fix: Dict):
        """创建缺失的关系"""
        rel_type = fix.get('relationship_type')
        from_id = fix.get('from_id')
        to_id = fix.get('to_id')
        properties = fix.get('properties', {})

        # 动态构建查询
        props_str = ""
        if properties:
            props_list = [f"r.{key} = ${key}" for key in properties.keys()]
            props_str = "SET " + ", ".join(props_list)

        query = f"""
        MATCH (from_node {{elementId: $fromId}})
        MATCH (to_node {{elementId: $toId}})
        MERGE (from_node)-[r:`{rel_type}`]->(to_node)
        {props_str}
        RETURN count(r)
        """

        params = {
            'fromId': from_id,
            'toId': to_id,
            **properties
        }

        session.run(query, params)
        logger.info(f"创建了关系: {from_id} -[{rel_type}]-> {to_id}")

    def _connect_to_root(self, session, fix: Dict):
        """连接元素到根模型"""
        element_id = fix.get('element_id')

        query = """
        MATCH (root:Model {elementId: 'unified-sysml-model'})
        MATCH (element {elementId: $elementId})
        MERGE (root)-[:CONTAINS]->(element)
        SET element.parentId = 'unified-sysml-model'
        RETURN count(*)
        """
        session.run(query, {'elementId': element_id})
        logger.info(f"将元素 {element_id} 连接到根模型")
