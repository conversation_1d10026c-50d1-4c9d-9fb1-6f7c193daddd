"""
Neo4j存储Agent - 负责管理SysML模型在Neo4j中的存储和查询
基于Gemini方案一的图数据库驱动架构
"""
import json
import logging
from typing import Dict, List, Any,  Optional, Tuple
from neo4j import GraphDatabase
import traceback

from config.settings import settings

logger = logging.getLogger(__name__)

class Neo4jStorageAgent:
    """
    Neo4j存储代理，负责SysML模型的图数据库操作
    """
    
    def __init__(self, uri: str = None, user: str = None, password: str = None):
        """
        初始化Neo4j连接
        
        参数:
            uri: Neo4j连接URI，默认从配置读取
            user: 用户名，默认从配置读取  
            password: 密码，默认从配置读取
        """
        self.uri = uri or getattr(settings, 'neo4j_uri', 'bolt:/localhost/:7687')
        self.user = user or getattr(settings, 'neo4j_user', 'neo4j')
        self.password = password or getattr(settings, 'neo4j_password', '123456789')
        
        self._driver = None
        self._connect()
        
    def _connect(self):
        """建立Neo4j连接"""
        try:
            self._driver = GraphDatabase.driver(
                self.uri, 
                auth=(self.user, self.password),
                connection_timeout=30, 
                max_connection_lifetime=3600
            )
            self._driver.verify_connectivity()
            logger.info(f"成功连接到Neo4j: {self.uri}")
        except Exception as e:
            logger.error(f"无法连接到Neo4j: {e}")
            self._driver = None
            raise
    
    def close(self):
        """关闭Neo4j连接"""
        if self._driver:
            self._driver.close()
            logger.info("Neo4j连接已关闭")

    def clear_database(self):
        """清空Neo4j数据库"""
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return False

        try:
            with self._driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            logger.info("Neo4j数据库已清空")
            return True
        except Exception as e:
            logger.error(f"清空Neo4j数据库失败: {e}")
            return False

    def find_duplicate_candidates(self) -> List[Dict[str, Any]]:
        """查找潜在的重复实体候选"""
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return []

        try:
            with self._driver.session() as session:
                # 查找相同类型和名称的元素
                query = """
                MATCH (n1), (n2)
                WHERE n1.elementId <> n2.elementId
                AND n1.type = n2.type
                AND n1.name = n2.name
                AND n1.elementId < n2.elementId  // 避免重复
                RETURN n1.elementId as id1, n2.elementId as id2,
                       n1.type as type, n1.name as name,
                       1.0 as similarity
                UNION
                MATCH (n1), (n2)
                WHERE n1.elementId <> n2.elementId
                AND n1.type = n2.type
                AND n1.name <> n2.name
                AND n1.elementId < n2.elementId
                AND (
                    toLower(n1.name) = toLower(n2.name) OR
                    toLower(replace(n1.name, ' ', '')) = toLower(replace(n2.name, ' ', '')) OR
                    toLower(replace(n1.name, '_', '')) = toLower(replace(n2.name, '_', ''))
                )
                RETURN n1.elementId as id1, n2.elementId as id2,
                       n1.type as type, n1.name + ' / ' + n2.name as name,
                       0.8 as similarity
                """

                result = session.run(query)
                candidates = []

                for record in result:
                    candidates.append({
                        'id1': record['id1'],
                        'id2': record['id2'],
                        'type': record['type'],
                        'name': record['name'],
                        'similarity': record['similarity']
                    })

                logger.info(f"找到{len(candidates)}对潜在重复实体")
                return candidates

        except Exception as e:
            logger.error(f"查找重复实体候选失败: {e}")
            return []

    def get_element_details(self, element_id: str) -> Optional[Dict[str, Any]]:
        """获取元素详细信息"""
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return None

        try:
            with self._driver.session() as session:
                query = """
                MATCH (n {elementId: $elementId})
                RETURN properties(n) as props
                """

                result = session.run(query, {'elementId': element_id})
                record = result.single()

                if record:
                    return record['props']
                else:
                    logger.warning(f"未找到元素: {element_id}")
                    return None

        except Exception as e:
            logger.error(f"获取元素详情失败: {e}")
            return None

    def merge_entities(self, canonical_id: str, discarded_ids: List[str]) -> bool:
        """合并实体"""
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return False

        try:
            with self._driver.session() as session:
                for discarded_id in discarded_ids:
                    # 合并属性
                    merge_props_query = """
                    MATCH (canonical {elementId: $canonicalId})
                    MATCH (discarded {elementId: $discardedId})
                    SET canonical += discarded
                    """
                    session.run(merge_props_query, {
                        'canonicalId': canonical_id,
                        'discardedId': discarded_id
                    })

                    # 重定向所有指向discarded的关系到canonical
                    redirect_incoming_query = """
                    MATCH (other)-[r]->(discarded {elementId: $discardedId})
                    MATCH (canonical {elementId: $canonicalId})
                    WHERE other.elementId <> $canonicalId
                    CREATE (other)-[r2:REDIRECTED]->(canonical)
                    SET r2 = properties(r)
                    DELETE r
                    """
                    session.run(redirect_incoming_query, {
                        'canonicalId': canonical_id,
                        'discardedId': discarded_id
                    })

                    # 重定向所有从discarded出发的关系到canonical
                    redirect_outgoing_query = """
                    MATCH (discarded {elementId: $discardedId})-[r]->(other)
                    MATCH (canonical {elementId: $canonicalId})
                    WHERE other.elementId <> $canonicalId
                    CREATE (canonical)-[r2:REDIRECTED]->(other)
                    SET r2 = properties(r)
                    DELETE r
                    """
                    session.run(redirect_outgoing_query, {
                        'canonicalId': canonical_id,
                        'discardedId': discarded_id
                    })

                    # 删除discarded节点
                    delete_query = """
                    MATCH (discarded {elementId: $discardedId})
                    DETACH DELETE discarded
                    """
                    session.run(delete_query, {'discardedId': discarded_id})

                    logger.info(f"成功合并实体: {discarded_id} -> {canonical_id}")

                # 清理REDIRECTED关系，转换为正确的关系类型
                self._cleanup_redirected_relationships(session)

                return True

        except Exception as e:
            logger.error(f"合并实体失败: {e}")
            return False

    def _cleanup_redirected_relationships(self, session):
        """清理重定向关系，转换为正确的关系类型"""
        try:
            # 将REDIRECTED关系转换回原始关系类型
            cleanup_query = """
            MATCH (a)-[r:REDIRECTED]->(b)
            WITH a, b, r,
                 CASE
                   WHEN r.originalField = 'parentId' THEN 'CONTAINS'
                   WHEN r.originalField = 'sourceRequirementId' THEN 'DERIVES_FROM'
                   WHEN r.originalField = 'blockId' THEN 'SATISFIES'
                   ELSE 'REFERENCES'
                 END as newType
            CALL apoc.create.relationship(a, newType, properties(r), b) YIELD rel
            DELETE r
            """
            session.run(cleanup_query)

        except Exception as e:
            logger.warning(f"清理重定向关系时出现警告: {e}")
            # 如果没有APOC插件，使用简单的删除
            session.run("MATCH ()-[r:REDIRECTED]-() DELETE r")
    
    def clear_database(self):
        """清空数据库（谨慎使用）"""
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return False
            
        try:
            with self._driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
            return True
        except Exception as e:
            logger.error(f"清空数据库失败: {e}")
            return False
    
    def load_sysml_json(self, json_data: Dict[str, Any], source_name: str) -> bool:
        """
        将SysML JSON数据加载到Neo4j
        
        参数:
            json_data: SysML JSON数据
            source_name: 数据源名称（如"需求图"、"活动图"等）
            
        返回:
            是否成功加载
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return False
            
        try:
            with self._driver.session() as session:
                # 创建节点
                self._create_nodes(session, json_data, source_name)
                # 创建关系
                self._create_relationships(session, json_data, source_name)
            
            logger.info(f"成功加载{source_name}数据到Neo4j")
            return True
            
        except Exception as e:
            logger.error(f"加载{source_name}数据到Neo4j失败: {e}")
            traceback.print_exc()
            return False
    
    def _create_nodes(self, session, json_data: Dict[str, Any], source_name: str):
        """创建节点"""
        elements = json_data.get("elements", [])
        model_info = json_data.get("model", {})
        
        # 处理model节点
        if model_info:
            if isinstance(model_info, list) and model_info:
                model_info = model_info[0]
            
            model_query = """
            MERGE (m:Model:Element {elementId: $elementId})
            ON CREATE SET m.name = $name, m.type = 'Model', m.source = $source
            ON MATCH SET m.name = $name, m.sources = CASE
                WHEN m.sources IS NULL THEN [$source]
                WHEN NOT $source IN m.sources THEN m.sources + $source
                ELSE m.sources
            END
            """
            session.run(model_query, {
                'elementId': model_info.get('id', 'default-model'),
                'name': model_info.get('name', 'Default Model'),
                'source': source_name
            })
        
        # 按类型分组处理元素
        elements_by_type = {}
        for element in elements:
            element_type = element.get('type', 'Unknown')
            if element_type not in elements_by_type:
                elements_by_type[element_type] = []
            elements_by_type[element_type].append(element)
        
        # 批量创建各类型节点
        for element_type, type_elements in elements_by_type.items():
            self._create_nodes_by_type(session, element_type, type_elements, source_name)
    
    def _create_nodes_by_type(self, session, element_type: str, elements: List[Dict], source_name: str):
        """按类型批量创建节点"""
        if not elements:
            return

        # 构建节点属性
        node_data = []
        for element in elements:
            node_props = {
                'elementId': element.get('id'),
                'type': element_type,
                'source': source_name
            }

            # 添加通用属性
            if 'name' in element:
                node_props['name'] = element['name']
            if 'parentId' in element:
                node_props['parentId'] = element['parentId']

            # 添加特定类型属性 - 增强版：支持复杂对象属性的字符串化存储
            type_specific_attributes = {
                'Requirement': ['reqId', 'text', 'priority', 'status'],
                'Property': ['visibility', 'propertyKind', 'aggregation', 'typeId', 'multiplicity', 'associationId'],
                'Association': ['memberEndIds', 'sourceId', 'targetId'],
                'Block': ['isAbstract', 'isActive'],
                'Transition': ['sourceId', 'targetId', 'trigger', 'guard', 'effect'],
                'ControlFlow': ['sourceId', 'targetId', 'guard'],
                'ObjectFlow': ['sourceId', 'targetId', 'itemProperty'],
                'BindingConnector': ['end1', 'end2', 'sourceId', 'targetId'],
                'Connector': ['end1', 'end2', 'sourceId', 'targetId'],
                'AssemblyConnector': ['end1', 'end2', 'sourceId', 'targetId'],
                'ConstraintBlock': ['specification'],
                'DeriveReqt': ['derivedRequirementId', 'sourceRequirementId'],
                'Satisfy': ['blockId', 'requirementId'],
                'Verify': ['testCaseId', 'requirementId'],
                'Include': ['baseUseCaseId', 'includedUseCaseId'],
                'Extend': ['baseUseCaseId', 'extendingUseCaseId'],
                'Generalization': ['generalId', 'specificId'],
                'Message': ['sendEvent', 'receiveEvent', 'messageSort'],
                'Lifeline': ['representsId'],
                'UseCase': ['subject'],
                'Actor': ['isAbstract']
            }

            if element_type in type_specific_attributes:
                for prop in type_specific_attributes[element_type]:
                    if prop in element:
                        value = element[prop]
                        # 增强的属性处理逻辑：支持复杂对象的字符串化存储
                        if isinstance(value, (str, int, float, bool)) or value is None:
                            node_props[prop] = value
                        elif isinstance(value, list):
                            # 检查列表中是否包含复杂对象
                            if value and len(value) > 0 and isinstance(value[0], (dict, list)):
                                # 将复杂列表转换为JSON字符串存储
                                node_props[f"{prop}_json"] = json.dumps(value)
                                logger.debug(f"复杂列表属性字符串化: {element_type}[{element.get('id', 'unknown')}].{prop}")
                            else:
                                # 保留简单列表（基本类型的列表）
                                node_props[prop] = value
                        elif isinstance(value, dict):
                            # 将字典类型的属性转换为JSON字符串存储
                            node_props[f"{prop}_json"] = json.dumps(value)
                            logger.debug(f"字典属性字符串化: {element_type}[{element.get('id', 'unknown')}].{prop}")
                        else:
                            # 尝试将其他复杂类型转换为字符串
                            try:
                                node_props[f"{prop}_json"] = json.dumps(value)
                                logger.debug(f"复杂属性字符串化: {element_type}[{element.get('id', 'unknown')}].{prop} (类型: {type(value).__name__})")
                            except (TypeError, ValueError) as e:
                                logger.warning(f"无法字符串化属性: {element_type}[{element.get('id', 'unknown')}].{prop} - {e}")
                                continue

            # 通用属性处理：增强版，支持复杂对象的字符串化存储
            for key, value in element.items():
                if key not in ['id', 'type'] and key not in node_props:
                    # 增强的属性处理逻辑
                    if isinstance(value, (str, int, float, bool)):
                        node_props[key] = value
                    elif isinstance(value, list):
                        # 检查列表中是否包含复杂对象
                        if value and len(value) > 0 and isinstance(value[0], (dict, list)):
                            # 将复杂列表转换为JSON字符串存储
                            node_props[f"{key}_json"] = json.dumps(value)
                            logger.debug(f"通用复杂列表属性字符串化: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}].{key}")
                        else:
                            # 保留简单列表（基本类型的列表）
                            node_props[key] = value
                    elif isinstance(value, dict):
                        # 将字典类型的属性转换为JSON字符串存储
                        node_props[f"{key}_json"] = json.dumps(value)
                        logger.debug(f"通用字典属性字符串化: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}].{key}")
                    elif value is None:
                        # 保留None值
                        node_props[key] = value
                    else:
                        # 尝试将其他复杂类型转换为字符串
                        try:
                            node_props[f"{key}_json"] = json.dumps(value)
                            logger.debug(f"通用复杂属性字符串化: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}].{key} (类型: {type(value).__name__})")
                        except (TypeError, ValueError) as e:
                            logger.warning(f"无法字符串化通用属性: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}].{key} - {e}")
                            continue

            # 存储完整的原始数据
            node_props['originalData'] = json.dumps(element)
            node_data.append(node_props)

        # 改进设计：分层标签策略 (主标签:具体类型 + 辅助标签:功能分类)
        element_label = element_type.replace(' ', '_').replace('-', '_')  # 处理可能的空格和连字符

        # 确保标签名符合Neo4j标识符规范
        element_label = ''.join(c if c.isalnum() or c == '_' else '_' for c in element_label)

        # 获取功能分类标签
        category_labels = self._get_category_labels(element_type)
        all_labels = [element_label] + category_labels
        labels_str = ':'.join(all_labels)

        query = f"""
        UNWIND $nodes AS node
        MERGE (n:`{labels_str}` {{elementId: node.elementId}})
        ON CREATE SET n = node, n.sources = [node.source]
        ON MATCH SET n += node, n.sources = CASE
            WHEN n.sources IS NULL THEN [node.source]
            WHEN NOT node.source IN n.sources THEN n.sources + node.source
            ELSE n.sources
        END
        """

        session.run(query, {'nodes': node_data})
        logger.info(f"创建了{len(node_data)}个{element_type}节点，使用标签:{labels_str}")
    
    def _create_relationships(self, session, json_data: Dict[str, Any], source_name: str):
        """创建关系 - 基于JSON字段自动识别关系模式"""
        elements = json_data.get("elements", [])
        model_info = json_data.get("model", {})

        if isinstance(model_info, list) and model_info:
            model_info = model_info[0]
        model_id = model_info.get('id', 'default-model')

        # 创建元素ID到元素数据的映射
        elements_by_id = {elem.get('id'): elem for elem in elements if elem.get('id')}
        if model_id:
            elements_by_id[model_id] = model_info

        logger.info(f"开始创建关系 (来源: {source_name}, 元素数: {len(elements_by_id)})")

        # 1. 创建基本包含关系（基于parentId）
        self._create_parent_child_relationships(session, elements_by_id, model_id, source_name)

        # 2. 自动识别并创建所有ID引用关系
        self._create_id_reference_relationships(session, elements_by_id, source_name)

    def _create_parent_child_relationships(self, session, elements_by_id: Dict[str, Any], model_id: str, source_name: str):
        """创建父子包含关系 - 基于parentId字段"""
        logger.info(f"创建父子包含关系... (来源: {source_name})")

        parent_child_rels = []

        for element_id, element_data in elements_by_id.items():
            if element_id == model_id:
                continue

            parent_id = element_data.get('parentId')
            if not parent_id:
                parent_id = model_id  # 如果没有parentId，连接到模型根节点

            if parent_id and parent_id != element_id and parent_id in elements_by_id:
                parent_child_rels.append({
                    'parentId': parent_id,
                    'childId': element_id,
                    'source': source_name
                })

        if parent_child_rels:
            try:
                query = """
                UNWIND $rels AS rel
                MATCH (parent {elementId: rel.parentId})
                MATCH (child {elementId: rel.childId})
                MERGE (parent)-[r:CONTAINS]->(child)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                result = session.run(query, {'rels': parent_child_rels})
                summary = result.consume()
                created_count = summary.counters.relationships_created
                logger.info(f"CONTAINS关系: 处理{len(parent_child_rels)}个, 实际创建{created_count}个")
            except Exception as e:
                logger.error(f"创建CONTAINS关系失败: {e}")
        else:
            logger.warning(f"没有找到任何父子关系 (来源: {source_name})")

    def _create_id_reference_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """自动识别并创建所有ID引用关系"""
        logger.info(f"自动识别ID引用关系... (来源: {source_name})")

        # 定义关系类型映射 - 基于字段名称模式
        relationship_patterns = {
            # 需求关系
            'sourceRequirementId': 'DERIVES_FROM',
            'derivedRequirementId': 'DERIVES_TO',
            'blockId': 'SATISFIES',
            'requirementId': 'SATISFIED_BY',
            'testCaseId': 'VERIFIES',

            # 活动图关系
            'sourceId': 'FLOWS_FROM',
            'targetId': 'FLOWS_TO',

            # 状态机关系
            'signalId': 'REFERENCES_SIGNAL',

            # 类型关系
            'typeId': 'HAS_TYPE',
            'unitId': 'HAS_UNIT',

            # 序列图关系
            'representsId': 'REPRESENTS',
            'sendEventId': 'HAS_SEND_EVENT',
            'receiveEventId': 'HAS_RECEIVE_EVENT',
            'signatureId': 'INVOKES_OPERATION',
            'coveredId': 'COVERS_LIFELINE',
            'messageId': 'BELONGS_TO_MESSAGE',

            # 其他引用关系
            'behavior': 'CALLS_BEHAVIOR',
            'from': 'REFERENCES',
        }

        # 特殊处理的数组字段
        array_patterns = {
            'memberEndIds': 'MEMBER_END',
            'triggerIds': 'HAS_TRIGGER',
            'nodeIds': 'CONTAINS_NODE',
        }

        all_relationships = []

        # 遍历所有元素，查找ID引用字段
        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type', 'Unknown')

            # 处理单个ID引用字段
            for field_name, field_value in element_data.items():
                if field_name.endswith('Id') and field_value and field_value != element_id:
                    # 跳过非字符串类型的字段值（如字典、列表等复杂对象）
                    if not isinstance(field_value, str):
                        continue
                    # 确保引用的元素存在
                    if field_value in elements_by_id:
                        rel_type = relationship_patterns.get(field_name, f'REFERENCES_{field_name.upper()}')

                        # 根据元素类型和字段调整关系方向和类型
                        from_id, to_id, final_rel_type = self._determine_relationship_direction(
                            element_id, field_value, element_type, field_name, rel_type
                        )

                        all_relationships.append({
                            'fromId': from_id,
                            'toId': to_id,
                            'relType': final_rel_type,
                            'source': source_name,
                            'originalField': field_name
                        })

            # 处理数组ID引用字段
            for field_name, field_value in element_data.items():
                if field_name in array_patterns and isinstance(field_value, list):
                    rel_type = array_patterns[field_name]
                    for ref_id in field_value:
                        if ref_id and ref_id in elements_by_id:
                            all_relationships.append({
                                'fromId': element_id,
                                'toId': ref_id,
                                'relType': rel_type,
                                'source': source_name,
                                'originalField': field_name
                            })

            # 处理复杂对象字段（如 end1, end2, specification）
            for field_name, field_value in element_data.items():
                if isinstance(field_value, dict):
                    # 处理连接器的 end1/end2 字段
                    if field_name in ['end1', 'end2'] and element_type in ['AssemblyConnector', 'BindingConnector', 'Connector']:
                        self._process_connector_end(element_id, field_name, field_value, elements_by_id, all_relationships, source_name)
                    # 处理约束块的 specification 字段
                    elif field_name == 'specification' and element_type == 'ConstraintBlock':
                        # specification 字段通常包含表达式，不需要创建关系，但可以记录
                        pass

        logger.info(f"识别到 {len(all_relationships)} 个ID引用关系")

        # 按关系类型分组执行
        if all_relationships:
            rel_groups = {}
            for rel in all_relationships:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                try:
                    query = f"""
                    UNWIND $rels AS rel
                    MATCH (from {{elementId: rel.fromId}})
                    MATCH (to {{elementId: rel.toId}})
                    MERGE (from)-[r:{rel_type}]->(to)
                    ON CREATE SET r.source = rel.source, r.sources = [rel.source], r.originalField = rel.originalField
                    ON MATCH SET r.sources = CASE
                        WHEN r.sources IS NULL THEN [rel.source]
                        WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                        ELSE r.sources
                    END
                    """
                    result = session.run(query, {'rels': rels})
                    summary = result.consume()
                    created_count = summary.counters.relationships_created
                    logger.info(f"{rel_type}关系: 处理{len(rels)}个, 实际创建{created_count}个")
                except Exception as e:
                    logger.error(f"创建{rel_type}关系失败: {e}")
        else:
            logger.warning(f"没有找到任何ID引用关系 (来源: {source_name})")

    def _determine_relationship_direction(self, element_id: str, ref_id: str, element_type: str,
                                        field_name: str, default_rel_type: str) -> tuple:
        """根据元素类型和字段名确定关系方向和类型"""

        # 特殊处理某些关系类型的方向
        if element_type == 'DeriveReqt':
            if field_name == 'sourceRequirementId':
                return ref_id, element_id, 'DERIVES_TO'  # source -> derived
            elif field_name == 'derivedRequirementId':
                return element_id, ref_id, 'DERIVES_FROM'  # derived -> source

        elif element_type == 'Satisfy':
            if field_name == 'blockId':
                return ref_id, element_id, 'SATISFIES'  # block -> requirement
            elif field_name == 'requirementId':
                return element_id, ref_id, 'SATISFIED_BY'  # satisfy -> requirement

        elif element_type == 'Verify':
            if field_name == 'testCaseId':
                return ref_id, element_id, 'VERIFIES'  # testcase -> requirement
            elif field_name == 'requirementId':
                return element_id, ref_id, 'VERIFIED_BY'  # verify -> requirement

        elif element_type in ['ControlFlow', 'ObjectFlow']:
            if field_name == 'sourceId':
                return ref_id, element_id, f'{element_type.upper()}_FROM'
            elif field_name == 'targetId':
                return element_id, ref_id, f'{element_type.upper()}_TO'

        elif element_type == 'Transition':
            if field_name == 'sourceId':
                return ref_id, element_id, 'TRANSITION_FROM'
            elif field_name == 'targetId':
                return element_id, ref_id, 'TRANSITION_TO'

        # 默认情况：从当前元素指向引用的元素
        return element_id, ref_id, default_rel_type

    def _create_containment_relationships(self, session, elements_by_id: Dict[str, Any], model_id: str, source_name: str):
        """创建基本包含关系 - 基于bdd_and_ibd_store.py的HAS_CHILD模式"""
        logger.info(f"创建基本包含关系... (来源: {source_name}, 元素数: {len(elements_by_id)})")

        contains_rels = []
        for element_id, element_data in elements_by_id.items():
            if element_id == model_id:
                continue

            parent_id = element_data.get('parentId')
            if not parent_id:
                parent_id = model_id

            if parent_id and parent_id != element_id:
                # 根据父子类型确定关系类型
                parent_data = elements_by_id.get(parent_id)
                if not parent_data:
                    logger.warning(f"找不到父元素 {parent_id} for 子元素 {element_id}")
                    continue

                parent_type = parent_data.get('type', 'Model')
                element_type = element_data.get('type')

                # 根据参考文件确定具体的关系类型
                rel_type = self._determine_containment_relationship_type(parent_type, element_type)

                contains_rels.append({
                    'parentId': parent_id,
                    'childId': element_id,
                    'relType': rel_type,
                    'source': source_name
                })

        logger.info(f"准备创建 {len(contains_rels)} 个包含关系")

        if contains_rels:
            # 按关系类型分组执行
            rel_groups = {}
            for rel in contains_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                try:
                    query = f"""
                    UNWIND $rels AS rel
                    MATCH (parent {{elementId: rel.parentId}})
                    MATCH (child {{elementId: rel.childId}})
                    MERGE (parent)-[r:{rel_type}]->(child)
                    ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                    ON MATCH SET r.sources = CASE
                        WHEN r.sources IS NULL THEN [rel.source]
                        WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                        ELSE r.sources
                    END
                    """
                    result = session.run(query, {'rels': rels})
                    logger.info(f"创建了{len(rels)}个{rel_type}关系")
                except Exception as e:
                    logger.error(f"创建{rel_type}关系失败: {e}")
        else:
            logger.warning(f"没有找到任何包含关系 (来源: {source_name})")

    def _determine_containment_relationship_type(self, parent_type: str, child_type: str) -> str:
        """根据父子元素类型确定包含关系类型"""
        # 基于参考文件的关系类型映射
        if parent_type == 'Model':
            return 'CONTAINS'
        elif parent_type == 'Package':
            return 'CONTAINS'
        elif parent_type in ['Block', 'Class']:
            if child_type == 'Property':
                return 'HAS_PROPERTY'
            elif child_type in ['FullPort', 'ProxyPort', 'Port']:
                return 'HAS_PORT'
            elif child_type == 'Operation':
                return 'HAS_OPERATION'
            elif child_type == 'Reception':
                return 'HAS_RECEPTION'
            elif child_type == 'Diagram':
                return 'HAS_DIAGRAM'
            else:
                return 'CONTAINS'
        elif parent_type == 'Operation':
            if child_type == 'Parameter':
                return 'HAS_PARAMETER'
        elif parent_type == 'Activity':
            if child_type in ['InitialNode', 'ActivityFinalNode', 'FlowFinalNode', 'DecisionNode',
                             'MergeNode', 'ForkNode', 'JoinNode', 'CallBehaviorAction']:
                return 'HAS_NODE'
            elif child_type in ['ControlFlow', 'ObjectFlow']:
                return 'HAS_EDGE'
            elif child_type == 'ActivityPartition':
                return 'HAS_PARTITION'
        elif parent_type == 'StateMachine':
            if child_type == 'Region':
                return 'HAS_REGION'
        elif parent_type == 'State':
            if child_type == 'Region':
                return 'HAS_REGION'
        elif parent_type == 'Region':
            if child_type in ['State', 'Pseudostate']:
                return 'HAS_SUBVERTEX'
            elif child_type == 'Transition':
                return 'HAS_TRANSITION'

        return 'CONTAINS'  # 默认关系类型

    def _create_type_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建类型引用关系 - 基于bdd_and_ibd_store.py的HAS_TYPE模式"""
        logger.info("创建类型引用关系...")

        type_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # Property -> Type (HAS_TYPE)
            if element_type == 'Property' and element_data.get('typeId'):
                type_rels.append({
                    'fromId': element_id,
                    'toId': element_data['typeId'],
                    'relType': 'HAS_TYPE',
                    'source': source_name
                })

            # Port -> Type (PORT_TYPE)
            elif element_type in ['FullPort', 'ProxyPort', 'Port'] and element_data.get('typeId'):
                type_rels.append({
                    'fromId': element_id,
                    'toId': element_data['typeId'],
                    'relType': 'PORT_TYPE',
                    'source': source_name
                })

            # Parameter -> Type (PARAM_TYPE)
            elif element_type == 'Parameter' and element_data.get('typeId'):
                type_rels.append({
                    'fromId': element_id,
                    'toId': element_data['typeId'],
                    'relType': 'PARAM_TYPE',
                    'source': source_name
                })

            # ValueType -> Unit (HAS_UNIT)
            elif element_type == 'ValueType' and element_data.get('unitId'):
                type_rels.append({
                    'fromId': element_id,
                    'toId': element_data['unitId'],
                    'relType': 'HAS_UNIT',
                    'source': source_name
                })

            # ValueType -> BaseType (BASE_TYPE)
            elif element_type == 'ValueType' and element_data.get('baseType'):
                base_type = element_data['baseType']
                if base_type not in ['Integer', 'Real', 'String']:  # 只处理自定义基础类型
                    type_rels.append({
                        'fromId': element_id,
                        'toId': base_type,
                        'relType': 'BASE_TYPE',
                        'source': source_name
                    })

            # Reception -> Signal (RECEPTION_SIGNAL)
            elif element_type == 'Reception' and element_data.get('signalId'):
                type_rels.append({
                    'fromId': element_id,
                    'toId': element_data['signalId'],
                    'relType': 'RECEPTION_SIGNAL',
                    'source': source_name
                })

        # 执行类型关系创建
        if type_rels:
            rel_groups = {}
            for rel in type_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                query = f"""
                UNWIND $rels AS rel
                MATCH (from {{elementId: rel.fromId}})
                MATCH (to {{elementId: rel.toId}})
                MERGE (from)-[r:{rel_type}]->(to)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                session.run(query, {'rels': rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

    def _create_association_relationships_comprehensive(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建关联关系 - 基于bdd_and_ibd_store.py的Association模式"""
        logger.info("创建关联关系...")

        assoc_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # Association -> Properties (ASSOC_MEMBER)
            if element_type == 'Association':
                member_end_ids = element_data.get('memberEndIds', [])
                for member_id in member_end_ids:
                    if member_id in elements_by_id:
                        assoc_rels.append({
                            'fromId': element_id,
                            'toId': member_id,
                            'relType': 'ASSOC_MEMBER',
                            'source': source_name
                        })

                # 处理navigableOwnedEndIds（如果存在）
                navigable_end_ids = element_data.get('navigableOwnedEndIds', [])
                for nav_id in navigable_end_ids:
                    if nav_id in elements_by_id:
                        assoc_rels.append({
                            'fromId': element_id,
                            'toId': nav_id,
                            'relType': 'HAS_NAVIGABLE_OWNED_END',
                            'source': source_name
                        })

            # Property -> Association (通过associationId)
            elif element_type == 'Property' and element_data.get('associationId'):
                assoc_id = element_data['associationId']
                if assoc_id in elements_by_id:
                    assoc_rels.append({
                        'fromId': element_id,
                        'toId': assoc_id,
                        'relType': 'REFERENCES_ASSOCIATION',
                        'source': source_name
                    })

        # 执行关联关系创建
        if assoc_rels:
            rel_groups = {}
            for rel in assoc_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                query = f"""
                UNWIND $rels AS rel
                MATCH (from {{elementId: rel.fromId}})
                MATCH (to {{elementId: rel.toId}})
                MERGE (from)-[r:{rel_type}]->(to)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                session.run(query, {'rels': rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

    def _create_requirement_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建需求关系 - 基于req_store.py的模式"""
        logger.info("创建需求关系...")

        req_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # DeriveReqt关系
            if element_type == 'DeriveReqt':
                source_req_id = element_data.get('sourceRequirementId')
                derived_req_id = element_data.get('derivedRequirementId')
                if source_req_id and derived_req_id:
                    req_rels.append({
                        'fromId': derived_req_id,
                        'toId': source_req_id,
                        'relType': 'DERIVES_FROM',
                        'relationId': element_id,
                        'source': source_name
                    })

            # Satisfy关系
            elif element_type == 'Satisfy':
                block_id = element_data.get('blockId')
                requirement_id = element_data.get('requirementId')
                if block_id and requirement_id:
                    req_rels.append({
                        'fromId': block_id,
                        'toId': requirement_id,
                        'relType': 'SATISFIES',
                        'relationId': element_id,
                        'source': source_name
                    })

            # Verify关系
            elif element_type == 'Verify':
                test_case_id = element_data.get('testCaseId')
                requirement_id = element_data.get('requirementId')
                if test_case_id and requirement_id:
                    req_rels.append({
                        'fromId': test_case_id,
                        'toId': requirement_id,
                        'relType': 'VERIFIES',
                        'relationId': element_id,
                        'source': source_name
                    })

        # 执行需求关系创建
        if req_rels:
            rel_groups = {}
            for rel in req_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                query = f"""
                UNWIND $rels AS rel
                MATCH (from {{elementId: rel.fromId}})
                MATCH (to {{elementId: rel.toId}})
                MERGE (from)-[r:{rel_type} {{relationId: rel.relationId}}]->(to)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                session.run(query, {'rels': rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

    def _create_connector_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建连接器关系 - 基于bdd_and_ibd_store.py的Connector模式"""
        logger.info("创建连接器关系...")

        connector_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # 处理各种Connector类型
            if element_type and element_type.endswith('Connector'):
                # 处理end1和end2连接
                for end_name in ['end1', 'end2']:
                    end_data = element_data.get(end_name)
                    if isinstance(end_data, dict):
                        # 处理partRefId, propertyRefId, portRefId
                        for ref_field in ['partRefId', 'propertyRefId', 'portRefId']:
                            ref_id = end_data.get(ref_field)
                            if ref_id and ref_id in elements_by_id:
                                connector_rels.append({
                                    'fromId': element_id,
                                    'toId': ref_id,
                                    'relType': 'CONNECT_END',
                                    'role': end_name,
                                    'refField': ref_field,
                                    'source': source_name
                                })

        # 执行连接器关系创建
        if connector_rels:
            query = """
            UNWIND $rels AS rel
            MATCH (connector {elementId: rel.fromId})
            MATCH (target {elementId: rel.toId})
            MERGE (connector)-[r:CONNECT_END {role: rel.role, ref_field: rel.refField}]->(target)
            ON CREATE SET r.source = rel.source, r.sources = [rel.source]
            ON MATCH SET r.sources = CASE
                WHEN r.sources IS NULL THEN [rel.source]
                WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                ELSE r.sources
            END
            """
            session.run(query, {'rels': connector_rels})
            logger.info(f"创建了{len(connector_rels)}个CONNECT_END关系")

    def _create_activity_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建活动图关系 - 基于act_store.py的模式"""
        logger.info(f"创建活动图关系... (来源: {source_name}, 元素数: {len(elements_by_id)})")

        activity_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # ControlFlow和ObjectFlow关系
            if element_type in ['ControlFlow', 'ObjectFlow']:
                source_id = element_data.get('sourceId')
                target_id = element_data.get('targetId')
                if source_id and target_id:
                    rel_type = 'CONTROL_FLOW' if element_type == 'ControlFlow' else 'OBJECT_FLOW'
                    activity_rels.append({
                        'fromId': source_id,
                        'toId': target_id,
                        'relType': rel_type,
                        'flowId': element_id,
                        'source': source_name
                    })
                    logger.debug(f"找到{element_type}关系: {source_id} -> {target_id}")

            # CallBehaviorAction -> Activity (CALLS)
            elif element_type == 'CallBehaviorAction' and element_data.get('behavior'):
                behavior_id = element_data['behavior']
                if behavior_id in elements_by_id:
                    activity_rels.append({
                        'fromId': element_id,
                        'toId': behavior_id,
                        'relType': 'CALLS',
                        'source': source_name
                    })

            # Pin关系 (HAS_PIN)
            elif element_type in ['InputPin', 'OutputPin']:
                parent_id = element_data.get('parentId')
                if parent_id and parent_id in elements_by_id:
                    parent_data = elements_by_id[parent_id]
                    if parent_data.get('type') in ['CallBehaviorAction', 'Action']:
                        activity_rels.append({
                            'fromId': parent_id,
                            'toId': element_id,
                            'relType': 'HAS_PIN',
                            'source': source_name
                        })

            # ActivityPartition关系
            elif element_type == 'ActivityPartition':
                # REPRESENTS关系
                represents_id = element_data.get('representsId')
                if represents_id and represents_id in elements_by_id:
                    activity_rels.append({
                        'fromId': element_id,
                        'toId': represents_id,
                        'relType': 'REPRESENTS',
                        'source': source_name
                    })

                # IN_PARTITION关系
                node_ids = element_data.get('nodeIds', [])
                for node_id in node_ids:
                    if node_id in elements_by_id:
                        node_data = elements_by_id[node_id]
                        # 排除ForkNode和JoinNode
                        if node_data.get('type') not in ['ForkNode', 'JoinNode']:
                            activity_rels.append({
                                'fromId': node_id,
                                'toId': element_id,
                                'relType': 'IN_PARTITION',
                                'source': source_name
                            })

        # 执行活动图关系创建
        logger.info(f"准备创建 {len(activity_rels)} 个活动图关系")

        if activity_rels:
            rel_groups = {}
            for rel in activity_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                try:
                    if rel_type in ['CONTROL_FLOW', 'OBJECT_FLOW']:
                        # Flow关系需要特殊处理，包含flowId
                        query = f"""
                        UNWIND $rels AS rel
                        MATCH (source {{elementId: rel.fromId}})
                        MATCH (target {{elementId: rel.toId}})
                        MERGE (source)-[r:{rel_type} {{elementId: rel.flowId}}]->(target)
                        ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                        ON MATCH SET r.sources = CASE
                            WHEN r.sources IS NULL THEN [rel.source]
                            WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                            ELSE r.sources
                        END
                        """
                    else:
                        query = f"""
                        UNWIND $rels AS rel
                        MATCH (from {{elementId: rel.fromId}})
                        MATCH (to {{elementId: rel.toId}})
                        MERGE (from)-[r:{rel_type}]->(to)
                        ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                        ON MATCH SET r.sources = CASE
                            WHEN r.sources IS NULL THEN [rel.source]
                            WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                            ELSE r.sources
                        END
                        """
                    result = session.run(query, {'rels': rels})
                    logger.info(f"创建了{len(rels)}个{rel_type}关系")
                except Exception as e:
                    logger.error(f"创建{rel_type}关系失败: {e}")
        else:
            logger.warning(f"没有找到任何活动图关系 (来源: {source_name})")

    def _create_state_machine_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建状态机关系 - 基于stm_store.py的模式"""
        logger.info("创建状态机关系...")

        sm_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # Transition关系
            if element_type == 'Transition':
                source_id = element_data.get('sourceId')
                target_id = element_data.get('targetId')
                if source_id and target_id:
                    sm_rels.extend([
                        {
                            'fromId': element_id,
                            'toId': source_id,
                            'relType': 'HAS_SOURCE',
                            'source': source_name
                        },
                        {
                            'fromId': element_id,
                            'toId': target_id,
                            'relType': 'HAS_TARGET',
                            'source': source_name
                        }
                    ])

                # Trigger关系
                trigger_ids = element_data.get('triggerIds', [])
                for trigger_id in trigger_ids:
                    if trigger_id in elements_by_id:
                        sm_rels.append({
                            'fromId': element_id,
                            'toId': trigger_id,
                            'relType': 'HAS_TRIGGER',
                            'source': source_name
                        })

            # SignalEvent -> Signal
            elif element_type == 'SignalEvent' and element_data.get('signalId'):
                signal_id = element_data['signalId']
                if signal_id in elements_by_id:
                    sm_rels.append({
                        'fromId': element_id,
                        'toId': signal_id,
                        'relType': 'REFERENCES_SIGNAL',
                        'source': source_name
                    })

            # State的entry/exit/doActivity行为关系
            elif element_type == 'State':
                for behavior_key, rel_type_str in [
                    ('entry', 'HAS_ENTRY_BEHAVIOR'),
                    ('exit', 'HAS_EXIT_BEHAVIOR'),
                    ('doActivity', 'HAS_DO_BEHAVIOR')
                ]:
                    behavior_data = element_data.get(behavior_key)
                    if isinstance(behavior_data, dict):
                        wrapper_id = behavior_data.get('wrapperActivityId')
                        called_id = behavior_data.get('calledBehaviorId')
                        if wrapper_id:
                            sm_rels.append({
                                'fromId': element_id,
                                'toId': wrapper_id,
                                'relType': rel_type_str,
                                'source': source_name
                            })
                        if wrapper_id and called_id:
                            sm_rels.append({
                                'fromId': wrapper_id,
                                'toId': called_id,
                                'relType': 'CALLS_BEHAVIOR',
                                'source': source_name
                            })

        # 执行状态机关系创建
        if sm_rels:
            rel_groups = {}
            for rel in sm_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                query = f"""
                UNWIND $rels AS rel
                MATCH (from {{elementId: rel.fromId}})
                MATCH (to {{elementId: rel.toId}})
                MERGE (from)-[r:{rel_type}]->(to)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                session.run(query, {'rels': rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

    def _create_sequence_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建序列图关系 - 基于sd_store.py的模式"""
        logger.info("创建序列图关系...")

        seq_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # Lifeline -> Property/Class (REPRESENTS_ELEMENT)
            if element_type == 'Lifeline' and element_data.get('representsId'):
                represents_id = element_data['representsId']
                if represents_id in elements_by_id:
                    seq_rels.append({
                        'fromId': element_id,
                        'toId': represents_id,
                        'relType': 'REPRESENTS_ELEMENT',
                        'source': source_name
                    })

            # Message关系
            elif element_type == 'Message':
                # Send/Receive Event关系
                send_event_id = element_data.get('sendEventId')
                receive_event_id = element_data.get('receiveEventId')
                if send_event_id and send_event_id in elements_by_id:
                    seq_rels.append({
                        'fromId': element_id,
                        'toId': send_event_id,
                        'relType': 'HAS_SEND_EVENT',
                        'source': source_name
                    })
                if receive_event_id and receive_event_id in elements_by_id:
                    seq_rels.append({
                        'fromId': element_id,
                        'toId': receive_event_id,
                        'relType': 'HAS_RECEIVE_EVENT',
                        'source': source_name
                    })

                # Signature关系
                signature_id = element_data.get('signatureId')
                if signature_id and signature_id in elements_by_id:
                    seq_rels.append({
                        'fromId': element_id,
                        'toId': signature_id,
                        'relType': 'INVOKES_OPERATION',
                        'source': source_name
                    })

            # MessageOccurrenceSpecification关系
            elif element_type == 'MessageOccurrenceSpecification':
                covered_id = element_data.get('coveredId')
                message_id = element_data.get('messageId')
                if covered_id and covered_id in elements_by_id:
                    seq_rels.append({
                        'fromId': element_id,
                        'toId': covered_id,
                        'relType': 'COVERS_LIFELINE',
                        'source': source_name
                    })
                if message_id and message_id in elements_by_id:
                    seq_rels.append({
                        'fromId': element_id,
                        'toId': message_id,
                        'relType': 'BELONGS_TO_MESSAGE',
                        'source': source_name
                    })

        # 执行序列图关系创建
        if seq_rels:
            rel_groups = {}
            for rel in seq_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                query = f"""
                UNWIND $rels AS rel
                MATCH (from {{elementId: rel.fromId}})
                MATCH (to {{elementId: rel.toId}})
                MERGE (from)-[r:{rel_type}]->(to)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                session.run(query, {'rels': rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

    def _create_usecase_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建用例图关系 - 基于uc_store.py的模式"""
        logger.info("创建用例图关系...")

        uc_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # Include关系
            if element_type == 'Include':
                source_id = element_data.get('sourceId')
                target_id = element_data.get('targetId')
                if source_id and target_id:
                    uc_rels.append({
                        'fromId': source_id,
                        'toId': target_id,
                        'relType': 'INCLUDES',
                        'source': source_name
                    })

            # Extend关系
            elif element_type == 'Extend':
                source_id = element_data.get('sourceId')
                target_id = element_data.get('targetId')
                if source_id and target_id:
                    uc_rels.append({
                        'fromId': source_id,
                        'toId': target_id,
                        'relType': 'EXTENDS',
                        'source': source_name
                    })

        # 执行用例图关系创建
        if uc_rels:
            rel_groups = {}
            for rel in uc_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                query = f"""
                UNWIND $rels AS rel
                MATCH (from {{elementId: rel.fromId}})
                MATCH (to {{elementId: rel.toId}})
                MERGE (from)-[r:{rel_type}]->(to)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                session.run(query, {'rels': rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

    def _create_parameter_relationships(self, session, elements_by_id: Dict[str, Any], source_name: str):
        """创建参数图关系 - 基于par_store.py的模式"""
        logger.info("创建参数图关系...")

        param_rels = []

        for element_id, element_data in elements_by_id.items():
            element_type = element_data.get('type')

            # ConstraintProperty关系
            if element_type == 'ConstraintProperty':
                parent_id = element_data.get('parentId')
                from_id = element_data.get('from')  # 指向ConstraintBlock

                if parent_id and parent_id in elements_by_id:
                    # Block -> ConstraintProperty (Owned关系)
                    param_rels.append({
                        'fromId': parent_id,
                        'toId': element_id,
                        'relType': 'OWNS',
                        'source': source_name
                    })

                if from_id and from_id in elements_by_id:
                    # ConstraintProperty -> ConstraintBlock (REFERENCES关系)
                    param_rels.append({
                        'fromId': element_id,
                        'toId': from_id,
                        'relType': 'REFERENCES',
                        'source': source_name
                    })

            # ConstraintBlock的Port关系
            elif element_type == 'ConstraintBlock':
                ports = element_data.get('ports', [])
                for port in ports:
                    if isinstance(port, dict):
                        from_prop_id = port.get('from')  # 指向ValueProperty
                        if from_prop_id and from_prop_id in elements_by_id:
                            param_rels.append({
                                'fromId': element_id,
                                'toId': from_prop_id,
                                'relType': 'PORT',
                                'source': source_name
                            })

        # 执行参数图关系创建
        if param_rels:
            rel_groups = {}
            for rel in param_rels:
                rel_type = rel['relType']
                if rel_type not in rel_groups:
                    rel_groups[rel_type] = []
                rel_groups[rel_type].append(rel)

            for rel_type, rels in rel_groups.items():
                query = f"""
                UNWIND $rels AS rel
                MATCH (from {{elementId: rel.fromId}})
                MATCH (to {{elementId: rel.toId}})
                MERGE (from)-[r:{rel_type}]->(to)
                ON CREATE SET r.source = rel.source, r.sources = [rel.source]
                ON MATCH SET r.sources = CASE
                    WHEN r.sources IS NULL THEN [rel.source]
                    WHEN NOT rel.source IN r.sources THEN r.sources + rel.source
                    ELSE r.sources
                END
                """
                session.run(query, {'rels': rels})
                logger.info(f"创建了{len(rels)}个{rel_type}关系")

    # 旧的方法已被上面的综合方法替代，保留此注释作为参考
    
    def find_duplicate_candidates(self) -> List[Tuple[str, str, float]]:
        """
        查找可能重复的元素候选对
        
        返回:
            (id1, id2, similarity_score)的列表
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return []
        
        try:
            with self._driver.session() as session:
                # 查找相同类型和名称的元素（改进：使用Element标签进行高效查询）
                query = """
                MATCH (n1:Element), (n2:Element)
                WHERE n1.elementId < n2.elementId
                AND n1.type = n2.type
                AND n1.name = n2.name
                AND n1.name IS NOT NULL
                AND n1.elementId IS NOT NULL
                AND n2.elementId IS NOT NULL
                RETURN n1.elementId as id1, n2.elementId as id2, 1.0 as score
                """
                
                result = session.run(query)
                candidates = [(record['id1'], record['id2'], record['score']) 
                             for record in result]
                
                logger.info(f"找到{len(candidates)}对可能重复的元素")
                return candidates
                
        except Exception as e:
            logger.error(f"查找重复候选失败: {e}")
            return []
    
    def get_element_details(self, element_id: str) -> Optional[Dict[str, Any]]:
        """获取元素详细信息"""
        if not self._driver:
            return None
            
        try:
            with self._driver.session() as session:
                query = """
                MATCH (n {elementId: $elementId})
                RETURN n
                """
                result = session.run(query, {'elementId': element_id})
                record = result.single()
                
                if record:
                    node = record['n']
                    return dict(node)
                return None
                
        except Exception as e:
            logger.error(f"获取元素{element_id}详情失败: {e}")
            return None
    
    def merge_elements(self, canonical_id: str, duplicate_ids: List[str]) -> bool:
        """
        合并重复元素
        
        参数:
            canonical_id: 保留的主元素ID
            duplicate_ids: 要合并的重复元素ID列表
            
        返回:
            是否成功合并
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return False
            
        try:
            with self._driver.session() as session:
                for dup_id in duplicate_ids:
                    # 简化的合并策略：合并属性并删除重复元素
                    merge_query = """
                    MATCH (dup {elementId: $dupId})
                    MATCH (canonical {elementId: $canonicalId})

                    // 合并sources属性
                    SET canonical.sources = CASE
                        WHEN canonical.sources IS NULL AND dup.sources IS NULL THEN []
                        WHEN canonical.sources IS NULL THEN dup.sources
                        WHEN dup.sources IS NULL THEN canonical.sources
                        ELSE canonical.sources + [x IN dup.sources WHERE NOT x IN canonical.sources]
                    END

                    // 删除重复元素（关系会自动删除）
                    DETACH DELETE dup
                    """

                    session.run(merge_query, {
                        'dupId': dup_id,
                        'canonicalId': canonical_id
                    })
                    
                logger.info(f"成功合并元素{duplicate_ids}到{canonical_id}")
                return True
                
        except Exception as e:
            logger.error(f"合并元素失败: {e}")
            return False
    
    def export_to_json(self) -> Dict[str, Any]:
        """
        从Neo4j导出完整的SysML JSON模型
        增强版：导出所有节点和关系信息，重建完整的SysML模型

        返回:
            统一的SysML JSON模型
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return {}

        try:
            with self._driver.session() as session:
                logger.info("🚀 开始从Neo4j导出完整的SysML模型")

                # 1. 获取模型信息
                model_info = self._export_model_info(session)

                # 2. 获取所有元素节点
                elements = self._export_all_elements(session)

                # 3. 重建关系信息
                elements_with_relations = self._rebuild_element_relationships(session, elements)

                # 4. 构建最终模型
                result = {
                    'model': model_info,  # model应该是字典而不是列表
                    'elements': elements_with_relations
                }

                # 5. 统计信息
                element_types = {}
                for element in elements_with_relations:
                    element_type = element.get('type', 'Unknown')
                    element_types[element_type] = element_types.get(element_type, 0) + 1

                logger.info(f"✅ 导出完成: {len(elements_with_relations)}个元素")
                logger.info(f"📊 元素类型分布: {element_types}")

                return result

        except Exception as e:
            logger.error(f"❌ 导出SysML模型失败: {e}")
            return {}

    def _export_model_info(self, session) -> Dict[str, Any]:
        """导出模型信息"""
        model_query = "MATCH (m:Model) RETURN m LIMIT 1"
        model_result = session.run(model_query)
        model_record = model_result.single()

        if model_record:
            model_node = model_record['m']
            return {
                'id': model_node.get('elementId', 'integrated-model'),
                'name': model_node.get('name', 'Integrated SysML Model'),
                'type': 'Model'
            }
        else:
            return {
                'id': 'integrated-model',
                'name': 'Integrated SysML Model',
                'type': 'Model'
            }

    def _export_all_elements(self, session) -> List[Dict[str, Any]]:
        """导出所有元素节点"""
        # 获取所有非Model节点
        elements_query = """
        MATCH (n)
        WHERE n.elementId IS NOT NULL
        AND n.type IS NOT NULL
        AND NOT n:Model
        RETURN n
        ORDER BY n.type, n.name
        """
        elements_result = session.run(elements_query)

        elements = []
        for record in elements_result:
            node = record['n']
            element = {
                'id': node.get('elementId'),
                'type': node.get('type'),
                'name': node.get('name', '')
            }

            # 添加所有其他属性，支持复杂对象属性的还原
            json_attributes = {}  # 存储需要反序列化的JSON属性

            for key, value in node.items():
                if key not in ['elementId', 'type', 'name', 'originalData'] and not key.startswith('_'):
                    if value is not None:
                        # 检查是否是JSON字符串化的属性
                        if key.endswith('_json'):
                            original_key = key[:-5]  # 移除'_json'后缀
                            try:
                                # 尝试反序列化JSON字符串
                                json_attributes[original_key] = json.loads(value)
                                logger.debug(f"成功还原复杂属性: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}].{original_key}")
                            except (json.JSONDecodeError, TypeError) as e:
                                logger.warning(f"无法还原JSON属性: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}].{key} - {e}")
                                # 如果反序列化失败，保留原始字符串值
                                element[key] = value
                        else:
                            # 普通属性直接添加
                            element[key] = value

            # 将反序列化的JSON属性添加到元素中
            element.update(json_attributes)

            # 如果存在originalData，可以作为备用数据源进行验证或补充
            if node.get('originalData'):
                try:
                    original_element = json.loads(node.get('originalData'))
                    # 检查是否有遗漏的复杂属性需要从原始数据中恢复
                    for orig_key, orig_value in original_element.items():
                        if orig_key not in element and isinstance(orig_value, (dict, list)):
                            element[orig_key] = orig_value
                            logger.debug(f"从originalData恢复复杂属性: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}].{orig_key}")
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"无法解析originalData: {element.get('type', 'Unknown')}[{element.get('id', 'unknown')}] - {e}")

            elements.append(element)

        logger.info(f"📦 导出了{len(elements)}个元素节点")
        return elements

    def _rebuild_element_relationships(self, session, elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """重建元素间的关系信息"""
        logger.info("🔗 重建元素间的关系信息")

        # 创建元素ID到元素的映射
        element_map = {elem['id']: elem for elem in elements}

        # 重建各种关系
        self._rebuild_containment_relationships(session, element_map)
        self._rebuild_reference_relationships(session, element_map)
        self._rebuild_flow_relationships(session, element_map)
        self._rebuild_special_relationships(session, element_map)

        return list(element_map.values())

    def _rebuild_containment_relationships(self, session, element_map: Dict[str, Dict]):
        """重建包含关系（parentId）"""
        containment_query = """
        MATCH (parent)-[:CONTAINS]->(child)
        WHERE parent.elementId IS NOT NULL AND child.elementId IS NOT NULL
        RETURN parent.elementId as parentId, child.elementId as childId
        """

        result = session.run(containment_query)
        containment_count = 0

        for record in result:
            parent_id = record['parentId']
            child_id = record['childId']

            if child_id in element_map:
                element_map[child_id]['parentId'] = parent_id
                containment_count += 1

        logger.info(f"🏗️ 重建了{containment_count}个包含关系")

    def _rebuild_reference_relationships(self, session, element_map: Dict[str, Dict]):
        """重建引用关系"""
        reference_patterns = [
            ('HAS_TYPE', 'typeId'),
            ('DERIVES_TO', 'derivedRequirementId'),
            ('DERIVES_FROM', 'sourceRequirementId'),
            ('SATISFIES', 'satisfiedRequirementId'),
            ('VERIFIES', 'verifiedRequirementId'),
            ('REFERENCES_ASSOCIATIONID', 'associationId'),
            ('REFERENCES_SIGNAL', 'signalId'),
            ('REFERENCES_CLASSIFIERBEHAVIORID', 'classifierBehaviorId'),
            ('HAS_UNIT', 'unitId')
        ]

        total_references = 0
        for rel_type, field_name in reference_patterns:
            query = f"""
            MATCH (source)-[:{rel_type}]->(target)
            WHERE source.elementId IS NOT NULL AND target.elementId IS NOT NULL
            RETURN source.elementId as sourceId, target.elementId as targetId
            """

            result = session.run(query)
            count = 0

            for record in result:
                source_id = record['sourceId']
                target_id = record['targetId']

                if source_id in element_map:
                    element_map[source_id][field_name] = target_id
                    count += 1

            if count > 0:
                logger.debug(f"  {rel_type}: {count}个关系")
                total_references += count

        logger.info(f"🔗 重建了{total_references}个引用关系")

    def _rebuild_flow_relationships(self, session, element_map: Dict[str, Dict]):
        """重建流关系"""
        flow_patterns = [
            ('CONTROLFLOW_FROM', 'CONTROLFLOW_TO', 'source', 'target'),
            ('OBJECTFLOW_FROM', 'OBJECTFLOW_TO', 'source', 'target'),
            ('TRANSITION_FROM', 'TRANSITION_TO', 'source', 'target'),
            ('FLOWS_FROM', 'FLOWS_TO', 'source', 'target')
        ]

        total_flows = 0
        for from_rel, to_rel, from_field, to_field in flow_patterns:
            # 获取流的源和目标
            query = f"""
            MATCH (flow)-[:{from_rel}]->(source), (flow)-[:{to_rel}]->(target)
            WHERE flow.elementId IS NOT NULL
            AND source.elementId IS NOT NULL
            AND target.elementId IS NOT NULL
            RETURN flow.elementId as flowId, source.elementId as sourceId, target.elementId as targetId
            """

            result = session.run(query)
            count = 0

            for record in result:
                flow_id = record['flowId']
                source_id = record['sourceId']
                target_id = record['targetId']

                if flow_id in element_map:
                    element_map[flow_id][from_field] = source_id
                    element_map[flow_id][to_field] = target_id
                    count += 1

            if count > 0:
                logger.debug(f"  {from_rel}/{to_rel}: {count}个流关系")
                total_flows += count

        logger.info(f"🌊 重建了{total_flows}个流关系")

    def _rebuild_special_relationships(self, session, element_map: Dict[str, Dict]):
        """重建特殊关系"""
        special_patterns = [
            # Association的memberEnd关系
            ('MEMBER_END', 'memberEndIds', True),  # 多值关系
            # Message的事件关系
            ('HAS_SEND_EVENT', 'sendEvent'),
            ('HAS_RECEIVE_EVENT', 'receiveEvent'),
            # Lifeline的represents关系
            ('REPRESENTS', 'represents'),
            # 其他特殊关系
            ('COVERS_LIFELINE', 'coveredLifelines', True),  # 多值关系
            ('BELONGS_TO_MESSAGE', 'messageId'),
            ('REFERENCES_GUARDID', 'guardId')
        ]

        total_special = 0
        for rel_type, field_name, *is_multi in special_patterns:
            is_multi_value = len(is_multi) > 0 and is_multi[0]

            if is_multi_value:
                # 处理多值关系
                query = f"""
                MATCH (source)-[:{rel_type}]->(target)
                WHERE source.elementId IS NOT NULL AND target.elementId IS NOT NULL
                RETURN source.elementId as sourceId, collect(target.elementId) as targetIds
                """

                result = session.run(query)
                count = 0

                for record in result:
                    source_id = record['sourceId']
                    target_ids = record['targetIds']

                    if source_id in element_map and target_ids:
                        element_map[source_id][field_name] = target_ids
                        count += 1

            else:
                # 处理单值关系
                query = f"""
                MATCH (source)-[:{rel_type}]->(target)
                WHERE source.elementId IS NOT NULL AND target.elementId IS NOT NULL
                RETURN source.elementId as sourceId, target.elementId as targetId
                """

                result = session.run(query)
                count = 0

                for record in result:
                    source_id = record['sourceId']
                    target_id = record['targetId']

                    if source_id in element_map:
                        element_map[source_id][field_name] = target_id
                        count += 1

            if count > 0:
                logger.debug(f"  {rel_type}: {count}个特殊关系")
                total_special += count

        logger.info(f"⭐ 重建了{total_special}个特殊关系")

    def __enter__(self):
        return self
    
    def get_element_type_statistics(self) -> Dict[str, int]:
        """
        获取各种元素类型的统计信息

        返回:
            元素类型到数量的映射
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return {}

        try:
            with self._driver.session() as session:
                query = """
                MATCH (n:Element)
                WHERE n.elementId IS NOT NULL AND n.type IS NOT NULL
                RETURN n.type as elementType, count(n) as count
                ORDER BY count DESC
                """
                result = session.run(query)

                statistics = {}
                for record in result:
                    statistics[record['elementType']] = record['count']

                logger.info(f"元素类型统计: {statistics}")
                return statistics

        except Exception as e:
            logger.error(f"获取元素类型统计失败: {e}")
            return {}

    def _get_category_labels(self, element_type: str) -> List[str]:
        """
        根据元素类型获取简洁的分类标签

        参数:
            element_type: 元素类型

        返回:
            分类标签列表（简化版）
        """
        # 简化标签策略：只添加必要的分类标签
        category_labels = []

        # 只为特殊情况添加额外标签
        if element_type in {'FullPort', 'ProxyPort'}:
            category_labels.append('Port')
        elif element_type.endswith('Connector'):
            category_labels.append('Connector')

        return category_labels

    def get_all_labels(self) -> List[str]:
        """
        获取数据库中所有的标签

        返回:
            标签列表
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return []

        try:
            with self._driver.session() as session:
                query = "CALL db.labels()"
                result = session.run(query)

                labels = [record['label'] for record in result]
                logger.info(f"数据库中的所有标签: {labels}")
                return labels

        except Exception as e:
            logger.error(f"获取标签列表失败: {e}")
            return []

    def get_elements_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        按功能分类获取元素

        参数:
            category: 分类名称 (Structural, Behavioral, RequirementElement, Parametric)

        返回:
            元素列表
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return []

        try:
            with self._driver.session() as session:
                query = f"""
                MATCH (n:{category})
                WHERE n.elementId IS NOT NULL
                RETURN n.elementId as id, n.type as type, n.name as name
                ORDER BY n.type, n.name
                """
                result = session.run(query)

                elements = []
                for record in result:
                    elements.append({
                        'id': record['id'],
                        'type': record['type'],
                        'name': record['name']
                    })

                logger.info(f"找到 {len(elements)} 个 {category} 类型的元素")
                return elements

        except Exception as e:
            logger.error(f"按分类查询元素失败: {e}")
            return []

    def get_category_statistics(self) -> Dict[str, int]:
        """
        获取各个功能分类的统计信息

        返回:
            分类到数量的映射
        """
        if not self._driver:
            logger.error("Neo4j驱动未初始化")
            return {}

        try:
            with self._driver.session() as session:
                categories = ['Structural', 'Behavioral', 'RequirementElement', 'Parametric']
                statistics = {}

                for category in categories:
                    query = f"MATCH (n:{category}) RETURN count(n) as count"
                    result = session.run(query)
                    record = result.single()
                    if record:
                        statistics[category] = record['count']
                    else:
                        statistics[category] = 0

                logger.info(f"分类统计: {statistics}")
                return statistics

        except Exception as e:
            logger.error(f"获取分类统计失败: {e}")
            return {}

    def _process_connector_end(self, element_id: str, field_name: str, field_value: dict,
                              elements_by_id: Dict[str, Any], all_relationships: list, source_name: str):
        """处理连接器端点的复杂对象字段"""
        # 提取端点中的ID引用
        for key, value in field_value.items():
            if key.endswith('Id') and isinstance(value, str) and value and value in elements_by_id:
                # 根据字段名确定关系类型
                if key == 'partRefId':
                    rel_type = f'CONNECTS_TO_PART_{field_name.upper()}'
                elif key == 'portRefId':
                    rel_type = f'CONNECTS_TO_PORT_{field_name.upper()}'
                elif key == 'propertyRefId':
                    rel_type = f'CONNECTS_TO_PROPERTY_{field_name.upper()}'
                else:
                    rel_type = f'CONNECTS_TO_{key.upper()}_{field_name.upper()}'

                all_relationships.append({
                    'fromId': element_id,
                    'toId': value,
                    'relType': rel_type,
                    'source': source_name,
                    'originalField': f'{field_name}.{key}'
                })

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
