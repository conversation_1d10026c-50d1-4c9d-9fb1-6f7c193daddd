"""
JSON到XML转换Agent - 负责将合并后的SysML JSON数据转换为标准的SysML XMI文件
"""
import json
import xml.etree.ElementTree as ET
from xml.dom import minidom
import traceback
import logging
from collections import defaultdict
from typing import Dict, Any,  Optional, Tuple
from pathlib import Path

from src.graph.workflow_state import WorkflowState, ProcessStatus

logger = logging.getLogger(__name__)


class EnhancedSysMLGenerator:
    """
    增强版SysML生成器，专门用于处理合并后的JSON数据并转换为XMI格式
    
    特点：
    - 支持合并后的JSON数据结构（包含sources, source, originalData等字段）
    - 改进的错误处理和日志记录
    - 更好的数据清理和标准化
    - 增强的关系处理逻辑
    """
    
    NAMESPACES = {
        "xmi": "http://www.omg.org/spec/XMI/20131001",
        "uml": "http://www.omg.org/spec/UML/20131001",
        "sysml": "http://www.omg.org/spec/SysML/20181001/SysML",
        "StandardProfile": "http://www.omg.org/spec/UML/20131001/StandardProfile",
        "MagicDraw_Profile": "http://www.omg.org/spec/UML/20131001/MagicDrawProfile",
        "MD_Customization_for_SysML__additional_stereotypes": "http://www.magicdraw.com/spec/Customization/180/SysML",
        "DSL_Customization": "http://www.magicdraw.com/schemas/DSL_Customization.xmi",
        "diagram": "http://www.nomagic.com/ns/magicdraw/core/diagram/1.0"
    }
    
    PRIMITIVE_TYPE_MAP = {
        "Real": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real",
        "Integer": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Integer",
        "String": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.String",
        "Boolean": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Boolean",
        "VerdictKind": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"
    }

    def __init__(self, json_data):
        """
        初始化增强版SysML生成器
        
        参数:
            json_data: JSON数据（字符串或字典）
        """
        # 支持字符串或字典输入
        if isinstance(json_data, str):
            self.json_data = json.loads(json_data)
        elif isinstance(json_data, dict):
            self.json_data = json_data
        else:
            raise ValueError("输入必须是JSON字符串或字典")
            
        self.elements_by_id = {}
        self.children_by_parent = defaultdict(list)
        self.xml_elements = {}
        self.stereotypes_to_apply = defaultdict(list)
        self.lifelines_to_update = defaultdict(list)
        
        self.nested_connector_ends_to_apply = []
        self.nested_connector_ends_to_stereotype = []  # 新增：用于NestedConnectorEnd构造型
        self.associations_to_apply = []
        self.diagrams_to_apply = []

        self.model_id = None
        self.model_name = None
        self.xmi_root = None
        
        # 增强功能：统计和日志
        self.processing_stats = {
            'total_elements': 0,
            'processed_elements': 0,
            'skipped_elements': 0,
            'errors': []
        }

    def _clean_element_data(self, elem_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        清理和标准化元素数据
        
        参数:
            elem_data: 原始元素数据
            
        返回:
            清理后的元素数据，如果数据无效则返回None
        """
        try:
            # 必须有id和type
            if not elem_data.get("id") or not elem_data.get("type"):
                logger.warning(f"元素缺少必要的id或type字段: {elem_data}")
                return None
            
            # 创建清理后的副本
            cleaned = {
                "id": elem_data["id"],
                "type": elem_data["type"]
            }
            
            # 复制标准字段
            standard_fields = [
                "name", "parentId", "visibility", "isAbstract", "typeId",
                "multiplicity", "aggregation", "associationId", "memberEndIds",
                "sourceId", "targetId", "requirementId", "testCaseId", "blockId",
                "derivedRequirementId", "sourceRequirementId", "reqId", "text",
                "end1", "end2", "propertyKind", "specification",  # 添加连接器和其他重要字段
                "reqId", "text", "verdict",  # 添加需求图相关字段
                "guard", "behavior", "nodeIds", "representsId",  # 添加活动图相关字段
                "signalId", "kind", "triggerIds", "effect",  # 添加状态机图相关字段
                "entry", "exit", "doActivity", "classifierBehaviorId",  # 添加状态机行为相关字段
                "sendEventId", "receiveEventId", "messageSort", "signatureId", "arguments",  # 添加序列图Message相关字段
                "coveredId", "messageId", "interactionOperator", "coveredLifelineIds", "specification"  # 添加序列图其他相关字段
            ]
            
            for field in standard_fields:
                if field in elem_data and elem_data[field] is not None:
                    cleaned[field] = elem_data[field]
            
            # 处理特殊字段
            if "sources" in elem_data:
                cleaned["_sources"] = elem_data["sources"]
            if "source" in elem_data:
                cleaned["_source"] = elem_data["source"]
            if "originalData" in elem_data:
                cleaned["_originalData"] = elem_data["originalData"]
                
            return cleaned
            
        except Exception as e:
            logger.error(f"清理元素数据时出错: {e}, 元素: {elem_data}")
            return None

    def _generate_id(self, base_id: str, suffix: str) -> str:
        """生成唯一ID"""
        clean_base = str(base_id).replace("-", "_")
        clean_suffix = str(suffix).replace("-", "_").replace(":", "_")
        return f"_{clean_base}_{clean_suffix}"

    def _clean_xml_attributes(self, attrs: Dict[str, Any]) -> Dict[str, str]:
        """清理XML属性，确保没有None值或空值"""
        cleaned = {}
        for key, value in attrs.items():
            if value is not None and str(value).strip() != "":
                cleaned[key] = str(value)
        return cleaned

    def _safe_create_element(self, parent, tag, attrs=None):
        """安全创建XML元素，自动清理属性"""
        if attrs is None:
            attrs = {}
        cleaned_attrs = self._clean_xml_attributes(attrs)
        return ET.SubElement(parent, tag, cleaned_attrs)

    def generate_xmi(self) -> Optional[str]:
        """
        执行从JSON到XMI的完整转换流程
        
        返回:
            生成的XMI字符串，如果失败则返回None
        """
        try:
            logger.info("开始JSON到XMI转换流程")
            self._preprocess_data()
            self._create_base_structure()
            self._post_process_structure()
            self._apply_stereotypes()
            result = self._prettify_xml()
            
            logger.info(f"XMI转换完成。处理统计: {self.processing_stats}")
            return result
            
        except Exception as e:
            error_msg = f"生成XMI过程中发生严重错误: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            self.processing_stats['errors'].append(error_msg)
            return None

    def _preprocess_data(self):
        """预处理JSON数据"""
        logger.info("--- 阶段 1: 预处理JSON数据 ---")
        
        if "elements" not in self.json_data:
            raise ValueError("错误: JSON数据必须包含 'elements' 键。")

        # 清理和标准化元素数据
        cleaned_elements = []
        for elem in self.json_data["elements"]:
            cleaned_elem = self._clean_element_data(elem)
            if cleaned_elem:
                cleaned_elements.append(cleaned_elem)
            else:
                self.processing_stats['skipped_elements'] += 1

        self.elements_by_id = {elem["id"]: elem for elem in cleaned_elements}
        self.processing_stats['total_elements'] = len(self.elements_by_id)
        
        # 处理模型信息
        model_value = self.json_data.get("model")
        model_data = {}
        if isinstance(model_value, list):
            if model_value: 
                model_data = model_value[0]
        elif isinstance(model_value, dict):
            model_data = model_value
            
        self.model_id = model_data.get("id", "unified-sysml-model")
        self.model_name = model_data.get("name", "Unified SysML Model")
        
        # 确保模型数据在元素字典中
        if self.model_id not in self.elements_by_id:
            model_data["type"] = "Model"
            self.elements_by_id[self.model_id] = model_data

        # 建立父子关系和分类处理
        for elem_id, elem_data in self.elements_by_id.items():
            try:
                parent_id = elem_data.get("parentId")
                if parent_id:
                    # 修复父ID不匹配的问题：如果父ID不存在，但是是模型相关的，则使用当前模型ID
                    if parent_id not in self.elements_by_id:
                        # 检查是否是模型相关的父ID（通常包含"model"关键字）
                        if "model" in parent_id.lower():
                            logger.info(f"修复元素 {elem_id} 的父ID从 {parent_id} 到 {self.model_id}")
                            parent_id = self.model_id
                            elem_data["parentId"] = parent_id  # 更新元素数据

                    self.children_by_parent[parent_id].append(elem_id)

                elem_type = elem_data.get("type")
                stereotype_category = self._get_stereotype_category(elem_type, elem_data)
                if stereotype_category:
                    self.stereotypes_to_apply[stereotype_category].append(elem_data)
                    
                # 识别需要后处理的元素
                if elem_type == "Association": 
                    self.associations_to_apply.append(elem_data)
                if elem_type == "Diagram": 
                    self.diagrams_to_apply.append(elem_data)
                    
                self.processing_stats['processed_elements'] += 1
                
            except Exception as e:
                error_msg = f"处理元素 {elem_id} 时出错: {str(e)}"
                logger.warning(error_msg)
                self.processing_stats['errors'].append(error_msg)
            
        logger.info(f"数据预处理完成。共找到 {self.processing_stats['total_elements']} 个元素，"
                   f"成功处理 {self.processing_stats['processed_elements']} 个，"
                   f"跳过 {self.processing_stats['skipped_elements']} 个。")

    def _get_stereotype_category(self, elem_type: str, elem_data: Dict[str, Any]) -> Optional[str]:
        """获取元素的构造型分类"""
        category_map = {
            "Requirement": "Requirement", "Block": "Block", "TestCase": "TestCase",
            "ValueType": "ValueType", "Unit": "Unit", "ConstraintBlock": "ConstraintBlock",
            "InterfaceBlock": "InterfaceBlock", "FullPort": "FullPort", "ProxyPort": "ProxyPort",
            "ConstraintParameter": "ConstraintParameter", "ActivityPartition": "ActivityPartition",
            "BindingConnector": "BindingConnector", "DeriveReqt": "Abstraction",
            "Satisfy": "Abstraction", "Verify": "Abstraction"
        }

        if elem_type in category_map:
            return category_map[elem_type]
        if elem_type == "Property" and "propertyKind" in elem_data:
            return "Property"
        return None

    def _create_base_structure(self):
        """创建XMI根、模型并递归创建所有元素"""
        logger.info("--- 阶段 2: 创建完整的UML结构 ---")

        self.xmi_root = ET.Element("xmi:XMI", attrib={"xmi:version": "2.5"})
        for prefix, uri in self.NAMESPACES.items():
            self.xmi_root.set(f"xmlns:{prefix}", uri)

        model_attrs = {"xmi:type": "uml:Model", "xmi:id": self.model_id, "name": self.model_name}
        model_xml = self._safe_create_element(self.xmi_root, "uml:Model", model_attrs)
        self.xml_elements[self.model_id] = model_xml

        self._create_element_recursive(self.model_id, model_xml)

    def _create_element_recursive(self, parent_id: str, parent_xml_node):
        """递归地为给定的父节点创建所有子元素"""
        for child_id in sorted(self.children_by_parent.get(parent_id, [])):
            if child_id in self.xml_elements:
                continue
            child_data = self.elements_by_id.get(child_id)
            if not child_data:
                continue

            elem_type = child_data.get("type")

            # 在主创建阶段跳过关联、用例图关系和状态机子元素，留给后处理
            if elem_type in ["Association", "Include", "Extend", "Generalization"]:
                continue

            # 跳过状态机的子元素，它们将在StateMachine和Region的特殊处理中创建
            parent_data = self.elements_by_id.get(parent_id)
            if parent_data and parent_data.get("type") in ["StateMachine", "Region"]:
                if elem_type in ["Region", "State", "Pseudostate", "Transition"]:
                    continue

            elem_name = child_data.get("name")
            attrs = {"xmi:id": child_id}
            if elem_name:
                attrs["name"] = elem_name

            tag_name, xmi_type = self._determine_tag_and_type(child_data)
            if not tag_name:
                continue

            attrs["xmi:type"] = xmi_type

            current_parent_xml = parent_xml_node
            # 用例图关系元素需要在源元素下创建 (基于uc_func.py)
            if elem_type in ["Include", "Extend", "Generalization"]:
                source_id = child_data.get("sourceId")
                if source_id and source_id in self.xml_elements:
                    current_parent_xml = self.xml_elements[source_id]
                else:
                    logger.warning(f"关系 '{child_id}' 的源节点 '{source_id}' 在创建时未找到。跳过此关系。")
                    continue

            # 创建XML元素
            xml_elem = self._safe_create_element(current_parent_xml, tag_name, attrs)
            self.xml_elements[child_id] = xml_elem
            self._populate_element_details(xml_elem, child_data)

            # 特殊处理：用例图关系元素需要特殊的父子关系 (基于uc_func.py)
            if elem_type in ["Include", "Extend", "Generalization"]:
                # 这些关系元素已经在正确的父元素下创建，不需要递归处理子元素
                pass
            elif elem_type == "Activity":
                # 如果是Activity，立即处理其内部内容
                self._populate_activity_content(xml_elem, child_data)
            elif elem_type == "StateMachine":
                # StateMachine需要特殊处理其Region子元素 (基于stm_func.py第192-196行)
                self._create_statemachine_content(xml_elem, child_data)
            elif elem_type == "Region":
                # Region需要特殊处理其子元素顺序 (基于stm_func.py第204-215行)
                self._create_region_content(xml_elem, child_data)
            elif elem_type == "Interaction":
                # Interaction需要特殊处理其子元素顺序 (基于sd_func.py第117-118行)
                self._create_interaction_content(xml_elem, child_data)
            else:
                # 递归处理子元素
                self._create_element_recursive(child_id, xml_elem)

    def _determine_tag_and_type(self, elem_data: Dict[str, Any]) -> Tuple[Optional[str], Optional[str]]:
        """确定XML标签名和XMI类型 - 基于参考文件的完整实现"""
        elem_type = elem_data.get("type")
        parent_type = self.elements_by_id.get(elem_data.get("parentId"), {}).get("type")
        tag, xtype = 'packagedElement', f"uml:{elem_type}"

        # === 用例图元素 (基于uc_func.py) ===
        if elem_type == "Actor":
            xtype = "uml:Actor"
        elif elem_type == "UseCase":
            xtype = "uml:UseCase"
        elif elem_type == "Include":
            tag = 'include'; xtype = "uml:Include"
        elif elem_type == "Extend":
            tag = 'extend'; xtype = "uml:Extend"
        elif elem_type == "Generalization":
            tag = 'generalization'; xtype = "uml:Generalization"
        elif elem_type == "Association":
            tag = 'packagedElement'; xtype = "uml:Association"

        # === 状态机图元素 (基于stm_func.py) ===
        elif elem_type == "StateMachine":
            tag = 'ownedBehavior' if parent_type == "Block" else 'packagedElement'
        elif elem_type == "Region":
            tag = 'region'
        elif elem_type == "State":
            tag = 'subvertex'
        elif elem_type == "FinalState":
            tag = 'subvertex'; xtype = "uml:FinalState"
        elif elem_type == "Pseudostate":
            tag = 'connectionPoint' if parent_type == "State" else 'subvertex'
            # Pseudostate需要特殊的xmi:type处理 (基于stm_func.py第266-276行)
            kind = elem_data.get("kind")
            if kind == "initial":
                xtype = "uml:Pseudostate"  # kind="initial"通常是隐式的
            elif kind in ["finalState", "final"]:
                xtype = "uml:FinalState"
            elif kind in ["choice", "junction", "entryPoint", "exitPoint", "fork", "join", "deepHistory", "shallowHistory"]:
                xtype = "uml:Pseudostate"
            else:
                xtype = "uml:Pseudostate"
        elif elem_type == "Transition":
            tag = 'transition'
        elif elem_type == "Signal":
            xtype = "uml:Signal"
        elif elem_type == "SignalEvent":
            xtype = "uml:SignalEvent"
        elif elem_type == "Event":
            xtype = "uml:AnyReceiveEvent"  # 默认事件类型

        # === BDD/IBD图元素 (基于bdd_and_ibd_func.py) ===
        elif elem_type in ["Block", "Requirement", "ConstraintBlock", "InterfaceBlock"]:
            xtype = "uml:Class"
        elif elem_type == "ValueType":
            xtype = "uml:DataType"
        elif elem_type == "Unit":
            xtype = "uml:InstanceSpecification"
        elif elem_type == "Enumeration":
            xtype = "uml:Enumeration"
        elif elem_type == "Property":
            tag = 'ownedAttribute'; xtype = "uml:Property"
        elif elem_type in ["FullPort", "ProxyPort", "Port"]:
            tag = 'ownedAttribute'; xtype = "uml:Port"
        elif elem_type == "ConstraintParameter":
            tag = 'ownedAttribute'; xtype = "uml:Port"
        elif elem_type == "Operation":
            tag = 'ownedOperation'
        elif elem_type == "Reception":
            tag = 'ownedReception'
        elif elem_type == "Parameter":
            tag = 'ownedParameter'
        elif elem_type == "EnumerationLiteral":
            tag = 'ownedLiteral'
        elif elem_type in ["AssemblyConnector", "BindingConnector"]:
            tag = 'ownedConnector'; xtype = "uml:Connector"

        # === 序列图元素 (基于sd_func.py) ===
        elif elem_type == "Interaction":
            tag = 'ownedBehavior' if parent_type in ["Block", "Class"] else 'packagedElement'
        elif elem_type == "Lifeline":
            tag = 'lifeline'
        elif elem_type == "Message":
            tag = 'message'
        elif elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification"]:
            tag = 'fragment'
        elif elem_type == "CombinedFragment":
            tag = 'fragment'
        elif elem_type == "InteractionOperand":
            tag = 'operand'
        elif elem_type == "InteractionConstraint":
            tag = 'guard'

        # === 活动图元素 (基于act_func.py) ===
        elif elem_type == "TestCase":
            xtype = "uml:Activity"
        elif elem_type in ["InitialNode", "ActivityFinalNode", "FlowFinalNode", "DecisionNode", "MergeNode", "ForkNode", "JoinNode", "CallBehaviorAction", "CentralBufferNode", "ActivityParameterNode"]:
            tag = 'node'
        elif elem_type in ["InputPin", "OutputPin"]:
            tag = 'argument' if elem_type == 'InputPin' else 'result'
        elif elem_type in ["ControlFlow", "ObjectFlow"]:
            tag = 'edge'
        elif elem_type == "ActivityPartition":
            tag = 'group'

        # === 需求图元素 ===
        elif elem_type in ["DeriveReqt", "Satisfy", "Verify"]:
            xtype = "uml:Abstraction"

        # === 图表元素 ===
        elif elem_type == "Diagram":
            return None, None

        return tag, xtype

    def _populate_element_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充元素的详细信息"""
        elem_id, elem_type = elem_data.get("id"), elem_data.get("type")

        if elem_type == "Property":
            # 基于par_func.py的逻辑处理Property
            prop_kind = elem_data.get("propertyKind")

            # 设置aggregation（基于par_func.py）
            xml_elem.set("aggregation", "composite")

            # 根据propertyKind设置visibility
            if prop_kind == "constraint":
                xml_elem.set("visibility", "private")

            # 处理关联引用
            if "associationId" in elem_data and elem_data["associationId"]:
                xml_elem.set("association", elem_data["associationId"])

            self._add_multiplicity(elem_data, xml_elem)
        elif elem_type in ["FullPort", "ProxyPort"]:
            xml_elem.set("aggregation", "composite")
            if elem_data.get("isBehavior"):
                xml_elem.set("isBehavior", str(elem_data["isBehavior"]).lower())
            self._add_multiplicity(elem_data, xml_elem)
        elif elem_type == "ConstraintParameter":
            # 基于par_func.py的逻辑处理ConstraintParameter
            xml_elem.set("visibility", "private")
            xml_elem.set("aggregation", "composite")
            self._add_multiplicity(elem_data, xml_elem)
        elif elem_type == "Operation":
            # 处理操作参数
            for param_data in elem_data.get("parameters", []):
                param_attrs = {
                    "xmi:type": "uml:Parameter",
                    "xmi:id": param_data["id"],
                    "name": param_data["name"],
                    "direction": param_data.get("direction", "in")
                }
                param_xml = ET.SubElement(xml_elem, "ownedParameter", param_attrs)
                self._add_type_ref_to_element(param_xml, param_data.get("typeId"))
        elif elem_type == "Reception":
            if "signalId" in elem_data:
                xml_elem.set("signal", elem_data["signalId"])
        elif elem_type == "TestCase":
            # TestCase需要特殊的内部结构
            self._populate_testcase_details(xml_elem, elem_data)
        # === 用例图关系处理 (基于uc_func.py) ===
        elif elem_type == "Generalization":
            if "targetId" in elem_data:
                xml_elem.set("general", elem_data["targetId"])
        elif elem_type == "Include":
            if "targetId" in elem_data:
                xml_elem.set("addition", elem_data["targetId"])
        elif elem_type == "Extend":
            if "targetId" in elem_data:
                xml_elem.set("extendedCase", elem_data["targetId"])
        elif elem_type in ["DeriveReqt", "Satisfy", "Verify"]:
            client_map = {"DeriveReqt": "derivedRequirementId", "Satisfy": "blockId", "Verify": "testCaseId"}
            supplier_map = {"DeriveReqt": "sourceRequirementId", "Satisfy": "requirementId", "Verify": "requirementId"}
            if elem_data.get(client_map.get(elem_type)):
                ET.SubElement(xml_elem, "client", {"xmi:idref": elem_data.get(client_map.get(elem_type))})
            if elem_data.get(supplier_map.get(elem_type)):
                ET.SubElement(xml_elem, "supplier", {"xmi:idref": elem_data.get(supplier_map.get(elem_type))})
        elif elem_type in ["AssemblyConnector", "BindingConnector"]:
            self._populate_connector_details(xml_elem, elem_data)
        elif elem_type == "Lifeline":
            if "representsId" in elem_data:
                xml_elem.set("represents", elem_data["representsId"])
        elif elem_type == "Message":
            self._populate_message_details(xml_elem, elem_data)
        elif elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification"]:
            self._populate_occurrence_specification_details(xml_elem, elem_data)
        elif elem_type == "CombinedFragment":
            self._populate_combined_fragment_details(xml_elem, elem_data)
        elif elem_type == "InteractionOperand":
            if "guardId" in elem_data and elem_data["guardId"]:
                xml_elem.set("guard", elem_data["guardId"])
        elif elem_type == "InteractionConstraint":
            self._populate_interaction_constraint_details(xml_elem, elem_data)
        elif elem_type == "State":
            self._populate_state_details(xml_elem, elem_data)
        elif elem_type == "Transition":
            self._populate_transition_details(xml_elem, elem_data)
        elif elem_type == "Pseudostate":
            if "kind" in elem_data:
                xml_elem.set("kind", elem_data["kind"])
        elif elem_type == "SignalEvent":
            logger.debug(f"处理SignalEvent {elem_data['id']}, 数据: {elem_data}")
            if "signalId" in elem_data:
                logger.debug(f"设置SignalEvent {elem_data['id']} 的signal属性为: {elem_data['signalId']}")
                xml_elem.set("signal", elem_data["signalId"])
            else:
                logger.debug(f"SignalEvent {elem_data['id']} 缺少signalId属性")

        # === 状态机图元素处理 (基于stm_func.py) ===
        elif elem_type == "StateMachine":
            # StateMachine的特殊处理已在_determine_tag_and_type中处理
            pass
        elif elem_type == "Region":
            # Region的visibility默认为public (基于stm_func.py第200行)
            xml_elem.set("visibility", "public")
        elif elem_type == "State":
            # State的visibility默认为public (基于stm_func.py第220行)
            xml_elem.set("visibility", "public")
            self._populate_state_behaviors(xml_elem, elem_data)
        elif elem_type == "Pseudostate":
            # Pseudostate的visibility默认为public (基于stm_func.py第256行)
            xml_elem.set("visibility", "public")
            kind = elem_data.get("kind")
            if kind and kind not in ["initial", "finalState", "final"]:
                # 只有非initial和非final的Pseudostate才需要显式设置kind属性
                xml_elem.set("kind", kind)
        elif elem_type == "Transition":
            # Transition的visibility默认为public (基于stm_func.py第285行)
            xml_elem.set("visibility", "public")
            self._populate_transition_details(xml_elem, elem_data)
        elif elem_type == "Signal":
            # Signal元素的基本处理
            pass
        elif elem_type == "ConstraintBlock":
            # 处理ConstraintBlock的约束规则（基于par_func.py第101-107行）
            if "specification" in elem_data:
                self._populate_constraint_block_details(xml_elem, elem_data)
        elif elem_type == "Property":
            # 基于bdd_and_ibd_func.py第184-206行的Property处理
            self._populate_property_details(xml_elem, elem_data)
        elif elem_type in ["FullPort", "ProxyPort", "FlowPort"]:
            # 基于bdd_and_ibd_func.py第208-217行的Port处理
            self._populate_port_details(xml_elem, elem_data)
        elif elem_type in ["AssemblyConnector", "BindingConnector"]:
            # 基于bdd_and_ibd_func.py第261-297行的Connector处理
            self._populate_connector_details(xml_elem, elem_data)
        elif elem_type == "Interaction":
            # 基于sd_func.py第117-118行的Interaction处理
            self._populate_interaction_details(xml_elem, elem_data)
        elif elem_type == "Lifeline":
            # 基于sd_func.py第154-155行的Lifeline处理
            self._populate_lifeline_details(xml_elem, elem_data)
        elif elem_type == "Message":
            # 基于sd_func.py第156-166行的Message处理
            self._populate_message_details(xml_elem, elem_data)
        elif elem_type in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification"]:
            # 基于sd_func.py第167-170行的MessageOccurrenceSpecification处理
            self._populate_message_occurrence_details(xml_elem, elem_data)
        elif elem_type == "CombinedFragment":
            # 基于sd_func.py第171-173行的CombinedFragment处理
            self._populate_combined_fragment_details(xml_elem, elem_data)
        elif elem_type == "InteractionConstraint":
            # 基于sd_func.py第174-181行的InteractionConstraint处理
            self._populate_interaction_constraint_details(xml_elem, elem_data)

        # 通用属性填充
        if "visibility" in elem_data and elem_data["visibility"] is not None:
            xml_elem.set("visibility", elem_data["visibility"])
        if "isAbstract" in elem_data and elem_data["isAbstract"] is not None:
            xml_elem.set("isAbstract", str(elem_data["isAbstract"]).lower())
        if "typeId" in elem_data and elem_data["typeId"] is not None:
            self._add_type_ref_to_element(xml_elem, elem_data["typeId"])

    def _populate_testcase_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充TestCase的特殊内部结构 - 基于req_func.py的实现"""
        elem_id = elem_data["id"]

        # 1. 创建ownedParameter for verdict (按照req_func.py第144-157行的逻辑)
        verdict_param_id = self._generate_internal_id(elem_id, "verdict_param")
        owned_param_attrs = {
            "xmi:type": "uml:Parameter",
            "xmi:id": verdict_param_id,
            "name": "verdict",
            "visibility": "public",
            "direction": "return"
        }
        owned_param_elem = ET.SubElement(xml_elem, "ownedParameter", owned_param_attrs)

        # 1a. 添加ownedParameter的type
        ET.SubElement(owned_param_elem, "type", {
            "href": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"
        })

        # 2. 创建node (ActivityParameterNode) for verdict (按照req_func.py第172-185行的逻辑)
        verdict_node_id = self._generate_internal_id(elem_id, "verdict_node")
        activity_node_attrs = {
            "xmi:type": "uml:ActivityParameterNode",
            "xmi:id": verdict_node_id,
            "name": "verdict",
            "visibility": "public",
            "parameter": verdict_param_id  # 引用上面创建的ownedParameter
        }
        activity_node_elem = ET.SubElement(xml_elem, "node", activity_node_attrs)

        # 2a. 添加ActivityParameterNode的type
        ET.SubElement(activity_node_elem, "type", {
            "href": "http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.VerdictKind"
        })

    def _generate_internal_id(self, base_id: str, suffix: str) -> str:
        """为TestCase内部元素生成ID (确保与主元素ID不同但相关) - 基于req_func.py"""
        clean_base = str(base_id).replace("-", "_")
        clean_suffix = str(suffix).replace("-", "_")
        return f"_{clean_base}_internal_{clean_suffix}"

    def _generate_stereotype_id(self, base_id: str, suffix: str = "application") -> str:
        """生成构造型ID - 基于req_func.py的逻辑"""
        clean_base = str(base_id).replace("-", "_")
        clean_suffix = str(suffix).replace("-", "_")

        # 特殊处理Requirement的后缀（以_结尾）
        if base_id.startswith("_") and suffix == "_":
            return f"{base_id}_"
        elif base_id.startswith("_") and suffix.endswith("_"):
            return f"{base_id}{suffix}"
        elif base_id.startswith("_") and suffix == "application":
            return f"{base_id}_application"

        return f"_{clean_base}_{clean_suffix}"

    def _populate_connector_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充连接器的端点详细信息 - 基于par_func.py的逻辑"""
        elem_id = elem_data["id"]
        elem_type = elem_data.get("type")

        # 设置visibility（基于par_func.py）
        xml_elem.set("visibility", "public")

        # 处理end1和end2
        end1_data = elem_data.get("end1")
        end2_data = elem_data.get("end2")

        if end1_data:
            # 生成end1的ID（基于par_func.py的格式）
            end1_id = f"{elem_id}_end1"
            end1_attrs = {"xmi:type": "uml:ConnectorEnd", "xmi:id": end1_id}

            # end1通常只有propertyRefId（基于par_func.py第147行）
            if "propertyRefId" in end1_data:
                end1_attrs["role"] = end1_data["propertyRefId"]

            ET.SubElement(xml_elem, "end", end1_attrs)

        if end2_data:
            # 生成end2的ID（基于par_func.py的格式）
            end2_id = f"{elem_id}_end2"
            end2_attrs = {"xmi:type": "uml:ConnectorEnd", "xmi:id": end2_id}

            # end2有partWithPort和role（基于par_func.py第148行）
            if "partRefId" in end2_data:
                end2_attrs["partWithPort"] = end2_data["partRefId"]
            if "portRefId" in end2_data:
                end2_attrs["role"] = end2_data["portRefId"]

            ET.SubElement(xml_elem, "end", end2_attrs)

            # 记录需要应用NestedConnectorEnd构造型（仅对end2，基于par_func.py第169行）
            if elem_type == "BindingConnector" and "partRefId" in end2_data:
                self.nested_connector_ends_to_apply.append((end2_id, end2_data["partRefId"]))

    def _populate_message_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充Message的详细信息"""
        if "sendEventId" in elem_data:
            xml_elem.set("sendEvent", elem_data["sendEventId"])
        if "receiveEventId" in elem_data:
            xml_elem.set("receiveEvent", elem_data["receiveEventId"])
        if "messageSort" in elem_data:
            xml_elem.set("messageSort", elem_data["messageSort"])
        if "signatureId" in elem_data:
            xml_elem.set("signature", elem_data["signatureId"])

        # 处理参数
        for arg_data in elem_data.get("arguments", []):
            arg_attrs = {"xmi:type": "uml:OpaqueExpression"}
            if "id" in arg_data:
                arg_attrs["xmi:id"] = arg_data["id"]
            arg_elem = ET.SubElement(xml_elem, "argument", arg_attrs)
            if "body" in arg_data:
                ET.SubElement(arg_elem, "body").text = str(arg_data["body"])
            if "language" in arg_data:
                ET.SubElement(arg_elem, "language").text = arg_data["language"]

    def _populate_occurrence_specification_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充事件发生规范的详细信息"""
        covered_id = elem_data.get("coveredId")
        if covered_id:
            xml_elem.set("covered", covered_id)
            self.lifelines_to_update[covered_id].append(elem_data["id"])

        if elem_data.get("type") == "MessageOccurrenceSpecification" and "messageId" in elem_data:
            xml_elem.set("message", elem_data["messageId"])

    def _populate_combined_fragment_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充组合片段的详细信息"""
        if "interactionOperator" in elem_data:
            xml_elem.set("interactionOperator", elem_data["interactionOperator"])

        for ll_id in elem_data.get("coveredLifelineIds", []):
            ET.SubElement(xml_elem, "covered", {"xmi:idref": ll_id})
            self.lifelines_to_update[ll_id].append(elem_data["id"])

    def _populate_interaction_constraint_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充交互约束的详细信息"""
        spec_data = elem_data.get("specification")
        if isinstance(spec_data, dict):
            spec_attrs = {"xmi:type": "uml:OpaqueExpression"}
            if "id" in spec_data:
                spec_attrs["xmi:id"] = spec_data["id"]
            spec_elem = ET.SubElement(xml_elem, "specification", spec_attrs)
            if "body" in spec_data:
                ET.SubElement(spec_elem, "body").text = spec_data["body"]
            if "language" in spec_data:
                ET.SubElement(spec_elem, "language").text = spec_data["language"]

    def _populate_state_behaviors(self, xml_elem, elem_data: Dict[str, Any]):
        """填充状态的行为详细信息 - 基于stm_func.py"""
        elem_id = elem_data["id"]
        elem_name = elem_data.get("name", elem_id)

        # 处理状态行为（entry, exit, doActivity）- 基于stm_func.py第229-234行
        for behavior_type in ["entry", "exit", "doActivity"]:
            if behavior_type in elem_data and elem_data[behavior_type]:
                self._create_behavior_activity_stm(xml_elem, behavior_type, elem_data[behavior_type], elem_name)

    def _create_behavior_activity_stm(self, parent_xml_element, behavior_type_tag: str, behavior_json_data: Dict[str, Any], owner_name: str = ""):
        """
        创建状态机的行为活动 - 基于stm_func.py的create_behavior_activity_xml函数
        behavior_type_tag: "entry", "exit", "doActivity", "effect"
        behavior_json_data: 行为的JSON数据，包含wrapperActivityId和calledBehaviorId
        owner_name: 拥有此行为的状态或转换的名称
        """
        if not behavior_json_data or "wrapperActivityId" not in behavior_json_data:
            logger.warning(f"行为 {behavior_type_tag} for {owner_name} 缺少 wrapperActivityId 或数据。")
            return None

        wrapper_activity_id = behavior_json_data["wrapperActivityId"]
        called_behavior_id = behavior_json_data.get("calledBehaviorId")

        activity_attrs = {"xmi:type": "uml:Activity", "xmi:id": wrapper_activity_id}

        # 设置包装活动的名称为被调用行为的名称 (基于stm_func.py第98-104行)
        activity_name_to_set = None
        if called_behavior_id and called_behavior_id in self.elements_by_id:
            called_behavior_data = self.elements_by_id[called_behavior_id]
            activity_name_to_set = called_behavior_data.get("name")

        if activity_name_to_set:
            activity_attrs["name"] = activity_name_to_set

        activity_elem = ET.SubElement(parent_xml_element, behavior_type_tag, activity_attrs)

        if called_behavior_id:
            # 创建内部活动结构 (基于stm_func.py第113-129行)
            initial_node_id = wrapper_activity_id + "_initial"
            cba_id = wrapper_activity_id + "_cba"
            final_node_id = wrapper_activity_id + "_final"
            cf1_id = wrapper_activity_id + "_cf1"
            cf2_id = wrapper_activity_id + "_cf2"

            ET.SubElement(activity_elem, "node", {"xmi:type": "uml:InitialNode", "xmi:id": initial_node_id, "visibility": "public"})

            # CallBehaviorAction也可以有名称 (基于stm_func.py第122-125行)
            cba_attrs = {"xmi:type": "uml:CallBehaviorAction", "xmi:id": cba_id, "behavior": called_behavior_id, "visibility": "public"}
            if activity_name_to_set:
                cba_attrs["name"] = f"Call_{activity_name_to_set}"
            ET.SubElement(activity_elem, "node", cba_attrs)

            ET.SubElement(activity_elem, "node", {"xmi:type": "uml:ActivityFinalNode", "xmi:id": final_node_id, "visibility": "public"})
            ET.SubElement(activity_elem, "edge", {"xmi:type": "uml:ControlFlow", "xmi:id": cf1_id, "source": initial_node_id, "target": cba_id, "visibility": "public"})
            ET.SubElement(activity_elem, "edge", {"xmi:type": "uml:ControlFlow", "xmi:id": cf2_id, "source": cba_id, "target": final_node_id, "visibility": "public"})

        return activity_elem

    def _create_statemachine_content(self, statemachine_xml_elem, statemachine_data: Dict[str, Any]):
        """创建StateMachine的内容 - 基于stm_func.py"""
        statemachine_id = statemachine_data["id"]

        # StateMachine必须至少有一个Region (基于stm_func.py第192-196行)
        children = self.children_by_parent.get(statemachine_id, [])
        logger.debug(f"StateMachine {statemachine_id} 的子元素: {children}")

        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            logger.debug(f"检查子元素: {child_id}, 类型: {child_data.get('type') if child_data else 'None'}")
            if child_data and child_data.get("type") == "Region":
                logger.debug(f"创建Region: {child_id}")
                # 直接创建Region元素，而不是递归调用
                tag, xtype = self._determine_tag_and_type(child_data)
                if tag and xtype:
                    xml_elem = ET.SubElement(statemachine_xml_elem, tag, {"xmi:type": xtype, "xmi:id": child_id})
                    if child_data.get("name"):
                        xml_elem.set("name", child_data["name"])
                    self._populate_element_details(xml_elem, child_data)
                    self.xml_elements[child_id] = xml_elem
                    # 然后处理Region的内容
                    self._create_region_content(xml_elem, child_data)

    def _create_region_content(self, region_xml_elem, region_data: Dict[str, Any]):
        """创建Region的内容 - 基于stm_func.py的顺序处理"""
        region_id = region_data["id"]
        children = self.children_by_parent.get(region_id, [])
        logger.debug(f"Region {region_id} 的子元素: {children}")

        # 先创建子顶点（State和Pseudostate）(基于stm_func.py第206-209行)
        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            logger.debug(f"检查Region子元素: {child_id}, 类型: {child_data.get('type') if child_data else 'None'}")
            if child_data and child_data.get("type") in ["State", "Pseudostate"]:
                logger.debug(f"创建State/Pseudostate: {child_id}")
                # 直接创建元素而不是递归调用
                tag, xtype = self._determine_tag_and_type(child_data)
                if tag and xtype:
                    xml_elem = ET.SubElement(region_xml_elem, tag, {"xmi:type": xtype, "xmi:id": child_id})
                    if child_data.get("name"):
                        xml_elem.set("name", child_data["name"])
                    self._populate_element_details(xml_elem, child_data)
                    self.xml_elements[child_id] = xml_elem

        # 然后创建转换 (基于stm_func.py第211-214行)
        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get("type") == "Transition":
                logger.debug(f"创建Transition: {child_id}")
                # 直接创建元素而不是递归调用
                tag, xtype = self._determine_tag_and_type(child_data)
                if tag and xtype:
                    xml_elem = ET.SubElement(region_xml_elem, tag, {"xmi:type": xtype, "xmi:id": child_id})
                    if child_data.get("name"):
                        xml_elem.set("name", child_data["name"])
                    self._populate_element_details(xml_elem, child_data)
                    self.xml_elements[child_id] = xml_elem

    def _populate_property_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充Property的详细信息 - 基于bdd_and_ibd_func.py第184-206行"""
        # 设置基本属性
        xml_elem.set("visibility", elem_data.get("visibility", "public"))
        xml_elem.set("aggregation", elem_data.get("aggregation", "none"))

        # 处理类型 (基于bdd_and_ibd_func.py第191-195行)
        type_id = elem_data.get("typeId")
        if type_id:
            primitive_href = self._get_primitive_href(type_id)
            if primitive_href:
                ET.SubElement(xml_elem, "type", {"href": primitive_href})
            elif type_id in self.elements_by_id:
                xml_elem.set("type", type_id)
            else:
                logger.warning(f"属性 '{elem_data['id']}' 的类型 '{type_id}' 未找到。")

        # 处理关联 (基于bdd_and_ibd_func.py第197-201行)
        assoc_id = elem_data.get("associationId")
        property_kind = elem_data.get("propertyKind")
        if property_kind in ['part', 'reference'] and assoc_id:
            if assoc_id in self.elements_by_id and self.elements_by_id[assoc_id].get('type') == 'Association':
                xml_elem.set("association", assoc_id)
            else:
                logger.warning(f"关联 '{assoc_id}' 未找到，跳过属性 '{elem_data['id']}' 的关联设置。")

        # 处理多重性 (基于bdd_and_ibd_func.py第205行)
        if "multiplicity" in elem_data:
            self._add_multiplicity(xml_elem, elem_data["multiplicity"])

    def _populate_port_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充Port的详细信息 - 基于bdd_and_ibd_func.py第208-217行"""
        xml_elem.set("visibility", elem_data.get("visibility", "public"))
        xml_elem.set("aggregation", "composite")

        # 处理类型
        type_id = elem_data.get("typeId")
        if type_id:
            primitive_href = self._get_primitive_href(type_id)
            if primitive_href:
                ET.SubElement(xml_elem, "type", {"href": primitive_href})
            elif type_id in self.elements_by_id:
                xml_elem.set("type", type_id)
            else:
                logger.warning(f"端口 '{elem_data['id']}' 的类型 '{type_id}' 未找到。")

        # 处理多重性
        if "multiplicity" in elem_data:
            self._add_multiplicity(xml_elem, elem_data["multiplicity"])

    def _populate_connector_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充Connector的详细信息 - 基于bdd_and_ibd_func.py第261-297行"""
        elem_id = elem_data["id"]
        elem_type = elem_data["type"]

        xml_elem.set("visibility", "public")

        # 处理连接器类型
        if "typeId" in elem_data and elem_data["typeId"] in self.elements_by_id:
            xml_elem.set("type", elem_data["typeId"])

        # 处理连接器端点 (基于bdd_and_ibd_func.py第265-294行)
        valid_ends = True
        for end_key in ['end1', 'end2']:
            end_data = elem_data.get(end_key)
            if not end_data:
                logger.error(f"连接器 '{elem_id}' 缺少 {end_key} 数据。")
                valid_ends = False
                break

            end_id = end_data.get('id')
            if not end_id:
                logger.error(f"连接器 '{elem_id}' 的 {end_key} 缺少 'id'。")
                valid_ends = False
                break

            # 创建连接器端点
            end_attrs = {'xmi:type': 'uml:ConnectorEnd', 'xmi:id': end_id}
            end_elem = ET.SubElement(xml_elem, 'end', end_attrs)
            self.xml_elements[end_id] = end_elem  # 存储连接器端点XML

            # 处理端点引用 (基于bdd_and_ibd_func.py第275-293行)
            part_ref_id = end_data.get('partRefId')
            port_ref_id = end_data.get('portRefId')
            prop_ref_id = end_data.get('propertyRefId')

            # 确定角色ID
            role_id_to_set = port_ref_id if elem_type == 'AssemblyConnector' else prop_ref_id
            if role_id_to_set is None:
                role_id_to_set = port_ref_id if port_ref_id else prop_ref_id

            # 验证角色目标元素存在
            if role_id_to_set and role_id_to_set not in self.elements_by_id:
                logger.warning(f"连接器 '{elem_id}' 的 {end_key} 角色目标元素 '{role_id_to_set}' 未找到。")
                role_id_to_set = None

            # 设置端点属性
            if part_ref_id:
                if part_ref_id in self.elements_by_id:
                    end_elem.set('partWithPort', part_ref_id)
                    if role_id_to_set:
                        ET.SubElement(end_elem, 'role', {'xmi:idref': role_id_to_set})
                        # 记录需要应用NestedConnectorEnd构造型的端点
                        self.nested_connector_ends_to_stereotype.append((end_id, part_ref_id))
                else:
                    logger.warning(f"连接器 '{elem_id}' 的 {end_key} 部件属性 '{part_ref_id}' 未找到。")
                    valid_ends = False
                    break
            else:
                # 边界连接器端点
                if role_id_to_set:
                    end_elem.set('role', role_id_to_set)

        if not valid_ends:
            logger.error(f"由于端点错误，移除无效连接器 '{elem_id}'。")
            # 这里应该移除元素，但在当前架构下比较复杂，先记录错误
            return False

        return True

    def _populate_interaction_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充Interaction的详细信息 - 基于sd_func.py第139行"""
        # Interaction的classifierBehavior处理已在通用逻辑中处理
        pass

    def _populate_lifeline_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充Lifeline的详细信息 - 基于sd_func.py第154-155行"""
        if "representsId" in elem_data and elem_data["representsId"]:
            xml_elem.set("represents", elem_data["representsId"])

    def _populate_message_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充Message的详细信息 - 基于sd_func.py第156-166行"""
        # 设置Message的基本属性
        if "sendEventId" in elem_data and elem_data["sendEventId"]:
            xml_elem.set("sendEvent", elem_data["sendEventId"])
        if "receiveEventId" in elem_data and elem_data["receiveEventId"]:
            xml_elem.set("receiveEvent", elem_data["receiveEventId"])
        if "signatureId" in elem_data and elem_data["signatureId"]:
            xml_elem.set("signature", elem_data["signatureId"])
        if "messageSort" in elem_data and elem_data["messageSort"]:
            xml_elem.set("messageSort", elem_data["messageSort"])

        # 处理Message的arguments (基于sd_func.py第161-166行)
        arguments = elem_data.get("arguments", [])
        for arg_data in arguments:
            arg_attrs = {"xmi:type": "uml:OpaqueExpression"}
            if "id" in arg_data:
                arg_attrs["xmi:id"] = arg_data["id"]

            arg_elem = ET.SubElement(xml_elem, "argument", arg_attrs)
            if "body" in arg_data:
                ET.SubElement(arg_elem, "body").text = str(arg_data["body"])
            if "language" in arg_data:
                ET.SubElement(arg_elem, "language").text = arg_data["language"]

    def _populate_message_occurrence_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充MessageOccurrenceSpecification的详细信息 - 基于sd_func.py第167-170行"""
        if "coveredId" in elem_data and elem_data["coveredId"]:
            xml_elem.set("covered", elem_data["coveredId"])

        # MessageOccurrenceSpecification特有的message属性
        if elem_data.get("type") == "MessageOccurrenceSpecification" and "messageId" in elem_data:
            xml_elem.set("message", elem_data["messageId"])

    def _populate_combined_fragment_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充CombinedFragment的详细信息 - 基于sd_func.py第171-173行"""
        if "interactionOperator" in elem_data and elem_data["interactionOperator"]:
            xml_elem.set("interactionOperator", elem_data["interactionOperator"])

        # 处理covered lifelines
        covered_lifeline_ids = elem_data.get("coveredLifelineIds", [])
        for ll_id in covered_lifeline_ids:
            ET.SubElement(xml_elem, "covered", {"xmi:idref": ll_id})

    def _populate_interaction_constraint_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充InteractionConstraint的详细信息 - 基于sd_func.py第174-181行"""
        spec_data = elem_data.get("specification")
        if isinstance(spec_data, dict):
            spec_attrs = {"xmi:type": "uml:OpaqueExpression"}
            if "id" in spec_data:
                spec_attrs["xmi:id"] = spec_data["id"]

            spec_elem = ET.SubElement(xml_elem, "specification", spec_attrs)
            if "body" in spec_data:
                ET.SubElement(spec_elem, "body").text = spec_data["body"]
            if "language" in spec_data:
                ET.SubElement(spec_elem, "language").text = spec_data["language"]

    def _create_interaction_content(self, interaction_xml_elem, interaction_data: Dict[str, Any]):
        """创建Interaction的内容 - 基于sd_func.py的顺序处理"""
        interaction_id = interaction_data["id"]
        children = self.children_by_parent.get(interaction_id, [])
        logger.debug(f"Interaction {interaction_id} 的子元素: {children}")

        # 按照sd_func.py的顺序创建子元素：
        # 1. 先创建Lifeline (基于sd_func.py第154-155行)
        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get("type") == "Lifeline":
                logger.debug(f"创建Lifeline: {child_id}")
                self._create_interaction_child_element(interaction_xml_elem, child_data)

        # 2. 然后创建Message (基于sd_func.py第156-166行)
        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get("type") == "Message":
                logger.debug(f"创建Message: {child_id}")
                self._create_interaction_child_element(interaction_xml_elem, child_data)

        # 3. 创建MessageOccurrenceSpecification和其他fragment (基于sd_func.py第167-170行)
        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get("type") in ["MessageOccurrenceSpecification", "DestructionOccurrenceSpecification", "CombinedFragment"]:
                logger.debug(f"创建Fragment: {child_id}")
                self._create_interaction_child_element(interaction_xml_elem, child_data)

        # 4. 创建其他子元素（如Property等）
        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get("type") not in ["Lifeline", "Message", "MessageOccurrenceSpecification", "DestructionOccurrenceSpecification", "CombinedFragment"]:
                logger.debug(f"创建其他Interaction子元素: {child_id}")
                self._create_element_recursive(child_id, interaction_xml_elem)

    def _create_interaction_child_element(self, parent_xml, child_data):
        """创建Interaction的子元素"""
        child_id = child_data["id"]
        tag, xtype = self._determine_tag_and_type(child_data)
        if tag and xtype:
            xml_elem = ET.SubElement(parent_xml, tag, {"xmi:type": xtype, "xmi:id": child_id})
            if child_data.get("name"):
                xml_elem.set("name", child_data["name"])
            self._populate_element_details(xml_elem, child_data)
            self.xml_elements[child_id] = xml_elem

            # 处理CombinedFragment的operand子元素
            if child_data.get("type") == "CombinedFragment":
                self._create_combined_fragment_operands(xml_elem, child_data)

    def _create_combined_fragment_operands(self, cf_xml_elem, cf_data):
        """创建CombinedFragment的operand子元素"""
        cf_id = cf_data["id"]
        children = self.children_by_parent.get(cf_id, [])

        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get("type") == "InteractionOperand":
                logger.debug(f"创建InteractionOperand: {child_id}")
                tag, xtype = self._determine_tag_and_type(child_data)
                if tag and xtype:
                    operand_elem = ET.SubElement(cf_xml_elem, tag, {"xmi:type": xtype, "xmi:id": child_id})
                    if child_data.get("name"):
                        operand_elem.set("name", child_data["name"])
                    self._populate_element_details(operand_elem, child_data)
                    self.xml_elements[child_id] = operand_elem

                    # 处理InteractionOperand的guard
                    self._create_interaction_operand_guard(operand_elem, child_data)

    def _create_interaction_operand_guard(self, operand_xml_elem, operand_data):
        """创建InteractionOperand的guard"""
        operand_id = operand_data["id"]
        children = self.children_by_parent.get(operand_id, [])

        for child_id in sorted(children):
            child_data = self.elements_by_id.get(child_id)
            if child_data and child_data.get("type") == "InteractionConstraint":
                logger.debug(f"创建InteractionConstraint: {child_id}")
                tag, xtype = self._determine_tag_and_type(child_data)
                if tag and xtype:
                    guard_elem = ET.SubElement(operand_xml_elem, tag, {"xmi:type": xtype, "xmi:id": child_id})
                    if child_data.get("name"):
                        guard_elem.set("name", child_data["name"])
                    self._populate_element_details(guard_elem, child_data)
                    self.xml_elements[child_id] = guard_elem

    def _populate_state_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充状态的详细信息 - 保持向后兼容"""
        self._populate_state_behaviors(xml_elem, elem_data)

    def _populate_transition_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充转换的详细信息 - 基于stm_func.py"""
        elem_id = elem_data["id"]
        elem_name = elem_data.get("name", elem_id)

        if "sourceId" in elem_data:
            xml_elem.set("source", elem_data["sourceId"])
        if "targetId" in elem_data:
            xml_elem.set("target", elem_data["targetId"])

        # 处理触发器 (基于stm_func.py第299-313行)
        if "triggerIds" in elem_data:
            for i, event_id_ref in enumerate(elem_data["triggerIds"]):
                trigger_id = f"{elem_id}_trigger_{i}"
                trigger_attrs = {"xmi:type": "uml:Trigger", "xmi:id": trigger_id, "visibility": "public"}

                trigger_sub_elem = ET.SubElement(xml_elem, "trigger", trigger_attrs)
                ET.SubElement(trigger_sub_elem, "event", {"xmi:idref": event_id_ref})

        # 处理守卫 (基于stm_func.py第317-327行)
        guard_json = elem_data.get("guard")
        if guard_json and "expression" in guard_json:
            guard_id = f"{elem_id}_guard"
            spec_id = f"{elem_id}_guard_spec"
            guard_elem = ET.SubElement(xml_elem, "guard", {"xmi:type": "uml:Constraint", "xmi:id": guard_id, "visibility": "public"})
            spec_elem = ET.SubElement(guard_elem, "specification", {"xmi:type": "uml:OpaqueExpression", "xmi:id": spec_id})
            ET.SubElement(spec_elem, "body").text = guard_json["expression"]
            if "language" in guard_json:
                ET.SubElement(spec_elem, "language").text = guard_json["language"]

        # 处理效果 (基于stm_func.py第330-333行)
        if "effect" in elem_data and elem_data["effect"]:
            self._create_behavior_activity_stm(xml_elem, "effect", elem_data["effect"], elem_name)

    def _populate_constraint_block_details(self, xml_elem, elem_data: Dict[str, Any]):
        """填充约束块的详细信息 - 基于par_func.py的逻辑"""
        elem_id = elem_data["id"]
        spec_data = elem_data["specification"]

        # 创建ownedRule (uml:Constraint) - 使用par_func.py的ID格式
        rule_attrs = {"xmi:type": "uml:Constraint", "xmi:id": f"{elem_id}_constraint"}
        rule_elem = ET.SubElement(xml_elem, "ownedRule", rule_attrs)

        # 约束规则总是约束自身这个块
        ET.SubElement(rule_elem, "constrainedElement", {"xmi:idref": elem_id})

        # 创建specification (uml:OpaqueExpression) - 使用par_func.py的ID格式
        spec_attrs = {"xmi:type": "uml:OpaqueExpression", "xmi:id": f"{elem_id}_spec"}
        spec_elem = ET.SubElement(rule_elem, "specification", spec_attrs)

        # 填充表达式的body和language
        ET.SubElement(spec_elem, "body").text = spec_data.get("expression", "")
        ET.SubElement(spec_elem, "language").text = spec_data.get("language", "")

    def _create_behavior_activity(self, parent_xml, tag: str, data: Dict[str, Any]):
        """辅助函数：为状态机创建内嵌的Activity行为"""
        if not data or "wrapperActivityId" not in data:
            return

        wrapper_id = data["wrapperActivityId"]
        called_id = data.get("calledBehaviorId")
        called_name = self.elements_by_id.get(called_id, {}).get("name") if called_id else None

        attrs = {"xmi:type": "uml:Activity", "xmi:id": wrapper_id}
        if called_name:
            attrs["name"] = called_name

        act_xml = ET.SubElement(parent_xml, tag, attrs)

        if called_id:
            cba_attrs = {
                "xmi:type": "uml:CallBehaviorAction",
                "xmi:id": self._generate_id(wrapper_id, "cba"),
                "behavior": called_id
            }
            ET.SubElement(act_xml, "node", cba_attrs)

    def _add_multiplicity(self, element_data: Dict[str, Any], parent_xml_element):
        """添加多重性信息"""
        multiplicity = element_data.get('multiplicity')
        if multiplicity is None:
            return
        lower_val, upper_val = '1', '1'
        if isinstance(multiplicity, str) and multiplicity.startswith('[') and multiplicity.endswith(']'):
            parts = multiplicity[1:-1].split('..')
            if len(parts) == 2:
                lower_val, upper_val = parts[0].strip(), parts[1].strip()
            elif len(parts) == 1:
                lower_val = upper_val = parts[0].strip()
        lower_attrs = {'xmi:type': 'uml:LiteralInteger', 'xmi:id': self._generate_id(element_data['id'], 'lower'), 'value': lower_val}
        upper_attrs = {'xmi:id': self._generate_id(element_data['id'], 'upper'), 'value': upper_val}
        upper_attrs['xmi:type'] = 'uml:LiteralUnlimitedNatural' if upper_val == '*' else 'uml:LiteralInteger'
        ET.SubElement(parent_xml_element, "lowerValue", lower_attrs)
        ET.SubElement(parent_xml_element, "upperValue", upper_attrs)

    def _get_primitive_href(self, type_name):
        """获取原始类型的HREF - 基于bdd_and_ibd_func.py第89-99行"""
        # 基于bdd_and_ibd_func.py的primitive_hrefs映射
        primitive_hrefs = {
            'Boolean': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Boolean',
            'Real': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Real',
            'Integer': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.Integer',
            'String': 'http://www.omg.org/spec/SysML/20181001/SysML.xmi#SysML_dataType.String',
            'int': 'UML_Standard_Profile.mdzip#eee_1045467100323_917313_65',
            'void': 'UML_Standard_Profile.mdzip#eee_1045467100323_249638_60',
            'float': 'UML_Standard_Profile.mdzip#eee_1045467100323_385364_62'
        }

        lookup_name = type_name
        if isinstance(type_name, str):
            if type_name.lower() == 'boolean':
                lookup_name = 'Boolean'
            elif type_name.lower() == 'integer':
                lookup_name = 'Integer'
            elif type_name.lower() == 'real':
                lookup_name = 'Real'
            elif type_name.lower() == 'string':
                lookup_name = 'String'

        return primitive_hrefs.get(lookup_name) or primitive_hrefs.get(type_name)

    def _add_type_ref_to_element(self, parent_xml, type_id):
        """添加类型引用 - 基于par_func.py的逻辑"""
        if not type_id:
            return

        # 基于par_func.py的逻辑：检查是否以http开头
        if str(type_id).startswith("http"):
            ET.SubElement(parent_xml, "type", {"href": type_id})
        # 检查是否是预定义的简写基础类型
        elif type_id in self.PRIMITIVE_TYPE_MAP:
            ET.SubElement(parent_xml, "type", {"href": self.PRIMITIVE_TYPE_MAP[type_id]})
        # 检查是否是简单的基础类型名称（如"Real", "Integer"等）
        elif type_id in ["Real", "Integer", "String", "Boolean"]:
            ET.SubElement(parent_xml, "type", {"href": self.PRIMITIVE_TYPE_MAP[type_id]})
        # 否则，假定为模型内部的ID引用，使用type属性
        else:
            parent_xml.set("type", type_id)

    def _populate_activity_content(self, activity_xml_elem, activity_data: Dict[str, Any]):
        """填充Activity内部所有元素 - 基于act_func.py的逻辑"""
        activity_id = activity_data["id"]

        # 查找此活动的所有子元素（基于act_func.py第76-90行的分类逻辑）
        node_ids = [cid for cid in self.children_by_parent.get(activity_id, [])
                   if self.elements_by_id[cid]['type'] not in ['ControlFlow', 'ObjectFlow', 'ActivityPartition']]
        edge_ids = [cid for cid in self.children_by_parent.get(activity_id, [])
                   if self.elements_by_id[cid]['type'] in ['ControlFlow', 'ObjectFlow']]
        group_ids = [cid for cid in self.children_by_parent.get(activity_id, [])
                    if self.elements_by_id[cid]['type'] == 'ActivityPartition']

        # 按照act_func.py第213-232行的顺序创建活动内容：
        # 1. 创建partition引用（基于act_func.py第214-218行）
        for group_id in group_ids:
            # 创建partition引用元素
            ET.SubElement(activity_xml_elem, "partition", {"xmi:idref": group_id})

        # 2. 创建edge元素（基于act_func.py第220-222行）
        for edge_id in edge_ids:
            self._create_activity_element(activity_xml_elem, edge_id)

        # 3. 创建node元素（除引脚外）（基于act_func.py第224-228行）
        for node_id in node_ids:
            node_data = self.elements_by_id[node_id]
            if node_data['type'] not in ['InputPin', 'OutputPin']:
                self._create_activity_element(activity_xml_elem, node_id)

        # 4. 创建group元素（分区定义）（基于act_func.py第230-232行）
        for group_id in group_ids:
            self._create_activity_element(activity_xml_elem, group_id)

    def _create_activity_element(self, parent_xml_element, elem_id: str):
        """为活动创建单个内部元素（节点、边、分区等）- 基于act_func.py的逻辑"""
        if elem_id in self.xml_elements:
            return

        elem_data = self.elements_by_id[elem_id]
        elem_type = elem_data["type"]
        elem_name = elem_data.get("name")

        # 确定标签名（基于act_func.py的映射）
        type_to_tag_map = {
            "ControlFlow": 'edge', "ObjectFlow": 'edge', "ActivityPartition": 'group',
            "InputPin": 'argument', "OutputPin": 'result',
            "InitialNode": 'node', "ActivityFinalNode": 'node', "FlowFinalNode": 'node',
            "DecisionNode": 'node', "MergeNode": 'node', "ForkNode": 'node', "JoinNode": 'node',
            "CallBehaviorAction": 'node', "ActivityParameterNode": 'node', "CentralBufferNode": 'node',
            "ObjectNode": 'node'
        }

        tag_name = type_to_tag_map.get(elem_type)
        if not tag_name:
            logger.warning(f"无法确定活动元素 {elem_type} (ID: {elem_id}) 的标签")
            return

        # 准备属性
        attrs = {"xmi:id": elem_id, "xmi:type": f"uml:{elem_type}"}
        if elem_name:
            attrs["name"] = elem_name

        # 添加visibility属性（基于act_func.py）
        if tag_name in ['edge', 'node', 'argument', 'result', 'group']:
            attrs["visibility"] = "public"

        # 处理特定属性
        if tag_name == 'edge' and elem_type in ["ControlFlow", "ObjectFlow"]:
            source_id = elem_data.get("sourceId")
            target_id = elem_data.get("targetId")
            if source_id and source_id in self.elements_by_id:
                attrs["source"] = source_id
            if target_id and target_id in self.elements_by_id:
                attrs["target"] = target_id
        elif tag_name == 'node' and elem_type == "CallBehaviorAction":
            behavior_id = elem_data.get("behavior")
            if behavior_id:  # 确保不是None或空字符串
                attrs["behavior"] = behavior_id
        elif tag_name in ['argument', 'result', 'node'] and elem_type in ["InputPin", "OutputPin", "ActivityParameterNode", "CentralBufferNode"]:
            type_id = elem_data.get("typeId")
            if type_id and type_id in self.elements_by_id:
                attrs["type"] = type_id
        elif tag_name == 'group':
            represents_id = elem_data.get("representsId")
            if represents_id and represents_id in self.elements_by_id:
                attrs["represents"] = represents_id

        # 创建XML元素
        xml_elem = self._safe_create_element(parent_xml_element, tag_name, attrs)
        self.xml_elements[elem_id] = xml_elem

        # 处理子元素
        if tag_name == 'edge' and elem_type in ["ControlFlow", "ObjectFlow"]:
            # 添加weight子元素
            weight_attrs = {"xmi:type": "uml:LiteralUnlimitedNatural",
                           "xmi:id": self._generate_id(elem_id, "weight"), "value": "1"}
            ET.SubElement(xml_elem, "weight", weight_attrs)

            # 处理guard条件
            guard_text = elem_data.get("guard")
            if elem_type == "ControlFlow" and guard_text:
                guard_attrs = {"xmi:type": "uml:OpaqueExpression",
                              "xmi:id": self._generate_id(elem_id, "guard")}
                guard_elem = ET.SubElement(xml_elem, "guard", guard_attrs)
                ET.SubElement(guard_elem, "body").text = guard_text
                ET.SubElement(guard_elem, "language").text = "English"

        elif tag_name == 'node' and elem_type == 'CallBehaviorAction':
            # 创建引脚
            for pin_id in self.children_by_parent.get(elem_id, []):
                pin_data = self.elements_by_id.get(pin_id)
                if pin_data and pin_data['type'] in ['InputPin', 'OutputPin']:
                    self._create_activity_element(xml_elem, pin_id)

        elif tag_name == 'group':  # ActivityPartition
            # 添加分区内的节点和边引用（基于act_func.py第338-346行的逻辑）
            for node_edge_id in elem_data.get("nodeIds", []):
                node_edge_data = self.elements_by_id.get(node_edge_id)
                if node_edge_data:
                    node_edge_type = node_edge_data.get("type")
                    # 关键修正：过滤掉Fork/Join节点（基于act_func.py第344行）
                    if node_edge_type not in ['ForkNode', 'JoinNode']:
                        ref_tag = "edge" if node_edge_type in ["ControlFlow", "ObjectFlow"] else "node"
                        ET.SubElement(xml_elem, ref_tag, {"xmi:idref": node_edge_id})

    def _post_process_structure(self):
        """后期处理关系和图表"""
        logger.info("--- 阶段 3: 后期处理关系和图表 ---")
        self._apply_usecase_relationships()  # 新增：处理用例图关系
        self._apply_associations()
        self._apply_nested_connector_ends()
        self._apply_nested_connector_end_stereotypes()  # 新增：处理NestedConnectorEnd构造型
        self._apply_lifeline_covered_by()
        self._apply_diagrams()

    def _apply_nested_connector_end_stereotypes(self):
        """应用NestedConnectorEnd构造型 - 基于bdd_and_ibd_func.py第387-395行"""
        logger.info(f"--- 阶段 3.2.5: 应用 {len(self.nested_connector_ends_to_stereotype)} 个NestedConnectorEnd构造型 ---")

        for end_id, part_prop_id in self.nested_connector_ends_to_stereotype:
            # 基于bdd_and_ibd_func.py第389行的ID模式
            stereotype_id = f"{end_id}_"
            stereotype_attrs = {
                'xmi:id': stereotype_id,
                'base_ConnectorEnd': end_id,
                'propertyPath': part_prop_id
            }

            # 验证连接器端点和属性路径都存在
            connector_end_xml = self.xml_elements.get(end_id)
            property_path_xml = self.xml_elements.get(part_prop_id)

            if connector_end_xml is not None and property_path_xml is not None:
                # 在根元素下创建NestedConnectorEnd构造型
                stereotype_elem = ET.SubElement(self.xmi_root, 'sysml:NestedConnectorEnd', stereotype_attrs)
                logger.debug(f"创建了NestedConnectorEnd构造型: {end_id} -> {part_prop_id}")
            else:
                logger.warning(f"无法创建NestedConnectorEnd构造型 {end_id}。连接器端点: {'找到' if connector_end_xml is not None else '未找到'}，属性路径 {part_prop_id}: {'找到' if property_path_xml is not None else '未找到'}。")

    def _apply_usecase_relationships(self):
        """应用用例图关系：Include、Extend、Generalization - 基于uc_func.py"""
        logger.info("--- 阶段 3.0: 应用用例图关系 ---")

        # 收集所有用例图关系
        usecase_relationships = []
        for elem_data in self.elements_by_id.values():
            if elem_data.get("type") in ["Include", "Extend", "Generalization"]:
                usecase_relationships.append(elem_data)

        logger.info(f"找到 {len(usecase_relationships)} 个用例图关系")

        for rel_data in usecase_relationships:
            rel_id = rel_data["id"]
            rel_type = rel_data["type"]
            source_id = rel_data.get("sourceId")
            target_id = rel_data.get("targetId")

            if not source_id or not target_id:
                logger.warning(f"关系 '{rel_id}' 缺少源或目标ID")
                continue

            source_xml = self.xml_elements.get(source_id)
            target_xml = self.xml_elements.get(target_id)

            if source_xml is None or target_xml is None:
                logger.warning(f"关系 '{rel_id}' 的源 '{source_id}' (找到: {source_xml is not None}) 或目标 '{target_id}' (找到: {target_xml is not None}) 元素未找到")
                logger.debug(f"可用的XML元素ID: {list(self.xml_elements.keys())}")
                continue

            # 创建关系元素 (基于uc_func.py的逻辑)
            attrs = {"xmi:type": f"uml:{rel_type}", "xmi:id": rel_id}
            if rel_data.get("name"):
                attrs["name"] = rel_data["name"]

            if rel_type == "Include":
                attrs["addition"] = target_id
                rel_xml = ET.SubElement(source_xml, "include", attrs)
            elif rel_type == "Extend":
                attrs["extendedCase"] = target_id
                rel_xml = ET.SubElement(source_xml, "extend", attrs)
            elif rel_type == "Generalization":
                attrs["general"] = target_id
                rel_xml = ET.SubElement(source_xml, "generalization", attrs)

            self.xml_elements[rel_id] = rel_xml
            logger.debug(f"创建了 {rel_type} 关系: {source_id} -> {target_id}")

    def _apply_associations(self):
        """后处理：创建Association元素并更新其两端的Property - 基于uc_func.py和bdd_and_ibd_func.py"""
        logger.info(f"--- 阶段 3.1: 应用 {len(self.associations_to_apply)} 个关联 ---")
        for assoc_data in self.associations_to_apply:
            parent_xml = self.xml_elements.get(assoc_data["parentId"])
            if not parent_xml:
                logger.warning(f"关联 '{assoc_data['id']}' 的父节点 '{assoc_data['parentId']}' 未找到。")
                continue

            # BDD/IBD 的关联通常没有sourceId/targetId，而是有memberEndIds
            member_ends = assoc_data.get("memberEndIds", [])
            # 用例图的关联有sourceId/targetId
            source_id = assoc_data.get("sourceId")
            target_id = assoc_data.get("targetId")

            if member_ends and len(member_ends) == 2:
                # BDD/IBD 关联逻辑 (基于bdd_and_ibd_func.py)
                if member_ends[0] in self.xml_elements and member_ends[1] in self.xml_elements:
                    assoc_xml = ET.SubElement(parent_xml, "packagedElement", {"xmi:type": "uml:Association", "xmi:id": assoc_data["id"]})
                    if assoc_data.get("name"):
                        assoc_xml.set("name", assoc_data["name"])
                    ET.SubElement(assoc_xml, "memberEnd", {"xmi:idref": member_ends[0]})
                    ET.SubElement(assoc_xml, "memberEnd", {"xmi:idref": member_ends[1]})
                    self.xml_elements[member_ends[0]].set("association", assoc_data["id"])
                    self.xml_elements[member_ends[1]].set("association", assoc_data["id"])
                    self.xml_elements[assoc_data["id"]] = assoc_xml
            elif source_id and target_id:
                # 用例图关联逻辑 (基于uc_func.py)
                assoc_xml = ET.SubElement(parent_xml, "packagedElement", {"xmi:type": "uml:Association", "xmi:id": assoc_data["id"]})
                if assoc_data.get("name"):
                    assoc_xml.set("name", assoc_data["name"])

                # 使用uc_func.py的命名模式
                end1_id = f"{assoc_data['id']}_end_source"
                end2_id = f"{assoc_data['id']}_end_target"

                # 创建ownedEnd属性 (基于uc_func.py第100-111行)
                ET.SubElement(assoc_xml, 'ownedEnd', {
                    'xmi:type': 'uml:Property',
                    'xmi:id': end1_id,
                    'type': source_id,
                    'association': assoc_data["id"]
                })
                ET.SubElement(assoc_xml, 'ownedEnd', {
                    'xmi:type': 'uml:Property',
                    'xmi:id': end2_id,
                    'type': target_id,
                    'association': assoc_data["id"]
                })

                # 添加memberEnd引用 (基于uc_func.py第114-115行)
                ET.SubElement(assoc_xml, 'memberEnd', {'xmi:idref': end1_id})
                ET.SubElement(assoc_xml, 'memberEnd', {'xmi:idref': end2_id})

                # 添加navigableOwnedEnd (基于uc_func.py第119-120行)
                ET.SubElement(assoc_xml, 'navigableOwnedEnd', {'xmi:idref': end1_id})
                ET.SubElement(assoc_xml, 'navigableOwnedEnd', {'xmi:idref': end2_id})

                self.xml_elements[assoc_data["id"]] = assoc_xml

    def _apply_nested_connector_ends(self):
        """应用NestedConnectorEnd构造型 - 基于par_func.py的逻辑"""
        logger.info(f"--- 阶段 3.2: 应用 {len(self.nested_connector_ends_to_apply)} 个嵌套连接器端点 ---")
        for end_id, part_id in self.nested_connector_ends_to_apply:
            # 基于par_func.py第172行的ID生成格式：f"{end_id}_stereotype"
            stereo_id = f"{end_id}_stereotype"
            stereo_attrs = {
                "xmi:id": stereo_id,
                "base_ConnectorEnd": end_id,
                "propertyPath": part_id
            }
            ET.SubElement(self.xmi_root, "sysml:NestedConnectorEnd", stereo_attrs)

    def _apply_lifeline_covered_by(self):
        """应用Lifeline的coveredBy引用"""
        logger.info(f"--- 阶段 3.3: 应用生命线覆盖关系 ---")
        for lifeline_id, covered_ids in self.lifelines_to_update.items():
            if lifeline_id in self.xml_elements:
                lifeline_xml = self.xml_elements[lifeline_id]
                for covered_id in covered_ids:
                    ET.SubElement(lifeline_xml, "coveredBy", {"xmi:idref": covered_id})

    def _apply_diagrams(self):
        """应用图表"""
        logger.info(f"--- 阶段 3.4: 应用 {len(self.diagrams_to_apply)} 个图表 ---")

        # 创建图表扩展
        if self.diagrams_to_apply:
            # 创建xmi:Extension
            extension = ET.SubElement(self.xmi_root, "xmi:Extension", {"extender": "MagicDraw UML 19.0"})

            # 创建mdOwnedDiagrams
            md_owned_diagrams = ET.SubElement(extension, "mdOwnedDiagrams")

            for diagram_data in self.diagrams_to_apply:
                diagram_attrs = {
                    "xmi:type": "diagram:Diagram",
                    "xmi:id": diagram_data["id"],
                    "name": diagram_data.get("name", "Unnamed Diagram"),
                    "diagramType": diagram_data.get("diagramType", "SysML Block Definition Diagram"),
                    "owner": diagram_data.get("parentId", self.model_id)
                }

                diagram_elem = ET.SubElement(md_owned_diagrams, "mdElement", diagram_attrs)

                # 添加图表元素
                for element_id in diagram_data.get("elementIds", []):
                    if element_id in self.xml_elements:
                        element_attrs = {
                            "xmi:type": "diagram:GraphElement",
                            "xmi:id": self._generate_id(diagram_data["id"], f"graph_{element_id}"),
                            "element": element_id
                        }
                        ET.SubElement(diagram_elem, "mdElement", element_attrs)

    def _apply_stereotypes(self):
        """应用SysML构造型"""
        logger.info("--- 阶段 4: 应用SysML构造型 ---")
        stereotype_map = {
            "Requirement": ("sysml:Requirement", "base_Class"),
            "Block": ("sysml:Block", "base_Class"),
            "InterfaceBlock": ("sysml:InterfaceBlock", "base_Class"),
            "TestCase": ("sysml:TestCase", "base_Behavior"),  # 基于req_func.py第276行
            "Abstraction": {
                "DeriveReqt": ("sysml:DeriveReqt", "base_Abstraction"),
                "Satisfy": ("sysml:Satisfy", "base_Abstraction"),
                "Verify": ("sysml:Verify", "base_Abstraction")
            },
            "ValueType": ("sysml:ValueType", "base_DataType"),
            "Unit": ("MD_Customization_for_SysML__additional_stereotypes:Unit", "base_InstanceSpecification"),
            "ConstraintBlock": ("sysml:ConstraintBlock", "base_Class"),
            "ConstraintParameter": ("MD_Customization_for_SysML__additional_stereotypes:ConstraintParameter", "base_Port"),
            "BindingConnector": ("sysml:BindingConnector", "base_Connector"),
            "ActivityPartition": ("sysml:AllocateActivityPartition", "base_ActivityPartition"),  # 基于act_func.py第393行
            "FullPort": ("sysml:FullPort", "base_Port"),
            "ProxyPort": ("sysml:ProxyPort", "base_Port"),
            "FlowPort": ("sysml:FlowPort", "base_Port"),
            "ItemFlow": ("sysml:ItemFlow", "base_InformationFlow"),
            "FlowProperty": ("sysml:FlowProperty", "base_Property"),
            "Allocate": ("sysml:Allocate", "base_Abstraction"),
            "Expose": ("sysml:Expose", "base_Dependency"),
            "ElementGroup": ("sysml:ElementGroup", "base_Comment"),
            "Problem": ("sysml:Problem", "base_Comment"),
            "Rationale": ("sysml:Rationale", "base_Comment"),
            "View": ("sysml:View", "base_Class"),
            "Viewpoint": ("sysml:Viewpoint", "base_Class"),
            "Stakeholder": ("sysml:Stakeholder", "base_Classifier"),

        }

        for category, elements in self.stereotypes_to_apply.items():
            for data in elements:
                elem_id = data.get("id")
                if elem_id not in self.xml_elements:
                    continue

                mapping = stereotype_map.get(category)
                if not mapping:
                    # 处理 Property 的构造型
                    if category == "Property":
                        kind = data.get("propertyKind")
                        prop_kind_map = {
                            "value": ("MD_Customization_for_SysML__additional_stereotypes:ValueProperty", "base_Property"),
                            "part": ("MD_Customization_for_SysML__additional_stereotypes:PartProperty", "base_Property"),
                            "reference": ("MD_Customization_for_SysML__additional_stereotypes:ReferenceProperty", "base_Property"),
                            "constraint": ("MD_Customization_for_SysML__additional_stereotypes:ConstraintProperty", "base_Property"),
                            "shared": ("MD_Customization_for_SysML__additional_stereotypes:SharedProperty", "base_Property"),
                            "flow": ("sysml:FlowProperty", "base_Property")
                        }
                        if kind in prop_kind_map:
                            mapping = prop_kind_map[kind]
                        else:
                            continue
                    else:
                        continue

                tag, base_attr = None, None
                if category == "Abstraction":
                    original_type = data.get("type")
                    if original_type in mapping:
                        tag, base_attr = mapping[original_type]
                else:
                    tag, base_attr = mapping

                if tag and base_attr:
                    # 基于req_func.py的构造型ID生成逻辑：Requirement使用特殊的"_"后缀
                    if data.get("type") == "Requirement":
                        # Requirement使用特殊的"_"后缀（基于req_func.py第253行）
                        stereo_id = f"{elem_id}_"
                    else:
                        # 其他类型使用标准的"_application"后缀
                        stereo_id = f"{elem_id}_application"

                    attrs = {"xmi:id": stereo_id, base_attr: elem_id}

                    # 特殊属性处理（基于req_func.py的逻辑）
                    if data.get("type") == "Requirement":
                        # 基于req_func.py第257-258行
                        attrs["Id"] = data.get("reqId", "")
                        attrs["Text"] = data.get("text", "")
                    elif data.get("type") == "TestCase":
                        # TestCase可能有verdict属性
                        if "verdict" in data:
                            attrs["verdict"] = data.get("verdict", "")
                    elif data.get("type") == "Unit":
                        attrs["dimension"] = data.get("dimension", "")
                        attrs["quantityKind"] = data.get("quantityKind", "")
                    elif data.get("type") == "ConstraintParameter":
                        attrs["isEncapsulated"] = str(data.get("isEncapsulated", False)).lower()
                    elif data.get("type") == "FlowPort":
                        attrs["direction"] = data.get("direction", "inout")
                        attrs["isAtomic"] = str(data.get("isAtomic", True)).lower()
                        attrs["isConjugated"] = str(data.get("isConjugated", False)).lower()

                    ET.SubElement(self.xmi_root, tag, attrs)

        logger.info(f"应用了 {sum(len(elements) for elements in self.stereotypes_to_apply.values())} 个构造型")

    def _prettify_xml(self) -> str:
        """格式化XML输出"""
        logger.info("--- 阶段 5: 格式化XML输出 ---")
        rough_string = ET.tostring(self.xmi_root, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ", encoding="UTF-8").decode()
        return "\n".join([line for line in pretty_xml.splitlines() if line.strip()])


class JSONToXMLAgent:
    """
    JSON到XML转换Agent
    
    负责将合并后的SysML JSON数据转换为标准的SysML XMI文件
    """
    
    def __init__(self):
        """初始化JSON到XML转换Agent"""
        logger.info("初始化JSON到XML转换Agent")
    
    def convert_json_to_xml(self, json_file_path: str, output_file_path: str) -> bool:
        """
        将JSON文件转换为XML文件
        
        参数:
            json_file_path: 输入JSON文件路径
            output_file_path: 输出XML文件路径
            
        返回:
            转换是否成功
        """
        try:
            logger.info(f"开始转换JSON文件: {json_file_path}")
            
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # 创建生成器并转换
            generator = EnhancedSysMLGenerator(json_data)
            xml_content = generator.generate_xmi()
            
            if xml_content is None:
                logger.error("XML生成失败")
                return False
            
            # 保存XML文件
            with open(output_file_path, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            
            logger.info(f"XML文件已保存到: {output_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"转换过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def process_workflow_state(self, state: WorkflowState) -> WorkflowState:
        """
        处理工作流状态，执行JSON到XML转换
        
        参数:
            state: 工作流状态
            
        返回:
            更新后的工作流状态
        """
        try:
            logger.info("JSON到XML转换Agent开始处理")
            
            # 检查是否有最终的JSON模型
            json_file_path = "data/output/latest_final_sysml_model.json"
            if not Path(json_file_path).exists():
                raise FileNotFoundError(f"找不到JSON文件: {json_file_path}")
            
            # 设置输出路径
            output_file_path = "data/output/final_sysml_model.xmi"
            
            # 执行转换
            success = self.convert_json_to_xml(json_file_path, output_file_path)
            
            if success:
                state.xml_conversion_status = ProcessStatus.COMPLETED
                state.xml_file_path = output_file_path
                logger.info("JSON到XML转换完成")
            else:
                state.xml_conversion_status = ProcessStatus.FAILED
                logger.error("JSON到XML转换失败")
            
            return state
            
        except Exception as e:
            logger.error(f"JSON到XML转换Agent处理失败: {str(e)}")
            state.xml_conversion_status = ProcessStatus.FAILED
            return state


def json_to_xml_agent(state: WorkflowState) -> WorkflowState:
    """
    JSON到XML转换Agent的入口函数
    
    参数:
        state: 工作流状态
        
    返回:
        更新后的工作流状态
    """
    agent = JSONToXMLAgent()
    return agent.process_workflow_state(state)
