"""
活动图agent - 负责基于输入内容创建SysML活动图
"""
import logging
import json
from typing import Dict, Any
import re
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage

from src.graph.workflow_state import WorkflowState, ProcessStatus
from config.settings import settings
from json_repair import repair_json

logger = logging.getLogger(__name__)

# 系统提示模板 - 活动图建模
prompt1_first = """
## 角色
你是一位专业的 SysML 活动图建模专家。你精通 SysML 活动图的规范，能够准确地从流程或工作流的自然语言描述中提取出活动、动作、对象、控制流、对象流、分区（泳道）、决策点、并发等元素，并理解它们之间的关系。

## 规则
你的目标是根据输入的文本描述，分析并生成构建 SysML 活动图所需的元素信息。请遵循以下步骤进行思考和分析，并生成中间的思考过程：

1.  **识别主要活动和包 (Package & Activity)**:
    *   确定文本描述的核心流程或活动，将其作为顶层 `Activity`。
    *   如果描述暗示了模块化或分组，可以定义一个 `Package` 来包含所有相关元素。
    *   识别是否有明确提到的子流程或可复用的行为，它们可能对应独立的 `Activity` 定义，并在主活动中通过 `CallBehaviorAction` 调用。
    *   为每个识别的元素分配合理的名称和临时ID（最终JSON中ID需全局唯一，可使用描述性名称加后缀，如 `-uuid`）。

2.  **识别活动节点 (Activity Nodes)**:
    *   **动作 (Actions)**: 找出流程中的具体步骤或任务。这些通常是动词短语。将它们识别为 `CallBehaviorAction` (如果调用其他行为或服务) 或 `OpaqueAction` (如果行为简单且在本图中定义)。
    *   **控制节点 (Control Nodes)**:
        *   `InitialNode`: 识别流程的起点。
        *   `ActivityFinalNode`: 识别流程的终点。
        *   `ForkNode`: 识别流程中开始并行执行的地方（例如，“同时”、“并行地”）。
        *   `JoinNode`: 识别并行路径汇合、需要等待所有并行分支完成的地方。
        *   `DecisionNode`: 识别基于条件进行分支的地方（例如，“如果...那么...否则...”）。记录判断条件（Guard）。
        *   `MergeNode`: 识别不同条件分支重新汇合到一个流的地方（不进行同步，与JoinNode不同）。
    *   **对象节点 (Object Nodes)**:
        *   `CentralBufferNode`: 识别共享的数据存储或缓冲区（例如，“共享缓存”、“存放区”）。
        *   `DataStoreNode`: （如果需要表示持久化存储）识别数据库或持久化存储。 (在此示例中未使用，但可作为扩展)
    *   为每个节点分配合理的名称和临时ID。

3.  **识别数据类型和参与者 (Blocks)**:
    *   **数据类型 (Blocks for Types)**: 识别在流程中传递或处理的数据、文档、消息等。将它们定义为 `Block` 元素，作为引脚 (Pin) 和对象节点 (Object Node) 的类型。
    *   **参与者/系统 (Blocks for Partitions)**: 识别执行动作的角色、部门、系统或组件。将它们定义为 `Block` 元素，用于后续定义活动分区 (泳道)。
    *   为每个 Block 分配合理的名称和临时ID。

4.  **识别活动分区 (Activity Partitions / Swimlanes)**:
    *   根据第3步识别的参与者/系统，定义 `ActivityPartition` (泳道)。
    *   明确每个分区 `represents` 哪个参与者 Block。
    *   将第2步识别的动作节点分配到相应的分区中。

5.  **识别引脚 (Pins) 并关联类型**:
    *   对于 `CallBehaviorAction` 或其他需要输入/输出的动作，识别其输入 (`InputPin`) 和输出 (`OutputPin`)。
    *   为每个引脚命名（通常反映其内容，如 `in_文档`, `out_状态`）。
    *   为每个引脚关联第3步中识别的数据类型 Block (`typeId`)。
    *   为每个引脚分配合理的名称和临时ID。

6.  **识别流 (Flows - Control & Object)**:
    *   **控制流 (Control Flow)**: 连接不需要传递数据的活动节点（包括控制节点和动作节点）。确定 `sourceId` 和 `targetId`。对于从 `DecisionNode` 出来的流，记录 `guard` 条件。
    *   **对象流 (Object Flow)**: 连接传递数据的节点，通常连接：
        *   动作的 `OutputPin` 到 下一个动作的 `InputPin`。
        *   动作的 `OutputPin` 到 `CentralBufferNode`。
        *   `CentralBufferNode` 到 动作的 `InputPin`。
        *   `InitialNode` 到 第一个动作的 `InputPin` (如果流程开始有输入对象)。
        *   动作的 `OutputPin` 到 `ActivityFinalNode` (如果流程结束有输出对象)。
    *   确定 `sourceId` 和 `targetId`。
    *   为每个流分配合理的名称和临时ID。

7.  **编译和整理**:
    *   汇总所有识别出的元素（包、活动、节点、块、分区、引脚、流）。
    *   检查元素间的关系是否完整（例如，引脚是否有父节点，流的源和目标是否存在，分区是否关联了块和节点等）。
    *   准备一个清晰的、结构化的中间表示，概述提取到的所有信息。

## 样例

### 输入样例：
"请描述一个文档审查和批准的工作流程。
该流程从接收到文档提交开始。首先，由“文档处理服务”负责“准备文档”以供审阅。准备好的文档会存放在一个共享的“待审阅文档缓存”中。
接下来，流程需要并行处理：准备好的文档需要同时发送给“部门A”和“部门B”进行审阅。这两个部门分别执行各自的审阅活动（“部门A审阅”和“部门B审阅”），审阅完成后各自输出审阅状态。
当两个部门的审阅都完成后（需要等待两者均完成），“文档处理服务”会执行“汇总审阅结果”的动作，它接收来自两个部门的审阅状态，并生成一个“最终决策”结果，该决策结果会存放在“审阅决策缓存”中。
然后，基于这个“最终决策”，“文档处理服务”进行判断：如果决策是“批准”，则执行“标记文档已批准”的动作；如果决策是“拒绝”，则执行“标记文档已拒绝”的动作。这两个动作都需要从缓存中获取相应的文档和决策信息。
无论文档是被批准还是被拒绝，处理完成后都会生成一个“通知上下文”（包含批准或拒绝的信息）。
最后，这个“通知上下文”被传递给“通知服务”，由它执行“发送通知”的动作。通知发送完毕后，整个文档审查流程结束。

在这个流程中，不同的动作由不同的服务或部门负责执行：
- 文档处理服务：负责准备文档、汇总结果、标记批准/拒绝。
- 部门A：负责部门A的审阅。
- 部门B：负责部门B的审阅。
- 通知服务：负责发送通知。"

### 输出文本:
请你按照如下的7步进行思考推理并输出：

#### 第一步：识别主要活动和包
- 主要活动是 "主文档审查活动" (act-main-review-uuid)。
- 所有元素可以放在一个名为 "DocumentReview" (pkg-docreview-uuid) 的包中。
- 存在子活动："部门A审查文档" (act-review-dept-a) 和 "部门B审查文档" (act-review-dept-b)。

#### 第二步：识别活动节点 (Actions, Control Nodes, Object Nodes)
- **起始节点**: "接收文档提交" (node-dr-start, InitialNode)。
- **动作节点 (CallBehaviorAction)**:
    - "准备文档" (node-dr-prepare)
    - "部门A审阅" (node-dr-review-a, 调用 act-review-dept-a)
    - "部门B审阅" (node-dr-review-b, 调用 act-review-dept-b)
    - "汇总审阅结果" (node-dr-consolidate)
    - "标记文档已批准" (node-dr-approve)
    - "标记文档已拒绝" (node-dr-reject)
    - "发送通知" (node-dr-send-notify)
- **控制节点**:
    - **ForkNode**: "分发审阅" (node-dr-fork)，用于并行处理部门A和B的审阅。
    - **JoinNode**: "等待审阅完成" (node-dr-join)，用于同步部门A和B的审阅结果。
    - **DecisionNode**: "审阅是否通过?" (node-dr-decision)，用于根据最终决策选择路径。
    - **MergeNode**: "汇合处理路径" (node-dr-merge)，用于合并批准和拒绝后的路径。
    - **ActivityFinalNode**: "审查结束" (node-dr-final)。
- **对象节点 (CentralBufferNode)**:
    - "待审阅文档缓存" (cbuf-dr-prepared-doc)，类型为 PreparedDocument (blk-prepared-doc-uuid)。
    - "审阅决策缓存" (cbuf-dr-final-decision)，类型为 FinalDecision (blk-final-decision-uuid)。

#### 第三步：识别数据类型 (Blocks for Types) 和 参与者/系统 (Blocks for Partitions)
- **数据类型 (Blocks)**:
    - DocumentSubmission (blk-doc-submission-uuid) - 输入
    - PreparedDocument (blk-prepared-doc-uuid) - 准备后的文档
    - ReviewStatus (blk-review-status-uuid) - 审阅状态
    - FinalDecision (blk-final-decision-uuid) - 最终决策
    - NotificationContext (blk-notification-context-uuid) - 通知内容
- **参与者/系统 (Blocks)**:
    - DocumentProcessingService (blk-docproc-svc-uuid)
    - ReviewDeptA (blk-review-a-svc-uuid)
    - ReviewDeptB (blk-review-b-svc-uuid)
    - NotificationService (blk-notify-svc-uuid)

#### 第四步：识别活动分区 (Activity Partitions / Swimlanes)
- "文档处理服务" 分区 (grp-docproc-uuid)，代表 blk-docproc-svc-uuid，包含节点：prepare, consolidate, approve, reject, decision, merge。
- "部门A" 分区 (grp-review-a-uuid)，代表 blk-review-a-svc-uuid，包含节点：review-a。
- "部门B" 分区 (grp-review-b-uuid)，代表 blk-review-b-svc-uuid，包含节点：review-b。
- "通知服务" 分区 (grp-notify-uuid)，代表 blk-notify-svc-uuid，包含节点：send-notify。

#### 第五步：识别引脚 (Pins) 并关联类型
- **Input Pins**:
    - `node-dr-prepare`: `in_提交文档` (pin-dr-prepare-in, type: DocumentSubmission)
    - `node-dr-review-a`: `in_文档` (pin-dr-review-a-in, type: PreparedDocument)
    - `node-dr-review-b`: `in_文档` (pin-dr-review-b-in, type: PreparedDocument)
    - `node-dr-consolidate`: `in_状态A` (pin-dr-consol-a-in, type: ReviewStatus), `in_状态B` (pin-dr-consol-b-in, type: ReviewStatus)
    - `node-dr-approve`: `in_待批准文档` (pin-dr-approve-in-doc, type: PreparedDocument), `in_批准决策` (pin-dr-approve-in-decision, type: FinalDecision)
    - `node-dr-reject`: `in_待拒绝文档` (pin-dr-reject-in-doc, type: PreparedDocument), `in_拒绝决策` (pin-dr-reject-in-decision, type: FinalDecision)
    - `node-dr-send-notify`: `in_通知上下文` (pin-dr-notify-in, type: NotificationContext)
- **Output Pins**:
    - `node-dr-prepare`: `out_待审阅文档` (pin-dr-prepare-out, type: PreparedDocument)
    - `node-dr-review-a`: `out_状态A` (pin-dr-review-a-out, type: ReviewStatus)
    - `node-dr-review-b`: `out_状态B` (pin-dr-review-b-out, type: ReviewStatus)
    - `node-dr-consolidate`: `out_最终决策` (pin-dr-consol-out, type: FinalDecision)
    - `node-dr-approve`: `out_批准通知上下文` (pin-dr-approve-out-ctx, type: NotificationContext)
    - `node-dr-reject`: `out_拒绝通知上下文` (pin-dr-reject-out-ctx, type: NotificationContext)

#### 第六步：识别流 (Control Flow & Object Flow)
- **Control Flows**:
    - start -> prepare (edge-dr-cf1)
    - prepare -> fork (edge-dr-cf2)
    - fork -> review-a (edge-dr-cf4-fork-a)
    - fork -> review-b (edge-dr-cf5-fork-b)
    - review-a -> join (edge-dr-cf6-reviewa-join)
    - review-b -> join (edge-dr-cf7-reviewb-join)
    - join -> consolidate (edge-dr-cf8-join-consol)
    - consolidate -> decision (edge-dr-cf9-consol-decision)
    - decision -> approve (edge-dr-cf10-decision-approve, guard: "[approved]")
    - decision -> reject (edge-dr-cf11-decision-reject, guard: "[rejected]")
    - approve -> merge (edge-dr-cf12-approve-merge)
    - reject -> merge (edge-dr-cf13-reject-merge)
    - merge -> send-notify (edge-dr-cf14-merge-notify)
    - send-notify -> final (edge-dr-cf15-notify-final)
- **Object Flows**:
    - start -> pin-dr-prepare-in (edge-dr-of1-prepare-in, (Implicit submission object))
    - pin-dr-prepare-out -> cbuf-dr-prepared-doc (edge-dr-of2-prepare-buf)
    - cbuf-dr-prepared-doc -> pin-dr-review-a-in (edge-dr-of3-buf-reviewa)
    - cbuf-dr-prepared-doc -> pin-dr-review-b-in (edge-dr-of4-buf-reviewb)
    - pin-dr-review-a-out -> pin-dr-consol-a-in (edge-dr-of5-reviewa-consol)
    - pin-dr-review-b-out -> pin-dr-consol-b-in (edge-dr-of6-reviewb-consol)
    - pin-dr-consol-out -> cbuf-dr-final-decision (edge-dr-of7-consol-buf)
    - cbuf-dr-prepared-doc -> pin-dr-approve-in-doc (edge-dr-of8-buf-approve)
    - cbuf-dr-final-decision -> pin-dr-approve-in-decision (edge-dr-of9-buf-decision-approve)
    * ... (包含所有 JSON 中列出的 Object Flow) ...
    - pin-dr-reject-out-ctx -> pin-dr-notify-in (edge-dr-of13-reject-notify)

#### 第七步：整理优化输出
---
模型: DocumentReviewApprovalModel_WithPartitions (model-docreview-v18-partitions-uuid)
包: DocumentReview (pkg-docreview-uuid)
  包含块 (数据类型): DocumentSubmission, PreparedDocument, ReviewStatus, FinalDecision, NotificationContext
  包含块 (参与者): DocumentProcessingService, ReviewDeptA, ReviewDeptB, NotificationService
  包含活动: 主文档审查活动 (act-main-review-uuid), 部门A审查文档 (act-review-dept-a), 部门B审查文档 (act-review-dept-b)

活动: 主文档审查活动 (act-main-review-uuid) in Package DocumentReview
  节点:
    InitialNode: 接收文档提交 (node-dr-start)
    CallBehaviorAction: 准备文档 (node-dr-prepare) [负责人: 文档处理服务]
      InputPin: in_提交文档 (pin-dr-prepare-in, type: DocumentSubmission)
      OutputPin: out_待审阅文档 (pin-dr-prepare-out, type: PreparedDocument)
    ForkNode: 分发审阅 (node-dr-fork)
    CallBehaviorAction: 部门A审阅 (node-dr-review-a) [负责人: 部门A] [调用: act-review-dept-a]
      InputPin: in_文档 (pin-dr-review-a-in, type: PreparedDocument)
      OutputPin: out_状态A (pin-dr-review-a-out, type: ReviewStatus)
    CallBehaviorAction: 部门B审阅 (node-dr-review-b) [负责人: 部门B] [调用: act-review-dept-b]
      InputPin: in_文档 (pin-dr-review-b-in, type: PreparedDocument)
      OutputPin: out_状态B (pin-dr-review-b-out, type: ReviewStatus)
    JoinNode: 等待审阅完成 (node-dr-join)
    CallBehaviorAction: 汇总审阅结果 (node-dr-consolidate) [负责人: 文档处理服务]
      InputPin: in_状态A (pin-dr-consol-a-in, type: ReviewStatus)
      InputPin: in_状态B (pin-dr-consol-b-in, type: ReviewStatus)
      OutputPin: out_最终决策 (pin-dr-consol-out, type: FinalDecision)
    DecisionNode: 审阅是否通过? (node-dr-decision) [负责人: 文档处理服务]
    CallBehaviorAction: 标记文档已批准 (node-dr-approve) [负责人: 文档处理服务]
      InputPin: in_待批准文档 (pin-dr-approve-in-doc, type: PreparedDocument)
      InputPin: in_批准决策 (pin-dr-approve-in-decision, type: FinalDecision)
      OutputPin: out_批准通知上下文 (pin-dr-approve-out-ctx, type: NotificationContext)
    CallBehaviorAction: 标记文档已拒绝 (node-dr-reject) [负责人: 文档处理服务]
      InputPin: in_待拒绝文档 (pin-dr-reject-in-doc, type: PreparedDocument)
      InputPin: in_拒绝决策 (pin-dr-reject-in-decision, type: FinalDecision)
      OutputPin: out_拒绝通知上下文 (pin-dr-reject-out-ctx, type: NotificationContext)
    MergeNode: 汇合处理路径 (node-dr-merge) [负责人: 文档处理服务]
    CallBehaviorAction: 发送通知 (node-dr-send-notify) [负责人: 通知服务]
      InputPin: in_通知上下文 (pin-dr-notify-in, type: NotificationContext)
    ActivityFinalNode: 审查结束 (node-dr-final)
    CentralBufferNode: 待审阅文档缓存 (cbuf-dr-prepared-doc, type: PreparedDocument)
    CentralBufferNode: 审阅决策缓存 (cbuf-dr-final-decision, type: FinalDecision)
  边 (Flows):
    ControlFlows: (start->prepare), (prepare->fork), (fork->review-a), (fork->review-b), (review-a->join), (review-b->join), (join->consolidate), (consolidate->decision), (decision->approve [approved]), (decision->reject [rejected]), (approve->merge), (reject->merge), (merge->send-notify), (send-notify->final)
    ObjectFlows: (start->prepare.in), (prepare.out->buffer_doc), (buffer_doc->review-a.in), (buffer_doc->review-b.in), (review-a.out->consolidate.in_a), (review-b.out->consolidate.in_b), (consolidate.out->buffer_decision), (buffer_doc->approve.in_doc), (buffer_decision->approve.in_decision), (buffer_doc->reject.in_doc), (buffer_decision->reject.in_decision), (approve.out_ctx->send-notify.in), (reject.out_ctx->send-notify.in)
  分区 (Partitions):
    文档处理服务 (grp-docproc-uuid) represents DocumentProcessingService, contains: prepare, consolidate, decision, approve, reject, merge
    部门A (grp-review-a-uuid) represents ReviewDeptA, contains: review-a
    部门B (grp-review-b-uuid) represents ReviewDeptB, contains: review-b
    通知服务 (grp-notify-uuid) represents NotificationService, contains: send-notify
---
活动: 部门A审查文档 (act-review-dept-a) in Package DocumentReview - (内容未指定)
---
活动: 部门B审查文档 (act-review-dept-b) in Package DocumentReview - (内容未指定)
---

### 思考过程映射JSON
此JSON仅仅作为参考。

```json
{
  "model": [
    {
      "id": "model-docreview-v18-partitions-uuid",
      "name": "DocumentReviewApprovalModel_WithPartitions"
    }
  ],
  "elements": [
    {
      "id": "pkg-docreview-uuid",
      "type": "Package",
      "name": "DocumentReview"
    },
    {
      "id": "blk-doc-submission-uuid",
      "type": "Block",
      "name": "DocumentSubmission",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-prepared-doc-uuid",
      "type": "Block",
      "name": "PreparedDocument",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-review-status-uuid",
      "type": "Block",
      "name": "ReviewStatus",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-final-decision-uuid",
      "type": "Block",
      "name": "FinalDecision",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-notification-context-uuid",
      "type": "Block",
      "name": "NotificationContext",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-docproc-svc-uuid",
      "type": "Block",
      "name": "DocumentProcessingService",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-review-a-svc-uuid",
      "type": "Block",
      "name": "ReviewDeptA",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-review-b-svc-uuid",
      "type": "Block",
      "name": "ReviewDeptB",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "blk-notify-svc-uuid",
      "type": "Block",
      "name": "NotificationService",
      "parentId": "pkg-docreview-uuid"
    },
    {
      "id": "act-main-review-uuid",
      "type": "Activity",
      "name": "主文档审查活动",
      "parentId": "pkg-docreview-uuid",
      "nodes": [
        "cbuf-dr-final-decision",
        "cbuf-dr-prepared-doc",
        "node-dr-approve",
        "node-dr-consolidate",
        "node-dr-decision",
        "node-dr-final",
        "node-dr-fork",
        "node-dr-join",
        "node-dr-merge",
        "node-dr-prepare",
        "node-dr-reject",
        "node-dr-review-a",
        "node-dr-review-b",
        "node-dr-send-notify",
        "node-dr-start"
      ],
      "edges": [
        "edge-dr-cf1",
        "edge-dr-cf10-decision-approve",
        "edge-dr-cf11-decision-reject",
        "edge-dr-cf12-approve-merge",
        "edge-dr-cf13-reject-merge",
        "edge-dr-cf14-merge-notify",
        "edge-dr-cf15-notify-final",
        "edge-dr-cf2",
        "edge-dr-cf4-fork-a",
        "edge-dr-cf5-fork-b",
        "edge-dr-cf6-reviewa-join",
        "edge-dr-cf7-reviewb-join",
        "edge-dr-cf8-join-consol",
        "edge-dr-cf9-consol-decision",
        "edge-dr-of1-prepare-in",
        "edge-dr-of10-buf-reject",
        "edge-dr-of11-buf-decision-reject",
        "edge-dr-of12-approve-notify",
        "edge-dr-of13-reject-notify",
        "edge-dr-of2-prepare-buf",
        "edge-dr-of3-buf-reviewa",
        "edge-dr-of4-buf-reviewb",
        "edge-dr-of5-reviewa-consol",
        "edge-dr-of6-reviewb-consol",
        "edge-dr-of7-consol-buf",
        "edge-dr-of8-buf-approve",
        "edge-dr-of9-buf-decision-approve"
      ],
      "groups": [
        "grp-docproc-uuid",
        "grp-notify-uuid",
        "grp-review-a-uuid",
        "grp-review-b-uuid"
      ]
    },
    {
      "id": "act-review-dept-a",
      "type": "Activity",
      "name": "部门A审查文档",
      "parentId": "pkg-docreview-uuid",
      "nodes": [],
      "edges": [],
      "groups": []
    },
    {
      "id": "act-review-dept-b",
      "type": "Activity",
      "name": "部门B审查文档",
      "parentId": "pkg-docreview-uuid",
      "nodes": [],
      "edges": [],
      "groups": []
    },
    {
      "id": "grp-docproc-uuid",
      "type": "ActivityPartition",
      "name": "文档处理服务",
      "representsId": "blk-docproc-svc-uuid",
      "parentId": "act-main-review-uuid",
      "nodeIds": [
        "node-dr-approve",
        "node-dr-consolidate",
        "node-dr-decision",
        "node-dr-merge",
        "node-dr-prepare",
        "node-dr-reject"
      ]
    },
    {
      "id": "grp-review-a-uuid",
      "type": "ActivityPartition",
      "name": "部门A",
      "representsId": "blk-review-a-svc-uuid",
      "parentId": "act-main-review-uuid",
      "nodeIds": [
        "node-dr-review-a"
      ]
    },
    {
      "id": "grp-review-b-uuid",
      "type": "ActivityPartition",
      "name": "部门B",
      "representsId": "blk-review-b-svc-uuid",
      "parentId": "act-main-review-uuid",
      "nodeIds": [
        "node-dr-review-b"
      ]
    },
    {
      "id": "grp-notify-uuid",
      "type": "ActivityPartition",
      "name": "通知服务",
      "representsId": "blk-notify-svc-uuid",
      "parentId": "act-main-review-uuid",
      "nodeIds": [
        "node-dr-send-notify"
      ]
    },
    {
      "id": "node-dr-start",
      "type": "InitialNode",
      "name": "接收文档提交",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-prepare",
      "type": "CallBehaviorAction",
      "name": "准备文档",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-review-a",
      "type": "CallBehaviorAction",
      "name": "部门A审阅",
      "behavior": "act-review-dept-a",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-review-b",
      "type": "CallBehaviorAction",
      "name": "部门B审阅",
      "behavior": "act-review-dept-b",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-consolidate",
      "type": "CallBehaviorAction",
      "name": "汇总审阅结果",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-approve",
      "type": "CallBehaviorAction",
      "name": "标记文档已批准",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-reject",
      "type": "CallBehaviorAction",
      "name": "标记文档已拒绝",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-send-notify",
      "type": "CallBehaviorAction",
      "name": "发送通知",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "pin-dr-prepare-in",
      "type": "InputPin",
      "name": "in_提交文档",
      "typeId": "blk-doc-submission-uuid",
      "parentId": "node-dr-prepare"
    },
    {
      "id": "pin-dr-review-a-in",
      "type": "InputPin",
      "name": "in_文档",
      "typeId": "blk-prepared-doc-uuid",
      "parentId": "node-dr-review-a"
    },
    {
      "id": "pin-dr-review-b-in",
      "type": "InputPin",
      "name": "in_文档",
      "typeId": "blk-prepared-doc-uuid",
      "parentId": "node-dr-review-b"
    },
    {
      "id": "pin-dr-consol-a-in",
      "type": "InputPin",
      "name": "in_状态A",
      "typeId": "blk-review-status-uuid",
      "parentId": "node-dr-consolidate"
    },
    {
      "id": "pin-dr-consol-b-in",
      "type": "InputPin",
      "name": "in_状态B",
      "typeId": "blk-review-status-uuid",
      "parentId": "node-dr-consolidate"
    },
    {
      "id": "pin-dr-approve-in-doc",
      "type": "InputPin",
      "name": "in_待批准文档",
      "typeId": "blk-prepared-doc-uuid",
      "parentId": "node-dr-approve"
    },
    {
      "id": "pin-dr-approve-in-decision",
      "type": "InputPin",
      "name": "in_批准决策",
      "typeId": "blk-final-decision-uuid",
      "parentId": "node-dr-approve"
    },
    {
      "id": "pin-dr-reject-in-doc",
      "type": "InputPin",
      "name": "in_待拒绝文档",
      "typeId": "blk-prepared-doc-uuid",
      "parentId": "node-dr-reject"
    },
    {
      "id": "pin-dr-reject-in-decision",
      "type": "InputPin",
      "name": "in_拒绝决策",
      "typeId": "blk-final-decision-uuid",
      "parentId": "node-dr-reject"
    },
    {
      "id": "pin-dr-notify-in",
      "type": "InputPin",
      "name": "in_通知上下文",
      "typeId": "blk-notification-context-uuid",
      "parentId": "node-dr-send-notify"
    },
    {
      "id": "pin-dr-prepare-out",
      "type": "OutputPin",
      "name": "out_待审阅文档",
      "typeId": "blk-prepared-doc-uuid",
      "parentId": "node-dr-prepare"
    },
    {
      "id": "pin-dr-review-a-out",
      "type": "OutputPin",
      "name": "out_状态A",
      "typeId": "blk-review-status-uuid",
      "parentId": "node-dr-review-a"
    },
    {
      "id": "pin-dr-review-b-out",
      "type": "OutputPin",
      "name": "out_状态B",
      "typeId": "blk-review-status-uuid",
      "parentId": "node-dr-review-b"
    },
    {
      "id": "pin-dr-consol-out",
      "type": "OutputPin",
      "name": "out_最终决策",
      "typeId": "blk-final-decision-uuid",
      "parentId": "node-dr-consolidate"
    },
    {
      "id": "pin-dr-approve-out-ctx",
      "type": "OutputPin",
      "name": "out_批准通知上下文",
      "typeId": "blk-notification-context-uuid",
      "parentId": "node-dr-approve"
    },
    {
      "id": "pin-dr-reject-out-ctx",
      "type": "OutputPin",
      "name": "out_拒绝通知上下文",
      "typeId": "blk-notification-context-uuid",
      "parentId": "node-dr-reject"
    },
    {
      "id": "cbuf-dr-prepared-doc",
      "type": "CentralBufferNode",
      "name": "待审阅文档缓存",
      "typeId": "blk-prepared-doc-uuid",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "cbuf-dr-final-decision",
      "type": "CentralBufferNode",
      "name": "审阅决策缓存",
      "typeId": "blk-final-decision-uuid",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-fork",
      "type": "ForkNode",
      "name": "分发审阅",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-join",
      "type": "JoinNode",
      "name": "等待审阅完成",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-decision",
      "type": "DecisionNode",
      "name": "审阅是否通过?",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-merge",
      "type": "MergeNode",
      "name": "汇合处理路径",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "node-dr-final",
      "type": "ActivityFinalNode",
      "name": "审查结束",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf1",
      "type": "ControlFlow",
      "sourceId": "node-dr-start",
      "targetId": "node-dr-prepare",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of1-prepare-in",
      "type": "ObjectFlow",
      "sourceId": "node-dr-start",
      "targetId": "pin-dr-prepare-in",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf2",
      "type": "ControlFlow",
      "sourceId": "node-dr-prepare",
      "targetId": "node-dr-fork",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf6-reviewa-join",
      "type": "ControlFlow",
      "sourceId": "node-dr-review-a",
      "targetId": "node-dr-join",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf7-reviewb-join",
      "type": "ControlFlow",
      "sourceId": "node-dr-review-b",
      "targetId": "node-dr-join",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf9-consol-decision",
      "type": "ControlFlow",
      "sourceId": "node-dr-consolidate",
      "targetId": "node-dr-decision",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf12-approve-merge",
      "type": "ControlFlow",
      "sourceId": "node-dr-approve",
      "targetId": "node-dr-merge",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf13-reject-merge",
      "type": "ControlFlow",
      "sourceId": "node-dr-reject",
      "targetId": "node-dr-merge",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf15-notify-final",
      "type": "ControlFlow",
      "sourceId": "node-dr-send-notify",
      "targetId": "node-dr-final",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of2-prepare-buf",
      "type": "ObjectFlow",
      "sourceId": "pin-dr-prepare-out",
      "targetId": "cbuf-dr-prepared-doc",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of5-reviewa-consol",
      "type": "ObjectFlow",
      "sourceId": "pin-dr-review-a-out",
      "targetId": "pin-dr-consol-a-in",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of6-reviewb-consol",
      "type": "ObjectFlow",
      "sourceId": "pin-dr-review-b-out",
      "targetId": "pin-dr-consol-b-in",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of7-consol-buf",
      "type": "ObjectFlow",
      "sourceId": "pin-dr-consol-out",
      "targetId": "cbuf-dr-final-decision",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of12-approve-notify",
      "type": "ObjectFlow",
      "sourceId": "pin-dr-approve-out-ctx",
      "targetId": "pin-dr-notify-in",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of13-reject-notify",
      "type": "ObjectFlow",
      "sourceId": "pin-dr-reject-out-ctx",
      "targetId": "pin-dr-notify-in",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of8-buf-approve",
      "type": "ObjectFlow",
      "sourceId": "cbuf-dr-prepared-doc",
      "targetId": "pin-dr-approve-in-doc",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of4-buf-reviewb",
      "type": "ObjectFlow",
      "sourceId": "cbuf-dr-prepared-doc",
      "targetId": "pin-dr-review-b-in",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of3-buf-reviewa",
      "type": "ObjectFlow",
      "sourceId": "cbuf-dr-prepared-doc",
      "targetId": "pin-dr-review-a-in",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of10-buf-reject",
      "type": "ObjectFlow",
      "sourceId": "cbuf-dr-prepared-doc",
      "targetId": "pin-dr-reject-in-doc",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of9-buf-decision-approve",
      "type": "ObjectFlow",
      "sourceId": "cbuf-dr-final-decision",
      "targetId": "pin-dr-approve-in-decision",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-of11-buf-decision-reject",
      "type": "ObjectFlow",
      "sourceId": "cbuf-dr-final-decision",
      "targetId": "pin-dr-reject-in-decision",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf5-fork-b",
      "type": "ControlFlow",
      "sourceId": "node-dr-fork",
      "targetId": "node-dr-review-b",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf4-fork-a",
      "type": "ControlFlow",
      "sourceId": "node-dr-fork",
      "targetId": "node-dr-review-a",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf8-join-consol",
      "type": "ControlFlow",
      "sourceId": "node-dr-join",
      "targetId": "node-dr-consolidate",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf10-decision-approve",
      "type": "ControlFlow",
      "sourceId": "node-dr-decision",
      "targetId": "node-dr-approve",
      "guard": "[approved]",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf11-decision-reject",
      "type": "ControlFlow",
      "sourceId": "node-dr-decision",
      "targetId": "node-dr-reject",
      "guard": "[rejected]",
      "parentId": "act-main-review-uuid"
    },
    {
      "id": "edge-dr-cf14-merge-notify",
      "type": "ControlFlow",
      "sourceId": "node-dr-merge",
      "targetId": "node-dr-send-notify",
      "parentId": "act-main-review-uuid"
    }
  ]
}

```

## 具体任务
输入：


"""

prompt1_last = "输出：请你一步一步进行推理思考。"
# 输出格式提示
prompt2 = """

根据以上详细的推理和整理优化输出，请严格按照以下 JSON 格式生成 SysML 活动图的完整描述。请确保：
1.  所有 `id` 字段都是全局唯一的。可以使用推理中建议的临时 ID（例如 `node-dr-start`）或生成新的唯一标识符。
2.  `parentId` 正确反映了元素的包含关系（例如，节点、边、分区属于某个活动；活动、块属于某个包）。
3.  `typeId` (用于 Pin 和 CentralBufferNode) 正确引用了相应的 Block ID。
4.  `representsId` (用于 ActivityPartition) 正确引用了代表的参与者 Block ID。
5.  `sourceId` 和 `targetId` (用于 Flow) 正确引用了源和目标元素的 ID。
6.  `behavior` (用于 CallBehaviorAction) 如果调用子活动，应引用子活动的 ID。
7.  `guard` (用于从 DecisionNode 出发的 ControlFlow) 被正确设置。
8.  `nodes`, `edges`, `groups` 列表 (用于 Activity) 包含了其直接子元素的 ID 列表。
9.  `nodeIds` 列表 (用于 ActivityPartition) 包含了该分区内的节点 ID 列表。
10. JSON中，在根下只能含有两个元素：`model`和`elements`

## 示例JSON参考如下
```json
{
  "model": [
    {
      "id": "model-unique-id",
      "name": "ModelName"
    }
  ],
  "elements": [
    // Packages
    {
      "id": "pkg-unique-id",
      "type": "Package",
      "name": "PackageName"
    },
    // Blocks (Data Types and Actors/Systems)
    {
      "id": "blk-data-type-id",
      "type": "Block",
      "name": "DataTypeName",
      "parentId": "pkg-unique-id" // Belongs to package
    },
     {
      "id": "blk-actor-system-id",
      "type": "Block",
      "name": "ActorSystemName",
      "parentId": "pkg-unique-id" // Belongs to package
    },
    // ... other blocks
    // Activities (Main and Sub-Activities)
    {
      "id": "act-main-activity-id",
      "type": "Activity",
      "name": "MainActivityName",
      "parentId": "pkg-unique-id", // Belongs to package
      "nodes": [/* List of node IDs within this activity */],
      "edges": [/* List of edge IDs within this activity */],
      "groups": [/* List of partition IDs within this activity */]
    },
    {
      "id": "act-sub-activity-id",
      "type": "Activity",
      "name": "SubActivityName",
      "parentId": "pkg-unique-id", // Belongs to package
      "nodes": [/* ... */],
      "edges": [/* ... */],
      "groups": [/* ... */]
    },
    // ... other activities
    // Activity Partitions (Swimlanes)
    {
      "id": "grp-partition-id",
      "type": "ActivityPartition",
      "name": "PartitionName",
      "representsId": "blk-actor-system-id", // Links to the Block it represents
      "parentId": "act-main-activity-id", // Belongs to an activity
      "nodeIds": [/* List of node IDs within this partition */]
    },
    // ... other partitions
    // Activity Nodes (Initial, Final, Actions, Control, Object)
    {
      "id": "node-initial-id",
      "type": "InitialNode",
      "name": "InitialNodeName",
      "parentId": "act-main-activity-id" // Belongs to an activity
    },
    {
      "id": "node-action-id",
      "type": "CallBehaviorAction", // or OpaqueAction
      "name": "ActionName",
      "behavior": "act-sub-activity-id", // Optional: if calling another activity
      "parentId": "act-main-activity-id" // Belongs to an activity
      // Pins will be separate elements with parentId linking here
    },
    {
      "id": "node-fork-id",
      "type": "ForkNode",
      "name": "ForkNodeName",
      "parentId": "act-main-activity-id"
    },
    {
      "id": "node-decision-id",
      "type": "DecisionNode",
      "name": "DecisionNodeName",
      "parentId": "act-main-activity-id"
    },
    {
      "id": "node-buffer-id",
      "type": "CentralBufferNode",
      "name": "BufferNodeName",
      "typeId": "blk-data-type-id", // Links to data type Block
      "parentId": "act-main-activity-id"
    },
     {
      "id": "node-final-id",
      "type": "ActivityFinalNode",
      "name": "FinalNodeName",
      "parentId": "act-main-activity-id"
    },
    // ... other nodes
    // Pins (Input and Output)
    {
      "id": "pin-input-id",
      "type": "InputPin",
      "name": "InputPinName",
      "typeId": "blk-data-type-id", // Links to data type Block
      "parentId": "node-action-id" // Belongs to an action node
    },
     {
      "id": "pin-output-id",
      "type": "OutputPin",
      "name": "OutputPinName",
      "typeId": "blk-data-type-id",
      "parentId": "node-action-id"
    },
    // ... other pins
    // Edges (Control Flow and Object Flow)
    {
      "id": "edge-control-flow-id",
      "type": "ControlFlow",
      "sourceId": "node-source-id",
      "targetId": "node-target-id",
      "guard": "[condition]", // Optional: for flows from DecisionNode
      "parentId": "act-main-activity-id" // Belongs to an activity
    },
    {
      "id": "edge-object-flow-id",
      "type": "ObjectFlow",
      "sourceId": "pin-or-node-source-id", // Can be Pin or Node ID
      "targetId": "pin-or-node-target-id", // Can be Pin or Node ID
      "parentId": "act-main-activity-id"
    }
    // ... other edges
  ]
}
```
请严格按照上面的JSON结构输出结果。
"""

def is_valid_json(json_str):
    """检查字符串是否是有效的JSON格式"""
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        logger.error("JSON 不合法")
        return False

def process_activity_task(state: WorkflowState, task_content: str) -> Dict[str, Any]:
    """
    处理活动图任务
    
    参数:
        state: 工作流状态
        task_content: 任务内容
        
    返回:
        处理结果的字典
    """
    logger.info("开始处理活动图任务")
    
    try:
        # 创建LLM
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )
        

        new_prompt1 = prompt1_first + task_content + prompt1_last
        # 执行第一步分析
        first_response = llm.invoke(new_prompt1)
        cot_result = first_response.content
        logger.info("完成第一步分析")
        

        # 执行第二步，生成JSON输出
        second_response = llm.invoke([HumanMessage(content=new_prompt1 + cot_result + prompt2)])
        json_str = second_response.content
        
        # 修复和验证JSON
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()
            
        json_str = re.sub(r'\\(?!["\\/bfnrtu])', r'\\\\', json_str)
        json_str = repair_json(json_str)
        
        if not is_valid_json(json_str):
            logger.error("生成的JSON格式不正确")
            return {"status": "error", "message": "生成的JSON格式不正确"}
        
        result = json.loads(json_str)
        logger.info("活动图任务处理完成")
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"活动图任务处理失败: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}

def activity_agent(state: WorkflowState, task_id: str, task_content: str) -> WorkflowState:
    """
    活动图Agent入口函数
    
    参数:
        state: 当前工作流状态
        task_id: 任务ID
        task_content: 任务内容
        
    返回:
        更新后的工作流状态
    """
    logger.info(f"活动图Agent开始处理任务 {task_id}")
    
    # 查找任务
    task_index = -1
    for i, task in enumerate(state.assigned_tasks):
        if task.id == task_id:
            task_index = i
            break
    
    if task_index == -1:
        state.error_message = f"找不到ID为 {task_id} 的任务"
        return state
    
    # 更新任务状态为进行中
    state.assigned_tasks[task_index].status = ProcessStatus.IN_PROGRESS
    
    try:
        # 处理活动图任务
        result = process_activity_task(state, task_content)
        
        if result["status"] == "success":
            # 更新任务结果和状态
            state.assigned_tasks[task_index].result = result["result"]
            state.assigned_tasks[task_index].status = ProcessStatus.COMPLETED
            logger.info(f"任务 {task_id} 处理完成")
        else:
            # 更新任务状态为失败
            state.assigned_tasks[task_index].status = ProcessStatus.FAILED
            state.assigned_tasks[task_index].error_message = result["message"]
            logger.error(f"任务 {task_id} 处理失败: {result['message']}")
    
    except Exception as e:
        # 更新任务状态为失败
        state.assigned_tasks[task_index].status = ProcessStatus.FAILED
        state.assigned_tasks[task_index].error_message = str(e)
        logger.error(f"任务 {task_id} 处理异常: {str(e)}", exc_info=True)
    
    return state 