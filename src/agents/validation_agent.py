"""
ValidationAgent - 负责验证和修复图数据库中的SysML模型
基于Gemini方案一的图数据库驱动架构
"""
import logging
import json
import uuid
from typing import Dict, List, Any, Optional
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from src.agents.neo4j_storage_agent import Neo4jStorageAgent
from config.settings import settings

logger = logging.getLogger(__name__)

class ValidationAgent:
    """
    模型验证代理，负责验证和修复Neo4j中的SysML模型
    """
    
    def __init__(self):
        """初始化验证代理"""
        self.storage_agent = None
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            timeout=None,
            max_tokens=None,
            temperature=0.0
        )
        
    def process(self, state: WorkflowState) -> WorkflowState:
        """
        处理工作流状态，执行模型验证和修复
        
        参数:
            state: 当前工作流状态
            
        返回:
            更新后的工作流状态
        """
        logger.info("ValidationAgent开始处理")
        
        # 检查是否已经执行过模型验证
        if any(task.type == "Model Validation" for task in state.assigned_tasks):
            logger.info("已经执行过模型验证操作，跳过")
            return state
        
        # 检查是否有实体融合任务或已完成的SysML任务
        fusion_tasks = [task for task in state.assigned_tasks if task.type == "Entity Fusion"]
        completed_sysml_tasks = [task for task in state.assigned_tasks
                               if task.status == ProcessStatus.COMPLETED and
                               task.result and
                               isinstance(task.result, dict) and
                               'elements' in task.result]

        if not fusion_tasks and not completed_sysml_tasks:
            logger.warning("没有找到成功的实体融合任务或SysML任务，无法进行模型验证")
            self._create_validation_task(state, "FAILED", "没有找到成功的实体融合任务或SysML任务")
            return state

        # 如果有融合任务但未完成，检查是否有其他已完成的任务
        if fusion_tasks and not any(task.status == ProcessStatus.COMPLETED for task in fusion_tasks):
            if not completed_sysml_tasks:
                logger.warning("实体融合任务未完成且没有其他可用的SysML任务")
                self._create_validation_task(state, "FAILED", "实体融合任务未完成且没有其他可用的SysML任务")
                return state
        
        try:
            # 初始化Neo4j存储代理
            self.storage_agent = Neo4jStorageAgent()
            
            # 执行验证
            validation_errors = self._validate_model()
            
            if not validation_errors:
                logger.info("模型验证通过，没有发现错误")
                self._create_validation_task(state, "COMPLETED", "模型验证通过", {
                    "errors_found": 0,
                    "errors_fixed": 0
                })
                return state
            
            logger.info(f"发现{len(validation_errors)}个验证错误")
            
            # 尝试修复错误
            fixed_count = 0
            for error in validation_errors:
                try:
                    if self._fix_error(error):
                        fixed_count += 1
                        logger.info(f"成功修复错误: {error['type']}")
                    else:
                        logger.warning(f"修复错误失败: {error['type']}")
                except Exception as e:
                    logger.error(f"修复错误时发生异常: {e}")
            
            # 清理孤立节点
            self._cleanup_orphaned_nodes()

            # 再次验证
            remaining_errors = self._validate_model()

            # 创建验证任务结果
            result_msg = f"发现{len(validation_errors)}个错误，成功修复{fixed_count}个，剩余{len(remaining_errors)}个"
            self._create_validation_task(state, "COMPLETED", result_msg, {
                "errors_found": len(validation_errors),
                "errors_fixed": fixed_count,
                "remaining_errors": len(remaining_errors)
            })

            logger.info(f"模型验证完成: {result_msg}")
            
        except Exception as e:
            error_msg = f"模型验证过程发生异常: {str(e)}"
            self._create_validation_task(state, "FAILED", error_msg)
            logger.error(error_msg, exc_info=True)
            
        finally:
            # 关闭Neo4j连接
            if self.storage_agent:
                self.storage_agent.close()
                self.storage_agent = None
        
        return state
    
    def _validate_model(self) -> List[Dict[str, Any]]:
        """
        验证模型，返回发现的错误列表
        
        返回:
            错误列表，每个错误包含type、description、elements等信息
        """
        errors = []
        
        if not self.storage_agent or not self.storage_agent._driver:
            return errors
        
        try:
            with self.storage_agent._driver.session() as session:
                # 检查悬空引用
                errors.extend(self._check_dangling_references(session))
                
                # 检查循环引用
                errors.extend(self._check_circular_references(session))
                
                # 检查缺失的必要元素
                errors.extend(self._check_missing_elements(session))
                
                # 检查类型一致性
                errors.extend(self._check_type_consistency(session))
                
        except Exception as e:
            logger.error(f"验证过程发生异常: {e}")
            errors.append({
                "type": "validation_exception",
                "description": f"验证过程异常: {str(e)}",
                "severity": "high"
            })
        
        return errors
    
    def _check_dangling_references(self, session) -> List[Dict[str, Any]]:
        """增强的悬空引用检查，基于Gemini方案一的设计"""
        errors = []

        # 检查parentId引用
        query = """
        MATCH (n)
        WHERE n.parentId IS NOT NULL
        AND NOT EXISTS {
            MATCH (parent {elementId: n.parentId})
        }
        RETURN n.elementId as elementId, n.parentId as parentId, n.type as type, n.name as name
        """

        result = session.run(query)
        for record in result:
            errors.append({
                "type": "dangling_parent_reference",
                "description": f"元素{record['elementId']}引用了不存在的父元素{record['parentId']}",
                "element_id": record['elementId'],
                "parent_id": record['parentId'],
                "element_type": record['type'],
                "element_name": record['name'],
                "severity": "medium",
                "repair_context": self._get_repair_context(session, record['elementId'])
            })

        # 检查typeId引用
        query = """
        MATCH (n)
        WHERE n.typeId IS NOT NULL
        AND NOT EXISTS {
            MATCH (type_elem {elementId: n.typeId})
        }
        RETURN n.elementId as elementId, n.typeId as typeId, n.type as type, n.name as name
        """

        result = session.run(query)
        for record in result:
            errors.append({
                "type": "dangling_type_reference",
                "description": f"元素{record['elementId']}引用了不存在的类型{record['typeId']}",
                "element_id": record['elementId'],
                "type_id": record['typeId'],
                "element_type": record['type'],
                "element_name": record['name'],
                "severity": "medium",
                "repair_context": self._get_repair_context(session, record['elementId'])
            })

        # 检查其他ID字段的悬空引用
        id_fields = ['sourceId', 'targetId', 'associationId', 'sourceRequirementId', 'derivedRequirementId']
        for field in id_fields:
            query = f"""
            MATCH (n)
            WHERE n.{field} IS NOT NULL
            AND NOT EXISTS {{
                MATCH (ref {{elementId: n.{field}}})
            }}
            RETURN n.elementId as elementId, n.{field} as refId, n.type as type, n.name as name
            """

            result = session.run(query)
            for record in result:
                errors.append({
                    "type": f"dangling_{field}_reference",
                    "description": f"元素{record['elementId']}的{field}引用了不存在的元素{record['refId']}",
                    "element_id": record['elementId'],
                    "reference_field": field,
                    "reference_id": record['refId'],
                    "element_type": record['type'],
                    "element_name": record['name'],
                    "severity": "medium",
                    "repair_context": self._get_repair_context(session, record['elementId'])
                })

        logger.info(f"🔍 发现{len(errors)}个悬空引用错误")
        return errors
    
    def _check_circular_references(self, session) -> List[Dict[str, Any]]:
        """检查循环引用"""
        errors = []
        
        # 检查CONTAINS关系的循环
        query = """
        MATCH path = (n:SysMLElement)-[:CONTAINS*]->(n)
        RETURN n.elementId as elementId, length(path) as cycle_length
        """
        
        result = session.run(query)
        for record in result:
            errors.append({
                "type": "circular_containment",
                "description": f"元素{record['elementId']}存在循环包含关系",
                "element_id": record['elementId'],
                "cycle_length": record['cycle_length'],
                "severity": "high"
            })
        
        return errors
    
    def _check_missing_elements(self, session) -> List[Dict[str, Any]]:
        """检查缺失的必要元素"""
        errors = []
        
        # 检查是否有根模型
        query = "MATCH (m:Model) RETURN count(m) as model_count"
        result = session.run(query)
        record = result.single()
        
        if record and record['model_count'] == 0:
            errors.append({
                "type": "missing_root_model",
                "description": "缺少根模型元素",
                "severity": "high"
            })
        
        # 检查是否有标准包结构
        standard_packages = ["Requirements", "UseCases", "SystemStructure", "SystemBehavior", "Libraries"]
        for pkg_name in standard_packages:
            query = """
            MATCH (p:Package {name: $pkg_name})
            RETURN count(p) as count
            """
            result = session.run(query, {'pkg_name': pkg_name})
            record = result.single()

            if record and record['count'] == 0:
                errors.append({
                    "type": "missing_standard_package",
                    "description": f"缺少标准包: {pkg_name}",
                    "package_name": pkg_name,
                    "severity": "low"
                })
        
        return errors
    
    def _check_type_consistency(self, session) -> List[Dict[str, Any]]:
        """检查类型一致性"""
        errors = []
        
        # 检查Property元素的typeId是否指向有效类型
        query = """
        MATCH (prop:SysMLElement {type: 'Property'})
        WHERE prop.typeId IS NOT NULL
        OPTIONAL MATCH (type_elem {elementId: prop.typeId})
        WHERE type_elem.type IN ['Block', 'ValueType', 'Enumeration', 'Signal']
        WITH prop, type_elem
        WHERE type_elem IS NULL
        RETURN prop.elementId as elementId, prop.typeId as typeId
        """
        
        result = session.run(query)
        for record in result:
            errors.append({
                "type": "invalid_property_type",
                "description": f"Property {record['elementId']}的typeId {record['typeId']}不是有效的类型",
                "element_id": record['elementId'],
                "type_id": record['typeId'],
                "severity": "medium"
            })
        
        return errors
    
    def _fix_error(self, error: Dict[str, Any]) -> bool:
        """
        修复单个错误

        参数:
            error: 错误信息

        返回:
            是否成功修复
        """
        error_type = error.get("type")

        # 优先使用增强的修复方法
        logger.debug(f"🔧 修复错误: {error_type} - {error.get('description')}")

        if error_type == "dangling_parent_reference":
            # 使用增强的修复方法
            return self._fix_dangling_parent_reference_enhanced(error)
        elif error_type.startswith("dangling_") and error_type.endswith("_reference"):
            # 处理其他悬空引用
            return self._fix_dangling_reference_enhanced(error)
        elif error_type == "circular_containment":
            return self._fix_circular_containment(error)
        elif error_type == "missing_root_model":
            return self._fix_missing_root_model(error)
        elif error_type == "missing_standard_package":
            return self._fix_missing_standard_package(error)
        elif error_type == "invalid_property_type":
            return self._fix_invalid_property_type(error)
        else:
            # 对于未知错误类型，尝试使用LLM获取修复策略
            logger.info(f"🤖 使用LLM修复未知错误类型: {error_type}")
            return self._fix_error_with_llm(error)
    
    def _fix_dangling_parent_reference(self, error: Dict[str, Any]) -> bool:
        """修复悬空父引用"""
        element_id = error.get("element_id")
        parent_id = error.get("parent_id")

        if not element_id or not parent_id:
            return False

        try:
            with self.storage_agent._driver.session() as session:
                # 简化策略：直接移除悬空引用，将元素重新分配到合适的标准包

                # 1. 获取元素信息
                element_query = """
                MATCH (n {elementId: $elementId})
                RETURN n.type as elementType, n.name as elementName
                """
                result = session.run(element_query, {'elementId': element_id})
                record = result.single()

                if not record:
                    return False

                element_type = record['elementType']

                # 2. 根据元素类型确定合适的标准包
                new_parent_id = self._get_standard_package_for_type(element_type)

                # 3. 更新元素的parentId到标准包
                update_query = """
                MATCH (n {elementId: $elementId})
                SET n.parentId = $newParentId
                """
                session.run(update_query, {
                    'elementId': element_id,
                    'newParentId': new_parent_id
                })

                # 4. 创建CONTAINS关系
                contains_query = """
                MATCH (parent {elementId: $parentId})
                MATCH (child {elementId: $childId})
                MERGE (parent)-[:CONTAINS]->(child)
                """
                session.run(contains_query, {
                    'parentId': new_parent_id,
                    'childId': element_id
                })

                logger.info(f"将悬空元素{element_id}重新分配到{new_parent_id}")
                return True

        except Exception as e:
            logger.error(f"修复悬空父引用失败: {e}")
            return False

        return False

    def _get_standard_package_for_type(self, element_type: str) -> str:
        """根据元素类型获取标准包ID"""
        type_to_package = {
            # 需求相关
            "Requirement": "pkg-requirements",
            "TestCase": "pkg-requirements",

            # 用例相关
            "Actor": "pkg-usecases",
            "UseCase": "pkg-usecases",
            "Include": "pkg-usecases",
            "Extend": "pkg-usecases",

            # 结构相关
            "Block": "pkg-structure",
            "Association": "pkg-structure",
            "InterfaceBlock": "pkg-structure",
            "Package": "pkg-structure",

            # 行为相关
            "Activity": "pkg-behavior",
            "StateMachine": "pkg-behavior",
            "Interaction": "pkg-behavior",
            "Node": "pkg-behavior",  # 活动节点归类到行为包

            # 参数相关
            "ConstraintBlock": "pkg-parametrics",

            # 库相关
            "ValueType": "pkg-libraries",
            "Enumeration": "pkg-libraries",
            "Signal": "pkg-libraries",
            "Unit": "pkg-libraries",
        }

        return type_to_package.get(element_type, "pkg-structure")  # 默认放到结构包

    def _cleanup_orphaned_nodes(self):
        """清理孤立节点"""
        logger.info("开始清理孤立节点")

        try:
            with self.storage_agent._driver.session() as session:
                # 查找孤立节点（没有入边也没有出边的节点，除了根模型）
                orphaned_query = """
                MATCH (n:SysMLElement)
                WHERE NOT (n)-[:CONTAINS]-()
                AND NOT ()-[:CONTAINS]->(n)
                AND n.elementId <> 'unified-sysml-model'
                AND n.source = 'validation_fix'
                RETURN n.elementId as elementId, n.type as elementType, n.name as elementName
                """

                result = session.run(orphaned_query)
                orphaned_nodes = list(result)

                if not orphaned_nodes:
                    logger.info("没有发现孤立节点")
                    return

                logger.info(f"发现{len(orphaned_nodes)}个孤立节点")

                for record in orphaned_nodes:
                    element_id = record['elementId']
                    element_type = record['elementType']
                    element_name = record['elementName']

                    # 将孤立节点连接到合适的标准包
                    standard_package = self._get_standard_package_for_type(element_type)

                    # 更新parentId
                    update_query = """
                    MATCH (n {elementId: $elementId})
                    SET n.parentId = $parentId
                    """
                    session.run(update_query, {
                        'elementId': element_id,
                        'parentId': standard_package
                    })

                    # 创建CONTAINS关系
                    contains_query = """
                    MATCH (parent {elementId: $parentId})
                    MATCH (child {elementId: $childId})
                    MERGE (parent)-[:CONTAINS]->(child)
                    """
                    session.run(contains_query, {
                        'parentId': standard_package,
                        'childId': element_id
                    })

                    logger.info(f"将孤立节点{element_name}({element_type})连接到{standard_package}")

        except Exception as e:
            logger.error(f"清理孤立节点失败: {e}")

    def _fix_missing_root_model(self, error: Dict[str, Any]) -> bool:
        """修复缺失的根模型"""
        try:
            with self.storage_agent._driver.session() as session:
                query = """
                CREATE (m:Model {
                    elementId: 'integrated-model-root',
                    type: 'Model',
                    name: 'Integrated SysML Model',
                    source: 'validation_fix'
                })
                """
                session.run(query)
                return True
                
        except Exception as e:
            logger.error(f"修复缺失根模型失败: {e}")
            return False
    
    def _fix_dangling_type_reference(self, error: Dict[str, Any]) -> bool:
        """修复悬空类型引用"""
        element_id = error.get("element_id")
        type_id = error.get("type_id")

        if not element_id or not type_id:
            return False

        try:
            with self.storage_agent._driver.session() as session:
                # 移除悬空的类型引用
                query = """
                MATCH (n {elementId: $elementId})
                REMOVE n.typeId
                """
                session.run(query, {'elementId': element_id})
                return True

        except Exception as e:
            logger.error(f"修复悬空类型引用失败: {e}")
            return False

    def _fix_circular_containment(self, error: Dict[str, Any]) -> bool:
        """修复循环包含关系"""
        element_id = error.get("element_id")

        if not element_id:
            return False

        try:
            with self.storage_agent._driver.session() as session:
                # 删除导致循环的CONTAINS关系
                query = """
                MATCH (n {elementId: $elementId})-[r:CONTAINS*]->(n)
                WITH r[0] as firstRel
                DELETE firstRel
                """
                session.run(query, {'elementId': element_id})
                return True

        except Exception as e:
            logger.error(f"修复循环包含关系失败: {e}")
            return False

    def _fix_invalid_property_type(self, error: Dict[str, Any]) -> bool:
        """修复无效的属性类型"""
        element_id = error.get("element_id")
        type_id = error.get("type_id")

        if not element_id:
            return False

        try:
            with self.storage_agent._driver.session() as session:
                # 移除无效的类型引用
                query = """
                MATCH (n {elementId: $elementId})
                REMOVE n.typeId
                """
                session.run(query, {'elementId': element_id})
                return True

        except Exception as e:
            logger.error(f"修复无效属性类型失败: {e}")
            return False

    def _fix_missing_standard_package(self, error: Dict[str, Any]) -> bool:
        """修复缺失的标准包"""
        package_name = error.get("package_name")
        if not package_name:
            return False

        try:
            with self.storage_agent._driver.session() as session:
                # 找到根模型
                model_query = "MATCH (m:Model) RETURN m.elementId as modelId LIMIT 1"
                result = session.run(model_query)
                record = result.single()

                if not record:
                    return False

                model_id = record['modelId']
                package_id = f"pkg-{package_name.lower()}-generated"

                # 创建标准包
                create_query = """
                CREATE (p:SysMLElement {
                    elementId: $packageId,
                    type: 'Package',
                    name: $packageName,
                    parentId: $modelId,
                    source: 'validation_fix'
                })
                """
                session.run(create_query, {
                    'packageId': package_id,
                    'packageName': package_name,
                    'modelId': model_id
                })

                # 创建包含关系
                contains_query = """
                MATCH (m {elementId: $modelId})
                MATCH (p {elementId: $packageId})
                MERGE (m)-[:CONTAINS]->(p)
                """
                session.run(contains_query, {
                    'modelId': model_id,
                    'packageId': package_id
                })

                return True

        except Exception as e:
            logger.error(f"修复缺失标准包失败: {e}")
            return False

    def _get_repair_context(self, session, element_id: str) -> Dict[str, Any]:
        """
        获取元素的修复上下文信息

        参数:
            session: Neo4j会话
            element_id: 元素ID

        返回:
            修复上下文信息
        """
        try:
            # 获取元素详情和可能的父元素候选
            query = """
            MATCH (n {elementId: $elementId})
            OPTIONAL MATCH (potential_parent)
            WHERE potential_parent.type IN ['Package', 'Block', 'Activity', 'StateMachine']
            AND potential_parent.elementId <> $elementId
            RETURN n as element,
                   collect(DISTINCT {
                       id: potential_parent.elementId,
                       type: potential_parent.type,
                       name: potential_parent.name
                   }) as potential_parents
            """

            result = session.run(query, {'elementId': element_id})
            record = result.single()

            if record:
                return {
                    'element': dict(record['element']),
                    'potential_parents': record['potential_parents'][:10],  # 限制候选数量
                    'element_summary': self._generate_element_summary(dict(record['element']))
                }
            return {}

        except Exception as e:
            logger.error(f"获取修复上下文失败: {e}")
            return {}

    def _generate_element_summary(self, element: Dict) -> str:
        """生成元素摘要"""
        element_type = element.get('type', 'Unknown')
        element_name = element.get('name', 'Unnamed')
        return f"{element_type} '{element_name}'"

    def _fix_dangling_parent_reference_enhanced(self, error: Dict[str, Any]) -> bool:
        """
        增强的悬空父引用修复，使用LLM智能选择父元素

        参数:
            error: 错误信息

        返回:
            是否修复成功
        """
        element_id = error.get("element_id")
        repair_context = error.get("repair_context", {})

        if not element_id or not repair_context:
            return self._fix_dangling_parent_reference(error)  # 回退到原方法

        try:
            # 使用LLM选择最合适的父元素
            repair_suggestion = self._get_llm_repair_suggestion(error, repair_context)

            if repair_suggestion and repair_suggestion.get("action") == "assign_parent":
                new_parent_id = repair_suggestion.get("new_parent_id")
                if new_parent_id:
                    return self._update_parent_reference(element_id, new_parent_id)
            elif repair_suggestion and repair_suggestion.get("action") == "remove_reference":
                return self._remove_parent_reference(element_id)

            # 如果LLM修复失败，回退到原方法
            return self._fix_dangling_parent_reference(error)

        except Exception as e:
            logger.error(f"增强修复悬空父引用失败: {e}")
            return self._fix_dangling_parent_reference(error)  # 回退到原方法

    def _get_llm_repair_suggestion(self, error: Dict[str, Any], repair_context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        使用LLM获取修复建议

        参数:
            error: 错误信息
            repair_context: 修复上下文

        返回:
            修复建议
        """
        try:
            prompt = self._build_repair_prompt(error, repair_context)

            response = self.llm.invoke([HumanMessage(content=prompt)])
            response_text = response.content.strip()

            # 解析JSON响应
            if response_text.startswith('```json'):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith('```'):
                response_text = response_text[3:-3].strip()

            repair_suggestion = json.loads(response_text)

            # 验证修复建议的格式
            if self._validate_repair_suggestion(repair_suggestion):
                return repair_suggestion
            else:
                logger.warning("LLM生成的修复建议格式不正确")
                return None

        except json.JSONDecodeError as e:
            logger.error(f"解析LLM修复建议JSON失败: {e}")
            return None
        except Exception as e:
            logger.error(f"LLM修复建议失败: {e}")
            return None

    def _build_repair_prompt(self, error: Dict[str, Any], repair_context: Dict[str, Any]) -> str:
        """构建修复建议的Prompt"""
        element = repair_context.get('element', {})
        potential_parents = repair_context.get('potential_parents', [])

        prompt = f"""## Role
You are an expert MBSE modeler specializing in SysML model validation and repair.

## Context
I have a SysML element with a dangling reference that needs to be fixed in a Neo4j graph database.

**Problem Element:**
- ID: {error.get('element_id', 'N/A')}
- Type: {error.get('element_type', 'N/A')}
- Name: {error.get('element_name', 'N/A')}
- Issue: {error.get('description', 'N/A')}

**Available Parent Candidates:**"""

        for i, parent in enumerate(potential_parents[:5]):  # 限制显示数量
            prompt += f"""
{i+1}. ID: {parent.get('id', 'N/A')}, Type: {parent.get('type', 'N/A')}, Name: {parent.get('name', 'N/A')}"""

        prompt += f"""

## Task
1. **Analyze:** Based on the element type and name, determine the most appropriate parent from the candidates.
2. **Reason:** Explain your reasoning considering MBSE best practices and SysML containment rules.
3. **Decide:** Choose one of the following actions:
   - "assign_parent": Assign the element to the most suitable parent
   - "remove_reference": Remove the dangling reference if no suitable parent exists

## Output Format
Provide your response as a single JSON object:

{{
  "reasoning": "Your step-by-step analysis here...",
  "action": "assign_parent" or "remove_reference",
  "new_parent_id": "ID of the chosen parent (if assign_parent)",
  "confidence": 0.0-1.0,
  "reason": "Brief reason for the decision"
}}"""

        return prompt

    def _validate_repair_suggestion(self, suggestion: Dict[str, Any]) -> bool:
        """验证修复建议的格式"""
        required_fields = ["reasoning", "action", "confidence", "reason"]

        for field in required_fields:
            if field not in suggestion:
                return False

        if suggestion["action"] not in ["assign_parent", "remove_reference"]:
            return False

        if suggestion["action"] == "assign_parent" and "new_parent_id" not in suggestion:
            return False

        return True

    def _update_parent_reference(self, element_id: str, new_parent_id: str) -> bool:
        """更新父引用"""
        try:
            with self.storage_agent._driver.session() as session:
                query = """
                MATCH (element {elementId: $elementId})
                MATCH (parent {elementId: $parentId})
                SET element.parentId = $parentId
                MERGE (parent)-[:CONTAINS]->(element)
                """
                session.run(query, {
                    'elementId': element_id,
                    'parentId': new_parent_id
                })
                logger.info(f"✅ 成功更新父引用: {element_id} -> {new_parent_id}")
                return True
        except Exception as e:
            logger.error(f"❌ 更新父引用失败: {e}")
            return False

    def _remove_parent_reference(self, element_id: str) -> bool:
        """移除父引用"""
        try:
            with self.storage_agent._driver.session() as session:
                query = """
                MATCH (element {elementId: $elementId})
                REMOVE element.parentId
                """
                session.run(query, {'elementId': element_id})
                logger.info(f"✅ 成功移除悬空父引用: {element_id}")
                return True
        except Exception as e:
            logger.error(f"❌ 移除父引用失败: {e}")
            return False

    def _fix_dangling_reference_enhanced(self, error: Dict[str, Any]) -> bool:
        """
        增强的悬空引用修复，处理各种类型的悬空引用

        参数:
            error: 错误信息

        返回:
            是否修复成功
        """
        element_id = error.get("element_id")
        reference_field = error.get("reference_field")
        reference_id = error.get("reference_id")

        if not element_id:
            return False

        try:
            with self.storage_agent._driver.session() as session:
                if reference_field:
                    # 移除特定字段的悬空引用
                    query = f"""
                    MATCH (n {{elementId: $elementId}})
                    REMOVE n.{reference_field}
                    """
                    session.run(query, {'elementId': element_id})
                    logger.info(f"✅ 成功移除悬空引用: {element_id}.{reference_field}")
                else:
                    # 回退到移除parentId
                    query = """
                    MATCH (n {elementId: $elementId})
                    REMOVE n.parentId
                    """
                    session.run(query, {'elementId': element_id})
                    logger.info(f"✅ 成功移除悬空父引用: {element_id}")

                return True

        except Exception as e:
            logger.error(f"❌ 修复悬空引用失败: {e}")
            return False

    def _get_llm_fix_strategy(self, error: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM获取修复策略"""
        prompt = f"""
## 角色
你是一位SysML模型修复专家。

## 任务
为以下验证错误提供修复策略：

错误类型: {error.get('type')}
错误描述: {error.get('description')}
错误详情: {json.dumps(error, indent=2, ensure_ascii=False)}

## 输出格式
请以JSON格式回复修复策略：
```json
{{
  "action": "remove_reference|create_parent|redirect_reference",
  "parent_name": "建议的父元素名称",
  "parent_type": "建议的父元素类型",
  "reasoning": "修复理由"
}}
```
"""
        
        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            
            # 解析响应
            if "```json" in response.content:
                json_str = response.content.split("```json")[1].split("```")[0].strip()
            else:
                json_str = response.content.strip()
            
            return json.loads(json_str)
            
        except Exception as e:
            logger.error(f"LLM修复策略获取失败: {e}")
            return {"action": "remove_reference"}

    def _fix_error_with_llm(self, error: Dict[str, Any]) -> bool:
        """
        使用LLM修复错误

        参数:
            error: 错误信息

        返回:
            是否成功修复
        """
        try:
            # 获取LLM修复策略
            fix_strategy = self._get_llm_fix_strategy(error)
            action = fix_strategy.get("action", "remove_reference")

            logger.info(f"LLM建议的修复策略: {action}")
            logger.info(f"修复理由: {fix_strategy.get('reasoning', '无')}")

            element_id = error.get("element_id")
            if not element_id:
                logger.warning("错误信息中缺少element_id，无法修复")
                return False

            if action == "remove_reference":
                return self._remove_problematic_reference(error)
            elif action == "create_parent":
                return self._create_missing_parent(error, fix_strategy)
            elif action == "redirect_reference":
                return self._redirect_reference(error, fix_strategy)
            else:
                logger.warning(f"未知的修复动作: {action}")
                return False

        except Exception as e:
            logger.error(f"使用LLM修复错误失败: {e}")
            return False

    def _remove_problematic_reference(self, error: Dict[str, Any]) -> bool:
        """移除有问题的引用"""
        try:
            element_id = error.get("element_id")
            with self.storage_agent._driver.session() as session:
                # 移除有问题的parentId或其他引用
                query = """
                MATCH (n {elementId: $elementId})
                REMOVE n.parentId
                RETURN n
                """
                result = session.run(query, {"elementId": element_id})
                return result.single() is not None

        except Exception as e:
            logger.error(f"移除有问题的引用失败: {e}")
            return False

    def _create_missing_parent(self, error: Dict[str, Any], fix_strategy: Dict[str, Any]) -> bool:
        """创建缺失的父元素"""
        try:
            parent_name = fix_strategy.get("parent_name", "DefaultParent")
            parent_type = fix_strategy.get("parent_type", "Package")
            parent_id = f"generated-{parent_type.lower()}-{uuid.uuid4().hex[:8]}"

            with self.storage_agent._driver.session() as session:
                # 创建父元素
                query = """
                CREATE (p:SysMLElement {
                    elementId: $parentId,
                    type: $parentType,
                    name: $parentName,
                    parentId: 'integrated-model-root'
                })
                RETURN p
                """
                session.run(query, {
                    "parentId": parent_id,
                    "parentType": parent_type,
                    "parentName": parent_name
                })

                # 更新子元素的parentId
                element_id = error.get("element_id")
                update_query = """
                MATCH (n {elementId: $elementId})
                SET n.parentId = $parentId
                RETURN n
                """
                result = session.run(update_query, {
                    "elementId": element_id,
                    "parentId": parent_id
                })

                logger.info(f"创建了父元素 {parent_id} 并更新了子元素 {element_id} 的引用")
                return result.single() is not None

        except Exception as e:
            logger.error(f"创建缺失的父元素失败: {e}")
            return False

    def _redirect_reference(self, error: Dict[str, Any], fix_strategy: Dict[str, Any]) -> bool:
        """重定向引用到合适的元素"""
        try:
            element_id = error.get("element_id")
            target_parent = fix_strategy.get("parent_name", "integrated-model-root")

            with self.storage_agent._driver.session() as session:
                # 查找合适的父元素
                query = """
                MATCH (p:SysMLElement)
                WHERE p.name = $targetParent OR p.elementId = $targetParent
                RETURN p.elementId as parentId
                LIMIT 1
                """
                result = session.run(query, {"targetParent": target_parent})
                record = result.single()

                if record:
                    parent_id = record["parentId"]
                    # 更新引用
                    update_query = """
                    MATCH (n {elementId: $elementId})
                    SET n.parentId = $parentId
                    RETURN n
                    """
                    update_result = session.run(update_query, {
                        "elementId": element_id,
                        "parentId": parent_id
                    })

                    logger.info(f"重定向元素 {element_id} 的引用到 {parent_id}")
                    return update_result.single() is not None
                else:
                    logger.warning(f"未找到目标父元素: {target_parent}")
                    return False

        except Exception as e:
            logger.error(f"重定向引用失败: {e}")
            return False

    def _create_validation_task(self, state: WorkflowState, status: str, message: str, result: Dict[str, Any] = None):
        """
        创建模型验证任务记录
        
        参数:
            state: 工作流状态
            status: 任务状态
            message: 状态消息
            result: 任务结果
        """
        task_status = ProcessStatus.COMPLETED if status == "COMPLETED" else ProcessStatus.FAILED
        
        validation_task = SysMLTask(
            id=f"VALIDATION-TASK-{uuid.uuid4()}",
            type="Model Validation",
            content=message,
            status=task_status,
            result=result,
            error_message=message if status == "FAILED" else None
        )
        
        state.assigned_tasks.append(validation_task)

def sysml_validation_agent(state: WorkflowState) -> WorkflowState:
    """
    SysML模型验证Agent工作流节点入口函数
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    logger.info("SysML模型验证Agent节点开始处理")
    
    agent = ValidationAgent()
    return agent.process(state)
