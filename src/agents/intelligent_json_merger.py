#!/usr/bin/env python3
"""
智能JSON合并Agent - 使用LLM智能合并多个SysML任务的JSON数据
解决ID冲突、概念重复、引用不一致等问题
创建标准MBSE包结构并重新分配元素
"""

import json
import logging
from typing import Dict, List, Any
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from config.settings import settings
from src.graph.workflow_state import  SysMLTask
from src.config.mbse_packages import MBSEPackageConfig

logger = logging.getLogger(__name__)

class IntelligentJSONMerger:
    """智能JSON合并Agent"""
    
    def __init__(self):
        """初始化智能JSON合并Agent"""
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.1
        )
    
    def merge_sysml_tasks(self, tasks: List[SysMLTask]) -> Dict[str, Any]:
        """
        智能合并多个SysML任务的JSON数据

        参数:
            tasks: SysML任务列表

        返回:
            合并后的统一JSON数据
        """
        logger.info(f"🔄 开始智能合并 {len(tasks)} 个SysML任务")

        try:
            # 1. 分析任务间的概念重叠和引用关系
            analysis = self._analyze_task_relationships(tasks)

            # 2. 使用LLM生成合并策略
            merge_strategy = self._generate_merge_strategy(tasks, analysis)

            # 3. 执行合并
            merged_data = self._execute_merge(tasks, merge_strategy)

            # 4. 创建标准MBSE包结构并重新分配元素
            merged_data = self._create_standard_package_structure(merged_data)

            # 5. 验证合并结果
            validation_result = self._validate_merged_data(merged_data, tasks)

            logger.info(f"✅ 智能合并完成: {validation_result['total_elements']} 个元素")

            return merged_data

        except Exception as e:
            logger.error(f"智能合并失败: {e}")
            # 回退到简单合并
            return self._fallback_merge(tasks)
    
    def _analyze_task_relationships(self, tasks: List[SysMLTask]) -> Dict[str, Any]:
        """分析任务间的关系"""
        logger.info("🔍 分析任务间的概念重叠和引用关系")
        
        analysis = {
            'task_summaries': [],
            'potential_duplicates': [],
            'cross_references': [],
            'missing_definitions': []
        }
        
        # 收集所有任务的元素信息
        all_elements = {}
        for task in tasks:
            if task.result and 'elements' in task.result:
                task_elements = {}
                for element in task.result['elements']:
                    element_id = element.get('id')
                    if element_id:
                        task_elements[element_id] = {
                            'type': element.get('type'),
                            'name': element.get('name'),
                            'parentId': element.get('parentId'),
                            'task_type': task.type
                        }
                
                all_elements[task.type] = task_elements
                analysis['task_summaries'].append({
                    'task_type': task.type,
                    'element_count': len(task_elements),
                    'element_types': list(set(elem['type'] for elem in task_elements.values() if elem['type']))
                })
        
        # 分析潜在重复
        for task1_type, elements1 in all_elements.items():
            for task2_type, elements2 in all_elements.items():
                if task1_type >= task2_type:  # 避免重复比较
                    continue
                
                for id1, elem1 in elements1.items():
                    for id2, elem2 in elements2.items():
                        # 检查是否是相同概念的不同表示
                        if (elem1['type'] == elem2['type'] and 
                            elem1['name'] == elem2['name'] and
                            id1 != id2):
                            analysis['potential_duplicates'].append({
                                'task1': task1_type,
                                'id1': id1,
                                'task2': task2_type,
                                'id2': id2,
                                'type': elem1['type'],
                                'name': elem1['name']
                            })
        
        # 分析跨任务引用
        for task_type, elements in all_elements.items():
            for element_id, element in elements.items():
                parent_id = element.get('parentId')
                if parent_id:
                    # 检查父元素是否在当前任务中定义
                    if parent_id not in elements:
                        # 检查是否在其他任务中定义
                        found_in_other_task = False
                        for other_task_type, other_elements in all_elements.items():
                            if other_task_type != task_type and parent_id in other_elements:
                                analysis['cross_references'].append({
                                    'referencing_task': task_type,
                                    'element_id': element_id,
                                    'referenced_id': parent_id,
                                    'defining_task': other_task_type
                                })
                                found_in_other_task = True
                                break
                        
                        if not found_in_other_task:
                            analysis['missing_definitions'].append({
                                'task': task_type,
                                'element_id': element_id,
                                'missing_parent_id': parent_id
                            })
        
        logger.info(f"分析完成: {len(analysis['potential_duplicates'])} 个潜在重复, "
                   f"{len(analysis['cross_references'])} 个跨任务引用, "
                   f"{len(analysis['missing_definitions'])} 个缺失定义")
        
        return analysis
    
    def _generate_merge_strategy(self, tasks: List[SysMLTask], analysis: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM生成合并策略"""
        logger.info("🤖 使用LLM生成智能合并策略")
        
        # 构建LLM提示
        prompt = self._build_merge_strategy_prompt(tasks, analysis)
        
        # 调用LLM
        response = self.llm.invoke([
            SystemMessage(content=self._get_merge_strategy_system_prompt()),
            HumanMessage(content=prompt)
        ])
        
        # 解析LLM响应
        try:
            # 提取JSON部分
            response_text = response.content
            start = response_text.find('{')
            end = response_text.rfind('}') + 1
            
            if start != -1 and end > start:
                json_part = response_text[start:end]
                strategy = json.loads(json_part)
                
                logger.info(f"LLM生成合并策略: {len(strategy.get('id_mappings', []))} 个ID映射, "
                           f"{len(strategy.get('element_merges', []))} 个元素合并")
                
                return strategy
            else:
                logger.warning("无法从LLM响应中提取JSON策略")
                return self._get_default_strategy()
                
        except Exception as e:
            logger.error(f"解析LLM合并策略失败: {e}")
            return self._get_default_strategy()
    
    def _get_merge_strategy_system_prompt(self) -> str:
        """获取合并策略的系统提示"""
        # 获取标准包结构信息
        package_info = []
        for package_type, pkg_info in MBSEPackageConfig.STANDARD_PACKAGES.items():
            package_info.append(f"- {pkg_info.id}: \"{pkg_info.name}\" ({pkg_info.description})")

        package_structure_text = "\n".join(package_info)

        return f"""你是一个专业的SysML数据合并专家。你的任务是分析多个SysML任务的JSON数据，识别概念重复和引用关系，生成智能合并策略。

合并原则：
1. 识别相同概念的不同表示，将它们合并为单一元素
2. 解决ID冲突，建立统一的ID映射
3. 修复跨任务的引用关系
4. 保持SysML模型的完整性和一致性
5. 建立符合MBSE规范的标准包层次结构

标准MBSE包结构：
{package_structure_text}

重要规则：
- 顶层元素（如Block、Requirement、Activity等）应分配到对应的标准包
- 子元素（如Property、Port、State等）应保持与父元素的关系
- 包结构将在后续步骤中自动创建，你只需要关注元素合并和ID映射

请以JSON格式返回合并策略：
{{
  "unified_model": {{
    "id": "统一模型ID",
    "name": "统一模型名称"
  }},
  "package_structure": [
    {{
      "id": "包ID",
      "name": "包名称",
      "parent_id": "父包ID",
      "description": "包描述"
    }}
  ],
  "id_mappings": [
    {{
      "original_task": "原任务类型",
      "original_id": "原ID",
      "unified_id": "统一ID",
      "reason": "映射原因"
    }}
  ],
  "element_merges": [
    {{
      "unified_id": "合并后的统一ID",
      "source_elements": [
        {{
          "task": "任务类型",
          "id": "元素ID"
        }}
      ],
      "merge_strategy": "合并策略",
      "reason": "合并原因"
    }}
  ],
  "reference_fixes": [
    {{
      "element_task": "元素所在任务",
      "element_id": "元素ID",
      "reference_field": "引用字段名",
      "old_reference": "旧引用ID",
      "new_reference": "新引用ID"
    }}
  ]
}}"""
    
    def _build_merge_strategy_prompt(self, tasks: List[SysMLTask], analysis: Dict[str, Any]) -> str:
        """构建合并策略提示"""
        
        prompt = f"""
请分析以下SysML任务数据并生成智能合并策略：

## 任务概览
"""
        
        for summary in analysis['task_summaries']:
            prompt += f"- {summary['task_type']}: {summary['element_count']} 个元素, 类型: {', '.join(summary['element_types'])}\n"
        
        prompt += f"""

## 潜在重复元素 ({len(analysis['potential_duplicates'])} 个)
"""
        for dup in analysis['potential_duplicates'][:10]:  # 限制数量
            prompt += f"- {dup['type']} '{dup['name']}': {dup['task1']}#{dup['id1']} vs {dup['task2']}#{dup['id2']}\n"
        
        prompt += f"""

## 跨任务引用 ({len(analysis['cross_references'])} 个)
"""
        for ref in analysis['cross_references'][:10]:  # 限制数量
            prompt += f"- {ref['referencing_task']}#{ref['element_id']} → {ref['defining_task']}#{ref['referenced_id']}\n"
        
        prompt += f"""

## 缺失定义 ({len(analysis['missing_definitions'])} 个)
"""
        for missing in analysis['missing_definitions'][:10]:  # 限制数量
            prompt += f"- {missing['task']}#{missing['element_id']} 引用缺失的 {missing['missing_parent_id']}\n"
        
        prompt += """

## 详细任务数据
"""
        
        for task in tasks:
            if task.result and 'elements' in task.result:
                elements = task.result['elements'][:5]  # 只显示前5个元素作为示例
                prompt += f"\n### {task.type} 任务 (示例元素)\n"
                for element in elements:
                    prompt += f"- {element.get('type')}: {element.get('name')} (ID: {element.get('id')}, parentId: {element.get('parentId')})\n"
        
        prompt += """

请基于以上分析生成智能合并策略，确保：
1. 消除重复元素
2. 建立统一的ID映射
3. 修复所有引用关系
4. 创建合理的包结构
"""
        
        return prompt

    def _execute_merge(self, tasks: List[SysMLTask], strategy: Dict[str, Any]) -> Dict[str, Any]:
        """执行合并策略"""
        logger.info("🔧 执行智能合并策略")

        # 创建统一的数据结构
        merged_data = {
            "model": [strategy.get("unified_model", {
                "id": "unified-sysml-model",
                "name": "Unified SysML Model"
            })],
            "elements": []
        }

        # 创建ID映射表
        id_mapping = {}
        for mapping in strategy.get("id_mappings", []):
            key = f"{mapping['original_task']}#{mapping['original_id']}"
            id_mapping[key] = mapping['unified_id']

        # 收集所有原始元素
        all_original_elements = {}
        for task in tasks:
            if task.result and 'elements' in task.result:
                for element in task.result['elements']:
                    key = f"{task.type}#{element.get('id')}"
                    all_original_elements[key] = {
                        **element,
                        '_source_task': task.type
                    }

        # 处理元素合并
        processed_elements = set()

        # 1. 处理需要合并的元素
        for merge in strategy.get("element_merges", []):
            unified_id = merge['unified_id']
            source_elements = merge['source_elements']

            # 检查源元素是否存在
            valid_source_elements = []
            for src in source_elements:
                key = f"{src['task']}#{src['id']}"
                if key in all_original_elements:
                    valid_source_elements.append(all_original_elements[key])
                else:
                    logger.warning(f"源元素不存在: {key}")
                    # 尝试查找可能的匹配
                    possible_keys = [k for k in all_original_elements.keys() if src['id'] in k and src['task'] in k]
                    if possible_keys:
                        logger.info(f"可能的匹配键: {possible_keys}")

            if not valid_source_elements:
                logger.warning(f"跳过合并 {unified_id}：没有找到有效的源元素")
                continue

            # 合并多个源元素
            merged_element = self._merge_elements(
                valid_source_elements,
                unified_id,
                merge['merge_strategy']
            )

            merged_data['elements'].append(merged_element)

            # 标记这些元素已处理
            for src in source_elements:
                processed_elements.add(f"{src['task']}#{src['id']}")

        # 2. 处理其他元素（应用ID映射）
        for key, element in all_original_elements.items():
            if key not in processed_elements:
                # 应用ID映射
                new_element = self._apply_id_mapping(element, id_mapping, strategy)
                merged_data['elements'].append(new_element)

        # 3. 添加包结构
        for package in strategy.get("package_structure", []):
            merged_data['elements'].append({
                "id": package['id'],
                "type": "Package",
                "name": package['name'],
                "parentId": package.get('parent_id', 'unified-sysml-model'),
                "_source": "unified_structure",
                "_auto_generated": True
            })

        logger.info(f"合并完成: {len(merged_data['elements'])} 个元素")
        return merged_data

    def _merge_elements(self, source_elements: List[Dict], unified_id: str, merge_strategy: str) -> Dict[str, Any]:
        """合并多个源元素"""
        if not source_elements:
            return {}

        # 以第一个元素为基础
        merged = source_elements[0].copy()
        merged['id'] = unified_id

        # 合并其他元素的属性
        for element in source_elements[1:]:
            for key, value in element.items():
                if key not in merged or not merged[key]:
                    merged[key] = value
                elif key == '_source_task':
                    # 记录所有源任务
                    if isinstance(merged[key], str):
                        merged[key] = [merged[key]]
                    if isinstance(merged[key], list) and value not in merged[key]:
                        merged[key].append(value)

        merged['_merged_from'] = [elem.get('id') for elem in source_elements]
        merged['_merge_strategy'] = merge_strategy

        return merged

    def _apply_id_mapping(self, element: Dict[str, Any], id_mapping: Dict[str, str], strategy: Dict[str, Any]) -> Dict[str, Any]:
        """应用ID映射到元素"""
        new_element = element.copy()

        # 应用ID映射
        task = element.get('_source_task')
        original_id = element.get('id')
        key = f"{task}#{original_id}"

        if key in id_mapping:
            new_element['id'] = id_mapping[key]

        # 修复引用
        for fix in strategy.get("reference_fixes", []):
            if (fix['element_task'] == task and
                fix['element_id'] == original_id and
                fix['reference_field'] in new_element):
                new_element[fix['reference_field']] = fix['new_reference']

        return new_element

    def _create_standard_package_structure(self, merged_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建标准MBSE包结构并重新分配元素

        参数:
            merged_data: 合并后的数据

        返回:
            包含标准包结构的数据
        """
        logger.info("🏗️ 创建标准MBSE包结构并重新分配元素")

        # 获取模型ID
        model_id = "unified-sysml-model"
        if merged_data.get("model") and len(merged_data["model"]) > 0:
            model_id = merged_data["model"][0].get("id", model_id)

        # 1. 创建标准包元素
        standard_packages = MBSEPackageConfig.create_standard_package_elements(model_id)
        logger.info(f"创建了 {len(standard_packages)} 个标准包")

        # 2. 重新分配现有元素到标准包
        reassigned_elements = self._reassign_elements_to_packages(
            merged_data.get("elements", []),
            model_id
        )

        # 3. 合并所有元素（标准包 + 重新分配的元素）
        all_elements = standard_packages + reassigned_elements

        # 4. 验证包结构
        validation_issues = MBSEPackageConfig.validate_package_structure(all_elements)
        if validation_issues["missing_packages"]:
            logger.warning(f"缺少包: {validation_issues['missing_packages']}")
        if validation_issues["incorrect_assignments"]:
            logger.warning(f"元素分配不正确: {len(validation_issues['incorrect_assignments'])} 个")

        # 5. 更新合并数据
        merged_data["elements"] = all_elements

        logger.info(f"包结构创建完成: {len(all_elements)} 个元素")
        return merged_data

    def _reassign_elements_to_packages(self, elements: List[Dict], model_id: str) -> List[Dict]:
        """
        重新分配元素到标准包

        参数:
            elements: 原始元素列表
            model_id: 模型ID

        返回:
            重新分配后的元素列表
        """
        logger.info(f"重新分配 {len(elements)} 个元素到标准包")

        reassigned_elements = []
        element_id_map = {elem.get("id"): elem for elem in elements if elem.get("id")}

        for element in elements:
            element_type = element.get("type")
            element_id = element.get("id")

            # 跳过已经是Package类型的元素（避免重复）
            if element_type == "Package":
                continue

            # 创建元素副本
            new_element = element.copy()

            # 判断是否是子元素（需要保持与父元素的关系）
            if MBSEPackageConfig.is_child_element(element_type):
                # 子元素保持原有的parentId关系
                parent_id = element.get("parentId")
                if parent_id and parent_id in element_id_map:
                    # 父元素存在，保持关系
                    new_element["parentId"] = parent_id
                else:
                    # 父元素不存在，分配到对应的标准包
                    new_element["parentId"] = MBSEPackageConfig.get_package_id_for_element_type(element_type)
            else:
                # 顶层元素分配到对应的标准包
                new_element["parentId"] = MBSEPackageConfig.get_package_id_for_element_type(element_type)

            # 添加包分配信息
            new_element["_package_assigned"] = True
            new_element["_original_parent"] = element.get("parentId")

            reassigned_elements.append(new_element)

        logger.info(f"重新分配完成: {len(reassigned_elements)} 个元素")
        return reassigned_elements

    def _validate_merged_data(self, merged_data: Dict[str, Any], original_tasks: List[SysMLTask]) -> Dict[str, Any]:
        """验证合并结果"""
        validation = {
            'total_elements': len(merged_data.get('elements', [])),
            'original_total': sum(len(task.result.get('elements', [])) for task in original_tasks if task.result),
            'element_types': {},
            'orphaned_references': []
        }

        # 统计元素类型
        element_ids = set()
        for element in merged_data.get('elements', []):
            elem_type = element.get('type')
            element_ids.add(element.get('id'))
            validation['element_types'][elem_type] = validation['element_types'].get(elem_type, 0) + 1

        # 检查孤立引用
        for element in merged_data.get('elements', []):
            parent_id = element.get('parentId')
            if parent_id and parent_id not in element_ids:
                validation['orphaned_references'].append({
                    'element_id': element.get('id'),
                    'missing_parent': parent_id
                })

        logger.info(f"验证结果: {validation['total_elements']} 个元素 "
                   f"(原始: {validation['original_total']}), "
                   f"{len(validation['orphaned_references'])} 个孤立引用")

        return validation

    def _get_default_strategy(self) -> Dict[str, Any]:
        """获取默认合并策略"""
        # 使用标准MBSE包配置
        package_structure = []
        for package_type, package_info in MBSEPackageConfig.STANDARD_PACKAGES.items():
            package_structure.append({
                "id": package_info.id,
                "name": package_info.name,
                "parent_id": package_info.parent_id or "unified-sysml-model",
                "description": package_info.description
            })

        return {
            "unified_model": {
                "id": "unified-sysml-model",
                "name": "Unified SysML Model"
            },
            "package_structure": package_structure,
            "id_mappings": [],
            "element_merges": [],
            "reference_fixes": []
        }

    def _fallback_merge(self, tasks: List[SysMLTask]) -> Dict[str, Any]:
        """回退合并策略"""
        logger.warning("使用回退合并策略")

        merged_data = {
            "model": [{"id": "unified-sysml-model", "name": "Unified SysML Model"}],
            "elements": []
        }

        # 简单合并所有元素
        for task in tasks:
            if task.result and 'elements' in task.result:
                for element in task.result['elements']:
                    element['_source'] = task.type
                    merged_data['elements'].append(element)

        # 即使是回退策略，也要创建标准包结构
        try:
            merged_data = self._create_standard_package_structure(merged_data)
        except Exception as e:
            logger.error(f"回退策略中创建包结构失败: {e}")

        return merged_data
