"""
SysML合并算法 - 纯算法逻辑实现，确保元素完整性
"""
import logging
import json
import copy
from typing import Dict, List, Any, Set, Tuple, Optional, Union

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask

logger = logging.getLogger(__name__)

class SysMLMerger:
    """
    SysML模型合并器 - 采用先平行连接再去重的算法逻辑
    """
    
    def __init__(self):
        """初始化合并器"""
        # 用于存储元素ID到元素的映射
        self.id_map = {}
        # 用于存储类型+名称到元素ID的映射
        self.name_type_map = {}
        # 用于存储别名映射（旧ID到规范ID）
        self.id_alias_map = {}
        # 最终合并结果
        self.result = {
            "model": {
                "id": "model-master-system-uuid",
                "name": "Integrated Vehicle AirConditioner System"
            },
            "elements": []
        }
        
        # 新增：存储所有原始元素，不进行去重
        self.all_original_elements = []
        # 新增：存储元素来源信息 {element_id: task_type}
        self.element_sources = {}
        # 新增：存储可能的重复元素组 {canonical_id: [duplicate_ids]}
        self.potential_duplicates = {}
        # 新增：存储元素的引用关系 {element_id: {ref_field: ref_id}}
        self.element_references = {}
        # 新增：存储父子关系映射 {parent_id: [child_ids]}
        self.parent_child_map = {}
        # 新增：存储子父关系映射 {child_id: parent_id}
        self.child_parent_map = {}
        # 新增：存储特殊元素类型的映射 {element_type: [element_ids]}
        self.type_element_map = {}
        
        # 标准包结构
        self.standard_packages = {
            "pkg-root-uuid": {
                "id": "pkg-root-uuid",
                "type": "Package",
                "name": "SystemModelPackage",
                "parentId": "model-master-system-uuid"
            },
            "pkg-reqs-uuid": {
                "id": "pkg-reqs-uuid",
                "type": "Package",
                "name": "Requirements",
                "parentId": "pkg-root-uuid"
            },
            "pkg-usecases-uuid": {
                "id": "pkg-usecases-uuid",
                "type": "Package",
                "name": "UseCases",
                "parentId": "pkg-root-uuid"
            },
            "pkg-structure-uuid": {
                "id": "pkg-structure-uuid",
                "type": "Package",
                "name": "SystemStructure",
                "parentId": "pkg-root-uuid"
            },
            "pkg-behavior-uuid": {
                "id": "pkg-behavior-uuid",
                "type": "Package",
                "name": "SystemBehavior",
                "parentId": "pkg-root-uuid"
            },
            "pkg-libraries-uuid": {
                "id": "pkg-libraries-uuid",
                "type": "Package",
                "name": "Libraries",
                "parentId": "pkg-root-uuid"
            }
        }
        # 元素类型到标准包的映射
        self.type_to_package = {
            # 需求相关
            "Requirement": "pkg-reqs-uuid",
            "TestCase": "pkg-reqs-uuid",
            "Verify": "pkg-reqs-uuid",
            "Satisfy": "pkg-reqs-uuid",
            # 用例相关
            "Actor": "pkg-usecases-uuid",
            "UseCase": "pkg-usecases-uuid",
            "Include": "pkg-usecases-uuid",  # 添加用例图关系类型
            "Extend": "pkg-usecases-uuid",   # 添加用例图关系类型
            "Generalization": "pkg-usecases-uuid",  # 添加泛化关系
            # 结构相关
            "Block": "pkg-structure-uuid",
            "Property": "pkg-structure-uuid",
            "Port": "pkg-structure-uuid",
            "ProxyPort": "pkg-structure-uuid",
            "Connector": "pkg-structure-uuid",
            "Association": "pkg-structure-uuid",
            "InterfaceBlock": "pkg-structure-uuid",
            # 行为相关
            "Activity": "pkg-behavior-uuid",
            "StateMachine": "pkg-behavior-uuid",
            "Interaction": "pkg-behavior-uuid",
            "State": "pkg-behavior-uuid",
            "Transition": "pkg-behavior-uuid",
            "Region": "pkg-behavior-uuid",
            "CallBehaviorAction": "pkg-behavior-uuid",
            "OpaqueAction": "pkg-behavior-uuid",
            "InitialNode": "pkg-behavior-uuid",
            "ActivityFinalNode": "pkg-behavior-uuid",
            "ControlFlow": "pkg-behavior-uuid",
            "ObjectFlow": "pkg-behavior-uuid",
            "ActivityPartition": "pkg-behavior-uuid",
            "DecisionNode": "pkg-behavior-uuid",
            "MergeNode": "pkg-behavior-uuid",
            "ForkNode": "pkg-behavior-uuid",
            "JoinNode": "pkg-behavior-uuid",
            # 类型与库相关
            "ValueType": "pkg-libraries-uuid",
            "Enumeration": "pkg-libraries-uuid",
            "Signal": "pkg-libraries-uuid",
            "Unit": "pkg-libraries-uuid",
            "ConstraintBlock": "pkg-libraries-uuid",
            "ConstraintParameter": "pkg-libraries-uuid"
        }
        # 需要保持原始parentId的元素类型
        self.keep_parent_types = {
            "State", "Transition", "Region", 
            "CallBehaviorAction", "OpaqueAction", "InitialNode", "ActivityFinalNode",
            "ControlFlow", "ObjectFlow", "ActivityPartition",
            "DecisionNode", "MergeNode", "ForkNode", "JoinNode",
            "Property", "Port", "ProxyPort", "Connector",
            "ConstraintParameter",
            # 添加用例图关系类型
            "Include", "Extend", "Generalization"
        }
        # 引用字段列表
        self.reference_fields = [
            "sourceId", "targetId", "typeId", "representsId", 
            "classifierBehaviorId", "blockId", "requirementId",
            "testCaseId", "coveredId", "messageId", "sendEventId", "receiveEventId",
            # 添加用例图关系特有的引用字段
            "extendedCase", "addition", "contract", "extension"
        ]
        # 活动图特有字段
        self.activity_specific_fields = [
            "nodes", "edges", "groups", "nodeIds"
        ]
        # 状态机特有字段
        self.statemachine_specific_fields = [
            "regions", "states", "transitions"
        ]
        # 交互图特有字段
        self.interaction_specific_fields = [
            "lifelineIds", "messageIds", "fragments"
        ]
        
        # 新增：特殊处理的元素类型
        self.special_element_types = {
            # 状态机相关
            "StateMachine", "State", "Region", "Transition",
            # 活动图相关
            "Activity", "CallBehaviorAction", "OpaqueAction", "InitialNode", 
            "ActivityFinalNode", "ControlFlow", "ObjectFlow", "ActivityPartition",
            "DecisionNode", "MergeNode", "ForkNode", "JoinNode",
            # 用例图相关（新增）
            "Include", "Extend", "Generalization", "Association", "Actor", "UseCase"
        }
        
    def create_standard_packages(self):
        """创建标准包结构"""
        for pkg in self.standard_packages.values():
            self.result["elements"].append(pkg)
            self.id_map[pkg["id"]] = pkg
    
    def is_container_element(self, element_type: str) -> bool:
        """判断元素是否是容器元素"""
        container_types = {
            "Activity", "StateMachine", "Region", "Block", 
            "ConstraintBlock", "Interaction", "Package"
        }
        return element_type in container_types
    
    def should_keep_parent(self, element: Dict[str, Any]) -> bool:
        """判断元素是否应保持原始parentId"""
        if element.get("type") in self.keep_parent_types:
            return True
        return False
    
    def get_target_package(self, element: Dict[str, Any]) -> str:
        """获取元素应该归属的包ID"""
        element_type = element.get("type", "")
        if element_type in self.type_to_package:
            return self.type_to_package[element_type]
        # 默认放入结构包
        return "pkg-structure-uuid"
    
    def merge_element_attributes(self, existing: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并两个元素的属性，保留更完整的信息
        
        策略：
        1. 保留所有字段，不丢失任何信息
        2. 如果字段是列表，合并列表
        3. 如果字段是字典，递归合并
        4. 如果字段是基本类型，保留非空值
        """
        result = copy.deepcopy(existing)
        
        for key, new_value in new.items():
            # 如果是新字段，直接添加
            if key not in result:
                result[key] = new_value
                continue
                
            existing_value = result[key]
            
            # 处理列表类型
            if isinstance(new_value, list) and isinstance(existing_value, list):
                # 合并列表，去重
                merged_list = existing_value.copy()
                for item in new_value:
                    if item not in merged_list:
                        merged_list.append(item)
                result[key] = merged_list
            
            # 处理字典类型
            elif isinstance(new_value, dict) and isinstance(existing_value, dict):
                # 递归合并字典
                result[key] = self.merge_element_attributes(existing_value, new_value)
            
            # 处理基本类型
            else:
                # 如果新值非空且旧值为空，或者新值更详细，则使用新值
                if (existing_value is None or existing_value == "") and (new_value is not None and new_value != ""):
                    result[key] = new_value
                # 如果新值是字符串且比旧值长，可能包含更多信息
                elif (isinstance(new_value, str) and isinstance(existing_value, str) and 
                      len(new_value) > len(existing_value)):
                    result[key] = new_value
        
        return result
    
    def is_same_element(self, elem1: Dict[str, Any], elem2: Dict[str, Any]) -> bool:
        """
        判断两个元素是否是同一个元素
        
        判断标准：
        1. ID相同，或
        2. 类型相同且名称完全相同
        """
        # ID相同
        if elem1.get("id") == elem2.get("id"):
            return True
        
        # 类型相同且名称完全相同
        if (elem1.get("type") == elem2.get("type") and 
            elem1.get("name") == elem2.get("name") and
            elem1.get("type") is not None and elem1.get("name") is not None):
            return True
        
        return False
    
    def collect_all_elements(self, tasks: List[SysMLTask]) -> List[Dict[str, Any]]:
        """
        收集所有任务中的所有元素，不进行任何去重
        
        参数:
            tasks: 已完成的SysML任务列表
            
        返回:
            所有元素的列表
        """
        # 清空原始元素列表，确保重新收集
        self.all_original_elements = []
        self.element_sources = {}
        self.parent_child_map = {}
        self.child_parent_map = {}
        self.type_element_map = {}
        
        for task in tasks:
            if task.result is None:
                continue
                
            # 获取元素列表
            elements = []
            if isinstance(task.result, dict):
                # 处理常规格式
                if "elements" in task.result:
                    elements = task.result.get("elements", [])
                # 处理model+elements格式
                elif "model" in task.result and isinstance(task.result["model"], list):
                    # 添加model元素
                    elements.extend(task.result["model"])
                    # 添加其他元素
                    if "elements" in task.result:
                        elements.extend(task.result["elements"])
            
            # 记录任务类型和元素数量
            task_type = task.type
            logger.info(f"从任务 '{task_type}' 中收集到 {len(elements)} 个元素")
            
            # 处理每个元素，记录来源和建立映射
            for element in elements:
                element_id = element.get("id")
                if not element_id:
                    logger.warning(f"跳过没有ID的元素: {element}")
                    continue
                
                # 记录元素来源
                self.element_sources[element_id] = task_type
                
                # 深拷贝元素，避免后续修改影响原始数据
                element_copy = copy.deepcopy(element)
                self.all_original_elements.append(element_copy)
                
                # 记录元素类型映射
                element_type = element.get("type")
                if element_type:
                    if element_type not in self.type_element_map:
                        self.type_element_map[element_type] = []
                    self.type_element_map[element_type].append(element_id)
                
                # 记录父子关系
                parent_id = element.get("parentId")
                if parent_id:
                    # 子->父映射
                    self.child_parent_map[element_id] = parent_id
                    # 父->子映射
                    if parent_id not in self.parent_child_map:
                        self.parent_child_map[parent_id] = []
                    self.parent_child_map[parent_id].append(element_id)
                
                # 记录元素的引用关系
                for field in self.reference_fields:
                    if field in element:
                        ref_id = element[field]
                        if ref_id:
                            if element_id not in self.element_references:
                                self.element_references[element_id] = {}
                            self.element_references[element_id][field] = ref_id
                
                # 处理特殊字段中的引用列表
                for field in ["nodes", "edges", "groups", "properties", "ports", "connectors"]:
                    if field in element and isinstance(element[field], list):
                        for item_id in element[field]:
                            if item_id:
                                if element_id not in self.element_references:
                                    self.element_references[element_id] = {}
                                if field not in self.element_references[element_id]:
                                    self.element_references[element_id][field] = []
                                self.element_references[element_id][field].append(item_id)
        
        logger.info(f"总共收集到 {len(self.all_original_elements)} 个原始元素")
        logger.info(f"识别到 {len(self.type_element_map)} 种不同的元素类型")
        
        # 记录特殊元素类型的数量
        special_elements_count = sum(len(self.type_element_map.get(t, [])) for t in self.special_element_types)
        logger.info(f"其中包含 {special_elements_count} 个特殊处理的元素（状态机和活动图相关）")
        
        return self.all_original_elements
    
    def process_elements(self, elements: List[Dict[str, Any]]):
        """
        处理所有元素，进行去重和合并
        
        参数:
            elements: 所有元素的列表
        """
        # 第一步：处理所有元素，进行去重和属性合并
        for element in elements:
            element_id = element.get("id")
            if not element_id:
                logger.warning(f"跳过没有ID的元素: {element}")
                continue
            
            # 检查是否已存在相同ID的元素
            if element_id in self.id_map:
                # 合并属性
                existing = self.id_map[element_id]
                merged = self.merge_element_attributes(existing, element)
                self.id_map[element_id] = merged
                logger.debug(f"合并相同ID的元素: {element_id}")
                continue
            
            # 检查是否存在相同类型和名称的元素
            element_type = element.get("type")
            element_name = element.get("name")
            if element_type and element_name:
                key = f"{element_type}:{element_name}"
                if key in self.name_type_map:
                    # 找到相同类型和名称的元素
                    canonical_id = self.name_type_map[key]
                    existing = self.id_map[canonical_id]
                    merged = self.merge_element_attributes(existing, element)
                    self.id_map[canonical_id] = merged
                    # 记录ID别名映射
                    self.id_alias_map[element_id] = canonical_id
                    logger.debug(f"合并相同类型和名称的元素: {element_id} -> {canonical_id}")
                    continue
                else:
                    # 记录类型和名称映射
                    self.name_type_map[key] = element_id
            
            # 新元素，直接添加
            self.id_map[element_id] = copy.deepcopy(element)
        
        # 第二步：调整元素的parentId
        for element_id, element in self.id_map.items():
            # 跳过标准包
            if element_id in self.standard_packages:
                continue
            
            # 获取元素类型
            element_type = element.get("type")
            
            # 如果是Model类型，跳过
            if element_type == "Model":
                continue
            
            # 如果是Package类型且不是标准包，跳过
            if element_type == "Package" and element_id not in self.standard_packages:
                continue
            
            # 检查是否需要保持原始parentId
            if self.should_keep_parent(element):
                # 检查parentId是否在别名映射中
                parent_id = element.get("parentId")
                if parent_id in self.id_alias_map:
                    # 更新为规范ID
                    element["parentId"] = self.id_alias_map[parent_id]
                continue
            
            # 获取目标包ID
            target_pkg_id = self.get_target_package(element)
            # 更新parentId
            element["parentId"] = target_pkg_id
    
    def fix_references(self):
        """
        修复元素间的引用关系
        
        对所有元素的引用字段进行检查，如果引用的ID在别名映射中，
        则更新为规范ID，确保引用关系的一致性。
        """
        logger.info("开始修复元素间的引用关系...")
        
        # 统计修复的引用数量
        fixed_references = 0
        
        # 处理所有元素
        for element_id, element in self.id_map.items():
            # 跳过已经在别名映射中的ID（这些元素将被合并）
            if element_id in self.id_alias_map:
                continue
            
            # 检查所有可能的引用字段
            for field in self.reference_fields:
                if field in element:
                    ref_id = element[field]
                    if ref_id and ref_id in self.id_alias_map:
                        # 更新为规范ID
                        element[field] = self.id_alias_map[ref_id]
                        fixed_references += 1
                        logger.debug(f"修复引用: {element_id}.{field}: {ref_id} -> {self.id_alias_map[ref_id]}")
            
            # 处理特殊字段，如列表中的ID
            array_fields = ["nodes", "edges", "groups", "properties", "ports", "connectors", "nodeIds", "lifelineIds", "messageIds", "fragments"]
            for field in array_fields:
                if field in element and isinstance(element[field], list):
                    updated_list = []
                    has_update = False
                    
                    for item_id in element[field]:
                        if item_id and item_id in self.id_alias_map:
                            updated_list.append(self.id_alias_map[item_id])
                            has_update = True
                            fixed_references += 1
                            logger.debug(f"修复数组引用: {element_id}.{field}: {item_id} -> {self.id_alias_map[item_id]}")
                        else:
                            updated_list.append(item_id)
                    
                    if has_update:
                        element[field] = updated_list
            
            # 处理特殊结构字段
            self._fix_special_structure_references(element)
            
            # 处理用例图特有字段（新增）
            element_type = element.get("type")
            if element_type in ["Include", "Extend"]:
                # 处理Include和Extend关系特有的字段
                for field in ["extendedCase", "addition", "contract", "extension"]:
                    if field in element:
                        ref_id = element[field]
                        if ref_id and ref_id in self.id_alias_map:
                            # 更新为规范ID
                            element[field] = self.id_alias_map[ref_id]
                            fixed_references += 1
                            logger.debug(f"修复用例图引用: {element_id}.{field}: {ref_id} -> {self.id_alias_map[ref_id]}")
        
        logger.info(f"引用关系修复完成，共修复了 {fixed_references} 个引用")
        
        # 记录用例图关系元素的数量
        usecase_relations = [elem_id for elem_id, elem in self.id_map.items() 
                            if elem.get("type") in ["Include", "Extend", "Generalization", "Association"] 
                            and elem_id not in self.id_alias_map]
        logger.info(f"修复后保留了 {len(usecase_relations)} 个用例图关系元素")
    
    def _fix_special_structure_references(self, element: Dict[str, Any]):
        """
        修复特殊结构字段中的引用关系
        
        参数:
            element: 要处理的元素
        """
        # 处理end1/end2结构（如Connector元素）
        for end_field in ["end1", "end2"]:
            if end_field in element and isinstance(element[end_field], dict):
                end = element[end_field]
                
                # 修复partRefId
                if "partRefId" in end and end["partRefId"] in self.id_alias_map:
                    end["partRefId"] = self.id_alias_map[end["partRefId"]]
                
                # 修复portRefId
                if "portRefId" in end and end["portRefId"] in self.id_alias_map:
                    end["portRefId"] = self.id_alias_map[end["portRefId"]]
        
        # 处理规格说明（如ConstraintBlock）
        if "specification" in element and isinstance(element["specification"], dict):
            spec = element["specification"]
            
            # 修复参数引用
            if "parameters" in spec and isinstance(spec["parameters"], list):
                for param in spec["parameters"]:
                    if isinstance(param, dict) and "refId" in param and param["refId"] in self.id_alias_map:
                        param["refId"] = self.id_alias_map[param["refId"]]
        
        # 处理活动图特有的结构
        element_type = element.get("type")
        if element_type == "Activity":
            # 修复活动分区的representsId
            if "partitions" in element and isinstance(element["partitions"], list):
                for partition in element["partitions"]:
                    if isinstance(partition, dict) and "representsId" in partition and partition["representsId"] in self.id_alias_map:
                        partition["representsId"] = self.id_alias_map[partition["representsId"]]
        
        # 处理交互图特有的结构
        elif element_type == "Interaction":
            # 修复生命线的representsId
            if "lifelines" in element and isinstance(element["lifelines"], list):
                for lifeline in element["lifelines"]:
                    if isinstance(lifeline, dict) and "representsId" in lifeline and lifeline["representsId"] in self.id_alias_map:
                        lifeline["representsId"] = self.id_alias_map[lifeline["representsId"]]
    
    def finalize_result(self):
        """生成最终合并结果"""
        # 将id_map中的所有元素添加到结果中
        for element_id, element in self.id_map.items():
            # 跳过已经在别名映射中的ID
            if element_id in self.id_alias_map:
                continue
            
            # 添加到结果
            self.result["elements"].append(element)
    
    def merge(self, state: WorkflowState) -> Dict[str, Any]:
        """
        执行合并过程
        
        参数:
            state: 工作流状态
            
        返回:
            合并结果
        """
        try:
            logger.info("开始执行纯算法逻辑的SysML合并（四阶段流程）")
            
            # 只处理成功完成的任务
            completed_tasks = [task for task in state.assigned_tasks if task.status == ProcessStatus.COMPLETED]
            if not completed_tasks:
                logger.error("没有找到已完成的SysML任务")
                return {"status": "error", "message": "没有找到已完成的SysML任务"}
            
            # 记录任务类型
            task_types = [task.type for task in completed_tasks]
            logger.info(f"找到 {len(completed_tasks)} 个可合并的任务: {', '.join(task_types)}")
            
            # 第一阶段：创建标准包结构
            logger.info("第一阶段：创建标准包结构")
            self.create_standard_packages()
            
            # 第二阶段：收集所有元素，不进行去重
            logger.info("第二阶段：收集所有元素，不进行去重")
            all_elements = self.collect_all_elements(completed_tasks)
            logger.info(f"收集到 {len(all_elements)} 个原始元素")
            
            # 第三阶段：建立元素映射，识别可能的重复元素
            logger.info("第三阶段：建立元素映射，识别可能的重复元素")
            potential_duplicates = self.build_element_mappings()
            duplicate_groups = len(potential_duplicates)
            logger.info(f"识别到 {duplicate_groups} 组可能的重复元素")
            
            # 第四阶段：智能去重，合并确认的重复元素
            logger.info("第四阶段：智能去重，合并确认的重复元素")
            self.smart_deduplication()
            
            # 第五阶段：修复引用关系
            logger.info("第五阶段：修复引用关系")
            self.fix_references()
            
            # 第六阶段：调整元素的parentId
            logger.info("第六阶段：调整元素的parentId")
            self.adjust_parent_ids()
            
            # 第七阶段：生成最终结果
            logger.info("第七阶段：生成最终结果")
            self.finalize_result()
            
            # 统计元素数量和类型
            elements_count = len(self.result["elements"])
            
            # 计算元素类型分布
            element_types = {}
            for element in self.result["elements"]:
                element_type = element.get("type", "未知")
                element_types[element_type] = element_types.get(element_type, 0) + 1
            
            # 记录元素分布信息
            logger.info(f"合并成功，最终模型包含 {elements_count} 个元素")
            logger.info(f"元素类型分布: {element_types}")
            
            # 验证合并结果
            self._validate_merge_result(all_elements)
            
            return {"status": "success", "result": self.result}
            
        except Exception as e:
            logger.error(f"合并SysML任务失败: {str(e)}", exc_info=True)
            return {"status": "error", "message": str(e)}
    
    def adjust_parent_ids(self):
        """
        调整元素的parentId，确保正确的父子关系
        """
        logger.info("开始调整元素的parentId...")
        
        # 统计调整的元素数量
        adjusted_count = 0
        
        # 用例图关系元素统计（新增）
        usecase_relations_adjusted = 0
        usecase_relations_preserved = 0
        
        # 处理所有元素
        for element_id, element in self.id_map.items():
            # 跳过已经在别名映射中的ID（这些元素将被合并）
            if element_id in self.id_alias_map:
                continue
            
            # 跳过标准包
            if element_id in self.standard_packages:
                continue
            
            # 获取元素类型
            element_type = element.get("type")
            
            # 如果是Model类型，跳过
            if element_type == "Model":
                continue
            
            # 如果是Package类型且不是标准包，跳过
            if element_type == "Package" and element_id not in self.standard_packages:
                continue
            
            # 检查是否需要保持原始parentId
            if self.should_keep_parent(element):
                # 检查parentId是否在别名映射中
                parent_id = element.get("parentId")
                if parent_id in self.id_alias_map:
                    # 更新为规范ID
                    element["parentId"] = self.id_alias_map[parent_id]
                    adjusted_count += 1
                    logger.debug(f"调整parentId: {element_id}.parentId: {parent_id} -> {self.id_alias_map[parent_id]}")
                
                # 特殊处理用例图关系元素（新增）
                if element_type in ["Include", "Extend", "Generalization"]:
                    # 检查源节点是否存在
                    source_id = element.get("sourceId")
                    if source_id and source_id in self.id_map:
                        # 确保用例图关系的parentId是其源节点
                        if element.get("parentId") != source_id:
                            old_parent = element.get("parentId")
                            element["parentId"] = source_id
                            adjusted_count += 1
                            usecase_relations_adjusted += 1
                            logger.debug(f"调整用例图关系元素parentId: {element_id}.parentId: {old_parent} -> {source_id}")
                        else:
                            usecase_relations_preserved += 1
                    else:
                        # 源节点不存在，使用目标包
                        target_pkg_id = self.get_target_package(element)
                        old_parent = element.get("parentId")
                        if old_parent != target_pkg_id:
                            element["parentId"] = target_pkg_id
                            adjusted_count += 1
                            usecase_relations_adjusted += 1
                            logger.debug(f"源节点不存在，调整用例图关系元素parentId: {element_id}.parentId: {old_parent} -> {target_pkg_id}")
                
                continue
            
            # 获取目标包ID
            target_pkg_id = self.get_target_package(element)
            
            # 如果当前parentId不是目标包ID，则更新
            current_parent_id = element.get("parentId")
            if current_parent_id != target_pkg_id:
                element["parentId"] = target_pkg_id
                adjusted_count += 1
                logger.debug(f"调整parentId: {element_id}.parentId: {current_parent_id} -> {target_pkg_id}")
        
        logger.info(f"parentId调整完成，共调整了 {adjusted_count} 个元素")
        
        # 记录用例图关系元素的调整情况（新增）
        if usecase_relations_adjusted > 0 or usecase_relations_preserved > 0:
            logger.info(f"用例图关系元素parentId调整：调整了 {usecase_relations_adjusted} 个，保留了 {usecase_relations_preserved} 个")
    
    def _validate_merge_result(self, original_elements: List[Dict[str, Any]]):
        """
        验证合并结果，确保没有元素丢失
        
        参数:
            original_elements: 原始元素列表
        """
        logger.info("开始验证合并结果...")
        
        # 计算原始元素和结果元素的类型分布
        original_types = {}
        for element in original_elements:
            element_type = element.get("type", "未知")
            original_types[element_type] = original_types.get(element_type, 0) + 1
        
        result_types = {}
        for element in self.result["elements"]:
            element_type = element.get("type", "未知")
            result_types[element_type] = result_types.get(element_type, 0) + 1
        
        # 检查是否有元素类型丢失
        missing_types = []
        for element_type, count in original_types.items():
            if element_type not in result_types:
                missing_types.append(element_type)
            elif result_types[element_type] < count * 0.5:  # 如果结果中的元素数量少于原始数量的一半，视为异常
                logger.warning(f"元素类型 {element_type} 可能有丢失: 原始 {count} 个，结果 {result_types[element_type]} 个")
        
        if missing_types:
            logger.warning(f"以下元素类型在结果中完全丢失: {', '.join(missing_types)}")
        
        # 特别关注特殊元素类型
        for element_type in self.special_element_types:
            if element_type in original_types:
                original_count = original_types[element_type]
                result_count = result_types.get(element_type, 0)
                if result_count < original_count:
                    logger.warning(f"特殊元素类型 {element_type} 有丢失: 原始 {original_count} 个，结果 {result_count} 个")
        
        # 特别关注用例图关系元素（新增）
        usecase_relation_types = ["Include", "Extend", "Generalization", "Association"]
        original_usecase_relations = sum(original_types.get(t, 0) for t in usecase_relation_types)
        result_usecase_relations = sum(result_types.get(t, 0) for t in usecase_relation_types)
        
        if original_usecase_relations > 0:
            retention_rate = result_usecase_relations / original_usecase_relations
            logger.info(f"用例图关系元素保留率: {retention_rate:.2%} (原始 {original_usecase_relations} 个，结果 {result_usecase_relations} 个)")
            
            if retention_rate < 0.8:
                logger.warning(f"用例图关系元素保留率低于80%，可能有大量关系丢失")
                
                # 详细分析每种关系类型
                for relation_type in usecase_relation_types:
                    if relation_type in original_types:
                        orig_count = original_types[relation_type]
                        res_count = result_types.get(relation_type, 0)
                        logger.warning(f"  - {relation_type} 关系: 原始 {orig_count} 个，结果 {res_count} 个，保留率 {res_count/orig_count:.2%}")
        
        # 计算总体保留率
        original_count = len(original_elements)
        result_count = len(self.result["elements"]) - len(self.standard_packages)  # 减去标准包的数量
        retention_rate = result_count / original_count if original_count > 0 else 0
        
        logger.info(f"元素保留率: {retention_rate:.2%} (原始 {original_count} 个，结果 {result_count} 个)")
        
        # 如果保留率过低，发出警告
        if retention_rate < 0.8:
            logger.warning("元素保留率低于80%，可能有大量元素丢失")
        
        logger.info("合并结果验证完成")

    def build_element_mappings(self):
        """
        创建多维度的元素映射，识别可能的重复元素
        
        这个方法会建立以下映射：
        1. ID映射：元素ID到元素的映射
        2. 类型+名称映射：类型和名称组合到元素ID的映射
        3. 可能的重复元素组：规范ID到可能重复元素ID列表的映射
        """
        logger.info("开始建立元素映射关系...")
        
        # 清空现有映射
        self.id_map = {}
        self.name_type_map = {}
        self.potential_duplicates = {}
        
        # 首先添加标准包到ID映射
        for pkg_id, pkg in self.standard_packages.items():
            self.id_map[pkg_id] = pkg
        
        # 处理所有原始元素
        for element in self.all_original_elements:
            element_id = element.get("id")
            if not element_id:
                continue
            
            element_type = element.get("type")
            element_name = element.get("name")
            
            # 添加到ID映射
            if element_id not in self.id_map:
                self.id_map[element_id] = copy.deepcopy(element)
            else:
                # ID冲突，记录可能的重复
                if element_id not in self.potential_duplicates:
                    self.potential_duplicates[element_id] = []
                self.potential_duplicates[element_id].append(element_id)
                logger.debug(f"发现ID冲突: {element_id}")
            
            # 添加到类型+名称映射
            if element_type and element_name:
                key = f"{element_type}:{element_name}"
                if key not in self.name_type_map:
                    self.name_type_map[key] = element_id
                else:
                    # 类型+名称冲突，记录可能的重复
                    canonical_id = self.name_type_map[key]
                    if canonical_id != element_id:  # 避免重复添加相同ID
                        if canonical_id not in self.potential_duplicates:
                            self.potential_duplicates[canonical_id] = []
                        if element_id not in self.potential_duplicates[canonical_id]:
                            self.potential_duplicates[canonical_id].append(element_id)
                        logger.debug(f"发现类型+名称冲突: {element_id} -> {canonical_id}")
        
        # 统计可能的重复元素
        duplicate_count = sum(len(duplicates) for duplicates in self.potential_duplicates.values())
        logger.info(f"建立了 {len(self.id_map)} 个元素的ID映射")
        logger.info(f"建立了 {len(self.name_type_map)} 个元素的类型+名称映射")
        logger.info(f"识别到 {len(self.potential_duplicates)} 组可能的重复元素，共 {duplicate_count} 个元素")
        
        # 特殊处理状态机和活动图元素
        self._build_special_element_mappings()
        
        return self.potential_duplicates
    
    def _build_special_element_mappings(self):
        """
        为特殊元素类型（状态机和活动图相关）建立更精确的映射
        """
        logger.info("开始处理特殊元素类型的映射...")
        
        # 处理状态机元素
        self._map_state_machine_elements()
        
        # 处理活动图元素
        self._map_activity_elements()
    
    def _map_state_machine_elements(self):
        """处理状态机相关元素的映射"""
        # 获取所有状态机元素
        state_machines = self.type_element_map.get("StateMachine", [])
        logger.info(f"处理 {len(state_machines)} 个状态机元素")
        
        for sm_id in state_machines:
            # 获取状态机的所有子元素（Region、State、Transition等）
            if sm_id in self.parent_child_map:
                children = self.parent_child_map[sm_id]
                logger.debug(f"状态机 {sm_id} 有 {len(children)} 个直接子元素")
                
                # 递归处理所有子元素
                self._process_state_machine_hierarchy(sm_id, children)
    
    def _process_state_machine_hierarchy(self, parent_id, child_ids):
        """递归处理状态机层次结构中的元素"""
        for child_id in child_ids:
            # 检查子元素是否有自己的子元素
            if child_id in self.parent_child_map:
                grandchildren = self.parent_child_map[child_id]
                self._process_state_machine_hierarchy(child_id, grandchildren)
            
            # 获取子元素
            if child_id in self.id_map:
                child = self.id_map[child_id]
                child_type = child.get("type")
                
                # 特殊处理Transition元素
                if child_type == "Transition":
                    # 记录转换的源和目标，这对于识别相似转换很重要
                    source_id = child.get("sourceId")
                    target_id = child.get("targetId")
                    if source_id and target_id:
                        transition_key = f"Transition:{source_id}->{target_id}"
                        # 将这个键添加到一个特殊映射中，用于后续去重
                        if transition_key not in self.name_type_map:
                            self.name_type_map[transition_key] = child_id
                        else:
                            # 可能的重复转换
                            canonical_id = self.name_type_map[transition_key]
                            if canonical_id != child_id:
                                if canonical_id not in self.potential_duplicates:
                                    self.potential_duplicates[canonical_id] = []
                                if child_id not in self.potential_duplicates[canonical_id]:
                                    self.potential_duplicates[canonical_id].append(child_id)
                                logger.debug(f"发现可能的重复转换: {child_id} -> {canonical_id}")
    
    def _map_activity_elements(self):
        """处理活动图相关元素的映射"""
        # 获取所有活动图元素
        activities = self.type_element_map.get("Activity", [])
        logger.info(f"处理 {len(activities)} 个活动图元素")
        
        for act_id in activities:
            # 获取活动图的所有节点和边
            if act_id in self.id_map:
                activity = self.id_map[act_id]
                nodes = activity.get("nodes", [])
                edges = activity.get("edges", [])
                logger.debug(f"活动图 {act_id} 有 {len(nodes)} 个节点和 {len(edges)} 条边")
                
                # 处理所有节点
                for node_id in nodes:
                    if node_id in self.id_map:
                        node = self.id_map[node_id]
                        node_type = node.get("type")
                        node_name = node.get("name", "")
                        
                        # 为节点创建更精确的映射键
                        if node_type:
                            # 使用类型、名称和父活动图ID创建唯一键
                            node_key = f"{node_type}:{node_name}:in:{act_id}"
                            if node_key not in self.name_type_map:
                                self.name_type_map[node_key] = node_id
                            else:
                                # 可能的重复节点
                                canonical_id = self.name_type_map[node_key]
                                if canonical_id != node_id:
                                    if canonical_id not in self.potential_duplicates:
                                        self.potential_duplicates[canonical_id] = []
                                    if node_id not in self.potential_duplicates[canonical_id]:
                                        self.potential_duplicates[canonical_id].append(node_id)
                                    logger.debug(f"发现可能的重复活动节点: {node_id} -> {canonical_id}")
                
                # 处理所有边
                for edge_id in edges:
                    if edge_id in self.id_map:
                        edge = self.id_map[edge_id]
                        edge_type = edge.get("type")
                        source_id = edge.get("sourceId")
                        target_id = edge.get("targetId")
                        
                        # 为边创建更精确的映射键
                        if edge_type and source_id and target_id:
                            edge_key = f"{edge_type}:{source_id}->{target_id}"
                            if edge_key not in self.name_type_map:
                                self.name_type_map[edge_key] = edge_id
                            else:
                                # 可能的重复边
                                canonical_id = self.name_type_map[edge_key]
                                if canonical_id != edge_id:
                                    if canonical_id not in self.potential_duplicates:
                                        self.potential_duplicates[canonical_id] = []
                                    if edge_id not in self.potential_duplicates[canonical_id]:
                                        self.potential_duplicates[canonical_id].append(edge_id)
                                    logger.debug(f"发现可能的重复活动边: {edge_id} -> {canonical_id}")

    def smart_deduplication(self):
        """
        基于映射关系进行智能去重
        
        对于确定的重复元素，合并属性并创建ID别名映射
        对于不确定的情况，保留所有元素，避免信息丢失
        """
        logger.info("开始执行智能去重...")
        
        # 清空ID别名映射
        self.id_alias_map = {}
        
        # 处理所有可能的重复元素组
        processed_count = 0
        merged_count = 0
        preserved_count = 0
        
        # 用例图关系元素统计（新增）
        usecase_relations_processed = 0
        usecase_relations_merged = 0
        usecase_relations_preserved = 0
        
        for canonical_id, duplicate_ids in self.potential_duplicates.items():
            processed_count += len(duplicate_ids)
            
            # 获取规范元素
            if canonical_id not in self.id_map:
                logger.warning(f"规范ID {canonical_id} 不在ID映射中，跳过")
                continue
                
            canonical_element = self.id_map[canonical_id]
            canonical_type = canonical_element.get("type")
            
            # 对于特殊元素类型，使用更保守的去重策略
            if canonical_type in self.special_element_types:
                # 检查是否是用例图关系元素（新增）
                is_usecase_relation = canonical_type in ["Include", "Extend", "Generalization", "Association"]
                if is_usecase_relation:
                    usecase_relations_processed += len(duplicate_ids)
                
                # 检查是否确实是重复元素
                for dup_id in duplicate_ids:
                    if dup_id == canonical_id:
                        continue  # 跳过自身
                        
                    if dup_id not in self.id_map:
                        continue
                        
                    dup_element = self.id_map[dup_id]
                    
                    # 检查特殊属性是否匹配
                    if self._is_special_element_duplicate(canonical_element, dup_element):
                        # 确认是重复元素，合并属性
                        merged_element = self.merge_element_attributes(canonical_element, dup_element)
                        self.id_map[canonical_id] = merged_element
                        
                        # 创建ID别名映射
                        self.id_alias_map[dup_id] = canonical_id
                        merged_count += 1
                        
                        # 更新用例图关系元素统计（新增）
                        if is_usecase_relation:
                            usecase_relations_merged += 1
                            logger.debug(f"合并用例图关系元素: {dup_id} -> {canonical_id}")
                        else:
                            logger.debug(f"合并特殊元素: {dup_id} -> {canonical_id}")
                    else:
                        # 不确定是重复元素，保留
                        preserved_count += 1
                        
                        # 更新用例图关系元素统计（新增）
                        if is_usecase_relation:
                            usecase_relations_preserved += 1
                            logger.debug(f"保留用例图关系元素: {dup_id}")
                        else:
                            logger.debug(f"保留特殊元素: {dup_id}")
            else:
                # 对于普通元素类型，使用标准去重策略
                for dup_id in duplicate_ids:
                    if dup_id == canonical_id:
                        continue  # 跳过自身
                        
                    if dup_id not in self.id_map:
                        continue
                        
                    dup_element = self.id_map[dup_id]
                    
                    # 合并属性
                    merged_element = self.merge_element_attributes(canonical_element, dup_element)
                    self.id_map[canonical_id] = merged_element
                    
                    # 创建ID别名映射
                    self.id_alias_map[dup_id] = canonical_id
                    merged_count += 1
                    logger.debug(f"合并普通元素: {dup_id} -> {canonical_id}")
        
        logger.info(f"智能去重完成，处理了 {processed_count} 个可能重复元素")
        logger.info(f"合并了 {merged_count} 个确认重复的元素")
        logger.info(f"保留了 {preserved_count} 个不确定的元素")
        
        # 记录用例图关系元素统计（新增）
        if usecase_relations_processed > 0:
            logger.info(f"用例图关系元素处理统计：处理了 {usecase_relations_processed} 个，"
                        f"合并了 {usecase_relations_merged} 个，保留了 {usecase_relations_preserved} 个")
        
        # 统计最终保留的用例图关系元素数量（新增）
        final_usecase_relations = [elem_id for elem_id, elem in self.id_map.items() 
                                if elem.get("type") in ["Include", "Extend", "Generalization", "Association"] 
                                and elem_id not in self.id_alias_map]
        logger.info(f"去重后保留了 {len(final_usecase_relations)} 个用例图关系元素")
        
        return self.id_alias_map
    
    def _is_special_element_duplicate(self, elem1: Dict[str, Any], elem2: Dict[str, Any]) -> bool:
        """
        判断两个特殊元素是否是重复元素
        
        参数:
            elem1: 第一个元素
            elem2: 第二个元素
            
        返回:
            如果是重复元素，返回True；否则返回False
        """
        # 检查基本属性
        if elem1.get("type") != elem2.get("type"):
            return False
            
        element_type = elem1.get("type")
        
        # 用例图关系元素（新增）
        if element_type in ["Include", "Extend", "Generalization", "Association"]:
            return self._is_usecase_relation_duplicate(elem1, elem2)
        
        # 根据元素类型使用不同的判断标准
        if element_type == "State":
            # 对于状态，检查名称和父Region
            if elem1.get("name") != elem2.get("name"):
                return False
                
            # 检查父Region是否相同或已被确认为重复
            parent1 = elem1.get("parentId")
            parent2 = elem2.get("parentId")
            
            if parent1 == parent2:
                return True
                
            # 检查父Region是否在别名映射中
            if parent2 in self.id_alias_map and self.id_alias_map[parent2] == parent1:
                return True
                
            return False
            
        elif element_type == "Transition":
            # 对于转换，检查源和目标
            source1 = elem1.get("sourceId")
            target1 = elem1.get("targetId")
            source2 = elem2.get("sourceId")
            target2 = elem2.get("targetId")
            
            # 直接匹配
            if source1 == source2 and target1 == target2:
                return True
                
            # 检查源和目标是否在别名映射中
            if source2 in self.id_alias_map:
                source2 = self.id_alias_map[source2]
            if target2 in self.id_alias_map:
                target2 = self.id_alias_map[target2]
                
            return source1 == source2 and target1 == target2
            
        elif element_type in ["ControlFlow", "ObjectFlow"]:
            # 对于控制流和对象流，检查源和目标
            source1 = elem1.get("sourceId")
            target1 = elem1.get("targetId")
            source2 = elem2.get("sourceId")
            target2 = elem2.get("targetId")
            
            # 直接匹配
            if source1 == source2 and target1 == target2:
                return True
                
            # 检查源和目标是否在别名映射中
            if source2 in self.id_alias_map:
                source2 = self.id_alias_map[source2]
            if target2 in self.id_alias_map:
                target2 = self.id_alias_map[target2]
                
            return source1 == source2 and target1 == target2
            
        elif element_type in ["CallBehaviorAction", "OpaqueAction", "InitialNode", "ActivityFinalNode", "DecisionNode", "MergeNode", "ForkNode", "JoinNode"]:
            # 对于活动节点，检查名称和父活动图
            if elem1.get("name") != elem2.get("name") and (elem1.get("name") or elem2.get("name")):
                return False
                
            # 检查父活动图是否相同或已被确认为重复
            parent1 = elem1.get("parentId")
            parent2 = elem2.get("parentId")
            
            if parent1 == parent2:
                return True
                
            # 检查父活动图是否在别名映射中
            if parent2 in self.id_alias_map and self.id_alias_map[parent2] == parent1:
                return True
                
            return False
            
        # 对于其他特殊元素类型，使用默认判断标准
        return elem1.get("name") == elem2.get("name") and elem1.get("type") == elem2.get("type")
        
    def _is_usecase_relation_duplicate(self, elem1: Dict[str, Any], elem2: Dict[str, Any]) -> bool:
        """
        判断两个用例图关系元素是否是重复元素
        
        参数:
            elem1: 第一个元素
            elem2: 第二个元素
            
        返回:
            如果是重复元素，返回True；否则返回False
        """
        # 检查基本属性
        if elem1.get("type") != elem2.get("type"):
            return False
            
        element_type = elem1.get("type")
        
        # 根据关系类型使用不同的判断标准
        if element_type in ["Include", "Extend"]:
            # 对于Include和Extend关系，检查源和目标
            source1 = elem1.get("sourceId")
            target1 = elem1.get("targetId")
            source2 = elem2.get("sourceId")
            target2 = elem2.get("targetId")
            
            # 直接匹配
            if source1 == source2 and target1 == target2:
                return True
                
            # 检查源和目标是否在别名映射中
            if source2 in self.id_alias_map:
                source2 = self.id_alias_map[source2]
            if target2 in self.id_alias_map:
                target2 = self.id_alias_map[target2]
                
            return source1 == source2 and target1 == target2
        
        elif element_type == "Generalization":
            # 对于泛化关系，检查源和目标
            source1 = elem1.get("sourceId")
            target1 = elem1.get("targetId")
            source2 = elem2.get("sourceId")
            target2 = elem2.get("targetId")
            
            # 直接匹配
            if source1 == source2 and target1 == target2:
                return True
                
            # 检查源和目标是否在别名映射中
            if source2 in self.id_alias_map:
                source2 = self.id_alias_map[source2]
            if target2 in self.id_alias_map:
                target2 = self.id_alias_map[target2]
                
            return source1 == source2 and target1 == target2
        
        elif element_type == "Association":
            # 对于关联关系，检查源和目标
            source1 = elem1.get("sourceId")
            target1 = elem1.get("targetId")
            source2 = elem2.get("sourceId")
            target2 = elem2.get("targetId")
            
            # 直接匹配
            if source1 == source2 and target1 == target2:
                return True
                
            # 检查源和目标是否在别名映射中
            if source2 in self.id_alias_map:
                source2 = self.id_alias_map[source2]
            if target2 in self.id_alias_map:
                target2 = self.id_alias_map[target2]
                
            return source1 == source2 and target1 == target2
        
        # 对于其他类型，使用默认判断标准
        return elem1.get("name") == elem2.get("name")


def algorithmic_merge_sysml_tasks(state: WorkflowState) -> Dict[str, Any]:
    """
    使用纯算法逻辑合并所有SysML任务的输出为一个统一的JSON
    
    参数:
        state: 工作流状态
        
    返回:
        合并后的JSON结果的字典
    """
    merger = SysMLMerger()
    return merger.merge(state) 