"""
参数图agent - 负责基于输入内容创建SysML参数图
"""
import logging
import json
from typing import Dict, Any
import re
from langchain.schema import HumanMessage
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI

from src.graph.workflow_state import WorkflowState, ProcessStatus
from config.settings import settings
from json_repair import repair_json

logger = logging.getLogger(__name__)

# 系统提示模板 - 参数图建模
prompt1_first = """
## 角色
你是一位专业的参数图分析专家，擅长从工程问题描述中提取参数关系图元素。你精通SysML参数图规范，能准确识别参数块、约束属性及其数学关系，并对元素间的引用关系进行严格校验。

## 规则
以下是我的提取输出规则：
```JSON
{
  "model": [ { "id": "model-cms-unique-id", "name": "CircularMotionModel" } ],
  "elements": [
    { "id": "pkg-cms-unique-id", "type": "Package", "name": "ParametricDiagram", "parentId": "model-cms-unique-id" },
    { "id": "block1", "type": "Block", "name": "CircularMotionSystem", "parentId": "pkg-cms-unique-id" },
    { "id": "prop1", "type": "Property", "name": "r", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop2", "type": "Property", "name": "ω", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop3", "type": "Property", "name": "v", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop4", "type": "Property", "name": "F", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop5", "type": "Property", "name": "m", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "constraintBlock1", "type": "ConstraintBlock", "name": "VelocityEquation", "parentId": "pkg-cms-unique-id", "specification": {"expression": "v = ω * r", "language": "English"} },
    { "id": "param1", "type": "ConstraintParameter", "name": "v", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "param2", "type": "ConstraintParameter", "name": "ω", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "param3", "type": "ConstraintParameter", "name": "r", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "constraintBlock2", "type": "ConstraintBlock", "name": "CentrifugalForceEquation", "parentId": "pkg-cms-unique-id", "specification": {"expression": "F = m * v^2 / r", "language": "English"} },
    { "id": "param4", "type": "ConstraintParameter", "name": "F", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param5", "type": "ConstraintParameter", "name": "m", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param6", "type": "ConstraintParameter", "name": "v", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param7", "type": "ConstraintParameter", "name": "r", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "constraint1", "type": "Property", "name": "VelocityEquation", "propertyKind": "constraint", "parentId": "block1", "typeId": "constraintBlock1" },
    { "id": "constraint2", "type": "Property", "name": "CentrifugalForceEquation", "propertyKind": "constraint", "parentId": "block1", "typeId": "constraintBlock2" },
    # ... connectors ...
    { "id": "conn1", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop3"}, "end2": {"partRefId": "constraint1", "portRefId": "param1"} },
    { "id": "conn2", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop2"}, "end2": {"partRefId": "constraint1", "portRefId": "param2"} },
    { "id": "conn3", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop1"}, "end2": {"partRefId": "constraint1", "portRefId": "param3"} },
    { "id": "conn4", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop4"}, "end2": {"partRefId": "constraint2", "portRefId": "param4"} },
    { "id": "conn5", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop5"}, "end2": {"partRefId": "constraint2", "portRefId": "param5"} },
    { "id": "conn6", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop3"}, "end2": {"partRefId": "constraint2", "portRefId": "param6"} },
    { "id": "conn7", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop1"}, "end2": {"partRefId": "constraint2", "portRefId": "param7"} }
  ]
}
```
### 提取规则：
#### 第一阶段：强制关系校验
1. 识别主块
  - 确定文本中描述的主要实体，通常为主句的主语或核心概念。。
  - 为其分配一个名称和ID（例如“block1”、“block2”等等）

2. 提取属性
  - 识别与主块相关的所有变量或属性，列出所有变量，即使未参与约束。
  - 假设类型为“Real”，除非文本明确指定其他类型
  - 为每个属性分配唯一ID（例如“prop1”、“prop2”等）。
  - 一个用例不能出现两种截然不同的动作，例如开启和关闭。

3. 识别约束语句
  - 查找描述属性之间关系的语句，通常是等式或数学表达式。
  - 记录每个约束语句。

4. 定义约束块
  - 为每个约束语句创建一个约束块。
  - 基于约束类型或描述性名称命名约束块。
  - 列出约束涉及的所有属性作为端口，每个端口包括：
    - 唯一ID（例如“param1”、“param2”等）。
    - 名称，与属性名称匹配。
    - 属性来源（即关联的属性ID），“from”字段，引用主块中相应属性的ID。
    - 属性类型（通常为“Real”，对应于主块中相应属性的类型）
  - 记录约束方程为字符串。

5.  在主块中实例化约束: 在主块（Block）中，为每个要使用的`ConstraintBlock`创建一个对应的`Property`，其`propertyKind`必须为`constraint`。这个`Property`的`typeId`指向`ConstraintBlock`的ID。

#### 第二阶段：强制关系校验
6: 创建连接器映射表 (【!!!最关键的校验步骤!!!】)
- 在完成上述所有元素的识别后，你**必须**创建一个Markdown表格，名为“连接器映射表”。
- 此表的目的是在生成最终JSON之前，预先规划和验证每一个`BindingConnector`的合法性。
- 表格必须包含以下列：
  - `连接器ID`: 为即将创建的连接器预分配一个ID (e.g., "conn1")。
  - `源属性 (end1)`: 填写值属性的ID和名称 (e.g., "prop1 (v)")。
  - `目标约束实例 (end2.partRefId)`: **必须**填写步骤5中创建的【约束属性】的ID (e.g., "constraint1_property")。
  - `目标参数 (end2.portRefId)`: **必须**填写步骤4中创建的【约束参数】的ID (e.g., "param1 (v)")。
  - `逻辑校验`: 简要说明连接的合理性 (e.g., "将块的'v'连接到方程的'v'参数")。

- **核心规则：表中的每一行都定义了一个完整的、合法的连接器。只有通过此表验证的连接，才能在最终的JSON中生成。如果一个约束块没有参数，或者一个值属性没有地方可以连接，则不应出现在此表中。**

#### 第三阶段：整理优化输出
7: 编译最终思考摘要
- 将上述所有步骤的分析结果进行汇总。

## 样例

输入样例：
"电动汽车动力系统中包含：
1. 电池模块：输出电压V_batt与电流I满足V_batt = EMF - R_int*I，其中EMF为电动势，R_int为内阻
2. 电机模块：输出扭矩T与转速ω满足T = K_t*I - B*ω，其中K_t为转矩常数，B为阻尼系数
两个模块通过功率平衡关联：V_batt*I = T*ω + Losses"

### 思考过程：
#### 第一阶段：元素识别
**步骤 1: 所有主块**
- Block: `id: "block1", name: "BatteryModule"`
- Block: `id: "block2", name: "MotorModule"`
- 为了封装系统级约束，我们将创建一个顶层块: `id: "block_system", name: "EV_PowerSystem"` (block1和block2将成为它的组成部分)

**步骤 2: 各个块的值属性**
- **block1 (BatteryModule) 的属性:**
  - `id: "prop1", name: "V_batt", parentId: "block1"`
  - `id: "prop2", name: "EMF", parentId: "block1"`
  - `id: "prop3", name: "R_int", parentId: "block1"`
  - `id: "prop4", name: "I", parentId: "block1"` (电流I源于电池)
  - `id: "prop5", name: "Losses", parentId: "block1"` (损耗与电池和电机相关，暂归于此)
- **block2 (MotorModule) 的属性:**
  - `id: "prop6", name: "T", parentId: "block2"`
  - `id: "prop7", name: "ω", parentId: "block2"`
  - `id: "prop8", name: "K_t", parentId: "block2"`
  - `id: "prop9", name: "B", parentId: "block2"`

**步骤 3: 所有约束块**
- ConstraintBlock: `id: "cb1", name: "BatteryModel"` (V_batt = EMF - R_int*I)
- ConstraintBlock: `id: "cb2", name: "MotorModel"` (T = K_t*I - B*ω)
- ConstraintBlock: `id: "cb3", name: "PowerBalance"` (V_batt*I = T*ω + Losses)

**步骤 4: 所有约束参数**
- **cb1 (BatteryModel) 的参数:** `p1(V_batt)`, `p2(EMF)`, `p3(R_int)`, `p4(I)`
- **cb2 (MotorModel) 的参数:** `p5(T)`, `p6(ω)`, `p7(K_t)`, `p8(B)`, `p9(I)`
- **cb3 (PowerBalance) 的参数:** `p10(V_batt)`, `p11(I)`, `p12(T)`, `p13(ω)`, `p14(Losses)`

**步骤 5: 实例化约束属性**
- 在 `block1` 内: `id: "cp1_prop", name: "BatteryModel", parentId: "block1", typeId: "cb1"`
- 在 `block2` 内: `id: "cp2_prop", name: "MotorModel", parentId: "block2", typeId: "cb2"`
- 在 `block_system` 内 (用于系统级关联): `id: "cp3_prop", name: "PowerBalance", parentId: "block_system", typeId: "cb3"`

#### 第二阶段：强制关系校验
**步骤 6: 连接器映射表**
| 连接器ID | 连接器父级ID | 源属性 (end1) | 目标约束实例 (end2.partRefId) | 目标参数 (end2.portRefId) | 逻辑校验 |
|:---|:---|:---|:---|:---|:---|
| conn1 | block1 | prop1 (V_batt) | cp1_prop | p1 (V_batt) | 连接电池V_batt到电池方程 |
| conn2 | block1 | prop2 (EMF)    | cp1_prop | p2 (EMF)    | 连接电池EMF到电池方程 |
| conn3 | block1 | prop3 (R_int)  | cp1_prop | p3 (R_int)  | 连接电池R_int到电池方程 |
| conn4 | block1 | prop4 (I)      | cp1_prop | p4 (I)      | 连接电池I到电池方程 |
| conn5 | block2 | prop6 (T)      | cp2_prop | p5 (T)      | 连接电机T到电机方程 |
| conn6 | block2 | prop7 (ω)      | cp2_prop | p6 (ω)      | 连接电机ω到电机方程 |
| conn7 | block2 | prop8 (K_t)    | cp2_prop | p7 (K_t)    | 连接电机K_t到电机方程 |
| conn8 | block2 | prop9 (B)      | cp2_prop | p8 (B)      | 连接电机B到电机方程 |
| conn9 | block2 | *ref to block1.prop4 (I)* | cp2_prop | p9 (I) | 连接共享电流I到电机方程 |
| conn10| block_system | *ref to block1.prop1 (V_batt)* | cp3_prop | p10(V_batt) | 连接电池V_batt到功率平衡方程 |
| conn11| block_system | *ref to block1.prop4 (I)* | cp3_prop | p11(I) | 连接共享电流I到功率平衡方程 |
| conn12| block_system | *ref to block2.prop6 (T)* | cp3_prop | p12(T) | 连接电机T到功率平衡方程 |
| conn13| block_system | *ref to block2.prop7 (ω)* | cp3_prop | p13(ω) | 连接电机ω到功率平衡方程 |
| conn14| block_system | *ref to block1.prop5 (Losses)*| cp3_prop | p14(Losses)| 连接损耗到功率平衡方程 |
*（注意：为了清晰，这里使用了`ref to`，在实际JSON中这需要通过嵌套属性或端口来实现，但映射表清晰地表达了意图。）*

#### 第三阶段：整理优化输出
---
主块：BatteryModule
属性：V_batt, EMF, R_int, I, Losses
约束块实例：BatteryModel(V_batt, EMF, R_int, I)
---
主块：MotorModule
属性：T, ω, K_t, B
约束块实例：MotorModel(T, ω, K_t, B, I)
---
系统块：EV_PowerSystem
(包含BatteryModule和MotorModule作为部件)
系统级约束块实例：PowerBalance(V_batt, I, T, ω, Losses)
---

## 具体任务
输入：      
"""
prompt1_last = "输出：请你一步一步进行推理思考。"

# 输出格式提示
prompt2 = """
因此，请从优化输出中，为我提取JSON对象，遵循以下的规则：
+ 确保所有id都是唯一的字符串。
+ 确保parentId, typeId, propertyRefId, partRefId, portRefId 都正确引用了其他元素的id。
+ 将所有元素（Package, Block, Property, ConstraintBlock, ConstraintParameter, BindingConnector）都放在顶级的elements列表中。

+ 在生成JSON之前，请再次确认每一个 `BindingConnector` 都严格满足以下校验规则：
  + `end2.partRefId` 引用的是一个【约束属性】（`propertyKind: "constraint"`）的ID。
  + `end2.portRefId` 引用的是一个【约束参数】（`ConstraintParameter`）的ID。
  + 该【约束参数】必须是在被引用的【约束属性】的类型（即`ConstraintBlock`）内部定义的。
  + 如果一个约束块没有参数，则绝不为它创建连接器。


+ 在根下只能含有两个元素：`model`和`elements`

## 示例JSON参考如下
  ```json
  {
  "model": [ { "id": "model-cms-unique-id", "name": "CircularMotionModel" } ],
  "elements": [
    { "id": "pkg-cms-unique-id", "type": "Package", "name": "ParametricDiagram", "parentId": "model-cms-unique-id" },
    { "id": "block1", "type": "Block", "name": "CircularMotionSystem", "parentId": "pkg-cms-unique-id" },
    { "id": "prop1", "type": "Property", "name": "r", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop2", "type": "Property", "name": "ω", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop3", "type": "Property", "name": "v", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop4", "type": "Property", "name": "F", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop5", "type": "Property", "name": "m", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "constraintBlock1", "type": "ConstraintBlock", "name": "VelocityEquation", "parentId": "pkg-cms-unique-id", "specification": {"expression": "v = ω * r", "language": "English"} },
    { "id": "param1", "type": "ConstraintParameter", "name": "v", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "param2", "type": "ConstraintParameter", "name": "ω", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "param3", "type": "ConstraintParameter", "name": "r", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "constraintBlock2", "type": "ConstraintBlock", "name": "CentrifugalForceEquation", "parentId": "pkg-cms-unique-id", "specification": {"expression": "F = m * v^2 / r", "language": "English"} },
    { "id": "param4", "type": "ConstraintParameter", "name": "F", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param5", "type": "ConstraintParameter", "name": "m", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param6", "type": "ConstraintParameter", "name": "v", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param7", "type": "ConstraintParameter", "name": "r", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "constraint1", "type": "Property", "name": "VelocityEquation", "propertyKind": "constraint", "parentId": "block1", "typeId": "constraintBlock1" },
    { "id": "constraint2", "type": "Property", "name": "CentrifugalForceEquation", "propertyKind": "constraint", "parentId": "block1", "typeId": "constraintBlock2" },
    # ... connectors ...
    { "id": "conn1", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop3"}, "end2": {"partRefId": "constraint1", "portRefId": "param1"} },
    { "id": "conn2", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop2"}, "end2": {"partRefId": "constraint1", "portRefId": "param2"} },
    { "id": "conn3", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop1"}, "end2": {"partRefId": "constraint1", "portRefId": "param3"} },
    { "id": "conn4", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop4"}, "end2": {"partRefId": "constraint2", "portRefId": "param4"} },
    { "id": "conn5", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop5"}, "end2": {"partRefId": "constraint2", "portRefId": "param5"} },
    { "id": "conn6", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop3"}, "end2": {"partRefId": "constraint2", "portRefId": "param6"} },
    { "id": "conn7", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop1"}, "end2": {"partRefId": "constraint2", "portRefId": "param7"} }
  ]
}
  ```
请严格按照上面的JSON结构输出结果。

下面是一个示例文本的输出结果：
```json
{
  "model": [ { "id": "model-cms-unique-id", "name": "CircularMotionModel" } ],
  "elements": [
    { "id": "pkg-cms-unique-id", "type": "Package", "name": "ParametricDiagram", "parentId": "model-cms-unique-id" },
    { "id": "block1", "type": "Block", "name": "CircularMotionSystem", "parentId": "pkg-cms-unique-id" },
    { "id": "prop1", "type": "Property", "name": "r", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop2", "type": "Property", "name": "ω", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop3", "type": "Property", "name": "v", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop4", "type": "Property", "name": "F", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "prop5", "type": "Property", "name": "m", "propertyKind": "value", "parentId": "block1", "typeId": "Real" },
    { "id": "constraintBlock1", "type": "ConstraintBlock", "name": "VelocityEquation", "parentId": "pkg-cms-unique-id", "specification": {"expression": "v = ω * r", "language": "English"} },
    { "id": "param1", "type": "ConstraintParameter", "name": "v", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "param2", "type": "ConstraintParameter", "name": "ω", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "param3", "type": "ConstraintParameter", "name": "r", "parentId": "constraintBlock1", "typeId": "Real" },
    { "id": "constraintBlock2", "type": "ConstraintBlock", "name": "CentrifugalForceEquation", "parentId": "pkg-cms-unique-id", "specification": {"expression": "F = m * v^2 / r", "language": "English"} },
    { "id": "param4", "type": "ConstraintParameter", "name": "F", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param5", "type": "ConstraintParameter", "name": "m", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param6", "type": "ConstraintParameter", "name": "v", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "param7", "type": "ConstraintParameter", "name": "r", "parentId": "constraintBlock2", "typeId": "Real" },
    { "id": "constraint1", "type": "Property", "name": "VelocityEquation", "propertyKind": "constraint", "parentId": "block1", "typeId": "constraintBlock1" },
    { "id": "constraint2", "type": "Property", "name": "CentrifugalForceEquation", "propertyKind": "constraint", "parentId": "block1", "typeId": "constraintBlock2" },
    # ... connectors ...
    { "id": "conn1", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop3"}, "end2": {"partRefId": "constraint1", "portRefId": "param1"} },
    { "id": "conn2", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop2"}, "end2": {"partRefId": "constraint1", "portRefId": "param2"} },
    { "id": "conn3", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop1"}, "end2": {"partRefId": "constraint1", "portRefId": "param3"} },
    { "id": "conn4", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop4"}, "end2": {"partRefId": "constraint2", "portRefId": "param4"} },
    { "id": "conn5", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop5"}, "end2": {"partRefId": "constraint2", "portRefId": "param5"} },
    { "id": "conn6", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop3"}, "end2": {"partRefId": "constraint2", "portRefId": "param6"} },
    { "id": "conn7", "type": "BindingConnector", "parentId": "block1", "end1": {"propertyRefId": "prop1"}, "end2": {"partRefId": "constraint2", "portRefId": "param7"} }
  ]
}

```
"""

def is_valid_json(json_str):
    """检查字符串是否是有效的JSON格式"""
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        logger.error("JSON 不合法")
        return False

def process_parameter_task(state: WorkflowState, task_content: str) -> Dict[str, Any]:
    """
    处理参数图任务
    
    参数:
        state: 工作流状态
        task_content: 任务内容
        
    返回:
        处理结果的字典
    """
    logger.info("开始处理参数图任务")
    
    try:
        # 创建LLM
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )

        
        new_prompt1 = prompt1_first + task_content + prompt1_last

        # 执行第一步分析
        first_response = llm.invoke(new_prompt1)
        cot_result = first_response.content
        logger.info("完成第一步分析")
        

        # 执行第二步，生成JSON输出
        second_response = llm.invoke([HumanMessage(content=new_prompt1 + cot_result + prompt2)])
        json_str = second_response.content
        
        # 修复和验证JSON
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()
            
        json_str = re.sub(r'\\(?!["\\/bfnrtu])', r'\\\\', json_str)
        json_str = repair_json(json_str)
        
        if not is_valid_json(json_str):
            logger.error("生成的JSON格式不正确")
            return {"status": "error", "message": "生成的JSON格式不正确"}
        
        result = json.loads(json_str)
        logger.info("参数图任务处理完成")
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"参数图任务处理失败: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}

def parameter_agent(state: WorkflowState, task_id: str, task_content: str) -> WorkflowState:
    """
    参数图Agent入口函数
    
    参数:
        state: 当前工作流状态
        task_id: 任务ID
        task_content: 任务内容
        
    返回:
        更新后的工作流状态
    """
    logger.info(f"参数图Agent开始处理任务 {task_id}")
    
    # 查找任务
    task_index = -1
    for i, task in enumerate(state.assigned_tasks):
        if task.id == task_id:
            task_index = i
            break
    
    if task_index == -1:
        state.error_message = f"找不到ID为 {task_id} 的任务"
        return state
    
    # 更新任务状态为进行中
    state.assigned_tasks[task_index].status = ProcessStatus.IN_PROGRESS
    
    try:
        # 处理参数图任务
        result = process_parameter_task(state, task_content)
        
        if result["status"] == "success":
            # 更新任务结果和状态
            state.assigned_tasks[task_index].result = result["result"]
            state.assigned_tasks[task_index].status = ProcessStatus.COMPLETED
            logger.info(f"任务 {task_id} 处理完成")
        else:
            # 更新任务状态为失败
            state.assigned_tasks[task_index].status = ProcessStatus.FAILED
            state.assigned_tasks[task_index].error_message = result["message"]
            logger.error(f"任务 {task_id} 处理失败: {result['message']}")
    
    except Exception as e:
        # 更新任务状态为失败
        state.assigned_tasks[task_index].status = ProcessStatus.FAILED
        state.assigned_tasks[task_index].error_message = str(e)
        logger.error(f"任务 {task_id} 处理异常: {str(e)}", exc_info=True)
    
    return state 