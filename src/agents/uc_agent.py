"""
用例图agent - 负责基于输入内容创建SysML用例图
"""
import logging
import json
from typing import Dict, Any
import re
from langchain.schema import HumanMessage
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI

from src.graph.workflow_state import WorkflowState, ProcessStatus
from config.settings import settings
from json_repair import repair_json

logger = logging.getLogger(__name__)

# 系统提示模板 - 用例图建模
prompt1_first = """
## 角色
你是一位出色的用例图分析大师，你熟悉各种复杂语境，你可以精准的提取出文本中的参与者、用例以及它们之间复杂的关系。

## 规则
以下是我的提取输出规则：
- 参与者：actor1, actor2, actor3。
- 用例：用例A, 用例B, 用例C, 用例D, 用例E。
- 关系：扩展，包含，泛化，关联；例如：actor1-[泛化]->actor3、actor1->[关联]->用例A、用例A<-[扩展]-用例B、用例A<-[扩展]-用例C、用例D-[包含]->用例E。

### 提取规则：

- **参与者映射规则**：

  - 主语或动词前的名词短语映射为候选参与者。
  - 介词短语中的名词映射为候选参与者。
  - 抽象概念或系统本身不作为参与者。

- **用例映射规则**：

  - 谓语或动词短语映射为候选用例。
  - 过于抽象或泛化的动词短语不作为用例。
  - 用例均以动词开头
  - 一个用例不能出现两种截然不同的动作，例如开启和关闭。

- **关系提取规则**：

  - 主语和谓语之间存在关联关系。
  - 包含“包括”、“由...组成”等关键词的句子表示用例之间存在**包含关系**。
  - 包含指示继承或分类的词汇表示参与者或用例之间存在**泛化关系**。
  - 包含“扩展到”、“增加”等关键词的句子表示用例之间存在**扩展关系**。
  - 参与者与用例之间通常存在关联关系。

### 注意事项
1. 参与者与用例只有关联关系，并且是由参与者指向用例。
2. 用例之间有包含、扩展关系。
3. 参与者与参与者之间有泛化关系。例如：黄金会员-[泛化]->普通用户。
4. '包含关系'的要点：
    - 当可以从两个或两个以上的用例中提取公共行为时，应该使用包含关系。其中提取出来的公共用例成为抽象用例，而把原始用例变成基本用例或基础用例。箭头指向抽象用例。例如：借书-[包含]->登录，还书-[包含]->登录。
    - 一个用例的功能太多时，可以使用包含关系建立若干个更小的用例。例如：查询信息-[包含]->查看余额信息，查询信息-[包含]->查看交易记录，查询信息-[包含]->查看上机记录。
5. 扩展用例在扩展点上增加新的维护和含义。扩展用例为基用例添加新的行为，箭头指向基本用例。例如：导出Excel<-[扩展]-查看余额信息，导出Excel<-[扩展]-查看交易记录，导出Excel<-[扩展]-查看上机记录。
6. 系统本身不可以当作参与者。

## 样例

输入样例：
在一个简单的车机空调案例中，司机可以通过车载系统开启空调，并设置相关参数，如温度、风向和湿度。开启空调的操作必须包含打火启动这一前置条件。与此同时，维修人员负责对空调进行检查和修理，确保其正常运行，维修完毕后，维修人员可以打印维修报告。

### 第一步识别参与者
- 司机
- 维修人员

### 第二步：识别用例
- 开启空调
- 设置相关参数
- 设置温度
- 设置风向
- 设置湿度
- 打火启动
- 检查空调
- 修理空调
- 打印维修报告

### 第三步：确定用例之间的关系, 这里需要注意“注意事项”中里面对包含关系和扩展关系的要点描述
- 开启空调-[包含]->打火启动
- 设置相关参数-[包含]->设置温度
- 设置相关参数-[包含]->设置风向
- 设置相关参数-[包含]->设置湿度
- 修理空调<-[扩展]-打印维修报告

### 第四步：确定参与者与用例的关系
- 司机->[关联]->开启空调
- 司机->[关联]->打火启动
- 司机->[关联]->设置相关参数
- 维修人员->[关联]->检查空调
- 维修人员->[关联]->修理空调

### 第五步：综合输出
- 参与者：司机, 维修人员。
- 用例：开启空调, 设置温度, 设置风向, 设置湿度, 打火启动, 维护空调, 修理空调。        
- 关系：司机->[关联]->开启空调、司机->[关联]->打火启动、司机->[关联]->设置相关参数、维修人员->[关联]->检查空调、维修人员->[关联]->修理空调、开启空调-[包含]->打火启动、设置相关参数-[包含]->设置温度、设置相关参数-[包含]->设置风向、设置相关参数-[包含]->设置湿度、修理空调<-[扩展]-打印维修报告

### 第六步：优化输出
这一步目的主要检查上一步的输出关系是否含有冗余的关系连线，以及关系是否正确且合理。
例如: 参与者A关联了用例A，用例A扩展了用例B，参与者A又关联了用例B，这种情况下是含有冗余关系的。简而言之，扩展关系意味着用例B的行为是由用例A的执行条件决定的，因此不需要再次显式地关联用例B，A参与者与用例A的关联已经包含了用例B的参与。

## 具体任务
输入：         
"""
prompt1_last = "输出：请你一步一步进行推理思考。"

# 输出格式提示
prompt2 = """
JSON中，在根下只能含有两个元素：`model`和`elements`

ID需要保证全局唯一。

因此，请从优化输出中，为我提取JSON对象，如下格式：

## 示例JSON参考如下
  ```json
 {
  "model": [
    {
      "id": "model-uc-unique-id", // 模型的唯一ID
      "name": "模型名称" // 例如 "智能农业用例模型"
    }
  ],
  "elements": [
    {
      "id": "pkg-uc-unique-id",     // 包的唯一ID
      "type": "Package",           // 类型固定为 "Package"
      "name": "包名称",              // 例如 "主要用例"
      "parentId": "model-uc-unique-id" // 父ID，指向模型ID
    },
    {
      "id": "actor-unique-id",     // 参与者的唯一ID
      "type": "Actor",             // 类型固定为 "Actor"
      "name": "参与者名称",
      "parentId": "pkg-uc-unique-id" // 父ID，指向包ID
    },
    {
      "id": "usecase-unique-id",   // 用例的唯一ID
      "type": "UseCase",           // 类型固定为 "UseCase"
      "name": "用例名称",
      "parentId": "pkg-uc-unique-id" // 父ID，指向包ID
    },
    {
      "id": "assoc-unique-id",       // 关联关系的唯一ID
      "type": "Association",       // 类型
      "sourceId": "actor-unique-id", // 源ID
      "targetId": "usecase-unique-id",// 目标ID
      "parentId": "pkg-uc-unique-id" // 父ID，指向包ID
    },
    {
      "id": "include-rel-unique-id", // 包含关系的唯一ID
      "type": "Include",
      "sourceId": "source-uc-id",
      "targetId": "target-uc-id",
      "parentId": "pkg-uc-unique-id"
    },
    {
      "id": "extend-rel-unique-id",  // 扩展关系的唯一ID
      "type": "Extend",
      "sourceId": "source-uc-id",
      "targetId": "target-uc-id",
      "parentId": "pkg-uc-unique-id"
    },
    {
      "id": "gen-rel-unique-id",     // 泛化关系的唯一ID
      "type": "Generalization",
      "sourceId": "source-actor-id",
      "targetId": "target-actor-id",
      "parentId": "pkg-uc-unique-id"
    }
  ]
}
  ```

请严格按照上面的JSON结构输出结果。
"""

def is_valid_json(json_str):
    """检查字符串是否是有效的JSON格式"""
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        logger.error("JSON 不合法")
        return False

def process_usecase_task(state: WorkflowState, task_content: str) -> Dict[str, Any]:
    """
    处理用例图任务
    
    参数:
        state: 工作流状态
        task_content: 任务内容
        
    返回:
        处理结果的字典
    """
    logger.info("开始处理用例图任务")
    
    try:
        # 创建LLM
        llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )

        
        new_prompt1 = prompt1_first + task_content + prompt1_last

        # 执行第一步分析
        first_response = llm.invoke(new_prompt1)
        cot_result = first_response.content
        logger.info("完成第一步分析")
        

        # 执行第二步，生成JSON输出
        second_response = llm.invoke([HumanMessage(content=new_prompt1 + cot_result + prompt2)])
        json_str = second_response.content
        
        # 修复和验证JSON
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()
            
        json_str = re.sub(r'\\(?!["\\/bfnrtu])', r'\\\\', json_str)
        json_str = repair_json(json_str)
        
        if not is_valid_json(json_str):
            logger.error("生成的JSON格式不正确")
            return {"status": "error", "message": "生成的JSON格式不正确"}
        
        result = json.loads(json_str)
        logger.info("用例图任务处理完成")
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"用例图任务处理失败: {str(e)}", exc_info=True)
        return {"status": "error", "message": str(e)}

def usecase_agent(state: WorkflowState, task_id: str, task_content: str) -> WorkflowState:
    """
    用例图Agent入口函数
    
    参数:
        state: 当前工作流状态
        task_id: 任务ID
        task_content: 任务内容
        
    返回:
        更新后的工作流状态
    """
    logger.info(f"用例图Agent开始处理任务 {task_id}")
    
    # 查找任务
    task_index = -1
    for i, task in enumerate(state.assigned_tasks):
        if task.id == task_id:
            task_index = i
            break
    
    if task_index == -1:
        state.error_message = f"找不到ID为 {task_id} 的任务"
        return state
    
    # 更新任务状态为进行中
    state.assigned_tasks[task_index].status = ProcessStatus.IN_PROGRESS
    
    try:
        # 处理用例图任务
        result = process_usecase_task(state, task_content)
        
        if result["status"] == "success":
            # 更新任务结果和状态
            state.assigned_tasks[task_index].result = result["result"]
            state.assigned_tasks[task_index].status = ProcessStatus.COMPLETED
            logger.info(f"任务 {task_id} 处理完成")
        else:
            # 更新任务状态为失败
            state.assigned_tasks[task_index].status = ProcessStatus.FAILED
            state.assigned_tasks[task_index].error_message = result["message"]
            logger.error(f"任务 {task_id} 处理失败: {result['message']}")
    
    except Exception as e:
        # 更新任务状态为失败
        state.assigned_tasks[task_index].status = ProcessStatus.FAILED
        state.assigned_tasks[task_index].error_message = str(e)
        logger.error(f"任务 {task_id} 处理异常: {str(e)}", exc_info=True)
    
    return state 