"""
文档处理Agent
负责读取文档并提取文本内容
"""
import logging
import os
import docx
import tiktoken

from src.graph.workflow_state import WorkflowState

logger = logging.getLogger(__name__)


def count_tokens(text: str) -> int:
    """
    计算文本的token数量
    
    参数:
        text: 要计算的文本
        
    返回:
        token数量
    """
    encoding = tiktoken.encoding_for_model("gpt-4")
    return len(encoding.encode(text))


def read_word_doc(doc_path: str) -> str:
    """
    读取Word文档
    
    参数:
        doc_path: 文档路径
        
    返回:
        文档内容
    """
    try:
        document = docx.Document(doc_path)
        full_text = []
        for para in document.paragraphs:
            if para.style and para.style.name.startswith('Heading'):
                level = int(para.style.name.split(' ')[1])
                full_text.append("\n" + "#" * level + " " + para.text.strip())
            else:
                full_text.append(para.text.strip())
        return "\n\n".join(full_text)
    except Exception as e:
        logger.error(f"读取Word文档失败: {str(e)}", exc_info=True)
        raise ValueError(f"读取Word文档失败: {str(e)}")


def process_document(state: WorkflowState) -> WorkflowState:
    """
    处理文档，提取文本内容
    
    参数:
        state: 当前工作流状态
        
    返回:
        更新后的工作流状态
    """
    if not state.input_doc_path or not os.path.exists(state.input_doc_path):
        state.error_message = f"文档路径无效: {state.input_doc_path}"
        return state
    
    try:
        logger.info(f"开始处理文档: {state.input_doc_path}")
        
        # 读取文档
        text_content = read_word_doc(state.input_doc_path)
        
        # 保存文档内容
        state.expanded_content = text_content
        
        logger.info(f"文档处理成功，文本长度: {len(text_content)} 字符")
        
        return state
        
    except Exception as e:
        logger.error(f"文档处理失败: {str(e)}", exc_info=True)
        state.error_message = f"文档处理失败: {str(e)}"
        return state