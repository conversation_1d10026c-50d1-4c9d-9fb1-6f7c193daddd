#!/usr/bin/env python3
"""
增量式融合Agent - 基于知识图谱的增量式融合与重构流程
参考Gemini方案建议实现
"""

import json
import logging
import uuid
from typing import Dict, List, Any, Optional
from langchain_openai import ChatOpenAI
# from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

from src.graph.workflow_state import WorkflowState, ProcessStatus, SysMLTask
from src.agents.neo4j_storage_agent import Neo4jStorageAgent
from config.settings import settings

logger = logging.getLogger(__name__)

class IncrementalFusionAgent:
    """增量式融合Agent - 实现基于知识图谱的增量式融合与重构"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.llm_model,
            api_key=settings.openai_api_key,
            base_url=settings.base_url,
            temperature=0.0
        )
        self.storage_agent = None
        self.master_graph_initialized = False
        
    def process(self, state: WorkflowState) -> WorkflowState:
        """
        执行增量式融合流程
        
        参数:
            state: 工作流状态
            
        返回:
            更新后的工作流状态
        """
        logger.info("🚀 开始增量式融合流程")
        
        try:
            # 初始化Neo4j存储
            self.storage_agent = Neo4jStorageAgent()
            
            # 获取所有已完成的SysML任务
            sysml_tasks = self._get_sysml_tasks(state)
            
            if len(sysml_tasks) < 2:
                logger.warning("需要至少2个已完成的SysML任务才能进行融合")
                return state
            
            logger.info(f"找到{len(sysml_tasks)}个待融合的SysML任务")
            
            # 执行增量式融合流程
            fusion_result = self._execute_incremental_fusion(sysml_tasks)
            
            if fusion_result:
                # 创建融合任务结果
                fusion_task = SysMLTask(
                    id="INCREMENTAL-FUSION-TASK",
                    type="Incremental Fusion",
                    content="增量式融合完成",
                    status=ProcessStatus.COMPLETED,
                    result=fusion_result
                )
                state.assigned_tasks.append(fusion_task)
                
                logger.info(f"✅ 增量式融合完成: {len(fusion_result.get('elements', []))} 个元素")
            else:
                logger.error("❌ 增量式融合失败")
                
        except Exception as e:
            logger.error(f"增量式融合过程发生异常: {e}", exc_info=True)
        finally:
            if self.storage_agent:
                self.storage_agent.close()
                
        return state
    
    def _get_sysml_tasks(self, state: WorkflowState) -> List[SysMLTask]:
        """获取SysML相关任务"""
        sysml_task_types = {
            "Requirement",
            "Use Case", 
            "Block Definition and Internal Block",
            "Activity",
            "State Machine",
            "Sequence",
            "Parametric"
        }
        
        tasks = [
            task for task in state.assigned_tasks 
            if task.status == ProcessStatus.COMPLETED and 
            task.result is not None and
            task.type in sysml_task_types
        ]
        
        logger.info("SysML任务列表:")
        for task in tasks:
            elements_count = len(task.result.get('elements', []))
            logger.info(f"  - {task.type}: {elements_count} 个元素")
        
        return tasks
    
    def _execute_incremental_fusion(self, tasks: List[SysMLTask]) -> Optional[Dict[str, Any]]:
        """
        执行增量式融合流程
        
        参数:
            tasks: SysML任务列表
            
        返回:
            融合后的JSON数据
        """
        logger.info("🔄 开始增量式融合流程")
        
        try:
            # 阶段0: 初始准备
            preprocessed_tasks = self._stage0_initial_preparation(tasks)
            
            # 阶段1: 知识图谱初始化
            base_task = self._stage1_initialize_master_graph(preprocessed_tasks)
            
            # 阶段2: 增量式融合循环
            remaining_tasks = [t for t in preprocessed_tasks if t != base_task]
            self._stage2_incremental_fusion_loop(remaining_tasks)
            
            # 阶段3: 全局优化与重构
            self._stage3_global_optimization()
            
            # 阶段4: 最终JSON导出
            final_json = self._stage4_export_final_json()
            
            logger.info("🎉 增量式融合流程完成")
            return final_json
            
        except Exception as e:
            logger.error(f"增量式融合流程失败: {e}")
            return None
    
    def _stage0_initial_preparation(self, tasks: List[SysMLTask]) -> List[SysMLTask]:
        """
        阶段0: 初始准备 - 全局唯一ID分配、结构扁平化、源数据标记
        
        参数:
            tasks: 原始任务列表
            
        返回:
            预处理后的任务列表
        """
        logger.info("📦 阶段0: 初始准备")
        
        preprocessed_tasks = []
        
        for task in tasks:
            logger.info(f"预处理任务: {task.type}")
            
            # 深拷贝任务结果
            preprocessed_result = json.loads(json.dumps(task.result))
            
            # 1. 全局唯一ID分配
            self._assign_global_unique_ids(preprocessed_result)
            
            # 2. 结构扁平化
            self._flatten_structure(preprocessed_result)
            
            # 3. 源数据标记
            self._mark_source_data(preprocessed_result, task.type)
            
            # 创建预处理后的任务
            preprocessed_task = SysMLTask(
                id=task.id,
                type=task.type,
                content=task.content,
                status=task.status,
                result=preprocessed_result
            )
            preprocessed_tasks.append(preprocessed_task)
        
        logger.info(f"✅ 阶段0完成: 预处理了{len(preprocessed_tasks)}个任务")
        return preprocessed_tasks
    
    def _assign_global_unique_ids(self, json_data: Dict[str, Any]):
        """为所有元素分配全局唯一ID"""
        elements = json_data.get('elements', [])
        
        for element in elements:
            # 保存原始ID
            if 'id' in element:
                element['originalId'] = element['id']
            
            # 分配新的UUID
            element['uuid'] = str(uuid.uuid4())
            element['id'] = element['uuid']  # 保持id字段为新的UUID
        
        # 更新model信息
        model_info = json_data.get('model', [])
        if isinstance(model_info, list) and model_info:
            model = model_info[0]
            if 'id' in model:
                model['originalId'] = model['id']
            model['uuid'] = str(uuid.uuid4())
            model['id'] = model['uuid']
    
    def _flatten_structure(self, json_data: Dict[str, Any]):
        """结构扁平化 - 将所有非Package元素的parentId指向model"""
        elements = json_data.get('elements', [])
        model_info = json_data.get('model', [])
        
        # 获取model ID
        model_id = None
        if isinstance(model_info, list) and model_info:
            model_id = model_info[0].get('id')
        
        if not model_id:
            logger.warning("未找到model ID，使用默认值")
            model_id = "default-model"
        
        # 记录Package元素的ID
        package_ids = set()
        for element in elements:
            if element.get('type') == 'Package':
                package_ids.add(element.get('id'))
        
        # 扁平化非Package元素
        for element in elements:
            if element.get('type') != 'Package':
                # 保存原始parentId
                if 'parentId' in element:
                    element['originalParentId'] = element['parentId']
                
                # 如果原来的parentId指向Package，则改为指向model
                original_parent = element.get('parentId')
                if original_parent in package_ids:
                    element['parentId'] = model_id
    
    def _mark_source_data(self, json_data: Dict[str, Any], source_view: str):
        """标记源数据"""
        elements = json_data.get('elements', [])
        
        for element in elements:
            element['sourceView'] = [source_view]
    
    def _stage1_initialize_master_graph(self, tasks: List[SysMLTask]) -> SysMLTask:
        """
        阶段1: 知识图谱初始化 - 选择基础视图并加载到Neo4j
        
        参数:
            tasks: 预处理后的任务列表
            
        返回:
            选择的基础任务
        """
        logger.info("🏗️ 阶段1: 知识图谱初始化")
        
        # 选择基础视图 - 优先选择Block Definition and Internal Block
        base_task = None
        for task in tasks:
            if task.type == "Block Definition and Internal Block":
                base_task = task
                break
        
        # 如果没有Block Definition，选择第一个任务
        if not base_task:
            base_task = tasks[0]
        
        logger.info(f"选择基础视图: {base_task.type}")
        
        # 清空Neo4j数据库
        self.storage_agent.clear_database()
        
        # 加载基础视图到Neo4j
        success = self._load_base_view_to_neo4j(base_task)
        
        if success:
            self.master_graph_initialized = True
            logger.info("✅ 阶段1完成: MasterGraph初始化成功")
        else:
            logger.error("❌ 阶段1失败: MasterGraph初始化失败")
            raise Exception("MasterGraph初始化失败")
        
        return base_task
    
    def _load_base_view_to_neo4j(self, base_task: SysMLTask) -> bool:
        """将基础视图加载到Neo4j"""
        try:
            with self.storage_agent._driver.session() as session:
                # 创建Model节点
                model_info = base_task.result.get('model', [])
                if isinstance(model_info, list) and model_info:
                    model = model_info[0]
                    session.run("""
                        CREATE (m:Model {
                            uuid: $uuid,
                            originalId: $originalId,
                            id: $id,
                            name: $name,
                            type: 'Model'
                        })
                    """, {
                        'uuid': model.get('uuid'),
                        'originalId': model.get('originalId'),
                        'id': model.get('id'),
                        'name': model.get('name', 'Unknown Model')
                    })
                
                # 创建Element节点
                elements = base_task.result.get('elements', [])
                for element in elements:
                    # 准备属性
                    props = {
                        'uuid': element.get('uuid'),
                        'originalId': element.get('originalId'),
                        'id': element.get('id'),
                        'name': element.get('name', ''),
                        'type': element.get('type', ''),
                        'sourceView': element.get('sourceView', [])
                    }
                    
                    # 添加其他属性
                    for key, value in element.items():
                        if key not in ['uuid', 'originalId', 'id', 'name', 'type', 'sourceView', 'parentId']:
                            if isinstance(value, (str, int, float, bool)):
                                props[key] = value
                            else:
                                props[key] = json.dumps(value, ensure_ascii=False)
                    
                    session.run("""
                        CREATE (e:Element {props})
                    """.replace('{props}', ', '.join([f'{k}: ${k}' for k in props.keys()])), props)
                
                # 创建父子关系
                for element in elements:
                    parent_id = element.get('parentId')
                    if parent_id:
                        session.run("""
                            MATCH (child:Element {uuid: $child_uuid})
                            MATCH (parent {id: $parent_id})
                            CREATE (child)-[:HAS_PARENT]->(parent)
                        """, {
                            'child_uuid': element.get('uuid'),
                            'parent_id': parent_id
                        })
            
            logger.info(f"基础视图加载完成: {len(elements)} 个元素")
            return True
            
        except Exception as e:
            logger.error(f"加载基础视图失败: {e}")
            return False
    
    def _stage2_incremental_fusion_loop(self, remaining_tasks: List[SysMLTask]):
        """
        阶段2: 增量式融合循环
        
        参数:
            remaining_tasks: 剩余待融合的任务列表
        """
        logger.info("🔄 阶段2: 增量式融合循环")
        
        for i, task in enumerate(remaining_tasks, 1):
            logger.info(f"融合任务 {i}/{len(remaining_tasks)}: {task.type}")
            
            try:
                # 1. 加载为临时视图
                self._load_as_temporary_view(task)
                
                # 2. 实体解析与链接
                self._entity_resolution_and_linking(task)
                
                # 3. 清理临时数据
                self._cleanup_temporary_data()
                
                logger.info(f"✅ 任务 {task.type} 融合完成")
                
            except Exception as e:
                logger.error(f"❌ 任务 {task.type} 融合失败: {e}")
                continue
        
        logger.info("✅ 阶段2完成: 增量式融合循环结束")
    
    def _load_as_temporary_view(self, task: SysMLTask):
        """将新数据加载为临时视图"""
        with self.storage_agent._driver.session() as session:
            elements = task.result.get('elements', [])
            
            for element in elements:
                # 准备属性
                props = {
                    'uuid': element.get('uuid'),
                    'originalId': element.get('originalId'),
                    'id': element.get('id'),
                    'name': element.get('name', ''),
                    'type': element.get('type', ''),
                    'sourceView': element.get('sourceView', [])
                }
                
                # 添加其他属性
                for key, value in element.items():
                    if key not in ['uuid', 'originalId', 'id', 'name', 'type', 'sourceView', 'parentId']:
                        if isinstance(value, (str, int, float, bool)):
                            props[key] = value
                        else:
                            props[key] = json.dumps(value, ensure_ascii=False)
                
                session.run("""
                    CREATE (t:Element:Temporary {props})
                """.replace('{props}', ', '.join([f'{k}: ${k}' for k in props.keys()])), props)
        
        logger.info(f"临时视图加载完成: {len(elements)} 个元素")
    
    def _entity_resolution_and_linking(self, task: SysMLTask):
        """实体解析与链接"""
        # 查找候选匹配对
        candidates = self._find_matching_candidates()
        
        logger.info(f"找到 {len(candidates)} 对候选匹配")
        
        # 对每个候选对使用LLM进行决策
        for candidate in candidates:
            master_uuid = candidate['master_uuid']
            temp_uuid = candidate['temp_uuid']
            
            # 获取元素详情
            master_element = self._get_element_by_uuid(master_uuid, is_temporary=False)
            temp_element = self._get_element_by_uuid(temp_uuid, is_temporary=True)
            
            if master_element and temp_element:
                # LLM决策
                decision = self._llm_fusion_decision(master_element, temp_element, task.content)
                
                # 执行决策
                self._execute_fusion_decision(decision, master_uuid, temp_uuid)
    
    def _find_matching_candidates(self) -> List[Dict[str, str]]:
        """查找匹配候选对"""
        with self.storage_agent._driver.session() as session:
            # 精确匹配
            result = session.run("""
                MATCH (m:Element), (t:Element:Temporary)
                WHERE NOT t:Merged 
                AND m.type = t.type 
                AND m.name = t.name
                RETURN m.uuid AS master_uuid, t.uuid AS temp_uuid, 
                       m.name AS name, m.type AS type, 1.0 AS similarity
            """)
            
            candidates = []
            for record in result:
                candidates.append({
                    'master_uuid': record['master_uuid'],
                    'temp_uuid': record['temp_uuid'],
                    'name': record['name'],
                    'type': record['type'],
                    'similarity': record['similarity']
                })
            
            return candidates
    
    def _get_element_by_uuid(self, uuid: str, is_temporary: bool = False) -> Optional[Dict[str, Any]]:
        """根据UUID获取元素详情"""
        with self.storage_agent._driver.session() as session:
            label = "Element:Temporary" if is_temporary else "Element"
            result = session.run(f"""
                MATCH (e:{label} {{uuid: $uuid}})
                RETURN properties(e) AS props
            """, {'uuid': uuid})

            record = result.single()
            return record['props'] if record else None

    def _llm_fusion_decision(self, master_element: Dict[str, Any], temp_element: Dict[str, Any],
                           original_text: str) -> Dict[str, Any]:
        """使用LLM进行融合决策"""
        prompt = f"""
## Role
You are an MBSE System Model Fusion Expert.

## Context
I am merging two system model views. I found two elements that might be the same entity.

- Master Element (already in the knowledge graph):
{json.dumps(master_element, indent=2, ensure_ascii=False)}

- Temporary Element (from the new view):
{json.dumps(temp_element, indent=2, ensure_ascii=False)}

- Original Text Snippet for Temporary Element:
{original_text[:500]}...

## Task
Analyze these two elements. Based on their properties and the original text context, decide on the correct action. Your answer MUST be one of three commands: "MERGE", "CREATE_NEW", or "FLAG_FOR_REVIEW".

1. **MERGE**: If they represent the exact same entity. The temporary element's information will be merged into the master.
2. **CREATE_NEW**: If they are distinct entities despite similar names. The temporary element will be added as a new, separate element to the graph.
3. **FLAG_FOR_REVIEW**: If you are uncertain and a human needs to decide.

## Output
Provide your response as a single JSON object:
{{
  "decision": "MERGE",
  "reasoning": "The names 'ControlSystem' and '自行车控制系统' are direct translations and refer to the same logical component responsible for system control, as confirmed by the text snippet."
}}
"""

        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            result = self._parse_llm_json_response(response.content)

            if result and result.get('decision') in ['MERGE', 'CREATE_NEW', 'FLAG_FOR_REVIEW']:
                return result
            else:
                logger.warning("LLM返回无效决策，默认为CREATE_NEW")
                return {"decision": "CREATE_NEW", "reasoning": "LLM返回无效决策"}

        except Exception as e:
            logger.error(f"LLM融合决策失败: {e}")
            return {"decision": "CREATE_NEW", "reasoning": f"LLM调用失败: {str(e)}"}

    def _parse_llm_json_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析LLM的JSON响应"""
        try:
            # 提取JSON内容
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].split("```")[0].strip()
            else:
                json_str = response.strip()

            return json.loads(json_str)

        except Exception as e:
            logger.error(f"解析LLM JSON响应失败: {e}")
            return None

    def _execute_fusion_decision(self, decision: Dict[str, Any], master_uuid: str, temp_uuid: str):
        """执行融合决策"""
        decision_type = decision.get('decision')
        reasoning = decision.get('reasoning', '')

        logger.info(f"执行决策: {decision_type} - {reasoning}")

        with self.storage_agent._driver.session() as session:
            if decision_type == "MERGE":
                # 合并节点
                session.run("""
                    MATCH (m:Element {uuid: $master_uuid})
                    MATCH (t:Element:Temporary {uuid: $temp_uuid})

                    // 合并sourceView数组
                    SET m.sourceView = m.sourceView + t.sourceView

                    // 合并其他属性（如果master中没有的话）
                    SET m += t

                    // 标记临时节点已合并
                    SET t:Merged
                """, {
                    'master_uuid': master_uuid,
                    'temp_uuid': temp_uuid
                })

                logger.info(f"✅ 合并完成: {temp_uuid} -> {master_uuid}")

            elif decision_type == "CREATE_NEW":
                # 不执行合并，临时节点将在后续步骤中被正式化
                logger.info(f"🆕 创建新元素: {temp_uuid}")

            elif decision_type == "FLAG_FOR_REVIEW":
                # 标记需要人工审查
                session.run("""
                    MATCH (m:Element {uuid: $master_uuid})
                    MATCH (t:Element:Temporary {uuid: $temp_uuid})
                    SET m:ReviewNeeded, t:ReviewNeeded
                    SET m.reviewReason = $reasoning, t.reviewReason = $reasoning
                """, {
                    'master_uuid': master_uuid,
                    'temp_uuid': temp_uuid,
                    'reasoning': reasoning
                })

                logger.warning(f"⚠️ 标记审查: {master_uuid} <-> {temp_uuid}")

    def _cleanup_temporary_data(self):
        """清理临时数据"""
        with self.storage_agent._driver.session() as session:
            # 将未合并的临时节点正式化
            session.run("""
                MATCH (t:Element:Temporary)
                WHERE NOT t:Merged
                REMOVE t:Temporary
            """)

            # 删除已合并的临时节点
            session.run("""
                MATCH (t:Element:Temporary:Merged)
                DETACH DELETE t
            """)

            logger.info("临时数据清理完成")

    def _stage3_global_optimization(self):
        """阶段3: 全局优化与重构"""
        logger.info("🎯 阶段3: 全局优化与重构")

        # 获取所有元素信息
        all_elements = self._get_all_elements_for_optimization()

        # 使用LLM设计包结构
        package_design = self._llm_design_package_structure(all_elements)

        if package_design:
            # 应用包结构
            self._apply_package_structure(package_design)
            logger.info("✅ 阶段3完成: 全局优化与重构完成")
        else:
            logger.warning("⚠️ 阶段3警告: 包结构设计失败，使用默认结构")
            self._apply_default_package_structure()

    def _get_all_elements_for_optimization(self) -> List[Dict[str, Any]]:
        """获取所有元素信息用于优化"""
        with self.storage_agent._driver.session() as session:
            result = session.run("""
                MATCH (e:Element)
                RETURN e.uuid AS uuid, e.name AS name, e.type AS type
            """)

            elements = []
            for record in result:
                elements.append({
                    'uuid': record['uuid'],
                    'name': record['name'],
                    'type': record['type']
                })

            return elements

    def _llm_design_package_structure(self, elements: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """使用LLM设计包结构"""
        prompt = f"""
## Role
You are a senior MBSE architect.

## Context
Here is a flat list of all elements in a complete system model.
{json.dumps(elements[:50], indent=2, ensure_ascii=False)}
{"... (truncated)" if len(elements) > 50 else ""}

Total elements: {len(elements)}

## Task
Design an optimal package structure for these elements based on SysML best practices. Create 5-7 high-level packages (e.g., Requirements, UseCases, Structure, Behavior, Libraries). For each element, decide which package it belongs to.

## Output Format
Provide a JSON object mapping each element's UUID to its new parent package's name.
{{
  "package_assignments": {{
    "uuid-1": "Requirements",
    "uuid-2": "SystemStructure",
    "uuid-3": "SystemBehavior"
  }},
  "package_definitions": {{
    "Requirements": "pkg-reqs-uuid",
    "SystemStructure": "pkg-structure-uuid",
    "SystemBehavior": "pkg-behavior-uuid",
    "UseCases": "pkg-usecases-uuid",
    "Libraries": "pkg-libraries-uuid"
  }}
}}
"""

        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            result = self._parse_llm_json_response(response.content)

            if result and 'package_assignments' in result and 'package_definitions' in result:
                logger.info("LLM包结构设计完成")
                return result
            else:
                logger.warning("LLM包结构设计返回格式无效")
                return None

        except Exception as e:
            logger.error(f"LLM包结构设计失败: {e}")
            return None

    def _apply_package_structure(self, package_design: Dict[str, Any]):
        """应用包结构"""
        with self.storage_agent._driver.session() as session:
            # 创建Package节点
            package_definitions = package_design.get('package_definitions', {})

            for package_name, package_id in package_definitions.items():
                session.run("""
                    MATCH (m:Model)
                    CREATE (p:Package {
                        uuid: $package_id,
                        id: $package_id,
                        name: $package_name,
                        type: 'Package'
                    })
                    CREATE (p)-[:HAS_PARENT]->(m)
                """, {
                    'package_id': package_id,
                    'package_name': package_name
                })

            # 更新元素的父子关系
            package_assignments = package_design.get('package_assignments', {})

            for element_uuid, package_name in package_assignments.items():
                package_id = package_definitions.get(package_name)
                if package_id:
                    session.run("""
                        MATCH (e:Element {uuid: $element_uuid})
                        MATCH (p:Package {id: $package_id})

                        // 删除旧的父子关系
                        OPTIONAL MATCH (e)-[r:HAS_PARENT]->()
                        DELETE r

                        // 创建新的父子关系
                        CREATE (e)-[:HAS_PARENT]->(p)
                    """, {
                        'element_uuid': element_uuid,
                        'package_id': package_id
                    })

        logger.info(f"包结构应用完成: {len(package_definitions)} 个包")

    def _apply_default_package_structure(self):
        """应用默认包结构"""
        default_packages = {
            "Requirements": "pkg-requirements-uuid",
            "UseCases": "pkg-usecases-uuid",
            "SystemStructure": "pkg-structure-uuid",
            "SystemBehavior": "pkg-behavior-uuid",
            "Libraries": "pkg-libraries-uuid"
        }

        # 元素类型到包的映射
        type_to_package = {
            "Requirement": "Requirements",
            "TestCase": "Requirements",
            "Actor": "UseCases",
            "UseCase": "UseCases",
            "Block": "SystemStructure",
            "Property": "SystemStructure",
            "Activity": "SystemBehavior",
            "StateMachine": "SystemBehavior",
            "ValueType": "Libraries",
            "Unit": "Libraries"
        }

        package_design = {
            "package_definitions": default_packages,
            "package_assignments": {}
        }

        # 获取所有元素并分配到默认包
        all_elements = self._get_all_elements_for_optimization()

        for element in all_elements:
            element_type = element.get('type', '')
            package_name = type_to_package.get(element_type, 'Libraries')
            package_design["package_assignments"][element['uuid']] = package_name

        self._apply_package_structure(package_design)

    def _stage4_export_final_json(self) -> Dict[str, Any]:
        """阶段4: 最终JSON导出"""
        logger.info("📤 阶段4: 最终JSON导出")

        with self.storage_agent._driver.session() as session:
            # 查询Model
            model_result = session.run("""
                MATCH (m:Model)
                RETURN properties(m) AS model_props
            """)

            model_record = model_result.single()
            model_info = model_record['model_props'] if model_record else {}

            # 查询所有元素（包括Package）
            elements_result = session.run("""
                MATCH (e)
                WHERE e:Element OR e:Package
                OPTIONAL MATCH (e)-[:HAS_PARENT]->(parent)
                RETURN properties(e) AS element_props, parent.id AS parent_id
                ORDER BY e.type, e.name
            """)

            elements = []
            for record in elements_result:
                element_props = record['element_props']
                parent_id = record['parent_id']

                # 清理属性
                clean_element = {}
                for key, value in element_props.items():
                    if key in ['uuid', 'originalId', 'sourceView']:
                        continue  # 跳过内部字段

                    if key == 'id':
                        clean_element['id'] = value
                    elif isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                        # 尝试解析JSON字符串
                        try:
                            clean_element[key] = json.loads(value)
                        except:
                            clean_element[key] = value
                    else:
                        clean_element[key] = value

                # 设置parentId
                if parent_id:
                    clean_element['parentId'] = parent_id

                elements.append(clean_element)

        # 构建最终JSON
        final_json = {
            "model": [model_info] if model_info else [],
            "elements": elements
        }

        logger.info(f"✅ 阶段4完成: 导出了{len(elements)}个元素")
        return final_json
