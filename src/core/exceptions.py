# src/core/exceptions.py

class MBSEGeneratorError(Exception):
    """Base exception for the MBSE Auto Generator."""
    pass

class LLMAgentError(MBSEGeneratorError):
    """Exception raised for errors in LLM Agent operations."""
    pass

class DocumentProcessingError(MBSEGeneratorError):
    """Exception raised for errors during document reading or chunking."""
    pass

class JsonNormalizationError(MBSEGeneratorError):
    """Exception raised for errors during JSON normalization or merging."""
    pass

class Neo4jConnectionError(MBSEGeneratorError):
    """Exception raised for errors connecting to or writing to Neo4j."""
    pass