# src/core/models.py
from pydantic import BaseModel, Field
from typing import List

class ClassificationItem(BaseModel):
    type: str = Field(description="The type of SysML information identified (e.g., 'Requirement', 'State Machine', 'Activity', 'Use Case', 'Block Definition', 'Internal Block', 'Constraint', 'None').")
    content: str = Field(description="The extracted text content corresponding to the identified type.")

class ClassificationResult(BaseModel):
    items: List[ClassificationItem] = Field(description="A list of identified SysML information items.")