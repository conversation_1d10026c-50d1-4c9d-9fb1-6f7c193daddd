"""
LightRAG Configuration Manager

This module provides configuration management for LightRAG to avoid storage lock issues
through environment configuration and initialization patterns.
"""

import os
import asyncio
from typing import Dict, Any, Optional
from lightrag import LightRAG
from lightrag.kg.shared_storage import initialize_share_data


class LightRAGConfig:
    """
    Configuration manager for LightRAG to avoid storage lock issues.
    
    This class provides a safe initialization pattern for LightRAG that ensures
    all storage components are properly initialized before use.
    """
    
    def __init__(self, working_dir: str = "./rag_storage", workers: int = 1):
        self.working_dir = working_dir
        self.workers = workers
        self._initialized = False
        
    async def initialize(self):
        """Initialize LightRAG with proper storage configuration."""
        if not self._initialized:
            # Ensure shared data is initialized
            initialize_share_data(workers=self.workers)
            
            # Create working directory if it doesn't exist
            os.makedirs(self.working_dir, exist_ok=True)
            
            self._initialized = True
    
    def create_lightrag(self, **kwargs) -> LightRAG:
        """
        Create a properly configured LightRAG instance.
        
        Args:
            **kwargs: Additional configuration for LightRAG
            
        Returns:
            LightRAG instance with safe storage configuration
        """
        # Ensure storage configuration is set
        config = {
            'working_dir': self.working_dir,
            'doc_status_storage': 'JsonDocStatusStorage',  # Use the fixed storage
            **kwargs
        }
        
        return LightRAG(**config)


class SafeLightRAGManager:
    """
    Safe LightRAG manager that handles initialization and storage lock issues.
    
    This class provides a safe interface for using LightRAG without worrying about
    storage lock initialization issues.
    """
    
    def __init__(self, working_dir: str = "./rag_storage", workers: int = 1):
        self.config = LightRAGConfig(working_dir=working_dir, workers=workers)
        self._lightrag: Optional[LightRAG] = None
    
    async def initialize(self, **kwargs):
        """Initialize the LightRAG instance safely."""
        await self.config.initialize()
        
        # Create LightRAG instance
        self._lightrag = self.config.create_lightrag(**kwargs)
        
        # Initialize all storage components
        if hasattr(self._lightrag, 'initialize'):
            await self._lightrag.initialize()
    
    @property
    def lightrag(self) -> LightRAG:
        """Get the initialized LightRAG instance."""
        if self._lightrag is None:
            raise RuntimeError("LightRAG not initialized. Call initialize() first.")
        return self._lightrag
    
    async def query(self, query: str, **kwargs):
        """Safe query method that ensures proper initialization."""
        if self._lightrag is None:
            await self.initialize()
        return await self._lightrag.aquery(query, **kwargs)
    
    async def insert(self, text: str, **kwargs):
        """Safe insert method that ensures proper initialization."""
        if self._lightrag is None:
            await self.initialize()
        return await self._lightrag.ainsert(text, **kwargs)


def get_safe_lightrag_config() -> Dict[str, Any]:
    """
    Get safe LightRAG configuration that avoids storage lock issues.
    
    Returns:
        Dictionary with safe configuration parameters
    """
    return {
        'working_dir': os.getenv('LIGHTRAG_STORAGE_DIR', './rag_storage'),
        'doc_status_storage': 'JsonDocStatusStorage',
        'kv_storage': 'JsonKVStorage',
        'vector_storage': 'NanoVectorDBStorage',
        'graph_storage': 'NetworkXStorage',
        'enable_llm_cache': True,
        'enable_doc_status': True,
    }


async def create_safe_lightrag(**kwargs) -> LightRAG:
    """
    Create a safe LightRAG instance with proper initialization.
    
    Args:
        **kwargs: Additional configuration parameters
        
    Returns:
        Properly initialized LightRAG instance
    """
    # Get safe configuration
    config = get_safe_lightrag_config()
    config.update(kwargs)
    
    # Ensure shared data initialization
    workers = int(os.getenv('LIGHTRAG_WORKERS', '1'))
    initialize_share_data(workers=workers)
    
    # Create and initialize LightRAG
    lightrag = LightRAG(**config)
    
    # Wait a bit to ensure initialization
    await asyncio.sleep(0.1)
    
    return lightrag