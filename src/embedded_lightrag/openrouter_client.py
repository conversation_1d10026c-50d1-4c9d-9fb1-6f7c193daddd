"""
OpenRouter API客户端
为LightRAG提供OpenRouter兼容的LLM和embedding接口
"""

import asyncio
import os
import aiohttp
import json
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)


class OpenRouterClient:
    """OpenRouter API客户端"""
    
    def __init__(self, api_key: str = None, base_url: str = "https://openrouter.ai/api/v1"):
        """初始化OpenRouter客户端"""
        self.api_key = api_key or os.getenv("OPENAI_API_KEY") or os.getenv("OPENROUTER_API_KEY")
        self.base_url = base_url
        if not self.api_key:
            raise ValueError("OpenRouter API key is required")
    
    async def _make_request(self, endpoint: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """发送API请求"""
        url = f"{self.base_url}/{endpoint}"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://ygagentlanggraph",  # 必需，用于OpenRouter
            "X-Title": "LightRAG-Integration"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"OpenRouter API error ({response.status}): {error_text}")
                return await response.json()


async def openrouter_complete(
    prompt: str,
    model: str = "deepseek/deepseek-chat-v3-0324",
    max_tokens: int = 4000,
    temperature: float = 0.3,
    **kwargs
) -> str:
    """OpenRouter LLM完成函数"""
    client = OpenRouterClient()
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": max_tokens,
        "temperature": temperature,
        "stream": False
    }
    
    try:
        response = await client._make_request("chat/completions", payload)
        return response["choices"][0]["message"]["content"]
    except Exception as e:
        logger.error(f"OpenRouter LLM error: {e}")
        raise


async def openrouter_embed(
    texts: List[str],
    model: str = "text-embedding-3-small",
    **kwargs
) -> List[List[float]]:
    """OpenRouter embedding函数"""
    client = OpenRouterClient()
    
    # 处理单个字符串
    if isinstance(texts, str):
        texts = [texts]
    
    payload = {
        "model": model,
        "input": texts
    }
    
    try:
        response = await client._make_request("embeddings", payload)
        return [item["embedding"] for item in response["data"]]
    except Exception as e:
        logger.error(f"OpenRouter embedding error: {e}")
        raise


# 兼容LightRAG的同步包装器
def openrouter_complete_sync(prompt: str, **kwargs) -> str:
    """同步包装器，兼容LightRAG的llm_model_func"""
    return asyncio.run(openrouter_complete(prompt, **kwargs))


def openrouter_embed_sync(texts: List[str], **kwargs) -> List[List[float]]:
    """同步包装器，兼容LightRAG的embedding_func"""
    return asyncio.run(openrouter_embed(texts, **kwargs))


# 配置示例
EXAMPLE_ENV = {
    "OPENAI_API_KEY": "your-openrouter-key-here",
    "OPENAI_BASE_URL": "https://openrouter.ai/api/v1",
    "LLM_MODEL": "deepseek/deepseek-chat-v3-0324",
    "EMBEDDING_MODEL": "text-embedding-3-small"
}