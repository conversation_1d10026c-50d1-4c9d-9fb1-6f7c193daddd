"""
LightRAG Storage Lock Fix

This module provides a fix for the JsonDocStatus storage lock issue without modifying the LightRAG source code.
The issue occurs when JsonDocStatusStorage._storage_lock is None and methods try to use it as an async context manager.
"""

import asyncio
from typing import Any, Dict, Optional
from lightrag.kg.json_doc_status_impl import JsonDocStatusStorage
from lightrag.kg.shared_storage import get_storage_lock, initialize_share_data
from lightrag.base import DocStatus


class FixedJsonDocStatusStorage(JsonDocStatusStorage):
    """
    A wrapper/fix for JsonDocStatusStorage that ensures storage lock is properly initialized.
    
    This class extends JsonDocStatusStorage to ensure the _storage_lock is properly
    initialized before any method uses it in async context managers.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._initialized_lock = False
    
    async def _ensure_storage_lock_initialized(self):
        """Ensure storage lock is properly initialized."""
        if not self._initialized_lock:
            if self._storage_lock is None:
                # Initialize the storage lock
                self._storage_lock = get_storage_lock()
            self._initialized_lock = True
    
    async def initialize(self):
        """Initialize storage data with proper lock initialization."""
        await self._ensure_storage_lock_initialized()
        await super().initialize()
    
    async def filter_keys(self, keys: set[str]) -> set[str]:
        """Ensure lock is initialized before filtering keys."""
        await self._ensure_storage_lock_initialized()
        return await super().filter_keys(keys)
    
    async def get_by_ids(self, ids: list[str]) -> list[dict[str, Any]]:
        """Ensure lock is initialized before getting by IDs."""
        await self._ensure_storage_lock_initialized()
        return await super().get_by_ids(ids)
    
    async def get_status_counts(self) -> dict[str, int]:
        """Ensure lock is initialized before getting status counts."""
        await self._ensure_storage_lock_initialized()
        return await super().get_status_counts()
    
    async def get_docs_by_status(self, status: DocStatus) -> dict[str, Any]:
        """Ensure lock is initialized before getting docs by status."""
        await self._ensure_storage_lock_initialized()
        return await super().get_docs_by_status(status)
    
    async def index_done_callback(self) -> None:
        """Ensure lock is initialized before index done callback."""
        await self._ensure_storage_lock_initialized()
        await super().index_done_callback()
    
    async def upsert(self, data: dict[str, dict[str, Any]]) -> None:
        """Ensure lock is initialized before upserting data."""
        await self._ensure_storage_lock_initialized()
        await super().upsert(data)
    
    async def get_by_id(self, id: str) -> Optional[dict[str, Any]]:
        """Ensure lock is initialized before getting by ID."""
        await self._ensure_storage_lock_initialized()
        return await super().get_by_id(id)
    
    async def delete(self, doc_ids: list[str]) -> None:
        """Ensure lock is initialized before deleting docs."""
        await self._ensure_storage_lock_initialized()
        await super().delete(doc_ids)
    
    async def drop(self) -> dict[str, str]:
        """Ensure lock is initialized before dropping data."""
        await self._ensure_storage_lock_initialized()
        return await super().drop()


class LightRAGStorageFix:
    """
    Manager class for applying LightRAG storage fixes.
    
    This class provides methods to patch LightRAG's storage system to avoid
    the storage lock issue without modifying the source code.
    """
    
    @staticmethod
    def apply_fix():
        """
        Apply the storage lock fix by monkey-patching the JsonDocStatusStorage.
        
        This replaces the original JsonDocStatusStorage with our fixed version.
        """
        import lightrag.lightrag
        from lightrag.kg.json_doc_status_impl import JsonDocStatusStorage
        
        # Replace the original class with our fixed version
        lightrag.lightrag.JsonDocStatusStorage = FixedJsonDocStatusStorage
        lightrag.kg.json_doc_status_impl.JsonDocStatusStorage = FixedJsonDocStatusStorage
    
    @staticmethod
    def ensure_shared_data_initialized(workers: int = 1):
        """
        Ensure shared data is properly initialized.
        
        Args:
            workers: Number of worker processes (1 for single process, >1 for multiprocess)
        """
        initialize_share_data(workers=workers)
    
    @staticmethod
    def create_fixed_storage(*args, **kwargs) -> FixedJsonDocStatusStorage:
        """
        Create a new FixedJsonDocStatusStorage instance.
        
        Args:
            *args: Arguments to pass to JsonDocStatusStorage
            **kwargs: Keyword arguments to pass to JsonDocStatusStorage
            
        Returns:
            FixedJsonDocStatusStorage instance
        """
        return FixedJsonDocStatusStorage(*args, **kwargs)


def patch_lightrag_storage():
    """
    Convenience function to apply the LightRAG storage fix.
    
    This function patches the LightRAG storage system to avoid the storage lock issue.
    """
    LightRAGStorageFix.apply_fix()
    LightRAGStorageFix.ensure_shared_data_initialized(workers=1)