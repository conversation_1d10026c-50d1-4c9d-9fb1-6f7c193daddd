"""
本地模型处理器
实现本地embedding和reranking模型支持
"""

import os
import logging
from typing import List, Union
import numpy as np

logger = logging.getLogger(__name__)

try:
    import torch
    from transformers import AutoTokenizer, AutoModel
    TORCH_AVAILABLE = True
except ImportError as e:
    logger.warning(f"PyTorch或transformers不可用: {e}")
    TORCH_AVAILABLE = False


class LocalEmbeddingModel:
    """本地embedding模型封装"""
    
    def __init__(self, model_path: str):
        """初始化本地embedding模型"""
        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch或transformers不可用，无法加载本地模型")
            
        self.model_path = model_path
        self.tokenizer = None
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            logger.info(f"加载本地embedding模型: {self.model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModel.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            logger.info("本地embedding模型加载成功")
        except Exception as e:
            logger.error(f"加载本地embedding模型失败: {e}")
            raise
    
    def embed(self, text: str) -> List[float]:
        """获取文本的embedding向量"""
        try:
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                # 使用最后一个token的隐藏状态作为embedding
                embedding = outputs.last_hidden_state[:, -1, :].squeeze()
                
            return embedding.cpu().numpy().tolist()
        except Exception as e:
            logger.error(f"embedding计算失败: {e}")
            raise
    
    def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """批量获取embedding"""
        return [self.embed(text) for text in texts]


class LocalRerankingModel:
    """本地reranking模型封装"""
    
    def __init__(self, model_path: str):
        """初始化本地reranking模型"""
        if not TORCH_AVAILABLE:
            raise RuntimeError("PyTorch或transformers不可用，无法加载本地模型")
            
        self.model_path = model_path
        self.tokenizer = None
        self.model = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            logger.info(f"加载本地reranking模型: {self.model_path}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModel.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            logger.info("本地reranking模型加载成功")
        except Exception as e:
            logger.error(f"加载本地reranking模型失败: {e}")
            raise
    
    def rerank(self, query: str, documents: List[str]) -> List[float]:
        """对文档进行reranking，返回相关性分数"""
        try:
            scores = []
            for doc in documents:
                # 构造输入: [CLS] query [SEP] doc [SEP]
                inputs = self.tokenizer(
                    query, doc,
                    return_tensors="pt",
                    truncation=True,
                    max_length=512,
                    padding=True
                )
                inputs = {k: v.to(self.device) for k, v in inputs.items()}
                
                with torch.no_grad():
                    outputs = self.model(**inputs)
                    # 使用CLS token的隐藏状态计算相关性
                    score = outputs.last_hidden_state[:, 0, :].squeeze()
                    # 简化的相关性分数计算
                    score = torch.sigmoid(score.mean())
                    scores.append(score.cpu().item())
            
            return scores
        except Exception as e:
            logger.error(f"reranking计算失败: {e}")
            raise


def create_local_embedding_func(model_path: str):
    """创建LightRAG兼容的本地embedding函数"""
    if not TORCH_AVAILABLE:
        raise RuntimeError("PyTorch或transformers不可用，无法创建本地embedding函数")
    
    model = LocalEmbeddingModel(model_path)
    
    async def local_embedding(texts: List[str]) -> List[List[float]]:
        """LightRAG兼容的embedding函数"""
        if isinstance(texts, str):
            texts = [texts]
        return model.embed_batch(texts)
    
    return local_embedding


def create_local_reranking_func(model_path: str):
    """创建LightRAG兼容的本地reranking函数"""
    if not TORCH_AVAILABLE:
        raise RuntimeError("PyTorch或transformers不可用，无法创建本地reranking函数")
    
    model = LocalRerankingModel(model_path)
    
    async def local_rerank(query: str, documents: List[str]) -> List[float]:
        """LightRAG兼容的reranking函数"""
        return model.rerank(query, documents)
    
    return local_rerank