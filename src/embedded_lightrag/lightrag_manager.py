"""
LightRAG管理器
为ygagentlanggraph项目提供LightRAG的封装和管理
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from lightrag import LightRAG, QueryParam
from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc

# 导入修复模块
from .lightrag_fix import LightRAGStorageFix

class LightRAGManager:
    """LightRAG管理器 - 项目级封装"""
    
    def __init__(self, working_dir: str = "./rag_storage", config: Dict[str, Any] = None):
        """
        初始化LightRAG管理器
        
        Args:
            working_dir: 工作目录路径
            config: 配置字典，包含LLM和存储配置
        """
        self.working_dir = working_dir
        self.config = config or {}
        self.rag = None
        self._initialized = False
        
    async def initialize(self, api_key: str, base_url: str = None, model_name: str = None) -> bool:
        """
        初始化LightRAG实例
        
        Args:
            api_key: OpenAI API密钥
            base_url: API基础URL（可选）
            model_name: 模型名称（可选）
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 应用LightRAG存储修复
            LightRAGStorageFix.apply_fix()
            LightRAGStorageFix.ensure_shared_data_initialized(workers=1)
            
            # 设置环境变量
            os.environ["OPENAI_API_KEY"] = api_key
            if base_url and base_url != "https://api.openai.com/v1":
                os.environ["OPENAI_BASE_URL"] = base_url
            
            # 创建存储目录
            os.makedirs(self.working_dir, exist_ok=True)
            
            # 初始化LightRAG
            self.rag = LightRAG(
                working_dir=self.working_dir,
                llm_model_func=openai_complete_if_cache,
                llm_model_name=model_name or "gpt-4o-mini",
                embedding_func=EmbeddingFunc(
                    embedding_dim=1536,
                    max_token_size=8192,
                    func=openai_embed
                ),
                # 轻量级存储配置
                vector_storage="NanoVectorDBStorage",
                graph_storage="NetworkXStorage",
                kv_storage="JsonKVStorage",
                doc_status_storage="JsonDocStatusStorage",
                # 优化参数
                chunk_token_size=self.config.get("chunk_size", 1000),
                chunk_overlap_token_size=self.config.get("chunk_overlap", 100),
                max_async=self.config.get("max_async", 2),
                enable_llm_cache=True
            )
            
            self._initialized = True
            return True
            
        except Exception as e:
            print(f"LightRAG初始化失败: {e}")
            return False
    
    async def insert_requirements(self, requirements: List[str], ids: List[str] = None) -> bool:
        """
        插入需求数据
        
        Args:
            requirements: 需求文本列表
            ids: 需求ID列表（可选）
            
        Returns:
            bool: 插入是否成功
        """
        if not self._initialized:
            raise RuntimeError("LightRAG未初始化，请先调用initialize()")
            
        try:
            if ids:
                await self.rag.ainsert(requirements, ids=ids)
            else:
                await self.rag.ainsert(requirements)
            return True
        except Exception as e:
            print(f"需求插入失败: {e}")
            return False
    
    async def query_requirements(self, query: str, mode: str = "hybrid") -> str:
        """
        查询需求知识图谱
        
        Args:
            query: 查询问题
            mode: 查询模式 (local/global/hybrid/naive)
            
        Returns:
            str: 查询结果
        """
        if not self._initialized:
            raise RuntimeError("LightRAG未初始化，请先调用initialize()")
            
        try:
            result = await self.rag.aquery(
                query,
                QueryParam(mode=mode)
            )
            return result
        except Exception as e:
            return f"查询失败: {e}"
    
    async def analyze_requirement_conflicts(self) -> str:
        """分析需求冲突"""
        return await self.query_requirements(
            "分析知识图谱中的需求是否存在冲突、重复或矛盾的地方",
            mode="global"
        )
    
    async def get_requirement_dependencies(self, requirement_id: str = None) -> str:
        """获取需求依赖关系"""
        if requirement_id:
            query = f"分析需求 {requirement_id} 与其他需求的依赖关系"
        else:
            query = "分析所有需求之间的依赖关系和优先级"
        
        return await self.query_requirements(query, mode="hybrid")
    
    async def summarize_requirements(self) -> str:
        """总结需求"""
        return await self.query_requirements(
            "总结系统中所有需求的核心功能和业务逻辑",
            mode="global"
        )
    
    async def search_similar_requirements(self, text: str) -> str:
        """搜索相似需求"""
        return await self.query_requirements(
            f"查找与以下内容相似的需求: {text}",
            mode="local"
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取LightRAG统计信息"""
        if not self._initialized:
            return {"initialized": False}
            
        # 这里可以添加更多统计信息
        return {
            "initialized": True,
            "working_dir": self.working_dir,
            "config": self.config
        }

# 全局实例
_lightrag_manager = None

async def get_lightrag_manager() -> LightRAGManager:
    """获取全局LightRAG管理器实例"""
    global _lightrag_manager
    if _lightrag_manager is None:
        _lightrag_manager = LightRAGManager()
    return _lightrag_manager