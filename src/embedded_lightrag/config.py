"""
LightRAG配置管理
使用config/settings.py中的配置
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class LightRAGConfig:
    """LightRAG配置类"""
    
    # 基础配置
    working_dir: str = "./rag_storage"
    
    # LLM配置 - 使用settings.py中的配置
    llm_model_name: str = "gpt-4o-mini"
    embedding_model_name: str = "text-embedding-3-small"
    
    # API配置
    api_base: str = "https://openrouter.ai/api/v1"
    api_key: str = ""
    
    # 本地模型配置
    embedding_model_path: str = "/home/<USER>/localai/Qwen3-embedding-8B"
    reranking_model_path: str = "/home/<USER>/localai/Qwen3-Reranker-4B"
    
    # 使用本地模型标志
    use_local_embedding: bool = True
    use_local_reranking: bool = True
    use_api_llm: bool = True
    
    def __post_init__(self):
        """从settings.py加载配置"""
        try:
            from config.settings import settings
            self.api_base = settings.base_url
            self.api_key = settings.openai_api_key
            self.llm_model_name = settings.llm_model
            self.embedding_model_path = settings.embedding_model_path
            self.reranking_model_path = settings.reranking_model_path
            self.use_local_embedding = settings.use_local_embedding
            self.use_local_reranking = settings.use_local_reranking
            self.use_api_llm = settings.use_api_llm
        except ImportError:
            # 降级到环境变量
            self.api_base = os.getenv("OPENAI_BASE_URL", self.api_base)
            self.api_key = os.getenv("OPENAI_API_KEY", self.api_key)
            self.llm_model_name = os.getenv("LLM_MODEL", self.llm_model_name)
            self.embedding_model_path = os.getenv("EMBEDDING_MODEL_PATH", self.embedding_model_path)
            self.reranking_model_path = os.getenv("RERANKING_MODEL_PATH", self.reranking_model_path)
            self.use_local_embedding = os.getenv("USE_LOCAL_EMBEDDING", "true").lower() == "true"
            self.use_local_reranking = os.getenv("USE_LOCAL_RERANKING", "true").lower() == "true"
            self.use_api_llm = os.getenv("USE_API_LLM", "true").lower() == "true"
        
        # 确保工作目录存在
        os.makedirs(self.working_dir, exist_ok=True)
    
    @classmethod
    def from_env(cls) -> "LightRAGConfig":
        """从环境变量和settings创建配置"""
        try:
            from config.settings import settings
            return cls(
                working_dir=os.getenv("LIGHTRAG_STORAGE_DIR", "./rag_storage"),
                api_base=settings.base_url,
                api_key=settings.openai_api_key,
                llm_model_name=settings.llm_model,
                embedding_model_path=settings.embedding_model_path,
                reranking_model_path=settings.reranking_model_path,
                use_local_embedding=settings.use_local_embedding,
                use_local_reranking=settings.use_local_reranking,
                use_api_llm=settings.use_api_llm
            )
        except ImportError:
            return cls(
                working_dir=os.getenv("LIGHTRAG_STORAGE_DIR", "./rag_storage"),
                api_base=os.getenv("OPENAI_BASE_URL", "https://openrouter.ai/api/v1"),
                api_key=os.getenv("OPENAI_API_KEY", ""),
                llm_model_name=os.getenv("LLM_MODEL", "gpt-4o-mini"),
                embedding_model_path=os.getenv("EMBEDDING_MODEL_PATH", "/home/<USER>/localai/Qwen3-embedding-8B"),
                reranking_model_path=os.getenv("RERANKING_MODEL_PATH", "/home/<USER>/localai/Qwen3-Reranker-4B"),
                use_local_embedding=os.getenv("USE_LOCAL_EMBEDDING", "true").lower() == "true",
                use_local_reranking=os.getenv("USE_LOCAL_RERANKING", "true").lower() == "true",
                use_api_llm=os.getenv("USE_API_LLM", "true").lower() == "true"
            )