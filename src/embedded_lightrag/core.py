"""
嵌入式LightRAG核心封装
基于官方示例的正确配置
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from lightrag import LightRAG, QueryParam
from lightrag.llm.openai import gpt_4o_mini_complete, openai_embed
from .openrouter_client import openrouter_complete_sync as openrouter_complete, openrouter_embed_sync as openrouter_embed
from lightrag.kg.shared_storage import initialize_pipeline_status

from .config import LightRAGConfig


class EmbeddedLightRAG:
    """嵌入式LightRAG封装类"""
    
    def __init__(self, config: Optional[LightRAGConfig] = None):
        """初始化嵌入式LightRAG"""
        self.config = config or LightRAGConfig.from_env()
        self.rag = None
        
    async def initialize(self) -> None:
        """初始化LightRAG实例"""
        try:
            # 选择embedding函数
            if self.config.use_local_embedding:
                try:
                    from .local_models import create_local_embedding_func
                    embedding_func = create_local_embedding_func(self.config.embedding_model_path)
                    print(f"🎯 使用本地embedding模型: {self.config.embedding_model_path}")
                except Exception as e:
                    print(f"⚠️  本地embedding模型加载失败，回退到OpenRouter: {e}")
                    embedding_func = openrouter_embed
            else:
                # 使用OpenRouter API embedding
                embedding_func = openrouter_embed
                print(f"🎯 使用OpenRouter API embedding: {self.config.embedding_model_name}")
            
            # 选择LLM函数
            if self.config.use_api_llm:
                # 使用OpenRouter API LLM
                llm_func = openrouter_complete
                print(f"🎯 使用OpenRouter API LLM: {self.config.llm_model_name}")
            else:
                raise NotImplementedError("本地LLM尚未实现")
            
            # 创建LightRAG实例
            self.rag = LightRAG(
                working_dir=self.config.working_dir,
                embedding_func=embedding_func,
                llm_model_func=llm_func,
            )
            
            # 初始化存储
            await self.rag.initialize_storages()
            await initialize_pipeline_status()
            
        except Exception as e:
            raise RuntimeError(f"LightRAG初始化失败: {e}")
    
    async def finalize(self) -> None:
        """清理资源"""
        if self.rag:
            await self.rag.finalize_storages()
    
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self.rag is not None
    
    async def insert_document(self, content: str, doc_id: Optional[str] = None) -> str:
        """插入文档到知识图谱"""
        if not self.is_initialized():
            raise RuntimeError("LightRAG未初始化，请先调用initialize()")
        
        try:
            # 异步插入文档
            doc_id = await self.rag.ainsert(content)
            return doc_id
        except Exception as e:
            raise RuntimeError(f"文档插入失败: {e}")
    
    async def query(self, query_text: str, mode: str = "naive") -> str:
        """查询知识图谱"""
        if not self.is_initialized():
            raise RuntimeError("LightRAG未初始化，请先调用initialize()")
        
        try:
            from lightrag import QueryParam
            param = QueryParam(mode=mode)
            result = await self.rag.aquery(query_text, param=param)
            return result
        except Exception as e:
            raise RuntimeError(f"查询失败: {e}")
    
    async def batch_insert(self, contents: List[str]) -> List[str]:
        """批量插入文档"""
        if not self.is_initialized():
            raise RuntimeError("LightRAG未初始化，请先调用initialize()")
        
        try:
            # 批量插入
            doc_ids = []
            for content in contents:
                doc_id = await self.rag.ainsert(content)
                doc_ids.append(doc_id)
            return doc_ids
        except Exception as e:
            raise RuntimeError(f"批量插入失败: {e}")
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息"""
        if not self.is_initialized():
            return {"status": "未初始化"}
        
        return {
            "status": "已初始化",
            "working_dir": self.config.working_dir
        }
    
    def clear_storage(self) -> None:
        """清空存储（谨慎使用）"""
        import shutil
        if os.path.exists(self.config.working_dir):
            shutil.rmtree(self.config.working_dir)
            os.makedirs(self.config.working_dir, exist_ok=True)


# 便捷函数
async def initialize_lightrag(config: Optional[LightRAGConfig] = None) -> EmbeddedLightRAG:
    """便捷初始化函数"""
    rag = EmbeddedLightRAG(config)
    await rag.initialize()
    return rag