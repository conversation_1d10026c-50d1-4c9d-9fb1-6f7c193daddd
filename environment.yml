name: ai_env_zsy
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - _python_abi3_support=1.0=hd8ed1ab_2
  - accelerate=1.9.0=pyhe01879c_0
  - aioboto3=14.1.0=pyhd8ed1ab_0
  - aiobotocore=2.21.1=pyhd8ed1ab_0
  - aiofiles=24.1.0=pyhd8ed1ab_1
  - aiohappyeyeballs=2.6.1=pyhd8ed1ab_0
  - aiohttp=3.12.15=py310h3406613_0
  - aioitertools=0.12.0=pyhd8ed1ab_1
  - aiosignal=1.4.0=pyhd8ed1ab_0
  - annotated-types=0.7.0=pyhd8ed1ab_1
  - anyio=4.9.0=pyh29332c3_0
  - anytree=2.13.0=pyhd8ed1ab_0
  - asttokens=3.0.0=pyhd8ed1ab_1
  - async-timeout=4.0.3=pyhd8ed1ab_0
  - attr=2.5.1=h166bdaf_1
  - attrs=25.3.0=pyh71513ae_0
  - autograd=1.8.0=pyhd8ed1ab_0
  - beartype=0.18.5=pyhd8ed1ab_0
  - boto3=1.37.1=pyhd8ed1ab_0
  - botocore=1.37.1=pyge310_1234567_0
  - brotli=1.1.0=hb9d3cd8_3
  - brotli-bin=1.1.0=hb9d3cd8_3
  - brotlicffi=*******=py310hf71b8c6_2
  - bzip2=1.0.8=h4bc722e_7
  - c-ares=1.34.5=hb9d3cd8_0
  - ca-certificates=2025.7.14=hbd8a1cb_0
  - certifi=2025.7.14=py310h06a4308_0
  - cffi=1.17.1=py310h1fdaa30_1
  - charset-normalizer=3.4.2=pyhd8ed1ab_0
  - click=8.2.1=pyh707e725_0
  - colorama=0.4.6=pyhd8ed1ab_1
  - colorlog=6.9.0=pyh707e725_1
  - contourpy=1.3.2=py310h3788b33_0
  - cpython=3.10.18=py310hd8ed1ab_0
  - cryptography=45.0.5=py310h6c63255_0
  - cuda-crt-tools=12.9.86=ha770c72_2
  - cuda-cudart=12.9.79=h5888daf_0
  - cuda-cudart_linux-64=12.9.79=h3f2d84a_0
  - cuda-cuobjdump=12.9.82=hbd13f7d_0
  - cuda-cupti=12.9.79=h9ab20c4_0
  - cuda-nvcc-tools=12.9.86=he02047a_2
  - cuda-nvdisasm=12.9.88=hbd13f7d_0
  - cuda-nvrtc=12.9.86=h5888daf_0
  - cuda-nvtx=12.9.79=h5888daf_0
  - cuda-nvvm-tools=12.9.86=h4bc722e_2
  - cuda-version=12.9=h4f385c5_3
  - cudnn=********=hbcb9cd8_1
  - cusparselt=*******=hcd2ec93_0
  - cycler=0.12.1=pyhd8ed1ab_1
  - dataclasses=0.8=pyhc8e2a94_3
  - dataclasses-json=0.6.7=pyhd8ed1ab_1
  - decorator=5.2.1=pyhd8ed1ab_0
  - distro=1.9.0=pyhd8ed1ab_1
  - dnspython=2.7.0=pyhff2d567_1
  - email-validator=2.2.0=pyhd8ed1ab_1
  - email_validator=2.2.0=hd8ed1ab_1
  - exceptiongroup=1.3.0=pyhd8ed1ab_0
  - executing=2.2.0=pyhd8ed1ab_0
  - fastapi=0.116.1=h26c32bb_1
  - fastapi-cli=0.0.8=pyhd8ed1ab_0
  - fastapi-core=0.116.1=pyhe01879c_1
  - filelock=3.18.0=pyhd8ed1ab_0
  - fonttools=4.59.0=py310h3406613_0
  - freetype=2.13.3=ha770c72_1
  - frozenlist=1.7.0=py310h9548a50_0
  - fsspec=2025.7.0=py310he3bba80_0
  - gensim=4.3.3=py310h27b3328_0
  - gmp=6.3.0=hac33072_2
  - gmpy2=2.2.1=py310he8512ff_0
  - graspologic=3.4.1=pyhd8ed1ab_0
  - graspologic-native=1.2.5=py310h505e2c1_0
  - greenlet=3.2.3=py310hf71b8c6_0
  - grpcio=1.68.2=py310h56e06c5_0
  - h11=0.16.0=py310h06a4308_0
  - hf-xet=1.1.5=py39h260a9e5_3
  - hnswlib=0.8.0=py310ha2bacc8_0
  - httpcore=1.0.9=py310h06a4308_0
  - httptools=0.6.4=py310ha75aee5_0
  - httpx=0.28.1=py310h06a4308_0
  - httpx-sse=0.4.1=pyhd8ed1ab_0
  - huggingface_hub=0.34.3=pyhd8ed1ab_0
  - hyppo=0.4.0=pyhd8ed1ab_0
  - icu=75.1=he02047a_0
  - idna=3.10=pyhd8ed1ab_1
  - importlib-metadata=8.7.0=pyhe01879c_1
  - importlib_metadata=8.7.0=h40b2b14_1
  - ipython=8.37.0=pyh8f84b5b_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jinja2=3.1.6=pyhd8ed1ab_0
  - jiter=0.10.0=py310h505e2c1_0
  - jmespath=1.0.1=pyhd8ed1ab_1
  - joblib=1.5.1=pyhd8ed1ab_0
  - json-repair=0.48.0=pyhd8ed1ab_0
  - jsonpatch=1.33=py310h06a4308_1
  - jsonpickle=4.1.1=pyhe01879c_0
  - jsonpointer=3.0.0=py310hff52083_1
  - kiwisolver=1.4.8=py310h3788b33_1
  - langchain=0.3.27=pymin39_hff2d567_0
  - langchain-core=0.3.72=pyhd8ed1ab_0
  - langchain-openai=0.3.28=pyhd8ed1ab_0
  - langchain-text-splitters=0.3.9=pyhd8ed1ab_0
  - langgraph=0.6.2=pyhd8ed1ab_0
  - langgraph-checkpoint=2.1.1=pyhe01879c_1
  - langgraph-prebuilt=0.6.2=pyhe01879c_0
  - langgraph-sdk=0.2.0=pyhe01879c_1
  - langsmith=0.3.45=pyhd8ed1ab_0
  - lcms2=2.17=h717163a_0
  - ld_impl_linux-64=2.44=h1423503_1
  - lerc=4.0.0=h0aef613_1
  - libabseil=20240722.0=cxx17_hbbce691_4
  - libblas=3.9.0=32_h59b9bed_openblas
  - libbrotlicommon=1.1.0=hb9d3cd8_3
  - libbrotlidec=1.1.0=hb9d3cd8_3
  - libbrotlienc=1.1.0=hb9d3cd8_3
  - libcap=2.75=h39aace5_0
  - libcblas=3.9.0=32_he106b2a_openblas
  - libcublas=12.9.1.4=h9ab20c4_0
  - libcudnn=********=hf7e9902_1
  - libcudnn-dev=********=h58dd1b1_1
  - libcudss0=0.4.0.2=he55f5cd_2
  - libcufft=11.4.1.4=h5888daf_0
  - libcufile=1.14.1.1=ha8da6e3_0
  - libcurand=10.3.10.19=h9ab20c4_0
  - libcusolver=11.7.5.82=h9ab20c4_1
  - libcusparse=12.5.10.65=h5888daf_1
  - libdeflate=1.24=h86f0d12_0
  - libexpat=2.7.1=hecca717_0
  - libffi=3.4.6=h2dba641_1
  - libfreetype=2.13.3=ha770c72_1
  - libfreetype6=2.13.3=h48d6fc4_1
  - libgcc=15.1.0=h767d61c_4
  - libgcc-ng=15.1.0=h69a702a_4
  - libgcrypt-lib=1.11.1=hb9d3cd8_0
  - libgfortran=15.1.0=h69a702a_4
  - libgfortran-ng=15.1.0=h69a702a_4
  - libgfortran5=15.1.0=hcea5267_4
  - libgomp=15.1.0=h767d61c_4
  - libgpg-error=1.55=h3f2d84a_0
  - libgrpc=1.68.2=h25350d4_0
  - libhwloc=2.12.1=default_h3d81e11_1000
  - libiconv=1.18=h4ce23a2_1
  - libjpeg-turbo=3.1.0=hb9d3cd8_0
  - liblapack=3.9.0=32_h7ac8fdf_openblas
  - libllvm20=20.1.8=hecd9e04_0
  - liblzma=5.8.1=hb9d3cd8_2
  - liblzma-devel=5.8.1=hb9d3cd8_2
  - libmagma=2.8.0=h566cb83_2
  - libnl=3.11.0=hb9d3cd8_0
  - libnsl=2.0.1=hb9d3cd8_1
  - libnvjitlink=12.9.86=h5888daf_1
  - libopenblas=0.3.30=pthreads_h94d23a6_1
  - libpng=1.6.50=h421ea60_1
  - libprotobuf=5.28.3=h6128344_1
  - libre2-11=2024.07.02=hbbce691_2
  - libsqlite=3.50.4=h0c1763c_0
  - libstdcxx=15.1.0=h8f9b012_4
  - libstdcxx-ng=15.1.0=h4852527_4
  - libsystemd0=257.7=h4e0b6ca_0
  - libtiff=4.7.0=hf01ce69_5
  - libtorch=2.6.0=cuda126_generic_h4a15719_200
  - libudev1=257.7=hbe16f8c_0
  - libuuid=2.38.1=h0b41bf4_0
  - libuv=1.51.0=hb03c661_1
  - libwebp-base=1.6.0=hd42ef1d_0
  - libxcb=1.17.0=h8a09558_0
  - libxcrypt=4.4.36=hd590300_1
  - libxml2=2.13.8=h4bc477f_0
  - libxslt=1.1.43=h7a3aeb2_0
  - libzlib=1.3.1=hb9d3cd8_2
  - llvmlite=0.44.0=py310h1a6248f_1
  - lxml=5.4.0=py310h490dddc_0
  - lz4-c=1.10.0=h5888daf_1
  - markdown-it-py=3.0.0=pyhd8ed1ab_1
  - markupsafe=3.0.2=py310h89163eb_1
  - marshmallow=3.26.1=pyhd8ed1ab_0
  - matplotlib-base=3.10.3=py310h68603db_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - mdurl=0.1.2=pyhd8ed1ab_1
  - mpc=1.3.1=h24ddda3_1
  - mpfr=4.2.1=h90cbb55_3
  - mpmath=1.3.0=pyhd8ed1ab_1
  - msgpack-python=1.1.1=py310h6a678d5_0
  - multidict=6.6.3=py310h89163eb_0
  - munkres=1.1.4=pyhd8ed1ab_1
  - mypy_extensions=1.1.0=pyha770c72_0
  - nano-vectordb=0.0.4.3=pyhd8ed1ab_1
  - nccl=2.27.7.1=h49b9d9a_0
  - ncurses=6.5=h2d0b736_3
  - neo4j=5.28.1=hd3eb1b0_0
  - neo4j-python-driver=5.28.1=py310h06a4308_0
  - networkx=3.4.2=pyh267e887_2
  - nomkl=1.0=h5ca1d4c_0
  - numba=0.61.2=py310h8648a56_1
  - numpy=1.26.4=py310hb13e2d6_0
  - ollama=0.9.6=cuda_129h63bc95c_
  - ollama-python=0.5.1=pyhd8ed1ab_0
  - openai=1.98.0=pyhd8ed1ab_0
  - openjpeg=2.5.3=h5fbd93e_0
  - openssl=3.5.1=h7b32b05_0
  - optree=0.17.0=py310h03d9f68_0
  - oracledb=3.3.0=py310h7c4b9e2_0
  - orjson=3.11.1=py310hd8f68c5_0
  - ormsgpack=1.10.0=py310he0c7687_0
  - packaging=25.0=pyh29332c3_1
  - pandas=2.3.1=py310h0158d43_0
  - parso=0.8.4=pyhd8ed1ab_1
  - patsy=1.0.1=pyhd8ed1ab_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pillow=11.3.0=py310h7e6dc6c_0
  - pip=25.2=pyh8b19718_0
  - pot=0.9.5=py310h5eaa309_0
  - prompt-toolkit=3.0.51=pyha770c72_0
  - propcache=0.3.1=py310h5eee18b_0
  - psutil=7.0.0=py310ha75aee5_0
  - pthread-stubs=0.4=hb9d3cd8_1002
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pybind11=3.0.0=pyh9380348_1
  - pybind11-global=3.0.0=pyhf748d72_1
  - pycparser=2.22=pyh29332c3_1
  - pydantic=2.11.7=py310h06a4308_0
  - pydantic-core=2.33.2=py310hc6f7160_0
  - pydantic-settings=2.10.1=pyh3cfb1c2_0
  - pygments=2.19.2=pyhd8ed1ab_0
  - pynndescent=0.5.13=pyhd8ed1ab_1
  - pyparsing=3.2.3=pyhe01879c_2
  - pysocks=1.7.1=pyha55dd90_7
  - python=3.10.18=hd6af730_0_cpython
  - python-dateutil=2.9.0.post0=pyhe01879c_2
  - python-docx=1.2.0=pyhff2d567_0
  - python-dotenv=1.1.1=pyhe01879c_0
  - python-gil=3.10.18=hd8ed1ab_0
  - python-multipart=0.0.20=pyhff2d567_0
  - python-tzdata=2025.2=pyhd8ed1ab_0
  - python-xxhash=3.5.0=py310ha75aee5_2
  - python_abi=3.10=8_cp310
  - pytorch=2.6.0=cuda126_generic_py310_h9bb2754_200
  - pytz=2025.2=py310h06a4308_0
  - pyvis=0.3.2=pyhd8ed1ab_1
  - pyyaml=6.0.2=py310h89163eb_2
  - qhull=2020.2=h434a139_5
  - rdma-core=58.0=h5888daf_0
  - re2=2024.07.02=h9925aae_2
  - readline=8.2=h8c095d6_2
  - regex=2025.7.34=py310h7c4b9e2_0
  - requests=2.32.4=py310h06a4308_0
  - requests-toolbelt=1.0.0=pyhd8ed1ab_1
  - rich=13.9.4=pyhd8ed1ab_1
  - rich-toolkit=0.14.9=pyhe01879c_0
  - s3transfer=0.11.3=pyhd8ed1ab_0
  - sacremoses=0.0.53=pyhd8ed1ab_0
  - safetensors=0.5.3=py310h505e2c1_0
  - scikit-learn=1.7.1=py310h228f341_0
  - scipy=1.12.0=py310hb13e2d6_2
  - seaborn=0.13.2=hd8ed1ab_3
  - seaborn-base=0.13.2=pyhd8ed1ab_3
  - setuptools=80.9.0=pyhff2d567_0
  - shellingham=1.5.4=pyhd8ed1ab_1
  - six=1.17.0=pyhe01879c_1
  - sleef=3.8=h1b44611_0
  - smart_open=7.3.0.post1=pyhe01879c_0
  - sniffio=1.3.1=pyhd8ed1ab_1
  - sqlalchemy=2.0.42=py310h7c4b9e2_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - starlette=0.47.2=pyh82d4cca_0
  - statsmodels=0.14.5=py310haaf2d95_0
  - sympy=1.14.0=pyh2585a3b_105
  - tbb=2022.2.0=hb60516a_0
  - tenacity=9.1.2=pyhd8ed1ab_0
  - threadpoolctl=3.6.0=pyhecae5ae_0
  - tk=8.6.13=noxft_hd72426e_102
  - tokenizers=0.21.4=py310h04b4747_0
  - tqdm=4.67.1=pyhd8ed1ab_1
  - traitlets=5.14.3=pyhd8ed1ab_1
  - transformers=4.17.0=pyhd8ed1ab_0
  - triton=3.2.0=cuda126py310h50ec074_1
  - typer=0.16.0=pyh167b9f4_0
  - typer-slim=0.16.0=pyhe01879c_0
  - typer-slim-standard=0.16.0=hf964461_0
  - typing-extensions=4.14.1=h4440ef1_0
  - typing-inspection=0.4.1=pyhd8ed1ab_0
  - typing_extensions=4.14.1=pyhe01879c_0
  - typing_inspect=0.9.0=pyhd8ed1ab_1
  - tzdata=2025b=h04d1e81_0
  - umap-learn=0.5.9.post2=py310hff52083_0
  - unicodedata2=16.0.0=py310ha75aee5_0
  - urllib3=2.5.0=py310h06a4308_0
  - uvicorn=0.35.0=pyh31011fe_0
  - uvicorn-standard=0.35.0=h31011fe_0
  - uvloop=0.21.0=py310ha75aee5_1
  - watchfiles=1.1.0=py310h505e2c1_0
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - websockets=15.0.1=py310ha75aee5_0
  - wheel=0.45.1=pyhd8ed1ab_1
  - wrapt=1.17.2=py310ha75aee5_0
  - xorg-libxau=1.0.12=hb9d3cd8_0
  - xorg-libxdmcp=1.1.5=hb9d3cd8_0
  - xxhash=0.8.3=hb47aa4a_0
  - xz=5.8.1=hbcc6ac9_2
  - xz-gpl-tools=5.8.1=hbcc6ac9_2
  - xz-tools=5.8.1=hb9d3cd8_2
  - yaml=0.2.5=h280c20c_3
  - yarl=1.20.1=py310h89163eb_0
  - zipp=3.23.0=pyhd8ed1ab_0
  - zlib=1.3.1=hb9d3cd8_2
  - zstandard=0.23.0=py310ha75aee5_2
  - zstd=1.5.7=hb8e6e7a_2
  - pip:
      - ascii-colors==0.11.4
      - backoff==2.2.1
      - configparser==7.2.0
      - dotenv==0.9.9
      - future==1.0.0
      - jsonlines==4.0.0
      - lightrag-hku==1.4.6
      - nest-asyncio==1.6.0
      - pipmaster==0.9.2
      - pyuca==1.2
      - tiktoken==0.7.0
      - xlsxwriter==3.2.5
prefix: /home/<USER>/miniforge3/envs/ai_env_lzy
