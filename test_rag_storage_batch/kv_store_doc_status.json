{"doc-a90122e219c9b574fae008f1957eb7e5": {"status": "failed", "error": "Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-or-v1*************************************************************0bc5. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}", "content": "用户登录需求：\n        1. 用户可以通过邮箱和密码登录系统\n        2. 支持记住登录状态\n        3. 登录失败时显示错误信息\n        4. 支持找回密码功能", "content_summary": "用户登录需求：\n        1. 用户可以通过邮箱和密码登录系统\n        2. 支持记住登录状态\n        3. 登录失败时显示错误信息\n        4. 支持找回密码功能", "content_length": 97, "created_at": "2025-08-01T03:11:11.449940+00:00", "updated_at": "2025-08-01T03:11:14.911481+00:00", "file_path": "unknown_source", "chunks_list": []}, "doc-ed64b24fba6eca55f86f7bf87324623c": {"status": "failed", "error": "Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-or-v1*************************************************************0bc5. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}", "content": "需求1：用户注册功能，支持邮箱和手机号注册", "content_summary": "需求1：用户注册功能，支持邮箱和手机号注册", "content_length": 21, "created_at": "2025-08-01T03:11:08.947097+00:00", "updated_at": "2025-08-01T03:11:14.686535+00:00", "file_path": "unknown_source", "chunks_list": []}, "doc-3902deff2aa90d3130a72d92605245f5": {"status": "failed", "error": "Error code: 401 - {'error': {'message': 'Incorrect API key provided: sk-or-v1*************************************************************0bc5. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}", "content": "需求2：商品管理功能，包括添加、编辑、删除商品", "content_summary": "需求2：商品管理功能，包括添加、编辑、删除商品", "content_length": 23, "created_at": "2025-08-01T03:11:10.175118+00:00", "updated_at": "2025-08-01T03:11:15.795241+00:00", "file_path": "unknown_source", "chunks_list": []}, "doc-5ca7d3835141b4ca396923098108b423": {"status": "processing", "chunks_count": 1, "chunks_list": ["chunk-5ca7d3835141b4ca396923098108b423"], "content": "需求3：订单管理功能，支持下单、支付、取消订单", "content_summary": "需求3：订单管理功能，支持下单、支付、取消订单", "content_length": 23, "created_at": "2025-08-01T03:11:25.878409+00:00", "updated_at": "2025-08-01T03:11:14.913790+00:00", "file_path": "unknown_source"}}